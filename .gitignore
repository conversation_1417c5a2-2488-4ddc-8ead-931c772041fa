# Dependencies
node_modules
.pnp
.pnp.js
.npm

# Testing
coverage
.nyc_output
cypress/screenshots/*
cypress/videos/*

# Production
build
dist
out
.next
.nuxt
.output

# Turbo
.turbo

# Misc
.DS_Store
*.pem
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Vercel
.vercel

# Netlify
.netlify

# TypeScript
*.tsbuildinfo
next-env.d.ts

# TypeScript generated files
*.d.ts
*.js
*.map
!*.config.js
!vite.config.ts
!env.d.ts

# Cache
.cache
.eslintcache
.stylelintcache
.parcel-cache

# Logs
logs
*.log

*.map

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Storybook
storybook-static

# Docusaurus
.docusaurus

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Temporary files
tmp
temp
.temp
docs/.vitepress/cache

# Go build artifacts
/backend/crf-backend
/backend/bin/

# Node.js compile cache
node-compile-cache/

# Additional build outputs
/internal/*/dist/
