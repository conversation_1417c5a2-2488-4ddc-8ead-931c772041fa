import type { BuildConfig } from 'unbuild'

/**
 * 基础unbuild配置
 */
function createBaseBuildConfig(externals: string[] = []): BuildConfig {
  return {
    entries: ['index'],
    declaration: true,
    clean: true,
    failOnWarn: false,
    rollup: {
      emitCJS: true,
      inlineDependencies: true
    },
    externals: [
      'vue',
      '@vue/runtime-core',
      '@vue/runtime-dom',
      '@vue/reactivity',
      '@vue/shared',
      ...externals
    ]
  }
}

/**
 * 组件库unbuild配置
 */
export function createComponentBuildConfig(): BuildConfig[] {
  return [
    {
      ...createBaseBuildConfig([
        'naive-ui',
        '@iconify/vue',
        '@crf/types',
        '@crf/utils',
        '@crf/hooks'
      ]),
      entries: [
        'index',
        'base/index',
        'card/index',
        'icon/index',
        'registry/index',
        'dev-tools/index',
        'lazy-load/index',
        'virtual-list/index'
      ]
    }
  ]
}

/**
 * 类型包unbuild配置
 */
export function createTypesBuildConfig(): BuildConfig[] {
  return [createBaseBuildConfig()]
}

/**
 * 工具包unbuild配置
 */
export function createUtilsBuildConfig(options: Partial<BuildConfig> = {}): BuildConfig[] {
  return [{
    ...createBaseBuildConfig(['@crf/types']),
    ...options
  }]
}

/**
 * Hooks包unbuild配置
 */
export function createHooksBuildConfig(): BuildConfig[] {
  return [createBaseBuildConfig(['@crf/types', '@crf/utils'])]
}

/**
 * 常量包unbuild配置
 */
export function createConstantsBuildConfig(): BuildConfig[] {
  return [createBaseBuildConfig()]
}

/**
 * Composables包unbuild配置
 */
export function createComposablesBuildConfig(options: Partial<BuildConfig> = {}): BuildConfig[] {
  return [{
    ...createBaseBuildConfig(['@crf/types', '@crf/utils', '@crf/hooks']),
    entries: ['src/index'],
    // 支持多入口点构建以减少内存压力
    rollup: {
      emitCJS: true,
      inlineDependencies: false,
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false
      },
      output: {
        sourcemap: false
      },
      esbuild: {
        target: 'es2020',
        minify: false
      }
    },
    ...options
  }]
}

/**
 * 主题包unbuild配置
 */
export function createThemeBuildConfig(options: Partial<BuildConfig> = {}): BuildConfig[] {
  return [{
    ...createBaseBuildConfig(['@crf/types', '@crf/constants', '@unocss/core', 'sass']),
    entries: ['src/index'],
    ...options
  }]
}

/**
 * 网络包unbuild配置
 */
export function createNetworkBuildConfig(options: Partial<BuildConfig> = {}): BuildConfig[] {
  return [{
    ...createBaseBuildConfig(['@crf/types', '@crf/utils', '@crf/constants', '@crf/composables']),
    entries: ['src/index'],
    ...options
  }]
}

/**
 * 编辑器核心包unbuild配置
 */
export function createEditorCoreBuildConfig(options: Partial<BuildConfig> = {}): BuildConfig[] {
  return [{
    ...createBaseBuildConfig(['@crf/types', '@crf/utils', '@crf/constants']),
    entries: ['src/index'],
    ...options
  }]
}
