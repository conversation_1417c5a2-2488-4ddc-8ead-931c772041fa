import { defineConfig, type UserConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import compression from 'vite-plugin-compression'

/**
 * CRF项目通用Vite配置
 * 提供优化的构建配置和开发体验
 */
export function createCrfViteConfig(options: {
  /** 项目根目录 */
  root?: string
  /** 构建输出目录 */
  outDir?: string
  /** 是否启用分析 */
  analyze?: boolean
  /** 是否启用压缩 */
  compress?: boolean
  /** 额外的别名配置 */
  alias?: Record<string, string>
  /** 额外的插件 */
  plugins?: any[]
}): UserConfig {
  const {
    root = process.cwd(),
    outDir = 'dist',
    analyze = false,
    compress = true,
    alias = {},
    plugins = []
  } = options

  return defineConfig({
    root,
    
    // 开发服务器配置
    server: {
      port: 3000,
      host: true,
      open: true,
      cors: true,
      hmr: {
        overlay: true
      }
    },
    
    // 预览服务器配置
    preview: {
      port: 4173,
      host: true,
      open: true
    },
    
    // 构建配置
    build: {
      outDir,
      sourcemap: process.env.NODE_ENV === 'development',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: process.env.NODE_ENV === 'production',
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          // 分包策略
          manualChunks: {
            // Vue 核心
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // UI 库
            'ui-vendor': ['naive-ui', 'element-plus'],
            // 工具库
            'utils-vendor': ['lodash-es', 'dayjs', 'axios'],
            // CRF 组件
            'crf-components': ['@crf/components'],
            // CRF 工具
            'crf-utils': ['@crf/utils', '@crf/composables']
          },
          // 文件命名
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') || []
            const ext = info[info.length - 1]
            
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)$/.test(assetInfo.name || '')) {
              return `media/[name]-[hash].${ext}`
            }
            if (/\.(png|jpe?g|gif|svg|webp|avif)$/.test(assetInfo.name || '')) {
              return `images/[name]-[hash].${ext}`
            }
            if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name || '')) {
              return `fonts/[name]-[hash].${ext}`
            }
            return `assets/[name]-[hash].${ext}`
          }
        }
      },
      // 构建性能优化
      chunkSizeWarningLimit: 1000,
      assetsInlineLimit: 4096
    },
    
    // 路径解析
    resolve: {
      alias: {
        '@': resolve(root, 'src'),
        '@crf/components': resolve(root, '../../packages/components'),
        '@crf/utils': resolve(root, '../../packages/utils'),
        '@crf/composables': resolve(root, '../../packages/composables'),
        '@crf/types': resolve(root, '../../packages/types'),
        '@crf/theme': resolve(root, '../../packages/theme'),
        ...alias
      }
    },
    
    // CSS 配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@crf/theme/src/styles/variables.scss";
            @use "@crf/theme/src/styles/mixins.scss";
          `
        }
      },
      modules: {
        localsConvention: 'camelCase'
      }
    },
    
    // 优化配置
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'naive-ui',
        'element-plus',
        'lodash-es',
        'dayjs',
        'axios'
      ],
      exclude: [
        '@crf/components',
        '@crf/utils',
        '@crf/composables',
        '@crf/types',
        '@crf/theme'
      ]
    },
    
    // 插件配置
    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      
      // 压缩插件
      ...(compress ? [
        compression({
          algorithm: 'gzip',
          ext: '.gz'
        }),
        compression({
          algorithm: 'brotliCompress',
          ext: '.br'
        })
      ] : []),
      
      // 分析插件
      ...(analyze ? [
        visualizer({
          filename: 'dist/stats.html',
          open: true,
          gzipSize: true,
          brotliSize: true
        })
      ] : []),
      
      // 额外插件
      ...plugins
    ],
    
    // 环境变量
    define: {
      __VUE_OPTIONS_API__: false,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    },
    
    // 实验性功能
    experimental: {
      renderBuiltUrl(filename, { hostType }) {
        if (hostType === 'js') {
          return { js: `/${filename}` }
        }
        return { relative: true }
      }
    }
  })
}

/**
 * 开发环境专用配置
 */
export function createDevConfig(options: Parameters<typeof createCrfViteConfig>[0] = {}) {
  return createCrfViteConfig({
    ...options,
    analyze: false,
    compress: false
  })
}

/**
 * 生产环境专用配置
 */
export function createProdConfig(options: Parameters<typeof createCrfViteConfig>[0] = {}) {
  return createCrfViteConfig({
    ...options,
    analyze: true,
    compress: true
  })
}

/**
 * 组件库构建配置
 */
export function createLibConfig(options: {
  /** 入口文件 */
  entry: string
  /** 库名称 */
  name: string
  /** 输出目录 */
  outDir?: string
  /** 外部依赖 */
  external?: string[]
}) {
  const { entry, name, outDir = 'dist', external = [] } = options
  
  return defineConfig({
    build: {
      lib: {
        entry,
        name,
        formats: ['es', 'umd'],
        fileName: (format) => `index.${format}.js`
      },
      outDir,
      rollupOptions: {
        external: [
          'vue',
          'vue-router',
          'pinia',
          ...external
        ],
        output: {
          globals: {
            vue: 'Vue',
            'vue-router': 'VueRouter',
            pinia: 'Pinia'
          }
        }
      }
    },
    plugins: [
      vue()
    ]
  })
}

// 导出默认配置
export default createCrfViteConfig
