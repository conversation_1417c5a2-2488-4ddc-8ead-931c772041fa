{"name": "@crf/build-config", "version": "1.0.0", "description": "CRF项目构建配置", "type": "module", "main": "index.ts", "exports": {".": {"import": "./index.ts", "require": "./index.ts"}, "./vite": {"import": "./vite.config.ts", "require": "./vite.config.ts"}}, "files": ["index.ts", "vite.config.ts"], "dependencies": {"vite": "^6.2.4", "@vitejs/plugin-vue": "^5.2.1", "rollup-plugin-visualizer": "^5.12.0", "vite-plugin-compression": "^0.5.1"}, "peerDependencies": {"vue": "^3.5.0"}, "keywords": ["vite", "build", "config", "crf", "medical"], "author": "CRF Team", "license": "MIT"}