# @crf/build-config

CRF项目的统一构建配置包，提供预设的Vite和unbuild配置。

## 功能特性

- 🚀 预设的Vite配置，优化开发和生产构建
- 📦 预设的unbuild配置，适用于不同类型的包
- 🔧 可扩展的配置选项
- 📝 TypeScript支持
- 🎯 针对monorepo优化

## 安装

```bash
pnpm add -D @crf/build-config
```

## Vite配置

### 基础用法

```typescript
// vite.config.ts
import { createCrfViteConfig } from '@crf/build-config/vite'

export default createCrfViteConfig({
  // 可选配置
})
```

### 配置选项

```typescript
interface ViteConfigOptions {
  /** 项目根目录 */
  root?: string
  /** 构建输出目录 */
  outDir?: string
  /** 是否启用分析 */
  analyze?: boolean
  /** 是否启用压缩 */
  compress?: boolean
  /** 额外的别名配置 */
  alias?: Record<string, string>
  /** 额外的插件 */
  plugins?: any[]
}
```

### 预设配置

- **开发配置**: `createDevConfig()`
- **生产配置**: `createProdConfig()`
- **库配置**: `createLibConfig()`

## unbuild配置

### 包类型预设

#### 组件包
```typescript
// build.config.ts
import { createComponentBuildConfig } from '@crf/build-config'

export default createComponentBuildConfig({
  entries: ['src/index'],
  externals: ['additional-external']
})
```

#### 工具包
```typescript
import { createUtilsBuildConfig } from '@crf/build-config'

export default createUtilsBuildConfig()
```

#### 类型包
```typescript
import { createTypesBuildConfig } from '@crf/build-config'

export default createTypesBuildConfig()
```

#### 组合式函数包
```typescript
import { createComposablesBuildConfig } from '@crf/build-config'

export default createComposablesBuildConfig({
  entries: [
    'src/index',
    'src/specific-composable'
  ]
})
```

#### 主题包
```typescript
import { createThemeBuildConfig } from '@crf/build-config'

export default createThemeBuildConfig()
```

#### 网络包
```typescript
import { createNetworkBuildConfig } from '@crf/build-config'

export default createNetworkBuildConfig()
```

#### 编辑器核心包
```typescript
import { createEditorCoreBuildConfig } from '@crf/build-config'

export default createEditorCoreBuildConfig()
```

#### 常量包
```typescript
import { createConstantsBuildConfig } from '@crf/build-config'

export default createConstantsBuildConfig()
```

#### Hooks包
```typescript
import { createHooksBuildConfig } from '@crf/build-config'

export default createHooksBuildConfig()
```

## 自定义配置

所有预设配置都支持传入自定义选项：

```typescript
import { createComponentBuildConfig } from '@crf/build-config'

export default createComponentBuildConfig({
  entries: ['src/index', 'src/components'],
  externals: ['custom-external'],
  declaration: true,
  clean: true
})
```

## 最佳实践

1. **选择合适的预设**: 根据包的类型选择对应的预设配置
2. **最小化外部依赖**: 只添加必要的外部依赖
3. **多入口点**: 对于大型包，使用多入口点减少构建压力
4. **类型声明**: 确保生成TypeScript声明文件

## 配置示例

### 完整的包配置示例

```typescript
// packages/my-package/build.config.ts
import { createComponentBuildConfig } from '@crf/build-config'
import type { BuildConfig } from 'unbuild'

const config: BuildConfig[] = createComponentBuildConfig({
  entries: [
    'src/index',
    'src/components/index',
    'src/utils/index'
  ],
  externals: [
    'lodash-es',
    'dayjs'
  ]
})

export default config
```

### package.json脚本配置

```json
{
  "scripts": {
    "build": "unbuild",
    "dev": "unbuild --watch",
    "type-check": "vue-tsc --noEmit"
  },
  "devDependencies": {
    "@crf/build-config": "workspace:*",
    "unbuild": "^3.5.0"
  }
}
```

## 许可证

MIT
