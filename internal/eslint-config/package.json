{"name": "@crf/eslint-config", "version": "1.0.0", "description": "ESLint 统一配置 - 扁平配置格式", "type": "module", "files": ["index.js"], "main": "index.js", "exports": {".": "./index.js"}, "peerDependencies": {"eslint": "^9.0.0"}, "dependencies": {"@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-unicorn": "^59.0.1", "eslint-plugin-vue": "^10.1.0", "jsonc-eslint-parser": "^2.4.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "yaml-eslint-parser": "^1.3.0"}, "devDependencies": {"eslint": "^9.27.0"}}