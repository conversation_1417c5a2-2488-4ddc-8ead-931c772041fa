#!/bin/bash

# 安全构建脚本 - 解决内存问题
set -e

echo "🔧 设置内存限制为8GB..."
export NODE_OPTIONS="--max-old-space-size=8192"

echo "🧹 清理构建缓存..."
pnpm clean || true

echo "📦 分批构建packages..."

# 第一批：基础工具包
echo "构建基础包..."
pnpm -r --filter="@crf/types" build
pnpm -r --filter="@crf/constants" build  
pnpm -r --filter="@crf/utils" build

# 第二批：组合式API和主题
echo "构建hooks和主题..."
pnpm -r --filter="@crf/hooks" build
pnpm -r --filter="@crf/theme" build

# 第三批：复杂组件包（单独构建）
echo "构建组件包..."
pnpm -r --filter="@crf/components" build

# 第四批：核心编辑器
echo "构建编辑器核心..."
pnpm -r --filter="@crf/editor-core" build
pnpm -r --filter="@crf/composables" build

echo "🚀 构建应用..."
pnpm run build:apps

echo "✅ 构建完成！"