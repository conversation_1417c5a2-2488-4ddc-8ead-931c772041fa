import { defineConfig, presetAttributify, presetIcons } from 'unocss'
import  { presetWind3 } from '@unocss/preset-wind3'

export default defineConfig({
  // 启用预设
  presets: [
    presetWind3(), // 默认预设，包含Tailwind CSS兼容的工具类
    presetAttributify(), // 属性化模式
    presetIcons({
      collections: {
        // 可以添加图标集合
      }
    })
  ],
  
  // 主题配置
  theme: {
    colors: {
      // 与SASS变量保持一致的颜色系统
      primary: {
        DEFAULT: 'var(--primary-color)',
        hover: 'var(--primary-hover)',
        pressed: 'var(--primary-pressed)',
        50: '#f0f4ff',
        100: '#e0e9ff',
        500: 'var(--primary-color)',
        600: 'var(--primary-hover)',
        700: 'var(--primary-pressed)',
      },
      secondary: 'var(--secondary-color)',
      accent: 'var(--accent-color)',
      success: 'var(--success-color)',
      warning: 'var(--warning-color)',
      error: 'var(--error-color)',
      info: 'var(--info-color)',
      
      // 文本颜色
      text: {
        primary: 'var(--text-primary)',
        secondary: 'var(--text-secondary)',
        tertiary: 'var(--text-tertiary)',
        disabled: 'var(--text-disabled)',
      },
      
      // 背景颜色
      bg: {
        base: 'var(--body-color)',
        page: 'var(--page-background)',
        card: 'var(--card-background)',
      },
      
      // 边框颜色
      border: {
        DEFAULT: 'var(--border-color)',
        light: 'var(--border-color)',
        divider: 'var(--divider-color)',
      },
      
      // 编辑器专用颜色
      edit: {
        bg: 'var(--edit-background-color)',
        border: 'var(--edit-border-color)',
        'disabled-text': 'var(--edit-disabled-text-color)',
      },
      'block-hover': 'var(--color-block-hover)',
    },
    spacing: {
      // 编辑器布局尺寸
      'header': 'var(--edit-header-height)',
      'block': 'var(--edit-block-width)',
      'config': 'var(--edit-config-width)',
      // 扩展间距系统
      '18': '4.5rem',
      '72': '18rem',
      '84': '21rem',
      '96': '24rem',
    },
    borderRadius: {
      'DEFAULT': 'var(--border-radius)',
      'sm': 'var(--border-radius-sm)',
      'md': 'var(--border-radius-md)',
      'lg': 'var(--border-radius-lg)',
      'xl': 'var(--border-radius-xl)',
    },
    fontSize: {
      'xs': '0.75rem',
      'sm': '0.875rem',
      'base': '1rem',
      'lg': '1.125rem',
      'xl': '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    lineHeight: {
      'tight': '1.25',
      'normal': '1.5',
      'relaxed': '1.75',
    },
    boxShadow: {
      'sm': 'var(--box-shadow-1)',
      'DEFAULT': 'var(--box-shadow-2)',
      'lg': 'var(--box-shadow-3)',
    }
  },
  
  // 快捷方式定义
  shortcuts: {
    // 按钮样式
    'btn': 'px-4 py-2 rounded cursor-pointer transition-all duration-200 font-medium',
    'btn-primary': 'btn bg-primary text-white hover:bg-primary-hover active:bg-primary-pressed',
    'btn-secondary': 'btn bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300',
    'btn-outline': 'btn border border-primary text-primary hover:bg-primary hover:text-white',
    'btn-ghost': 'btn text-primary hover:bg-primary-50',
    'btn-sm': 'px-3 py-1.5 text-sm rounded',
    'btn-lg': 'px-6 py-3 text-lg rounded-lg',
    
    // 卡片样式
    'card': 'bg-white rounded-lg shadow-sm border border-gray-200 p-4',
    'card-base': 'bg-white border border-gray-200 rounded shadow-sm',
    'card-hover': 'card hover:shadow-md transition-shadow duration-200',
    'card-bordered': 'card border-2',
    'card-compact': 'card p-3',
    'card-spacious': 'card p-6',
    
    // 表单样式
    'form-section': 'bg-white',
    'form-item': 'mb-4',
    'form-group': 'mb-4',
    'form-label': 'block text-sm font-medium text-gray-700 mb-1',
    'form-input': 'w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors',
    'form-error': 'text-sm text-error mt-1',
    
    // 布局样式
    'flex-center': 'flex items-center justify-center',
    'flex-between': 'flex items-center justify-between',
    'flex-col-center': 'flex flex-col items-center justify-center',
    'flex-start': 'flex items-center justify-start',
    'flex-end': 'flex items-center justify-end',
    'grid-center': 'grid place-items-center',
    'absolute-center': 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
    
    // 文本样式
    'text-heading': 'text-2xl font-bold text-gray-900',
    'text-subheading': 'text-lg font-semibold text-gray-800',
    'text-body': 'text-base text-gray-700',
    'text-caption': 'text-sm text-gray-500',
    'text-muted': 'text-gray-400',
    
    // 状态样式
    'loading': 'opacity-50 pointer-events-none',
    'disabled': 'opacity-60 cursor-not-allowed',
    'hidden-mobile': 'hidden md:block',
    'hidden-desktop': 'block md:hidden',
    'state-success': 'border-success bg-green-50',
    'state-warning': 'border-warning bg-yellow-50',
    'state-error': 'border-red-500 bg-red-50',
    
    // 间距样式
    'section-padding': 'px-4 py-6 md:px-6 md:py-8',
    'container-padding': 'px-4 md:px-6 lg:px-8',
    
    // 编辑器专用样式
    'editor-panel': 'h-full bg-white border-r border-edit-border',
    'editor-content': 'flex-1 bg-edit-bg overflow-auto',
    'component-item': 'p-3 border border-gray-200 rounded mb-2 cursor-pointer hover:border-primary transition-colors',
    'component-selected': 'border-primary bg-blue-50',
    'edit-panel': 'bg-white border border-edit-border rounded-lg p-4',
    'edit-toolbar': 'flex items-center gap-2 p-2 bg-gray-50 border-b border-gray-200',
    'edit-block': 'border border-dashed border-gray-300 rounded p-4 hover:border-primary transition-colors',
    'edit-block-active': 'edit-block border-primary bg-primary-50',
    
    // 拖拽相关样式
    'drag-ghost': 'opacity-50 bg-primary border-2 border-dashed border-primary-hover',
    'drag-chosen': 'transform rotate-1',
    'drag-moving': 'bg-white shadow-lg',
    
    // 响应式容器
    'container': 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
    'container-sm': 'max-w-3xl mx-auto px-4 sm:px-6',
    'container-xs': 'max-w-md mx-auto px-4',
  },
  
  // 规则定义
  rules: [
    // 自定义动画规则
    [/^animate-fade-in-(.+)$/, ([, d]) => ({
      animation: `fadeIn ${d}ms ease-in-out`
    })],
    [/^animate-slide-in-(.+)$/, ([, d]) => ({
      animation: `slideIn ${d}ms ease-out`
    })],
    
    // 自定义阴影
    ['shadow-editor', { 'box-shadow': '0 4px 12px rgba(0, 0, 0, 0.05)' }],
    ['shadow-component', { 'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)' }],
    
    // 滚动条样式
    ['scrollbar-thin', {
      'scrollbar-width': 'thin',
      'scrollbar-color': 'var(--primary-color) transparent'
    }],
  ],
  
  // 变体定义
  variants: [
    // 支持暗色模式
    (matcher) => {
      if (!matcher.startsWith('dark:')) return matcher
      return {
        matcher: matcher.slice(5),
        selector: s => `.dark ${s}`
      }
    },
    
    // 支持移动端变体
    (matcher) => {
      if (!matcher.startsWith('mobile:')) return matcher
      return {
        matcher: matcher.slice(7),
        selector: s => s,
        mediaQuery: '(max-width: 768px)'
      }
    }
  ],
  
  // 安全列表（确保这些类不会被清除）
  safelist: [
    'flex',
    'items-center',
    'justify-center',
    'bg-white',
    'text-primary',
    'border-primary',
    'hover:bg-primary-hover',
    'transition-colors',
    'rounded',
    'shadow-sm',
    'p-4',
    'mb-4',
    'w-full',
    'h-full'
  ],
  
  // 预检样式（重置样式冲突处理）
  preflights: [
    {
      getCSS: () => `
        /* UnoCSS与SASS兼容性处理 */
        :root {
          /* 确保CSS变量可用 */
          --uno-primary: var(--primary-color, #2563eb);
          --uno-spacing: 1rem;
        }
        
        /* 防止与Element Plus样式冲突 */
        .el-button {
          /* 保持Element Plus按钮样式优先级 */
        }
      `
    }
  ],
  
  // 内容扫描配置
  content: {
    filesystem: [
      'src/**/*.{vue,js,ts,jsx,tsx}',
      'packages/**/*.{vue,js,ts,jsx,tsx}'
    ]
  }
})