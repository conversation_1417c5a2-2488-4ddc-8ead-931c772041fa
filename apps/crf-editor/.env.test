# 测试环境配置
NODE_ENV=test

# 应用基础信息
VITE_APP_TITLE=CRF表单编辑器 (测试版)
VITE_APP_VERSION=1.0.0-test
VITE_APP_DESCRIPTION=可视化表单编辑器 - 测试环境

# API 配置
VITE_API_BASE_URL=http://test-api.example.com/api
VITE_API_TIMEOUT=15000

# 应用配置
VITE_APP_BASE_URL=/
VITE_APP_PUBLIC_PATH=/

# 功能开关 - 测试环境部分启用
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_CONSOLE_LOG=true
VITE_ENABLE_ERROR_LOG=true

# 开发工具 - 测试环境启用
VITE_ENABLE_VUE_DEVTOOLS=true
VITE_ENABLE_PERFORMANCE_MONITOR=true

# 构建配置 - 测试环境启用源码映射
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_ANALYZE=true

# 其他配置
VITE_APP_STORAGE_PREFIX=crf_editor_test_
VITE_APP_DEFAULT_LANGUAGE=zh-CN

# 测试专用配置
VITE_TEST_TIMEOUT=30000
VITE_TEST_COVERAGE=true
VITE_TEST_MOCK_API=true
