import type { CoreValidationResult } from '@crf/type-definitions'

// 定义本地类型
type FormData = Record<string, unknown>
type ValidationResult = CoreValidationResult
import type { FormSchema, FormSection } from '@/types/schema'

// 导出选项接口
export interface ExportOptions {
  format: 'json' | 'table'
  includeMetadata?: boolean
  sections?: string[]
}

// 表格数据接口
export interface TableData {
  headers: string[]
  rows: unknown[][]
}

/**
 * 数据转换工具类
 * 用于处理 CRF 编辑器的数据转换、导入导出等功能
 */
export class DataConverter {
  /**
   * 将 Schema 和表单数据转换为表格格式
   */
  static toTableData(
    schema: FormSchema,
    formData: FormData,
    validationResults: ValidationResult,
  ): TableData {
    const headers: string[] = []
    const rows: unknown[][] = [[]] // 第一行用于存储数据

    // 递归处理章节和字段
    const processSection = (section: FormSection, parentPath = '') => {
      const path = parentPath ? `${parentPath} > ${section.name}` : section.name

      // 处理当前章节的字段
      section.blocks?.forEach((block) => {
        headers.push(`${path} > ${String(block.name || '')}`)
        if (rows[0]) {
          const blockId = String(block.id)
          rows[0].push(formData[blockId])
        }
      })

      // Note: FormSection does not have children property
    }

    // 处理所有章节
    schema.sections.forEach((section: FormSection) => processSection(section))

    // 添加验证状态
    if (validationResults && rows[0]) {
      headers.push('验证状态')
      rows[0].push(
        Object.values(validationResults).every(
          (result: Record<string, unknown>) => result.valid,
        )
          ? '通过'
          : '未通过',
      )
    }

    return { headers, rows }
  }

  /**
   * 将表格数据转换回表单数据
   */
  static fromTableData(tableData: TableData): {
    formData: Record<string, unknown>
    validationResults: Record<string, { isValid: boolean; errors: string[] }>
  } {
    const formData: Record<string, unknown> = {}
    const validationResults: Record<
      string,
      { isValid: boolean; errors: string[] }
    > = {}

    // 跳过表头
    tableData.rows.forEach((row) => {
      const [id, , , value, status, errors] = row
      const idStr = String(id)
      formData[idStr] = value
      validationResults[idStr] = {
        isValid: status === '通过',
        errors:
          typeof errors === 'string' ? errors.split('; ').filter(Boolean) : [],
      }
    })

    return { formData, validationResults }
  }

  /**
   * 导出数据
   */
  static exportData(
    schema: FormSchema,
    formData: FormData,
    validationResults: ValidationResult,
    options: ExportOptions,
  ): unknown {
    const { format, includeMetadata = true, sections = [] } = options

    // 过滤章节
    const filterSchema = (schema: FormSchema): FormSchema => {
      if (!sections.length) return schema

      const filterSection = (section: FormSection): FormSection | null => {
        if (!sections.includes(section.id)) return null

        return {
          ...section,
        }
      }

      return {
        ...schema,
        sections: schema.sections
          .map(filterSection)
          .filter(
            (section: unknown): section is FormSection => section !== null,
          ),
      }
    }

    // 过滤表单数据
    const filterFormData = (
      formData: FormData,
      schema: FormSchema,
    ): FormData => {
      const result: FormData = {}
      const collectBlockIds = (section: FormSection): string[] => {
        const ids: string[] = []
        section.blocks?.forEach((block) => ids.push(String(block.id)))
        return ids
      }

      const validBlockIds = schema.sections
        .flatMap(collectBlockIds)
        .filter(Boolean)

      Object.entries(formData).forEach(([key, value]) => {
        if (validBlockIds.includes(key)) {
          result[key] = value
        }
      })

      return result
    }

    // 根据格式处理数据
    if (format === 'json') {
      const filteredSchema = filterSchema(schema)
      const filteredFormData = filterFormData(formData, filteredSchema)

      return {
        ...(includeMetadata
          ? {
              metadata: {
                version: '1.0',
                exportTime: new Date().toISOString(),
                schema: filteredSchema,
              },
            }
          : {}),
        formData: filteredFormData,
        validationResults,
      }
    } else {
      // 转换为表格格式
      return this.toTableData(
        filterSchema(schema),
        filterFormData(formData, schema),
        validationResults,
      )
    }
  }

  /**
   * 导入数据
   */
  static importData(
    data: unknown,
    format: 'json' | 'table',
    schema: FormSchema,
  ): { formData: FormData; error?: string } {
    try {
      if (format === 'json') {
        // 验证数据结构
        const dataAsJson = data as Record<string, unknown>
        if (!dataAsJson.formData || typeof dataAsJson.formData !== 'object') {
          throw new Error('无效的 JSON 数据格式')
        }

        // 验证数据字段
        const validateFormData = (
          formData: Record<string, unknown>,
        ): FormData => {
          const result: FormData = {}
          const validBlockIds = new Set<string>()

          // 收集所有有效的 block ID
          const collectBlockIds = (section: FormSection) => {
            section.blocks?.forEach((block) => {
              validBlockIds.add(String(block.id))
            })
          }
          schema.sections.forEach(collectBlockIds)

          // 验证并过滤数据
          Object.entries(formData).forEach(([key, value]) => {
            if (validBlockIds.has(key)) {
              result[key] = value
            }
          })

          return result
        }

        return {
          formData: validateFormData(
            dataAsJson.formData as Record<string, unknown>,
          ),
        }
      } else {
        // 处理表格数据
        if (
          !Array.isArray((data as Record<string, unknown>).rows) ||
          !Array.isArray((data as Record<string, unknown>).headers)
        ) {
          throw new Error('无效的表格数据格式')
        }

        const formData: FormData = {}
        const headerMap = new Map<string, string>()

        // 构建字段映射
        const buildHeaderMap = (section: FormSection, parentPath = '') => {
          const path = parentPath
            ? `${parentPath} > ${section.name}`
            : section.name

          section.blocks?.forEach((block) => {
            const header = `${path} > ${String(block.name || '')}`
            headerMap.set(header, String(block.id))
          })

          // Note: FormSection does not have children property
        }
        schema.sections.forEach((section: FormSection) =>
          buildHeaderMap(section),
        )

        // 解析数据
        const dataAsRecord = data as Record<string, unknown>
        const headers = Array.isArray(dataAsRecord.headers)
          ? dataAsRecord.headers
          : []
        const rows = Array.isArray(dataAsRecord.rows) ? dataAsRecord.rows : []

        headers.forEach((header: string, index: number) => {
          const blockId = headerMap.get(header)
          if (blockId && rows[0]) {
            formData[blockId] = rows[0][index]
          }
        })

        return { formData }
      }
    } catch (error) {
      return {
        formData: {},
        error: error instanceof Error ? error.message : '导入失败',
      }
    }
  }

  /**
   * 验证导入的数据
   */
  static validateImportData(data: Record<string, unknown>): boolean {
    // 验证表格数据
    if (Array.isArray(data.rows) && Array.isArray(data.headers)) {
      const headers = data.headers as string[]
      const rows = data.rows as unknown[][]

      return (
        headers.length > 0 && // 确保有列数据
        rows.every((row: unknown[]) => row.length === headers.length)
      ) // 确保每行数据完整
    }

    // 验证 JSON 数据
    return (
      data.schema !== undefined &&
      typeof data.schema === 'object' &&
      data.schema !== null &&
      Array.isArray((data.schema as Record<string, unknown>).sections) &&
      typeof data.formData === 'object'
    )
  }
}
