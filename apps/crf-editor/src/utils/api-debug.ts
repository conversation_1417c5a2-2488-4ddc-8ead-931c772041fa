// API URL 调试工具
import { API_CONFIG, getApiUrl } from '@/api'

export const debugApiUrls = () => {
  console.log('=== API URL 调试信息 ===')
  console.log('环境:', import.meta.env.DEV ? '开发环境' : '生产环境')
  console.log('API_CONFIG:', API_CONFIG)

  console.log('\n=== URL 构建测试 ===')
  const testEndpoints = [
    '/auth/login',
    '/auth/register',
    '/users',
    '/templates',
  ]

  testEndpoints.forEach((endpoint) => {
    const fullUrl = getApiUrl(endpoint)
    console.log(`${endpoint} -> ${fullUrl}`)
  })

  console.log('\n=== 健康检查URL ===')
  const healthUrl = import.meta.env.DEV
    ? '/health'
    : `${API_CONFIG.BASE_URL}/health`
  console.log(`健康检查: ${healthUrl}`)

  console.log('=== 调试完成 ===')
}

// 在开发环境下自动调试
if (import.meta.env.DEV) {
  debugApiUrls()
}
