/**
 * CRF 编辑器组件注册工具
 * 使用新的组件注册系统
 */

import {
  componentRegistry,
  ComponentCategory,
  type ComponentMeta,
} from '@crf/ui-components'

// 重新导出类型，保持兼容性
export type { ComponentMeta }
export { ComponentCategory }

// 兼容旧版本的组件类型定义
export type ComponentType = string

// 兼容旧版本的组件注册信息
export interface ComponentRegistration {
  meta: ComponentMeta
  component: Record<string, unknown>
  configComponent?: Record<string, unknown>
  enabled: boolean
  order: number
}

/**
 * 获取组件
 */
export function getComponent(
  type: ComponentType,
): Record<string, unknown> | null {
  return componentRegistry.getComponent(type) as Record<string, unknown> | null
}

/**
 * 获取组件名称（用于动态组件渲染）
 */
export function getComponentName(type: ComponentType | string): string {
  // 使用新的注册系统获取组件元数据
  const meta = componentRegistry.getMeta(type)
  if (meta) {
    return meta.name
  }

  // 如果没有注册，使用传统的映射方式
  const componentNameMap: Record<string, string> = {
    text: 'CrfText',
    'crf-text': 'CrfText',
    textarea: 'CrfTextarea',
    'crf-textarea': 'CrfTextarea',
    radio: 'CrfRadio',
    'crf-radio': 'CrfRadio',
    checkbox: 'CrfCheckbox',
    'crf-checkbox': 'CrfCheckbox',
    select: 'CrfSelect',
    'crf-select': 'CrfSelect',
    number: 'CrfNumber',
    'crf-number': 'CrfNumber',
    date: 'CrfDate',
    'crf-date': 'CrfDate',
    'date-range': 'CrfDateRange',
    'crf-date-range': 'CrfDateRange',
    time: 'CrfTime',
    'crf-time': 'CrfTime',
    'time-range': 'CrfTimeRange',
    'crf-time-range': 'CrfTimeRange',
    card: 'CrfCard',
    'crf-card': 'CrfCard',
    icon: 'CrfIcon',
    'crf-icon': 'CrfIcon',
    'svg-icon': 'CrfSvgIcon',
    'crf-svg-icon': 'CrfSvgIcon',
  }

  // 标准化组件代码（移除前缀）
  let normalizedType = type
  if (typeof type === 'string' && type.startsWith('crf-')) {
    normalizedType = type.substring(4)
  }

  // 直接从映射表中获取组件名称
  const componentName =
    componentNameMap[type] || componentNameMap[normalizedType]
  if (componentName) {
    return componentName
  }

  // 未注册的组件返回 div，避免渲染错误
  console.warn(`[ComponentRegistry] 组件未注册: ${type}，回退到 div`)
  return 'div'
}

/**
 * 获取组件元数据
 */
export function getComponentMeta(type: ComponentType): ComponentMeta | null {
  return componentRegistry.getMeta(type)
}

/**
 * 获取配置组件
 */
export function getConfigComponent(
  type: ComponentType,
): Record<string, unknown> | null {
  return componentRegistry.getConfigComponent(type) as Record<
    string,
    unknown
  > | null
}

/**
 * 获取所有已注册的组件
 */
export function getAllComponents(): ComponentMeta[] {
  return componentRegistry
    .getAllMeta()
    .filter((meta) => meta.visible !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0))
}

/**
 * 按分类获取组件
 */
export function getComponentsByCategory(
  category: ComponentCategory,
): ComponentMeta[] {
  return componentRegistry.getComponentsByCategory(category)
}

/**
 * 获取所有分类
 */
export function getCategories(): ComponentCategory[] {
  return Object.values(ComponentCategory)
}

/**
 * 搜索组件
 */
export function searchComponents(query: string): ComponentMeta[] {
  return componentRegistry.search({ query }).components
}

/**
 * 检查组件是否已注册
 */
export function hasComponent(type: ComponentType): boolean {
  return componentRegistry.isRegistered(type)
}

/**
 * 获取组件统计信息
 */
export function getComponentStats() {
  return componentRegistry.getStats()
}
