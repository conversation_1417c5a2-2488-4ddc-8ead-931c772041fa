import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user-store'
import { createDiscreteApi } from 'naive-ui'
import { useErrorHandler } from '@/composables/useErrorHandler'

// 布局组件
import AppLayout from '@/layouts/AppLayout.vue'

// 页面组件
import LoginPage from '@/pages/auth/login.vue'
import RegisterPage from '@/pages/auth/register.vue'
import EditorPage from '@/pages/editor/index.vue'
import FormsPage from '@/pages/forms/index.vue'
import InstancesPage from '@/pages/instances/index.vue'
import FormFillIndexPage from '@/pages/forms/form-fill-index.vue'
import FormFillEditPage from '@/pages/forms/edit.vue'
import FormFillViewPage from '@/pages/forms/view.vue'

// 管理页面组件
import AdminIndexPage from '@/pages/admin/index.vue'
import AdminUsersPage from '@/pages/admin/users.vue'
import AdminRolesPage from '@/pages/admin/roles.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    // 重定向到项目工作台
    {
      path: '/',
      redirect: '/projects',
    },

    // 认证相关路由
    {
      path: '/auth',
      children: [
        {
          path: 'login',
          name: 'Login',
          component: LoginPage,
          meta: {
            title: '登录 - CRF表单编辑器',
            requiresAuth: false,
            hideForAuth: true,
          },
        },
        {
          path: 'register',
          name: 'Register',
          component: RegisterPage,
          meta: {
            title: '注册 - CRF表单编辑器',
            requiresAuth: false,
            hideForAuth: true,
          },
        },
      ],
    },

    // 主应用路由
    {
      path: '/',
      component: AppLayout,
      meta: { requiresAuth: true },
      children: [
        // 项目管理页面 - 作为主入口
        {
          path: 'projects',
          name: 'Projects',
          component: () => import('@/pages/projects/index.vue'),
          meta: {
            title: '项目工作台 - CRF表单编辑器',
            requiresPermission: {
              resource: 'project',
              action: 'read',
            },
          },
        },
        {
          path: 'projects/create',
          name: 'ProjectCreate',
          component: () => import('@/pages/projects/create.vue'),
          meta: {
            title: '创建项目 - CRF表单编辑器',
            requiresPermission: {
              resource: 'project',
              action: 'create',
            },
          },
        },
        {
          path: 'projects/:id',
          name: 'ProjectDetail',
          component: () => import('@/pages/projects/detail.vue'),
          meta: {
            title: '项目详情 - CRF表单编辑器',
            requiresPermission: {
              resource: 'project',
              action: 'read',
            },
          },
        },
        {
          path: 'projects/:id/templates',
          name: 'ProjectTemplates',
          component: () => import('@/pages/projects/templates.vue'),
          meta: {
            title: '项目表单模板 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'read',
            },
          },
        },
        {
          path: 'projects/:id/data',
          name: 'ProjectData',
          component: () => import('@/pages/projects/data.vue'),
          meta: {
            title: '项目数据收集 - CRF表单编辑器',
            requiresPermission: {
              resource: 'instance',
              action: 'read',
            },
          },
        },
        {
          path: 'projects/:id/analysis',
          name: 'ProjectAnalysis',
          component: () => import('@/pages/projects/analysis.vue'),
          meta: {
            title: '项目数据分析 - CRF表单编辑器',
            requiresPermission: {
              resource: 'data',
              action: 'read',
            },
          },
        },

        // 表单设计 - 模板管理
        {
          path: 'templates',
          name: 'Templates',
          component: () => import('@/pages/templates/index.vue'),
          meta: {
            title: '表单设计 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'read',
            },
          },
        },
        {
          path: 'templates/create',
          name: 'TemplateCreate',
          component: () => import('@/pages/templates/create.vue'),
          meta: {
            title: '创建模板 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'create',
            },
          },
        },
        {
          path: 'templates/:id/detail',
          name: 'TemplateDetail',
          component: () => import('@/pages/templates/detail.vue'),
          meta: {
            title: '模板详情 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'read',
            },
          },
        },

        // 数据收集 - 实例管理
        {
          path: 'instances',
          name: 'Instances',
          component: InstancesPage,
          meta: {
            title: '数据收集 - CRF表单编辑器',
          },
        },

        // 数据分析 - 新增入口
        {
          path: 'data-analysis',
          name: 'DataAnalysis',
          component: () => import('@/pages/data-analysis/index.vue'),
          meta: {
            title: '数据分析 - CRF表单编辑器',
            requiresPermission: {
              resource: 'data',
              action: 'read',
            },
          },
        },

        // 兼容旧路由 - 表单管理重定向到项目工作台
        {
          path: 'forms',
          redirect: '/projects',
        },
        {
          path: 'forms/:id/data',
          name: 'FormData',
          component: () => import('@/pages/forms/[id]/data.vue'),
          meta: {
            title: '表单数据 - CRF表单编辑器',
          },
        },

        // 表单填写入口
        {
          path: 'form-fill',
          name: 'FormFillIndex',
          component: FormFillIndexPage,
          meta: {
            title: '表单填写 - CRF表单编辑器',
          },
        },

        // 管理相关路由
        {
          path: 'admin',
          name: 'Admin',
          component: AdminIndexPage,
          meta: {
            title: '系统管理 - CRF表单编辑器',
            requiresPermission: {
              resource: 'user',
              action: 'read',
            },
          },
        },
        {
          path: 'admin/users',
          name: 'AdminUsers',
          component: AdminUsersPage,
          meta: {
            title: '用户管理 - CRF表单编辑器',
            requiresPermission: {
              resource: 'user',
              action: 'read',
            },
          },
        },
        {
          path: 'admin/roles',
          name: 'AdminRoles',
          component: AdminRolesPage,
          meta: {
            title: '角色管理 - CRF表单编辑器',
            requiresPermission: {
              resource: 'role',
              action: 'read',
            },
          },
        },
        {
          path: 'admin/i18n-demo',
          name: 'MedicalI18nDemo',
          component: () => import('@/components/medical/MedicalI18nDemo.vue'),
          meta: {
            title: '医疗国际化演示 - CRF表单编辑器',
            requiresPermission: {
              resource: 'admin',
              action: 'read',
            },
          },
        },

        // 其他页面
        // 仪表板页面
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/pages/dashboard/index.vue'),
          meta: {
            title: '仪表板 - CRF表单编辑器',
          },
        },
        // 设置页面
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/pages/settings/index.vue'),
          meta: {
            title: '系统设置 - CRF表单编辑器',
          },
        },
        {
          path: 'settings/api-test',
          name: 'ApiTest',
          component: () => import('@/pages/settings/api-test.vue'),
          meta: {
            title: 'API测试 - CRF表单编辑器',
          },
        },
        // 个人资料页面
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/pages/user/profile.vue'),
          meta: {
            title: '个人资料 - CRF表单编辑器',
          },
        },
        // 模板管理页面
        {
          path: 'templates',
          name: 'Templates',
          component: () => import('@/pages/templates/index.vue'),
          meta: {
            title: '模板管理 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'read',
            },
          },
        },
        {
          path: 'templates/create',
          name: 'TemplateCreate',
          component: () => import('@/pages/templates/create.vue'),
          meta: {
            title: '创建模板 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'create',
            },
          },
        },
        {
          path: 'templates/:id/detail',
          name: 'TemplateDetail',
          component: () => import('@/pages/templates/detail.vue'),
          meta: {
            title: '模板详情 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'read',
            },
          },
        },
        // 项目管理页面
        {
          path: 'projects',
          name: 'Projects',
          component: () => import('@/pages/projects/index.vue'),
          meta: {
            title: '项目管理 - CRF表单编辑器',
            requiresPermission: {
              resource: 'project',
              action: 'read',
            },
          },
        },
        {
          path: 'projects/create',
          name: 'ProjectCreate',
          component: () => import('@/pages/projects/create.vue'),
          meta: {
            title: '创建项目 - CRF表单编辑器',
            requiresPermission: {
              resource: 'project',
              action: 'create',
            },
          },
        },
        // 发布管理页面
        {
          path: 'publish',
          name: 'Publish',
          component: () => import('@/pages/publish/index.vue'),
          meta: {
            title: '发布管理 - CRF表单编辑器',
            requiresPermission: {
              resource: 'template',
              action: 'publish',
            },
          },
        },
        // 组件测试页面（开发环境）
        {
          path: 'component-test',
          name: 'ComponentTest',
          component: () => import('@/pages/ComponentTest.vue'),
          meta: {
            title: '组件测试 - CRF表单编辑器',
          },
        },
      ],
    },

    // 编辑器页面（全屏显示）
    {
      path: '/editor',
      name: 'Editor',
      component: EditorPage,
      meta: {
        title: 'CRF表单编辑器',
        requiresAuth: true,
      },
    },

    // 表单填写相关页面（需要登录）
    {
      path: '/form-fill/edit/:id',
      name: 'FormFillEdit',
      component: FormFillEditPage,
      meta: {
        title: '填写表单 - CRF表单编辑器',
        requiresAuth: true,
      },
    },
    {
      path: '/form-fill/view/:id',
      name: 'FormFillView',
      component: FormFillViewPage,
      meta: {
        title: '查看表单 - CRF表单编辑器',
        requiresAuth: true,
      },
    },

    // 表单填写页面（公开访问）
    {
      path: '/forms/:id/fill',
      name: 'FormFill',
      component: () => import('@/pages/forms/fill.vue'),
      meta: {
        title: '填写表单 - CRF表单编辑器',
        requiresAuth: false, // 允许匿名用户填写
      },
    },

    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/pages/error/404.vue'),
      meta: {
        title: '页面未找到',
      },
    },
  ],
})

// 路由相关的消息API已在 setupErrorHandler 中创建

// 获取错误处理实例（在路由守卫外创建，避免每次路由变化都创建新实例）
const { handleRoutePermissionError } = useErrorHandler()

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const userStore = useUserStore()

  console.log('路由守卫检查:', {
    路径: to.path,
    需要认证: !!to.meta?.requiresAuth,
    需要权限: !!to.meta?.requiresPermission,
    当前登录状态: userStore.isLoggedIn,
    用户信息: !!userStore.user,
    Token存在: !!userStore.token,
    本地Token存在: !!localStorage.getItem('auth_token'),
  })

  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }

  // 如果用户已登录但访问登录/注册页面，重定向到项目工作台
  if (to.meta?.hideForAuth && userStore.isLoggedIn) {
    console.log('已登录用户访问登录页，重定向到项目工作台')
    next('/projects')
    return
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    // 检查是否已登录
    if (!userStore.isLoggedIn) {
      console.log('用户未登录，尝试初始化认证状态')
      // 尝试从本地存储恢复认证状态
      await userStore.initAuth()

      console.log('初始化后的登录状态:', userStore.isLoggedIn)

      // 如果初始化后仍未登录，重定向到登录页
      if (!userStore.isLoggedIn) {
        console.log('认证失败，重定向到登录页')
        next('/auth/login')
        return
      }
    }

    // 检查权限要求
    if (
      to.meta?.requiresPermission &&
      typeof to.meta.requiresPermission === 'object'
    ) {
      const permission = to.meta.requiresPermission as {
        resource: string
        action: string
        scope?: string
      }
      const hasPermission = userStore.hasPermission(
        permission.resource,
        permission.action,
        permission.scope || 'global',
      )

      console.log('权限检查:', {
        需要权限: permission,
        拥有权限: hasPermission,
        用户角色: userStore.user,
      })

      if (!hasPermission) {
        console.log('权限不足，重定向到项目工作台')
        handleRoutePermissionError(to.name as string)
        next('/projects')
        return
      }
    }
  }

  console.log('路由守卫通过，允许访问')
  next()
})

export default router
