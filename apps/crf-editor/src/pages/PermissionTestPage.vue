<template>
  <div class="permission-test-page">
    <n-space vertical size="large">
      <n-card title="权限错误组件测试">
        <n-space vertical>
          <p>测试不同类型的权限错误提示样式：</p>

          <n-button type="error" @click="showStaticError">
            显示静态权限错误（页面内）
          </n-button>

          <n-button type="warning" @click="showFloatingError">
            显示浮动权限错误（全局）
          </n-button>

          <n-button type="info" @click="clearErrors"> 清除所有错误 </n-button>
        </n-space>
      </n-card>

      <!-- 静态权限错误展示 -->
      <PermissionError
        v-if="showStatic"
        title="您没有访问此页面的权限"
        description="请联系管理员获取相应权限。"
        :show-close="true"
        @close="showStatic = false"
      />

      <!-- 其他权限错误样式测试 -->
      <PermissionError
        v-if="showStatic"
        title="权限不足"
        description="您的当前角色无法执行此操作。"
        :show-close="true"
        @close="showStatic = false"
      />

      <!-- 旧版本权限警告对比 -->
      <n-card title="旧版本对比" v-if="showComparison">
        <v-alert type="warning" variant="tonal">
          <v-alert-title>权限不足</v-alert-title>
          您没有管理系统的权限。请联系管理员获取相应权限。（旧版本样式）
        </v-alert>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NSpace, NCard, NButton } from 'naive-ui'
import PermissionError from '@/components/common/PermissionError.vue'
import {
  showNoPermissionError,
  clearPermissionError,
} from '@/utils/permission-error'

// 状态
const showStatic = ref(false)
const showComparison = ref(true)

// 方法
const showStaticError = () => {
  showStatic.value = true
}

const showFloatingError = () => {
  showNoPermissionError('这是一个全局浮动的权限错误提示，会自动消失。')
}

const clearErrors = () => {
  showStatic.value = false
  clearPermissionError()
}
</script>

<style lang="scss" scoped>
.permission-test-page {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}
</style>
