<template>
  <div class="templates-container">
    <!-- 页面头部 -->
    <div class="templates-header">
      <div class="header-content">
        <h1 class="page-title">表单管理</h1>
        <p class="page-subtitle">管理您的所有CRF表单模板</p>
      </div>
      <div class="header-actions">
        <n-button type="primary" @click="handleCreateTemplate">
          <n-icon><Plus /></n-icon>
          新建表单
        </n-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <div class="filter-left">
        <n-input
          v-model:value="searchQuery"
          placeholder="搜索表单名称或描述..."
          clearable
          @input="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <n-icon :component="Search" />
          </template>
        </n-input>
      </div>
      <div class="filter-right">
        <n-select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          clearable
          @update:value="handleFilter"
          class="status-filter"
        >
          <n-option label="草稿" value="draft" />
          <n-option label="已发布" value="published" />
          <n-option label="已归档" value="archived" />
        </n-select>
        <n-button @click="handleRefresh">
          <template #icon>
            <n-icon :component="Refresh" />
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 表单列表 -->
    <n-spin :show="loading">
      <div class="templates-grid">
        <div
          v-for="template in filteredTemplates"
          :key="template.id as string"
          class="template-card"
          @click="handleViewTemplate(template)"
        >
          <div class="card-header">
            <div class="template-info">
              <h3 class="template-title">
                {{ template.title || template.name }}
              </h3>
              <p class="template-description">
                {{ template.description || '暂无描述' }}
              </p>
            </div>
            <div class="template-status">
              <n-tag
                :type="getStatusType(template.status as string)"
                size="small"
              >
                {{ getStatusText(template.status as string) }}
              </n-tag>
            </div>
          </div>

          <div class="card-meta">
            <div class="meta-item">
              <n-icon :component="Calendar" />
              <span>{{ formatDate(template.updated_at as string) }}</span>
            </div>
            <div class="meta-item">
              <n-icon :component="User" />
              <span>{{ template.created_by_name || '未知用户' }}</span>
            </div>
            <div class="meta-item">
              <n-icon :component="Document" />
              <span>v{{ template.version || '1.0.0' }}</span>
            </div>
          </div>

          <div class="card-actions" @click.stop>
            <n-button text @click="handleEditTemplate(template)">
              <template #icon>
                <n-icon :component="Edit" />
              </template>
              编辑
            </n-button>
            <n-button text @click="handleViewTemplate(template)">
              <template #icon>
                <n-icon :component="View" />
              </template>
              预览
            </n-button>
            <n-dropdown
              @select="(key: string) => handleDropdownAction(key, template)"
              :options="getDropdownOptions(template)"
            >
              <n-button text>
                <template #icon>
                  <n-icon :component="MoreFilled" />
                </template>
              </n-button>
            </n-dropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-if="!loading && filteredTemplates.length === 0"
          class="empty-state"
        >
          <n-icon :component="Document" />
          <h3>
            {{ searchQuery || statusFilter ? '暂无匹配的表单' : '暂无表单' }}
          </h3>
          <p>
            {{
              searchQuery || statusFilter
                ? '尝试调整搜索条件'
                : '创建您的第一个表单模板'
            }}
          </p>
          <n-button
            type="primary"
            @click="handleCreateTemplate"
            v-if="!searchQuery && !statusFilter"
          >
            新建表单
          </n-button>
        </div>
      </div>
    </n-spin>

    <!-- 分页 -->
    <div class="pagination-section" v-if="pagination.total > 0">
      <n-pagination
        v-model:page="pagination.current"
        v-model:page-size="pagination.size"
        :item-count="pagination.total"
        :page-sizes="[12, 24, 48]"
        show-size-picker
        show-quick-jumper
        @update:page="handleCurrentChange"
        @update:page-size="handleSizeChange"
      />
    </div>

    <!-- 删除确认对话框 -->
    <n-modal
      v-model:show="deleteDialogVisible"
      title="确认删除"
      style="width: 400px"
      preset="dialog"
      :mask-closable="false"
    >
      <p>确定要删除表单 "{{ selectedTemplate?.title }}" 吗？</p>
      <p class="warning-text">此操作不可恢复，请谨慎操作。</p>
      <template #action>
        <n-button @click="deleteDialogVisible = false">取消</n-button>
        <n-button type="error" @click="confirmDelete" :loading="deleting">
          确认删除
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, useDialog, NDropdown } from 'naive-ui'
import type { DropdownOption } from 'naive-ui'
import {
  Add as Plus,
  Search,
  Refresh,
  Calendar,
  Person as User,
  Document,
  Create as Edit,
  Eye as View,
  EllipsisHorizontal as MoreFilled,
  Copy as CopyDocument,
  CloudUpload as Upload,
  FolderOpen as FolderRemove,
  Trash as Delete,
} from '@vicons/ionicons5'
import { templateAPI } from '@/api'
import { getErrorMessage } from '@/utils/error-messages'

defineOptions({
  name: 'TemplatesPage',
})

const router = useRouter()
const message = useMessage()
const dialog = useDialog()
const loading = ref(false)
const deleting = ref(false)
const templates = ref<Record<string, unknown>[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const deleteDialogVisible = ref(false)
const selectedTemplate = ref<Record<string, unknown> | null>(null)

// 分页配置
const pagination = ref({
  current: 1,
  size: 12,
  total: 0,
})

// 过滤后的模板列表
const filteredTemplates = computed(() => {
  let filtered = templates.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (template) =>
        ((template.title as string) || (template.name as string))
          .toLowerCase()
          .includes(query) ||
        ((template.description as string) || '').toLowerCase().includes(query),
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(
      (template) => template.status === statusFilter.value,
    )
  }

  return filtered
})

// 获取模板列表
const fetchTemplates = async () => {
  try {
    loading.value = true
    const response = await templateAPI.getTemplates({
      limit: pagination.value.size,
      offset: (pagination.value.current - 1) * pagination.value.size,
    })

    if (response.success) {
      templates.value = response.data?.templates || []
      pagination.value.total = response.data?.pagination?.total || 0
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '获取模板列表失败，请稍后重试',
    })
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  // 实时搜索，无需额外处理
}

// 处理筛选
const handleFilter = () => {
  // 筛选变化，重置到第一页
  pagination.value.current = 1
}

// 处理刷新
const handleRefresh = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  pagination.value.current = 1
  fetchTemplates()
}

// 处理新建模板
const handleCreateTemplate = () => {
  router.push('/templates/create')
}

// 处理编辑模板
const handleEditTemplate = (template: Record<string, unknown>) => {
  router.push(`/editor?templateId=${template.id}`)
}

// 处理查看模板
const handleViewTemplate = (template: Record<string, unknown>) => {
  router.push(`/templates/${template.id}`)
}

// 获取下拉菜单选项
const getDropdownOptions = (
  template: Record<string, unknown>,
): DropdownOption[] => {
  const options: DropdownOption[] = [
    {
      label: '复制',
      key: 'duplicate',
      icon: () => h('n-icon', null, { default: () => h(CopyDocument) }),
    },
  ]

  if (template.status === 'draft') {
    options.push({
      label: '发布',
      key: 'publish',
      icon: () => h('n-icon', null, { default: () => h(Upload) }),
    })
  }

  if (template.status !== 'archived') {
    options.push({
      label: '归档',
      key: 'archive',
      icon: () => h('n-icon', null, { default: () => h(FolderRemove) }),
    })
  }

  options.push({
    label: '删除',
    key: 'delete',
    icon: () => h('n-icon', null, { default: () => h(Delete) }),
  })

  return options
}

// 处理下拉菜单操作
const handleDropdownAction = async (
  command: string,
  template: Record<string, unknown>,
) => {
  selectedTemplate.value = template

  switch (command) {
    case 'duplicate':
      await handleDuplicateTemplate(template)
      break
    case 'publish':
      await handlePublishTemplate(template)
      break
    case 'archive':
      await handleArchiveTemplate(template)
      break
    case 'delete':
      deleteDialogVisible.value = true
      break
  }
}

// 复制模板
const handleDuplicateTemplate = async (_template: Record<string, unknown>) => {
  try {
    message.info('复制功能开发中...')
  } catch (error) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '复制模板失败，请稍后重试',
    })
    message.error(errorMessage)
  }
}

// 发布模板
const handlePublishTemplate = async (_template: Record<string, unknown>) => {
  try {
    message.info('发布功能开发中...')
  } catch (error) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '发布模板失败，请稍后重试',
    })
    message.error(errorMessage)
  }
}

// 归档模板
const handleArchiveTemplate = async (template: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认归档',
        content: `确定要归档表单 "${template.title as string}" 吗？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消')),
      })
    })

    message.info('归档功能开发中...')
  } catch (error) {
    // 用户取消操作
  }
}

// 确认删除
const confirmDelete = async () => {
  if (!selectedTemplate.value) return

  try {
    deleting.value = true
    await templateAPI.deleteTemplate(selectedTemplate.value.id as string)
    message.success('删除成功')
    deleteDialogVisible.value = false
    selectedTemplate.value = null
    await fetchTemplates()
  } catch (error) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '删除模板失败，请稍后重试',
    })
    message.error(errorMessage)
  } finally {
    deleting.value = false
  }
}

// 关闭删除对话框
const handleCloseDeleteDialog = () => {
  if (!deleting.value) {
    deleteDialogVisible.value = false
    selectedTemplate.value = null
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.current = 1
  fetchTemplates()
}

const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  fetchTemplates()
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

onMounted(() => {
  fetchTemplates()
})
</script>

<style lang="scss" scoped>
.templates-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.header-content {
  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a202c;
    margin: 0 0 4px 0;
  }

  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
  }
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.filter-left {
  flex: 1;
  margin-right: 20px;

  .search-input {
    max-width: 400px;
  }
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;

  .status-filter {
    width: 120px;
  }
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  min-height: 400px;
}

.template-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #3b82f6;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.template-info {
  flex: 1;
  margin-right: 12px;
}

.template-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.template-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-status {
  flex-shrink: 0;
}

.card-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #94a3b8;

  .n-icon {
    font-size: 12px;
  }
}

.card-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
  margin-top: 16px;
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #64748b;
  text-align: center;

  .n-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    font-size: 18px;
    margin: 0 0 8px 0;
    color: #374151;
  }

  p {
    font-size: 14px;
    margin: 0 0 24px 0;
    max-width: 300px;
  }
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.warning-text {
  color: #ef4444;
  font-size: 14px;
}

@media (max-width: 768px) {
  .templates-container {
    padding: 16px;
  }

  .templates-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-left {
    margin-right: 0;

    .search-input {
      max-width: none;
    }
  }

  .filter-right {
    justify-content: flex-end;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .card-meta {
    gap: 12px;
  }

  .card-actions {
    gap: 4px;
  }
}
</style>
