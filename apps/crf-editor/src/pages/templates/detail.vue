<template>
  <div class="template-detail-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <n-skeleton text :repeat="8" />
    </div>

    <!-- 模板详情 -->
    <div v-else-if="template" class="template-detail">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <div class="template-info">
            <h1 class="template-title">
              {{ template.title || template.name }}
            </h1>
            <div class="template-meta">
              <n-tag
                :type="getStatusType(template.status as string)"
                size="small"
              >
                {{ getStatusText(template.status as string) }}
              </n-tag>
              <span class="version">v{{ template.version || '1.0.0' }}</span>
              <span class="update-time">{{
                formatDate(template.updated_at as string)
              }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <n-button @click="handleBack">返回</n-button>
          <n-button type="primary" @click="handleEdit"> 编辑模板 </n-button>
          <n-dropdown @select="handleAction" :options="dropdownOptions">
            <n-button type="primary">
              更多操作
              <template #icon>
                <CrfIcon icon="arrow-down" />
              </template>
            </n-button>
          </n-dropdown>
        </div>
      </div>

      <!-- 模板内容 -->
      <div class="template-content">
        <n-grid :cols="24" :x-gap="24">
          <!-- 左侧主要信息 -->
          <n-grid-item :span="16">
            <!-- 基本信息 -->
            <div class="info-section">
              <h3>基本信息</h3>
              <n-descriptions :column="2" bordered>
                <n-descriptions-item label="模板名称">
                  {{ template.name }}
                </n-descriptions-item>
                <n-descriptions-item label="模板标题">
                  {{ template.title }}
                </n-descriptions-item>
                <n-descriptions-item label="所属项目">
                  {{ template.project_name || '未指定' }}
                </n-descriptions-item>
                <n-descriptions-item label="关键字">
                  {{ template.keyword || '无' }}
                </n-descriptions-item>
                <n-descriptions-item label="创建时间">
                  {{ formatDateTime(template.created_at as string) }}
                </n-descriptions-item>
                <n-descriptions-item label="更新时间">
                  {{ formatDateTime(template.updated_at as string) }}
                </n-descriptions-item>
                <n-descriptions-item label="创建人">
                  {{ template.created_by || '未知' }}
                </n-descriptions-item>
                <n-descriptions-item label="当前版本">
                  v{{ template.version || '1.0.0' }}
                </n-descriptions-item>
              </n-descriptions>
            </div>

            <!-- 描述信息 -->
            <div class="info-section">
              <h3>模板描述</h3>
              <div class="description-content">
                {{ template.description || '暂无描述' }}
              </div>
            </div>

            <!-- 表单结构 -->
            <div class="info-section">
              <h3>表单结构</h3>
              <div class="form-structure">
                <div
                  v-if="(template.form_structure as any)?.sections"
                  class="sections-list"
                >
                  <div
                    v-for="section in (template.form_structure as any).sections"
                    :key="section.id"
                    class="section-item"
                  >
                    <div class="section-header">
                      <h4>{{ section.name }}</h4>
                      <n-tag size="small" type="info">
                        {{ section.blocks?.length || 0 }} 个字段
                      </n-tag>
                    </div>
                    <div
                      v-if="section.blocks && section.blocks.length > 0"
                      class="fields-list"
                    >
                      <div
                        v-for="block in section.blocks"
                        :key="block.id"
                        class="field-item"
                      >
                        <span class="field-name">{{
                          block.title || block.name
                        }}</span>
                        <n-tag size="small">{{ block.type }}</n-tag>
                      </div>
                    </div>
                  </div>
                </div>
                <n-empty v-else description="暂无表单结构" />
              </div>
            </div>
          </n-grid-item>

          <!-- 右侧统计信息 -->
          <n-grid-item :span="8">
            <!-- 使用统计 -->
            <div class="stats-section">
              <h3>使用统计</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">
                    {{ template.instance_count || 0 }}
                  </div>
                  <div class="stat-label">实例数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    {{ template.submission_count || 0 }}
                  </div>
                  <div class="stat-label">提交次数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    {{ template.download_count || 0 }}
                  </div>
                  <div class="stat-label">下载次数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ template.view_count || 0 }}</div>
                  <div class="stat-label">浏览次数</div>
                </div>
              </div>
            </div>

            <!-- 版本历史 -->
            <div class="versions-section">
              <h3>版本历史</h3>
              <div v-loading="versionsLoading" class="versions-list">
                <div
                  v-for="version in versions"
                  :key="version.id as string"
                  class="version-item"
                  :class="{ active: version.version === template.version }"
                >
                  <div class="version-info">
                    <div class="version-number">v{{ version.version }}</div>
                    <div class="version-time">
                      {{ formatDate(version.created_at as string) }}
                    </div>
                  </div>
                  <div class="version-description">
                    {{ version.description || '无描述' }}
                  </div>
                </div>
                <n-empty
                  v-if="!versionsLoading && versions.length === 0"
                  description="暂无版本历史"
                />
              </div>
            </div>

            <!-- 相关操作 -->
            <div class="actions-section">
              <h3>快速操作</h3>
              <div class="action-buttons">
                <n-button type="primary" block @click="handleCreateInstance">
                  创建实例
                </n-button>
                <n-button block @click="handleViewInstances">
                  查看实例
                </n-button>
                <n-button block @click="handlePreview"> 预览模板 </n-button>
              </div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <n-result
        status="error"
        title="模板不存在"
        description="请检查模板ID是否正确"
      >
        <template #footer>
          <n-button type="primary" @click="handleBack">返回</n-button>
        </template>
      </n-result>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage, useDialog, NDropdown } from 'naive-ui'
import type { DropdownOption } from 'naive-ui'
import { CrfIcon } from '@crf/ui-components'
import { templateAPI } from '@/api'

defineOptions({
  name: 'TemplateDetailPage',
})

const route = useRoute()
const router = useRouter()
const message = useMessage()
const dialog = useDialog()

// 状态
const loading = ref(true)
const versionsLoading = ref(false)
const template = ref<Record<string, unknown> | null>(null)
const versions = ref<Record<string, unknown>[]>([])

// 下拉菜单选项
const dropdownOptions: DropdownOption[] = [
  { key: 'duplicate', label: '复制模板' },
  { key: 'export', label: '导出模板' },
  { key: 'publish', label: '发布模板' },
  { key: 'delete', label: '删除模板' },
]

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取模板详情
const fetchTemplateDetail = async () => {
  try {
    loading.value = true
    const templateId = route.params.id as string

    const response = await templateAPI.getTemplate(templateId)
    if (response.success && response.data) {
      template.value = response.data.template
      fetchVersions()
    } else {
      template.value = null
    }
  } catch (error) {
    console.error('获取模板详情失败:', error)
    template.value = null
  } finally {
    loading.value = false
  }
}

// 获取版本历史
const fetchVersions = async () => {
  try {
    versionsLoading.value = true
    const templateId = route.params.id as string

    const response = await templateAPI.getTemplateVersions(templateId)
    if (response.success && response.data) {
      versions.value = response.data.versions || []
    }
  } catch (error) {
    console.error('获取版本历史失败:', error)
  } finally {
    versionsLoading.value = false
  }
}

// 处理操作
const handleAction = async (command: string) => {
  switch (command) {
    case 'duplicate':
      handleDuplicate()
      break
    case 'export':
      handleExport()
      break
    case 'publish':
      handlePublish()
      break
    case 'delete':
      handleDelete()
      break
  }
}

// 复制模板
const handleDuplicate = async () => {
  try {
    await new Promise((resolve, reject) => {
      dialog.create({
        title: '复制模板',
        content: '请输入新模板的名称',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消')),
      })
    })

    // TODO: 调用复制API
    message.success('模板复制成功')
  } catch (error) {
    if (error !== 'cancel') {
      message.error('复制模板失败')
    }
  }
}

// 导出模板
const handleExport = () => {
  message.info('导出功能开发中...')
}

// 发布模板
const handlePublish = async () => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认发布',
        content: `确定要发布模板 "${template.value?.name}" 吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消')),
      })
    })

    const response = await templateAPI.publishTemplate(
      template.value?.id as string,
    )
    if (response.success) {
      message.success('模板发布成功')
      fetchTemplateDetail() // 刷新数据
    }
  } catch (error) {
    if (error !== 'cancel') {
      message.error('发布模板失败')
    }
  }
}

// 删除模板
const handleDelete = async () => {
  try {
    await new Promise((resolve, reject) => {
      dialog.error({
        title: '确认删除',
        content: `确定要删除模板 "${template.value?.name}" 吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消')),
      })
    })

    const response = await templateAPI.deleteTemplate(
      template.value?.id as string,
    )
    if (response.success) {
      message.success('模板删除成功')
      router.push('/templates')
    }
  } catch (error) {
    if (error !== 'cancel') {
      message.error('删除模板失败')
    }
  }
}

// 其他操作
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  router.push(`/editor?templateId=${template.value?.id as string}`)
}

const handleCreateInstance = () => {
  router.push(`/instances/create?templateId=${template.value?.id as string}`)
}

const handleViewInstances = () => {
  router.push(`/instances?templateId=${template.value?.id as string}`)
}

const handlePreview = () => {
  router.push(`/editor?templateId=${template.value?.id as string}&mode=preview`)
}

// 生命周期
onMounted(() => {
  fetchTemplateDetail()
})
</script>

<style lang="scss" scoped>
.template-detail-page {
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;

    .header-left {
      .template-info {
        .template-title {
          font-size: 28px;
          font-weight: 600;
          color: var(--crf-text-color);
          margin: 0 0 12px 0;
        }

        .template-meta {
          display: flex;
          align-items: center;
          gap: 16px;
          font-size: 14px;
          color: var(--crf-text-color-secondary);

          .version {
            font-weight: 500;
            color: var(--primary-color);
          }
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .template-content {
    .info-section {
      background: var(--crf-card-bg);
      border: 1px solid var(--crf-border-color);
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 24px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 16px 0;
      }

      .description-content {
        font-size: 14px;
        color: var(--crf-text-color);
        line-height: 1.6;
        padding: 16px;
        background: var(--gray-100);
        border-radius: 8px;
      }

      .form-structure {
        .sections-list {
          .section-item {
            border: 1px solid var(--crf-border-color);
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;

            &:last-child {
              margin-bottom: 0;
            }

            .section-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 16px;
              background: var(--gray-100);
              border-bottom: 1px solid var(--crf-border-color);

              h4 {
                font-size: 16px;
                font-weight: 500;
                color: var(--crf-text-color);
                margin: 0;
              }
            }

            .fields-list {
              padding: 16px;

              .field-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid var(--gray-200);

                &:last-child {
                  border-bottom: none;
                }

                .field-name {
                  font-size: 14px;
                  color: var(--crf-text-color);
                }
              }
            }
          }
        }
      }
    }

    .stats-section,
    .versions-section,
    .actions-section {
      background: var(--crf-card-bg);
      border: 1px solid var(--crf-border-color);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 16px 0;
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .stat-item {
        text-align: center;
        padding: 16px;
        background: var(--gray-100);
        border-radius: 8px;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--primary-color);
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: var(--crf-text-color-secondary);
          margin-top: 4px;
        }
      }
    }

    .versions-list {
      max-height: 300px;
      overflow-y: auto;

      .version-item {
        padding: 12px;
        border: 1px solid var(--crf-border-color);
        border-radius: 6px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        &.active {
          border-color: var(--primary-color);
          background: rgba(37, 99, 235, 0.05);
        }

        .version-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .version-number {
            font-weight: 500;
            color: var(--primary-color);
          }

          .version-time {
            font-size: 12px;
            color: var(--crf-text-color-secondary);
          }
        }

        .version-description {
          font-size: 13px;
          color: var(--crf-text-color-secondary);
        }
      }
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .template-detail-page {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .template-content {
      .n-row {
        flex-direction: column;

        .n-col {
          width: 100% !important;
        }
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
