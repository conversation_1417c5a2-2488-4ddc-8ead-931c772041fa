<template>
  <div class="api-test-page">
    <n-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>API 连接测试</span>
          <n-button text @click="runAllTests">运行所有测试</n-button>
        </div>
      </template>

      <div class="test-section">
        <h3>后端连接测试</h3>
        <n-button @click="testBackendConnection" :loading="backendLoading"
          >测试后端连接</n-button
        >
        <div v-if="backendResult" class="test-result">
          <n-tag :type="(backendResult as any).success ? 'success' : 'error'">
            {{ (backendResult as any).success ? '成功' : '失败' }}
          </n-tag>
          <pre>{{ JSON.stringify(backendResult, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>系统健康检查</h3>
        <n-button @click="testHealthCheck" :loading="healthLoading"
          >测试健康检查</n-button
        >
        <div v-if="healthResult" class="test-result">
          <n-tag :type="(healthResult as any).success ? 'success' : 'error'">
            {{ (healthResult as any).success ? '成功' : '失败' }}
          </n-tag>
          <pre>{{ JSON.stringify(healthResult, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>用户认证测试</h3>
        <n-form :model="loginForm" inline>
          <n-form-item label="用户名">
            <n-input
              v-model:value="loginForm.username"
              placeholder="输入用户名"
            />
          </n-form-item>
          <n-form-item label="密码">
            <n-input
              v-model:value="loginForm.password"
              type="password"
              placeholder="输入密码"
            />
          </n-form-item>
          <n-form-item>
            <n-button @click="testLogin" :loading="loginLoading"
              >使用API测试登录</n-button
            >
          </n-form-item>
          <n-form-item>
            <n-button @click="testDirectLogin" :loading="loginLoading"
              >直接测试登录</n-button
            >
          </n-form-item>
        </n-form>
        <div v-if="loginResult" class="test-result">
          <n-tag :type="(loginResult as any).success ? 'success' : 'error'">
            {{ (loginResult as any).success ? '成功' : '失败' }}
          </n-tag>
          <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>用户API测试</h3>
        <n-button @click="testUserList" :loading="userLoading"
          >获取用户列表</n-button
        >
        <div v-if="userResult" class="test-result">
          <n-tag :type="(userResult as any).success ? 'success' : 'error'">
            {{ (userResult as any).success ? '成功' : '失败' }}
          </n-tag>
          <pre>{{ JSON.stringify(userResult, null, 2) }}</pre>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  systemAPI,
  authAPI,
  templateAPI,
  userAPI,
  API_CONFIG,
  getApiUrl,
} from '@/api'

// 响应式状态
const message = useMessage()
const backendLoading = ref(false)
const backendResult = ref<unknown>(null)

const healthLoading = ref(false)
const healthResult = ref<unknown>(null)

const loginLoading = ref(false)
const loginResult = ref<unknown>(null)
const loginForm = ref({
  username: 'test',
  password: 'test123',
})

const templateLoading = ref(false)
const templateResult = ref<unknown>(null)

const userLoading = ref(false)
const userResult = ref<unknown>(null)

// 在组件挂载时显示调试信息
onMounted(() => {
  console.log('=== API调试信息 ===')
  console.log('环境:', import.meta.env.DEV ? '开发环境' : '生产环境')
  console.log('API配置:', API_CONFIG)
  console.log('登录URL:', getApiUrl('/auth/login'))
  console.log(
    '健康检查URL:',
    import.meta.env.DEV ? '/health' : `${API_CONFIG.BASE_URL}/health`,
  )
})

// 测试后端连接
const testBackendConnection = async () => {
  try {
    backendLoading.value = true
    backendResult.value = null

    // 直接使用fetch测试后端连接，避免复杂的CORS处理
    const response = await fetch('http://localhost:3000/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.ok) {
      const data = await response.json()
      backendResult.value = {
        success: true,
        message: '后端连接成功',
        data: data,
      }
      message.success('后端连接成功')
    } else {
      backendResult.value = {
        success: false,
        message: `后端连接失败，状态码: ${response.status}`,
        status: response.status,
      }
      message.error(`后端连接失败，状态码: ${response.status}`)
    }
  } catch (error: unknown) {
    backendResult.value = {
      success: false,
      error: (error as Error).message || '网络错误',
    }
    message.error(`后端连接失败: ${(error as Error).message}`)
  } finally {
    backendLoading.value = false
  }
}

// 测试健康检查
const testHealthCheck = async () => {
  try {
    healthLoading.value = true
    healthResult.value = null

    const result = await systemAPI.healthCheck()
    healthResult.value = result

    if (result.success) {
      message.success('健康检查通过')
    } else {
      message.error('健康检查失败')
    }
  } catch (error: unknown) {
    healthResult.value = {
      success: false,
      error: (error as Error).message || '网络错误',
    }
    message.error(`健康检查失败: ${(error as Error).message}`)
  } finally {
    healthLoading.value = false
  }
}

// 测试用户登录
const testLogin = async () => {
  try {
    loginLoading.value = true
    loginResult.value = null

    const result = await authAPI.login(loginForm.value)
    loginResult.value = result

    if (result.success) {
      message.success('登录测试成功')
    } else {
      message.error('登录测试失败')
    }
  } catch (error: unknown) {
    loginResult.value = {
      success: false,
      error: (error as Error).message || '网络错误',
    }
    message.error(`登录测试失败: ${(error as Error).message}`)
  } finally {
    loginLoading.value = false
  }
}

// 直接测试登录（不使用API封装）
const testDirectLogin = async () => {
  try {
    loginLoading.value = true
    loginResult.value = null

    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginForm.value),
    })

    if (response.ok) {
      const data = await response.json()
      loginResult.value = {
        success: true,
        message: '直接登录成功',
        data: data,
      }
      message.success('直接登录成功')
    } else {
      const errorData = await response.json().catch(() => ({}))
      loginResult.value = {
        success: false,
        message: `直接登录失败，状态码: ${response.status}`,
        status: response.status,
        error: errorData,
      }
      message.error(`直接登录失败，状态码: ${response.status}`)
    }
  } catch (error: unknown) {
    loginResult.value = {
      success: false,
      error: (error as Error).message || '网络错误',
    }
    message.error(`直接登录失败: ${(error as Error).message}`)
  } finally {
    loginLoading.value = false
  }
}

// 测试模板列表
const testTemplateList = async () => {
  try {
    templateLoading.value = true
    templateResult.value = null

    const result = await templateAPI.getTemplates({ limit: 5 })
    templateResult.value = result

    if (result.success) {
      message.success('模板列表获取成功')
    } else {
      message.error('模板列表获取失败')
    }
  } catch (error: unknown) {
    templateResult.value = {
      success: false,
      error: (error as Error).message || '网络错误',
    }
    message.error(`模板列表获取失败: ${(error as Error).message}`)
  } finally {
    templateLoading.value = false
  }
}

// 测试用户列表
const testUserList = async () => {
  try {
    userLoading.value = true
    userResult.value = null

    const result = await userAPI.getUsers({ limit: 5 })
    userResult.value = result

    if (result.success) {
      message.success('用户列表获取成功')
    } else {
      message.error('用户列表获取失败')
    }
  } catch (error: unknown) {
    userResult.value = {
      success: false,
      error: (error as Error).message || '网络错误',
    }
    message.error(`用户列表获取失败: ${(error as Error).message}`)
  } finally {
    userLoading.value = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  message.info('开始运行所有API测试...')

  await testHealthCheck()
  await new Promise((resolve) => setTimeout(resolve, 1000))

  await testLogin()
  await new Promise((resolve) => setTimeout(resolve, 1000))

  await testTemplateList()
  await new Promise((resolve) => setTimeout(resolve, 1000))

  await testUserList()

  message.success('所有API测试完成')
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.test-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.test-result {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.test-result pre {
  margin: 10px 0 0 0;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
