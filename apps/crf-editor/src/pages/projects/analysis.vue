<template>
  <div class="project-analysis-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <n-button text @click="goBack" class="back-btn">
            <template #icon>
              <n-icon>
                <ArrowBackOutline />
              </n-icon>
            </template>
          </n-button>
          <div class="title-content">
            <h1>{{ projectName }} - 数据分析</h1>
            <n-text depth="3">分析项目数据，生成统计报告和可视化图表</n-text>
          </div>
        </div>
        <div class="header-actions">
          <n-button-group>
            <n-button @click="exportReport">
              <template #icon>
                <n-icon>
                  <DocumentOutline />
                </n-icon>
              </template>
              导出报告
            </n-button>
            <n-button type="primary" @click="generateReport">
              <template #icon>
                <n-icon>
                  <BarChartOutline />
                </n-icon>
              </template>
              生成报告
            </n-button>
          </n-button-group>
        </div>
      </div>
    </div>

    <!-- 分析概览 -->
    <div class="analysis-overview">
      <n-grid cols="1 s:2 m:4" responsive="screen" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic
              label="数据完整性"
              :value="analysisStats.completeness"
              suffix="%"
            >
              <template #prefix>
                <n-icon size="18" color="#10b981">
                  <CheckmarkCircleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic
              label="数据质量评分"
              :value="analysisStats.qualityScore"
            >
              <template #prefix>
                <n-icon size="18" color="#3b82f6">
                  <StarOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="异常数据" :value="analysisStats.anomalies">
              <template #prefix>
                <n-icon size="18" color="#ef4444">
                  <WarningOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="分析模型" :value="analysisStats.models">
              <template #prefix>
                <n-icon size="18" color="#8b5cf6">
                  <AnalyticsOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 分析内容 -->
    <div class="analysis-content">
      <n-tabs type="card" animated>
        <!-- 数据概览 -->
        <n-tab-pane name="overview" tab="数据概览">
          <n-grid cols="1 m:2" responsive="screen" :x-gap="24" :y-gap="24">
            <n-grid-item>
              <n-card title="数据分布统计">
                <div class="chart-container">
                  <n-empty description="图表功能开发中">
                    <template #icon>
                      <n-icon size="48" color="#d1d5db">
                        <BarChartOutline />
                      </n-icon>
                    </template>
                    <template #extra>
                      <n-text depth="3">将显示数据分布的柱状图和饼图</n-text>
                    </template>
                  </n-empty>
                </div>
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="完成率趋势">
                <div class="chart-container">
                  <n-empty description="图表功能开发中">
                    <template #icon>
                      <n-icon size="48" color="#d1d5db">
                        <TrendingUpOutline />
                      </n-icon>
                    </template>
                    <template #extra>
                      <n-text depth="3">将显示数据完成率的时间趋势</n-text>
                    </template>
                  </n-empty>
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <!-- 质量分析 -->
        <n-tab-pane name="quality" tab="质量分析">
          <n-space vertical :size="24">
            <n-card title="数据质量检查">
              <n-table>
                <thead>
                  <tr>
                    <th>检查项目</th>
                    <th>检查结果</th>
                    <th>影响程度</th>
                    <th>建议处理</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="check in qualityChecks" :key="check.id">
                    <td>{{ check.name }}</td>
                    <td>
                      <n-tag
                        :type="check.status === 'pass' ? 'success' : 'error'"
                      >
                        {{ check.status === 'pass' ? '通过' : '不通过' }}
                      </n-tag>
                    </td>
                    <td>
                      <n-tag :type="getSeverityType(check.severity)">
                        {{ getSeverityText(check.severity) }}
                      </n-tag>
                    </td>
                    <td>{{ check.suggestion }}</td>
                  </tr>
                </tbody>
              </n-table>
            </n-card>

            <n-card title="缺失值分析">
              <div class="chart-container">
                <n-empty description="缺失值分析图表开发中">
                  <template #icon>
                    <n-icon size="48" color="#d1d5db">
                      <SearchOutline />
                    </n-icon>
                  </template>
                  <template #extra>
                    <n-text depth="3">将显示各字段的缺失值统计</n-text>
                  </template>
                </n-empty>
              </div>
            </n-card>
          </n-space>
        </n-tab-pane>

        <!-- 统计分析 -->
        <n-tab-pane name="statistics" tab="统计分析">
          <n-grid cols="1 m:2" responsive="screen" :x-gap="24" :y-gap="24">
            <n-grid-item>
              <n-card title="描述性统计">
                <n-table>
                  <thead>
                    <tr>
                      <th>变量</th>
                      <th>均值</th>
                      <th>标准差</th>
                      <th>最小值</th>
                      <th>最大值</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="stat in descriptiveStats" :key="stat.variable">
                      <td>{{ stat.variable }}</td>
                      <td>{{ stat.mean }}</td>
                      <td>{{ stat.std }}</td>
                      <td>{{ stat.min }}</td>
                      <td>{{ stat.max }}</td>
                    </tr>
                  </tbody>
                </n-table>
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="相关性分析">
                <div class="chart-container">
                  <n-empty description="相关性矩阵图开发中">
                    <template #icon>
                      <n-icon size="48" color="#d1d5db">
                        <GridOutline />
                      </n-icon>
                    </template>
                    <template #extra>
                      <n-text depth="3">将显示变量间的相关性热图</n-text>
                    </template>
                  </n-empty>
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <!-- 报告生成 -->
        <n-tab-pane name="report" tab="报告生成">
          <n-space vertical :size="24">
            <n-card title="报告配置">
              <n-form>
                <n-grid cols="1 m:2" responsive="screen" :x-gap="24">
                  <n-grid-item>
                    <n-form-item label="报告标题">
                      <n-input
                        v-model:value="reportConfig.title"
                        placeholder="输入报告标题"
                      />
                    </n-form-item>
                    <n-form-item label="包含章节">
                      <n-checkbox-group v-model:value="reportConfig.sections">
                        <n-space vertical>
                          <n-checkbox value="overview" label="数据概览" />
                          <n-checkbox value="quality" label="质量分析" />
                          <n-checkbox value="statistics" label="统计分析" />
                          <n-checkbox value="charts" label="图表展示" />
                          <n-checkbox value="conclusions" label="结论建议" />
                        </n-space>
                      </n-checkbox-group>
                    </n-form-item>
                  </n-grid-item>
                  <n-grid-item>
                    <n-form-item label="报告格式">
                      <n-select
                        v-model:value="reportConfig.format"
                        :options="formatOptions"
                        placeholder="选择输出格式"
                      />
                    </n-form-item>
                    <n-form-item label="详细程度">
                      <n-radio-group v-model:value="reportConfig.detail">
                        <n-space>
                          <n-radio value="summary">摘要</n-radio>
                          <n-radio value="detailed">详细</n-radio>
                          <n-radio value="comprehensive">全面</n-radio>
                        </n-space>
                      </n-radio-group>
                    </n-form-item>
                  </n-grid-item>
                </n-grid>
              </n-form>
              <template #action>
                <n-space>
                  <n-button @click="previewReport">预览报告</n-button>
                  <n-button
                    type="primary"
                    @click="generateReport"
                    :loading="generating"
                  >
                    生成报告
                  </n-button>
                </n-space>
              </template>
            </n-card>

            <n-card title="历史报告">
              <n-list>
                <n-list-item v-for="report in historyReports" :key="report.id">
                  <n-list-item-meta>
                    <template #title>{{ report.title }}</template>
                    <template #description>
                      {{ report.format.toUpperCase() }} |
                      {{ formatDate(report.createdAt) }}
                    </template>
                  </n-list-item-meta>
                  <template #suffix>
                    <n-space>
                      <n-button size="small" @click="downloadReport(report)"
                        >下载</n-button
                      >
                      <n-button
                        size="small"
                        type="primary"
                        @click="viewReport(report)"
                        >查看</n-button
                      >
                    </n-space>
                  </template>
                </n-list-item>
              </n-list>
            </n-card>
          </n-space>
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline,
  DocumentOutline,
  BarChartOutline,
  CheckmarkCircleOutline,
  StarOutline,
  WarningOutline,
  AnalyticsOutline,
  TrendingUpOutline,
  SearchOutline,
  GridOutline,
} from '@vicons/ionicons5'

defineOptions({
  name: 'ProjectAnalysisPage',
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 项目ID和名称
const projectId = computed(() => route.params.id as string)
const projectName = ref('示例CRF项目')

// 分析统计
const analysisStats = ref({
  completeness: 87.5,
  qualityScore: 4.2,
  anomalies: 12,
  models: 3,
})

// 质量检查结果
const qualityChecks = ref([
  {
    id: 1,
    name: '数据完整性检查',
    status: 'pass',
    severity: 'low',
    suggestion: '数据完整性良好，无需处理',
  },
  {
    id: 2,
    name: '数据一致性检查',
    status: 'fail',
    severity: 'medium',
    suggestion: '发现部分数据不一致，建议人工核查',
  },
  {
    id: 3,
    name: '异常值检测',
    status: 'fail',
    severity: 'high',
    suggestion: '发现异常值，需要进一步验证',
  },
])

// 描述性统计
const descriptiveStats = ref([
  { variable: '年龄', mean: '45.2', std: '12.8', min: '18', max: '75' },
  { variable: '身高', mean: '165.4', std: '8.9', min: '150', max: '185' },
  { variable: '体重', mean: '67.3', std: '11.2', min: '45', max: '95' },
])

// 报告配置
const reportConfig = ref({
  title: '项目数据分析报告',
  sections: ['overview', 'quality', 'statistics'],
  format: 'pdf',
  detail: 'detailed',
})

const generating = ref(false)

// 格式选项
const formatOptions = ref([
  { label: 'PDF文档', value: 'pdf' },
  { label: 'Excel表格', value: 'excel' },
  { label: 'Word文档', value: 'word' },
  { label: 'HTML网页', value: 'html' },
])

// 历史报告
const historyReports = ref([
  {
    id: 1,
    title: '2024年1月数据分析报告',
    format: 'pdf',
    createdAt: '2024-01-25T10:30:00.000Z',
  },
  {
    id: 2,
    title: '数据质量评估报告',
    format: 'excel',
    createdAt: '2024-01-20T14:15:00.000Z',
  },
])

// 获取严重程度类型
const getSeverityType = (severity: string) => {
  const typeMap: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'error',
  }
  return typeMap[severity] || 'default'
}

// 获取严重程度文本
const getSeverityText = (severity: string) => {
  const textMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
  }
  return textMap[severity] || '未知'
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 返回项目详情
const goBack = () => {
  router.push(`/projects/${projectId.value}`)
}

// 导出报告
const exportReport = () => {
  message.info('报告导出功能开发中')
}

// 生成报告
const generateReport = async () => {
  try {
    generating.value = true

    // 模拟报告生成过程
    await new Promise((resolve) => setTimeout(resolve, 3000))

    message.success('报告生成成功')
  } catch (error) {
    console.error('生成报告失败:', error)
    message.error('生成报告失败')
  } finally {
    generating.value = false
  }
}

// 预览报告
const previewReport = () => {
  message.info('报告预览功能开发中')
}

// 下载报告
const downloadReport = (report: any) => {
  message.success(`正在下载报告: ${report.title}`)
}

// 查看报告
const viewReport = (report: any) => {
  message.info(`查看报告: ${report.title}`)
}

// 组件挂载
onMounted(() => {
  console.log('项目分析页面加载:', projectId.value)
})
</script>

<style lang="scss" scoped>
.project-analysis-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .page-title {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .back-btn {
        padding: 8px;
        border-radius: 6px;
        margin-top: 4px;

        &:hover {
          background-color: #f3f4f6;
        }
      }

      .title-content {
        h1 {
          margin: 0 0 4px 0;
          font-size: 28px;
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }
}

.analysis-overview {
  margin-bottom: 32px;

  .stat-card {
    :deep(.n-card-body) {
      padding: 20px;
    }

    :deep(.n-statistic) {
      .n-statistic-label {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 8px;
      }

      .n-statistic-value {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

.analysis-content {
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.n-table) {
    .n-table-thead th {
      background: #f9fafb;
      font-weight: 600;
    }
  }
}

:deep(.n-card) {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .n-card-header {
    font-weight: 600;
    color: #1f2937;
  }
}

:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.n-input),
:deep(.n-select) {
  border-radius: 8px;
}

:deep(.n-tabs) {
  .n-tabs-nav {
    margin-bottom: 24px;
  }

  .n-tab-pane {
    padding: 0;
  }
}

@media (max-width: 768px) {
  .page-header {
    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
  }
}
</style>
