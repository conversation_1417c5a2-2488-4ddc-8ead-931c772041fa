<template>
  <div class="project-templates-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <n-button text @click="goBack" class="back-btn">
            <template #icon>
              <n-icon>
                <ArrowBackOutline />
              </n-icon>
            </template>
          </n-button>
          <div class="title-content">
            <h1>{{ projectName }} - 表单模板</h1>
            <n-text depth="3">管理项目中的所有表单模板</n-text>
          </div>
        </div>
        <div class="header-actions">
          <n-button-group>
            <n-button @click="importTemplate">
              <template #icon>
                <n-icon>
                  <CloudUploadOutline />
                </n-icon>
              </template>
              导入模板
            </n-button>
            <n-button type="primary" @click="createTemplate">
              <template #icon>
                <n-icon>
                  <AddOutline />
                </n-icon>
              </template>
              创建模板
            </n-button>
          </n-button-group>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <n-card>
        <n-space align="center" justify="space-between">
          <n-space align="center">
            <n-input
              v-model:value="searchKeyword"
              placeholder="搜索模板名称或描述"
              clearable
              style="width: 300px"
            >
              <template #prefix>
                <n-icon>
                  <SearchOutline />
                </n-icon>
              </template>
            </n-input>
            <n-select
              v-model:value="statusFilter"
              placeholder="筛选状态"
              :options="statusOptions"
              clearable
              style="width: 150px"
            />
            <n-select
              v-model:value="typeFilter"
              placeholder="模板类型"
              :options="typeOptions"
              clearable
              style="width: 150px"
            />
          </n-space>
          <n-space align="center">
            <n-text depth="3">共 {{ filteredTemplates.length }} 个模板</n-text>
            <n-divider vertical />
            <n-radio-group v-model:value="viewMode" size="small">
              <n-radio-button value="grid">
                <n-icon>
                  <GridOutline />
                </n-icon>
              </n-radio-button>
              <n-radio-button value="list">
                <n-icon>
                  <ListOutline />
                </n-icon>
              </n-radio-button>
            </n-radio-group>
          </n-space>
        </n-space>
      </n-card>
    </div>

    <!-- 模板列表 -->
    <div class="templates-content">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="grid-view">
        <n-grid
          cols="1 s:2 m:3 l:4 xl:5"
          responsive="screen"
          :x-gap="16"
          :y-gap="16"
        >
          <n-grid-item v-for="template in filteredTemplates" :key="template.id">
            <n-card class="template-card" hoverable>
              <div class="template-content">
                <div class="template-header">
                  <div class="template-icon">
                    <n-icon size="24" :color="template.icon_color || '#3b82f6'">
                      <DocumentTextOutline />
                    </n-icon>
                  </div>
                  <n-dropdown
                    :options="getTemplateActions(template)"
                    @select="(key) => handleTemplateAction(key, template)"
                    trigger="click"
                    placement="bottom-end"
                  >
                    <n-button text class="action-btn">
                      <template #icon>
                        <n-icon>
                          <EllipsisVerticalOutline />
                        </n-icon>
                      </template>
                    </n-button>
                  </n-dropdown>
                </div>

                <div class="template-info">
                  <h3>{{ template.title }}</h3>
                  <p>{{ template.description || '暂无描述' }}</p>

                  <div class="template-meta">
                    <div class="meta-row">
                      <n-tag
                        size="small"
                        :type="getStatusType(template.status)"
                      >
                        {{ getStatusText(template.status) }}
                      </n-tag>
                      <n-tag size="small" type="info">
                        {{ getTypeText(template.template_type) }}
                      </n-tag>
                    </div>
                    <div class="meta-row">
                      <n-text depth="3">版本 {{ template.version }}</n-text>
                      <n-text depth="3"
                        >{{ template.usage_count || 0 }} 次使用</n-text
                      >
                    </div>
                  </div>
                </div>
              </div>

              <template #action>
                <n-space justify="space-between">
                  <n-text depth="3" style="font-size: 12px">
                    {{ formatDate(template.updated_at) }}
                  </n-text>
                  <n-space size="small">
                    <n-button size="small" @click="previewTemplate(template)"
                      >预览</n-button
                    >
                    <n-button
                      size="small"
                      type="primary"
                      @click="useTemplate(template)"
                      >使用</n-button
                    >
                  </n-space>
                </n-space>
              </template>
            </n-card>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <n-card>
          <n-data-table
            :columns="tableColumns"
            :data="filteredTemplates"
            :loading="loading"
            :pagination="pagination"
            striped
          />
        </n-card>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0" class="empty-state">
        <n-empty description="暂无符合条件的模板">
          <template #icon>
            <n-icon size="48" color="#d1d5db">
              <DocumentTextOutline />
            </n-icon>
          </template>
          <template #extra>
            <n-space>
              <n-button @click="clearFilters">清除筛选</n-button>
              <n-button type="primary" @click="createTemplate"
                >创建模板</n-button
              >
            </n-space>
          </template>
        </n-empty>
      </div>
    </div>

    <!-- 模板预览模态框 -->
    <n-modal
      v-model:show="showPreviewModal"
      preset="card"
      style="width: 90%; max-width: 1200px"
    >
      <template #header>
        <span>模板预览 - {{ previewTemplate?.title }}</span>
      </template>
      <div class="preview-content">
        <n-empty description="模板预览功能开发中">
          <template #icon>
            <n-icon size="48" color="#d1d5db">
              <EyeOutline />
            </n-icon>
          </template>
        </n-empty>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline,
  DocumentTextOutline,
  AddOutline,
  CloudUploadOutline,
  SearchOutline,
  GridOutline,
  ListOutline,
  EllipsisVerticalOutline,
  EyeOutline,
  CreateOutline,
  CopyOutline,
  ShareOutline,
  ArchiveOutline,
  TrashOutline,
} from '@vicons/ionicons5'
import type { DataTableColumns, DropdownOption } from 'naive-ui'

defineOptions({
  name: 'ProjectTemplatesPage',
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 项目ID
const projectId = computed(() => route.params.id as string)
const projectName = ref('示例CRF项目')

// 视图模式
const viewMode = ref<'grid' | 'list'>('grid')

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const typeFilter = ref<string | null>(null)

// 模板数据
const templates = ref([
  {
    id: '1',
    title: '患者基线信息采集模板',
    description: '用于收集患者基本信息、病史、体征等基线数据的标准化模板',
    status: 'published',
    template_type: 'template',
    version: '1.0.0',
    icon_color: '#3b82f6',
    usage_count: 45,
    created_at: '2024-01-15T08:00:00.000Z',
    updated_at: '2024-01-20T10:30:00.000Z',
  },
  {
    id: '2',
    title: '不良事件报告表',
    description:
      '专门用于记录临床试验中发生的不良事件，包含事件详情、严重程度评估等',
    status: 'draft',
    template_type: 'custom_form',
    version: '0.8.0',
    icon_color: '#ef4444',
    usage_count: 12,
    created_at: '2024-01-18T14:20:00.000Z',
    updated_at: '2024-01-22T16:45:00.000Z',
  },
  {
    id: '3',
    title: '实验室检查结果模板',
    description: '标准化的实验室检查结果记录表单，支持多种检验项目和参考值对比',
    status: 'published',
    template_type: 'template',
    version: '2.1.0',
    icon_color: '#10b981',
    usage_count: 67,
    created_at: '2024-01-10T09:15:00.000Z',
    updated_at: '2024-01-25T11:20:00.000Z',
  },
  {
    id: '4',
    title: '药物用药记录表',
    description: '记录患者用药情况，包含药物名称、剂量、频次、不良反应等信息',
    status: 'published',
    template_type: 'custom_form',
    version: '1.2.0',
    icon_color: '#8b5cf6',
    usage_count: 34,
    created_at: '2024-01-12T13:30:00.000Z',
    updated_at: '2024-01-24T15:10:00.000Z',
  },
  {
    id: '5',
    title: '随访评估表',
    description: '用于患者随访时的综合评估，包含症状变化、生活质量等多维度评估',
    status: 'archived',
    template_type: 'template',
    version: '1.0.0',
    icon_color: '#f59e0b',
    usage_count: 8,
    created_at: '2024-01-08T10:45:00.000Z',
    updated_at: '2024-01-15T12:30:00.000Z',
  },
])

const loading = ref(false)

// 预览模态框
const showPreviewModal = ref(false)
const previewTemplateData = ref<any>(null)

// 筛选选项
const statusOptions = ref([
  { label: '草稿', value: 'draft' },
  { label: '已发布', value: 'published' },
  { label: '已归档', value: 'archived' },
])

const typeOptions = ref([
  { label: '标准模板', value: 'template' },
  { label: '自定义表单', value: 'custom_form' },
])

// 过滤后的模板
const filteredTemplates = computed(() => {
  let result = [...templates.value]

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (template) =>
        template.title.toLowerCase().includes(keyword) ||
        (template.description &&
          template.description.toLowerCase().includes(keyword)),
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    result = result.filter((template) => template.status === statusFilter.value)
  }

  // 类型筛选
  if (typeFilter.value) {
    result = result.filter(
      (template) => template.template_type === typeFilter.value,
    )
  }

  return result
})

// 表格列配置
const tableColumns: DataTableColumns = [
  {
    title: '模板名称',
    key: 'title',
    render: (row: any) => {
      return h('div', { class: 'flex items-center gap-3' }, [
        h(
          'div',
          {
            class: 'w-8 h-8 rounded-lg flex items-center justify-center',
            style: { backgroundColor: '#f8fafc' },
          },
          [
            h(
              'n-icon',
              {
                size: 16,
                color: row.icon_color || '#3b82f6',
              },
              () => h(DocumentTextOutline),
            ),
          ],
        ),
        h('div', [
          h('div', { class: 'font-medium text-gray-900' }, row.title),
          h(
            'div',
            { class: 'text-sm text-gray-500' },
            row.description || '暂无描述',
          ),
        ]),
      ])
    },
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: any) => {
      return h(
        'n-tag',
        {
          type: getStatusType(row.status),
          size: 'small',
        },
        getStatusText(row.status),
      )
    },
  },
  {
    title: '类型',
    key: 'template_type',
    width: 120,
    render: (row: any) => {
      return h(
        'n-tag',
        {
          type: 'info',
          size: 'small',
        },
        getTypeText(row.template_type),
      )
    },
  },
  {
    title: '版本',
    key: 'version',
    width: 80,
  },
  {
    title: '使用次数',
    key: 'usage_count',
    width: 100,
    render: (row: any) => `${row.usage_count || 0} 次`,
  },
  {
    title: '更新时间',
    key: 'updated_at',
    width: 120,
    render: (row: any) => formatDate(row.updated_at),
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row: any) => {
      return h('n-space', { size: 'small' }, [
        h(
          'n-button',
          {
            size: 'small',
            onClick: () => previewTemplate(row),
          },
          '预览',
        ),
        h(
          'n-button',
          {
            size: 'small',
            type: 'primary',
            onClick: () => useTemplate(row),
          },
          '使用',
        ),
        h(
          'n-dropdown',
          {
            options: getTemplateActions(row),
            onSelect: (key: string) => handleTemplateAction(key, row),
            trigger: 'click',
          },
          () =>
            h(
              'n-button',
              {
                size: 'small',
                text: true,
              },
              () => h('n-icon', () => h(EllipsisVerticalOutline)),
            ),
        ),
      ])
    },
  },
]

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'warning',
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档',
  }
  return statusMap[status] || '未知'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    template: '标准模板',
    custom_form: '自定义表单',
  }
  return typeMap[type] || '未知'
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取模板操作菜单
const getTemplateActions = (template: any): DropdownOption[] => {
  return [
    {
      label: '编辑',
      key: 'edit',
      icon: () => h('n-icon', () => h(CreateOutline)),
    },
    {
      label: '复制',
      key: 'copy',
      icon: () => h('n-icon', () => h(CopyOutline)),
    },
    {
      label: '分享',
      key: 'share',
      icon: () => h('n-icon', () => h(ShareOutline)),
    },
    {
      type: 'divider',
      key: 'd1',
    },
    {
      label: template.status === 'archived' ? '恢复' : '归档',
      key: 'archive',
      icon: () => h('n-icon', () => h(ArchiveOutline)),
    },
    {
      label: '删除',
      key: 'delete',
      icon: () => h('n-icon', () => h(TrashOutline)),
    },
  ]
}

// 处理模板操作
const handleTemplateAction = (action: string, template: any) => {
  switch (action) {
    case 'edit':
      editTemplate(template)
      break
    case 'copy':
      copyTemplate(template)
      break
    case 'share':
      shareTemplate(template)
      break
    case 'archive':
      archiveTemplate(template)
      break
    case 'delete':
      deleteTemplate(template)
      break
  }
}

// 返回项目详情
const goBack = () => {
  router.push(`/projects/${projectId.value}`)
}

// 创建模板
const createTemplate = () => {
  router.push(`/templates/create?projectId=${projectId.value}`)
}

// 导入模板
const importTemplate = () => {
  message.info('模板导入功能开发中')
}

// 预览模板
const previewTemplate = (template: any) => {
  previewTemplateData.value = template
  showPreviewModal.value = true
}

// 使用模板
const useTemplate = (template: any) => {
  router.push(`/form-fill/edit/${template.id}`)
}

// 编辑模板
const editTemplate = (template: any) => {
  router.push(`/templates/${template.id}/edit`)
}

// 复制模板
const copyTemplate = (template: any) => {
  message.success(`模板"${template.title}"已复制`)
}

// 分享模板
const shareTemplate = (template: any) => {
  message.info('模板分享功能开发中')
}

// 归档/恢复模板
const archiveTemplate = (template: any) => {
  const isArchived = template.status === 'archived'
  template.status = isArchived ? 'published' : 'archived'
  message.success(`模板已${isArchived ? '恢复' : '归档'}`)
}

// 删除模板
const deleteTemplate = (template: any) => {
  message.warning('删除模板功能开发中')
}

// 清除筛选
const clearFilters = () => {
  searchKeyword.value = ''
  statusFilter.value = null
  typeFilter.value = null
}

// 加载项目信息
const loadProjectInfo = async () => {
  try {
    // 模拟API调用
    console.log('加载项目信息:', projectId.value)
    // 这里可以调用API获取项目信息
  } catch (error) {
    console.error('加载项目信息失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadProjectInfo()
})
</script>

<style lang="scss" scoped>
.project-templates-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .page-title {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .back-btn {
        padding: 8px;
        border-radius: 6px;
        margin-top: 4px;

        &:hover {
          background-color: #f3f4f6;
        }
      }

      .title-content {
        h1 {
          margin: 0 0 4px 0;
          font-size: 28px;
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }
}

.filter-section {
  margin-bottom: 24px;

  :deep(.n-card-body) {
    padding: 16px 20px;
  }
}

.templates-content {
  .grid-view {
    .template-card {
      height: 100%;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .template-content {
        .template-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .template-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .action-btn {
            padding: 4px;
          }
        }

        .template-info {
          h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.4;
          }

          p {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .template-meta {
            .meta-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  .list-view {
    :deep(.n-data-table) {
      .n-data-table-th {
        background: #f9fafb;
        font-weight: 600;
      }
    }
  }

  .empty-state {
    padding: 60px 0;
  }
}

.preview-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-card) {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .n-card-header {
    font-weight: 600;
    color: #1f2937;
  }
}

:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.n-input),
:deep(.n-select) {
  border-radius: 8px;
}

@media (max-width: 768px) {
  .page-header {
    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
  }

  .filter-section {
    :deep(.n-space) {
      flex-direction: column;
      align-items: stretch;

      .n-space {
        justify-content: center;
      }
    }
  }
}
</style>
