<template>
  <div class="projects-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <div class="header-title">
          <n-icon size="32" color="#3b82f6">
            <DocumentTextOutline />
          </n-icon>
          <div class="title-content">
            <h1 class="page-title">项目工作台</h1>
            <p class="page-description">管理研究项目，组织表单模板和数据收集</p>
          </div>
        </div>
      </div>
      <div class="header-right">
        <n-button-group>
          <n-button @click="handleImportProject">
            <template #icon>
              <n-icon><CloudUploadOutline /></n-icon>
            </template>
            导入项目
          </n-button>
          <n-button type="primary" @click="handleCreateProject">
            <template #icon>
              <n-icon><Plus /></n-icon>
            </template>
            创建项目
          </n-button>
        </n-button-group>
      </div>
    </div>

    <!-- 项目统计卡片 -->
    <div class="project-overview">
      <n-grid cols="2 s:3 m:4 l:6" responsive="screen" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="总项目数" :value="overviewStats.totalProjects">
              <template #prefix>
                <n-icon size="18" color="#3b82f6">
                  <FolderOpenOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="活跃项目" :value="overviewStats.activeProjects">
              <template #prefix>
                <n-icon size="18" color="#10b981">
                  <PlayCircleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="总模板数" :value="overviewStats.totalTemplates">
              <template #prefix>
                <n-icon size="18" color="#8b5cf6">
                  <DocumentTextOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="总数据量" :value="overviewStats.totalInstances">
              <template #prefix>
                <n-icon size="18" color="#f59e0b">
                  <BarChartOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="参与人员" :value="overviewStats.totalMembers">
              <template #prefix>
                <n-icon size="18" color="#ef4444">
                  <PeopleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="今日活跃" :value="overviewStats.todayActive">
              <template #prefix>
                <n-icon size="18" color="#06b6d4">
                  <FlashOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 筛选工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <n-input
          v-model:value="filters.search"
          placeholder="搜索项目名称"
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon><Search /></n-icon>
          </template>
        </n-input>
        <n-select
          v-model:value="filters.status"
          placeholder="项目状态"
          clearable
          style="width: 150px"
          @update:value="handleStatusChange"
        >
          <n-option label="进行中" value="active" />
          <n-option label="已完成" value="completed" />
          <n-option label="已暂停" value="paused" />
        </n-select>
      </div>
      <div class="toolbar-right">
        <n-button @click="handleRefresh">
          <template #icon>
            <n-icon><Refresh /></n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 项目卡片列表 -->
    <n-spin :show="loading">
      <div class="projects-grid">
        <div
          v-for="project in projects"
          :key="project.id"
          class="project-card"
          @click="handleViewProject(project)"
        >
          <div class="card-header">
            <div class="project-status">
              <n-tag :type="getStatusType(project.status)" size="small">
                {{ getStatusText(project.status) }}
              </n-tag>
            </div>
            <n-dropdown
              @select="(key: string) => handleCardAction(key, project)"
            >
              <n-button quaternary circle>
                <template #icon>
                  <n-icon><MoreFilled /></n-icon>
                </template>
              </n-button>
              <template #options>
                <n-dropdown-option key="view">
                  <template #icon>
                    <n-icon><EyeOutline /></n-icon>
                  </template>
                  查看详情
                </n-dropdown-option>
                <n-dropdown-option key="templates">
                  <template #icon>
                    <n-icon><Document /></n-icon>
                  </template>
                  表单模板
                </n-dropdown-option>
                <n-dropdown-option key="data">
                  <template #icon>
                    <n-icon><BarChartOutline /></n-icon>
                  </template>
                  数据收集
                </n-dropdown-option>
                <n-dropdown-option key="analysis">
                  <template #icon>
                    <n-icon><StatsChartOutline /></n-icon>
                  </template>
                  数据分析
                </n-dropdown-option>
                <n-dropdown-divider />
                <n-dropdown-option key="edit">
                  <template #icon>
                    <n-icon><Edit /></n-icon>
                  </template>
                  编辑项目
                </n-dropdown-option>
                <n-dropdown-option key="delete" style="color: #f56c6c">
                  <template #icon>
                    <n-icon><Delete /></n-icon>
                  </template>
                  删除项目
                </n-dropdown-option>
              </template>
            </n-dropdown>
          </div>

          <div class="card-content">
            <h3 class="project-title">{{ project.name }}</h3>
            <p class="project-description">
              {{ project.description || '暂无描述' }}
            </p>

            <div class="project-stats">
              <div class="stat-item">
                <span class="stat-value">{{
                  project.template_count || 0
                }}</span>
                <span class="stat-label">表单模板</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{
                  project.instance_count || 0
                }}</span>
                <span class="stat-label">数据条数</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ project.member_count || 0 }}</span>
                <span class="stat-label">团队成员</span>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="project-meta">
              <span class="meta-item">
                <n-icon><Calendar /></n-icon>
                {{ formatDate(project.created_at) }}
              </span>
              <span class="meta-item">
                <n-icon><User /></n-icon>
                {{ project.creator?.full_name || project.created_by || '未知' }}
              </span>
            </div>
            <div class="quick-actions">
              <n-button
                size="small"
                @click.stop="handleManageTemplates(project)"
              >
                表单设计
              </n-button>
              <n-button size="small" @click.stop="handleViewData(project)">
                数据收集
              </n-button>
              <n-button
                type="primary"
                size="small"
                @click.stop="handleViewProject(project)"
              >
                进入项目
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </n-spin>

    <!-- 空状态 -->
    <div v-if="!loading && projects.length === 0" class="empty-state">
      <n-empty description="暂无项目数据" :size="120">
        <template #icon>
          <n-icon size="64" color="#C0C4CC"><Document /></n-icon>
        </template>
        <template #extra>
          <n-button type="primary" @click="handleCreateProject">
            创建第一个项目
          </n-button>
        </template>
      </n-empty>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-wrapper">
      <n-pagination
        v-model:page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :item-count="pagination.total"
        :page-sizes="[12, 24, 48]"
        show-size-picker
        show-quick-jumper
        @update:page="handleCurrentChange"
        @update:page-size="handleSizeChange"
      >
        <template #prefix="{ itemCount }"> 共 {{ itemCount }} 条 </template>
      </n-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, useDialog, NDropdown } from 'naive-ui'
import {
  Add as Plus,
  Search,
  Refresh,
  EllipsisHorizontal as MoreFilled,
  Create as Edit,
  Document,
  Trash as Delete,
  EyeOutline,
  Calendar,
  Person as User,
  DocumentTextOutline,
  CloudUploadOutline,
  FolderOpenOutline,
  PlayCircleOutline,
  BarChartOutline,
  PeopleOutline,
  FlashOutline,
  StatsChartOutline,
} from '@vicons/ionicons5'
import { projectAPI } from '@/api'
import type { Project } from '@/types/api-types'
import { getStatusType, getStatusText, formatDate } from '@/utils/type-helpers'

defineOptions({
  name: 'ProjectsPage',
})

const router = useRouter()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const projects = ref<Project[]>([])

// 项目总览统计
const overviewStats = ref({
  totalProjects: 12,
  activeProjects: 8,
  totalTemplates: 45,
  totalInstances: 1256,
  totalMembers: 87,
  todayActive: 23,
})

// 筛选条件
const filters = reactive({
  search: '',
  status: '',
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0,
})

// 状态函数已从 type-helpers 导入

// formatDate函数已从 type-helpers 导入

// 获取项目列表
const fetchProjects = async (): Promise<void> => {
  try {
    loading.value = true

    // 模拟数据，实际项目中应该调用真实API
    const mockData = [
      {
        id: 1,
        name: '宫颈机能不全真实世界研究',
        description: '研究宫颈机能不全患者的临床特征和治疗效果',
        status: 'active',
        template_count: 8,
        instance_count: 156,
        member_count: 12,
        created_at: '2024-01-10',
        created_by: '张主任',
      },
      {
        id: 2,
        name: '妊娠期糖尿病队列研究',
        description: '多中心前瞻性队列研究，评估妊娠期糖尿病的发病率和危险因素',
        status: 'active',
        template_count: 6,
        instance_count: 89,
        member_count: 8,
        created_at: '2024-01-05',
        created_by: '李医生',
      },
    ]

    projects.value = mockData as any
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取项目列表失败:', error)
    message.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = (): void => {
  pagination.current = 1
  fetchProjects()
}

const handleStatusChange = (): void => {
  pagination.current = 1
  fetchProjects()
}

const handleRefresh = (): void => {
  fetchProjects()
}

const handleSizeChange = (size: number): void => {
  pagination.pageSize = size
  pagination.current = 1
  fetchProjects()
}

const handleCurrentChange = (page: number): void => {
  pagination.current = page
  fetchProjects()
}

const handleCreateProject = (): void => {
  router.push('/projects/create')
}

const handleViewProject = (project: Record<string, unknown>) => {
  router.push(`/projects/${project.id}`)
}

const handleManageTemplates = (project: Record<string, unknown>) => {
  router.push(`/templates?projectId=${project.id}`)
}

const handleCardAction = async (
  command: string,
  project: Record<string, unknown>,
) => {
  switch (command) {
    case 'view':
      router.push(`/projects/${project.id}`)
      break
    case 'edit':
      router.push(`/projects/${project.id}/edit`)
      break
    case 'templates':
      router.push(`/projects/${project.id}/templates`)
      break
    case 'data':
      router.push(`/projects/${project.id}/data`)
      break
    case 'analysis':
      router.push(`/projects/${project.id}/analysis`)
      break
    case 'delete':
      handleDeleteProject(project)
      break
  }
}

const handleImportProject = () => {
  message.info('项目导入功能开发中')
}

const handleViewData = (project: Record<string, unknown>) => {
  router.push(`/projects/${project.id}/data`)
}

const handleDeleteProject = async (project: Record<string, unknown>) => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '确认删除',
        content: `确定要删除项目 "${project.name}" 吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel'),
      })
    })

    // TODO: 调用删除API
    message.success('项目删除成功')
    fetchProjects()
  } catch (error) {
    if (error !== 'cancel') {
      message.error('删除项目失败')
    }
  }
}

// 生命周期
onMounted(() => {
  fetchProjects()
})
</script>

<style lang="scss" scoped>
.projects-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--crf-card-bg);
    border-radius: 8px;

    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 32px;

    .project-card {
      background: var(--crf-card-bg);
      border: 1px solid var(--crf-border-color);
      border-radius: 12px;
      padding: 24px;
      cursor: pointer;

      &:hover {
        box-shadow: none;
        border-color: var(--primary-color);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .project-status {
          flex: 1;
        }
      }

      .card-content {
        .project-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--crf-text-color);
          margin: 0 0 8px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .project-description {
          font-size: 14px;
          color: var(--crf-text-color-secondary);
          margin: 0 0 20px 0;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .project-stats {
          display: flex;
          justify-content: space-around;
          padding: 16px 0;
          border: 1px solid var(--crf-border-color);
          border-radius: 8px;
          background: var(--gray-100);

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .stat-value {
              font-size: 20px;
              font-weight: 600;
              color: var(--primary-color);
            }

            .stat-label {
              font-size: 12px;
              color: var(--crf-text-color-secondary);
              margin-top: 2px;
            }
          }
        }
      }

      .card-footer {
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid var(--crf-border-color);

        .project-meta {
          display: flex;
          gap: 16px;
          margin-bottom: 12px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--crf-text-color-secondary);

            .n-icon {
              font-size: 14px;
            }
          }
        }

        .actions {
          display: flex;
          justify-content: flex-end;
        }

        .quick-actions {
          display: flex;
          gap: 8px;
          justify-content: flex-end;
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 32px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .projects-page {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left {
        flex-direction: column;
        align-items: stretch;

        .n-input,
        .n-select {
          width: 100% !important;
        }
      }
    }

    .projects-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
