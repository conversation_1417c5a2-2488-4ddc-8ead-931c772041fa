<template>
  <div class="project-detail-page">
    <!-- 项目头部信息 -->
    <div class="project-header">
      <div class="header-content">
        <div class="project-info">
          <div class="project-title">
            <n-button text @click="goBack" class="back-btn">
              <template #icon>
                <n-icon>
                  <ArrowBackOutline />
                </n-icon>
              </template>
            </n-button>
            <h1>{{ projectData?.name || '项目详情' }}</h1>
            <n-tag
              v-if="projectData?.status"
              :type="getStatusType(projectData.status)"
            >
              {{ getStatusText(projectData.status) }}
            </n-tag>
          </div>
          <div class="project-description">
            <p>{{ projectData?.description || '暂无描述' }}</p>
          </div>
        </div>
        <div class="project-actions">
          <n-button-group>
            <n-button @click="editProject">
              <template #icon>
                <n-icon>
                  <CreateOutline />
                </n-icon>
              </template>
              编辑项目
            </n-button>
            <n-button type="primary" @click="createTemplate">
              <template #icon>
                <n-icon>
                  <AddOutline />
                </n-icon>
              </template>
              创建表单
            </n-button>
          </n-button-group>
        </div>
      </div>
    </div>

    <!-- 项目统计卡片 -->
    <div class="project-stats">
      <n-grid cols="2 s:3 m:4 l:6" responsive="screen" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="表单模板" :value="stats.templates">
              <template #prefix>
                <n-icon size="18" color="#3b82f6">
                  <DocumentTextOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="收集数据" :value="stats.instances">
              <template #prefix>
                <n-icon size="18" color="#10b981">
                  <BarChartOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="完成率" :value="stats.completion" suffix="%">
              <template #prefix>
                <n-icon size="18" color="#f59e0b">
                  <CheckmarkCircleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="参与人员" :value="stats.participants">
              <template #prefix>
                <n-icon size="18" color="#8b5cf6">
                  <PeopleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="今日新增" :value="stats.todayNew">
              <template #prefix>
                <n-icon size="18" color="#ef4444">
                  <TrendingUpOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="活跃度" :value="stats.activity" suffix="%">
              <template #prefix>
                <n-icon size="18" color="#06b6d4">
                  <FlashOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 项目功能区 -->
    <div class="project-content">
      <n-tabs type="card" animated v-model:value="activeTab">
        <!-- 表单模板管理 -->
        <n-tab-pane name="templates" tab="表单模板">
          <div class="templates-section">
            <div class="section-header">
              <div class="section-title">
                <h3>项目表单模板</h3>
                <n-text depth="3">管理项目中的所有表单模板</n-text>
              </div>
              <n-button type="primary" @click="createTemplate">
                <template #icon>
                  <n-icon>
                    <AddOutline />
                  </n-icon>
                </template>
                创建模板
              </n-button>
            </div>

            <div class="templates-list" v-if="templates.length > 0">
              <n-grid
                cols="1 s:2 m:3 l:4"
                responsive="screen"
                :x-gap="16"
                :y-gap="16"
              >
                <n-grid-item v-for="template in templates" :key="template.id">
                  <n-card
                    class="template-card"
                    hoverable
                    @click="goToTemplate(template.id)"
                  >
                    <div class="template-content">
                      <div class="template-icon">
                        <n-icon
                          size="24"
                          :color="template.icon_color || '#3b82f6'"
                        >
                          <DocumentTextOutline />
                        </n-icon>
                      </div>
                      <div class="template-info">
                        <h4>{{ template.title }}</h4>
                        <p>{{ template.description || '暂无描述' }}</p>
                        <div class="template-meta">
                          <n-tag
                            size="small"
                            :type="getTemplateStatusType(template.status)"
                          >
                            {{ getTemplateStatusText(template.status) }}
                          </n-tag>
                          <n-text depth="3"
                            >{{ template.usage_count || 0 }} 次使用</n-text
                          >
                        </div>
                      </div>
                    </div>
                    <template #action>
                      <n-space>
                        <n-button
                          size="small"
                          @click.stop="editTemplate(template.id)"
                          >编辑</n-button
                        >
                        <n-button
                          size="small"
                          type="primary"
                          @click.stop="useTemplate(template.id)"
                          >使用</n-button
                        >
                      </n-space>
                    </template>
                  </n-card>
                </n-grid-item>
              </n-grid>
            </div>

            <div v-else class="empty-state">
              <n-empty description="暂无表单模板">
                <template #icon>
                  <n-icon size="48" color="#d1d5db">
                    <DocumentTextOutline />
                  </n-icon>
                </template>
                <template #extra>
                  <n-button type="primary" @click="createTemplate"
                    >创建第一个模板</n-button
                  >
                </template>
              </n-empty>
            </div>
          </div>
        </n-tab-pane>

        <!-- 数据收集管理 -->
        <n-tab-pane name="data" tab="数据收集">
          <div class="data-section">
            <div class="section-header">
              <div class="section-title">
                <h3>数据收集管理</h3>
                <n-text depth="3">查看和管理项目中收集的所有数据</n-text>
              </div>
              <n-button-group>
                <n-button @click="exportProjectData">
                  <template #icon>
                    <n-icon>
                      <DownloadOutline />
                    </n-icon>
                  </template>
                  导出数据
                </n-button>
                <n-button type="primary" @click="goToDataCollection">
                  查看详情
                </n-button>
              </n-button-group>
            </div>

            <!-- 数据概览 -->
            <div class="data-overview">
              <n-data-table
                :columns="dataColumns"
                :data="instanceData"
                :loading="dataLoading"
                :pagination="dataPagination"
                striped
              />
            </div>
          </div>
        </n-tab-pane>

        <!-- 数据分析 -->
        <n-tab-pane name="analysis" tab="数据分析">
          <div class="analysis-section">
            <div class="section-header">
              <div class="section-title">
                <h3>数据分析</h3>
                <n-text depth="3">分析项目数据，生成统计报告</n-text>
              </div>
              <n-button type="primary" @click="goToAnalysis">
                查看完整分析
              </n-button>
            </div>

            <!-- 快速分析 -->
            <div class="quick-analysis">
              <n-grid cols="1 m:2" responsive="screen" :x-gap="24" :y-gap="24">
                <n-grid-item>
                  <n-card title="数据完成情况">
                    <div class="chart-placeholder">
                      <n-empty description="图表功能开发中">
                        <template #icon>
                          <n-icon size="48" color="#d1d5db">
                            <BarChartOutline />
                          </n-icon>
                        </template>
                      </n-empty>
                    </div>
                  </n-card>
                </n-grid-item>
                <n-grid-item>
                  <n-card title="数据质量分析">
                    <div class="chart-placeholder">
                      <n-empty description="图表功能开发中">
                        <template #icon>
                          <n-icon size="48" color="#d1d5db">
                            <StatsChartOutline />
                          </n-icon>
                        </template>
                      </n-empty>
                    </div>
                  </n-card>
                </n-grid-item>
              </n-grid>
            </div>
          </div>
        </n-tab-pane>

        <!-- 项目设置 -->
        <n-tab-pane name="settings" tab="项目设置">
          <div class="settings-section">
            <div class="section-header">
              <div class="section-title">
                <h3>项目设置</h3>
                <n-text depth="3">管理项目配置和权限</n-text>
              </div>
            </div>

            <n-grid cols="1 m:2" responsive="screen" :x-gap="24" :y-gap="24">
              <n-grid-item>
                <n-card title="基本信息">
                  <n-form>
                    <n-form-item label="项目名称">
                      <n-input
                        v-model:value="projectData.name"
                        placeholder="输入项目名称"
                      />
                    </n-form-item>
                    <n-form-item label="项目描述">
                      <n-input
                        v-model:value="projectData.description"
                        type="textarea"
                        placeholder="输入项目描述"
                        :rows="3"
                      />
                    </n-form-item>
                    <n-form-item label="项目状态">
                      <n-select
                        v-model:value="projectData.status"
                        :options="statusOptions"
                        placeholder="选择项目状态"
                      />
                    </n-form-item>
                  </n-form>
                  <template #action>
                    <n-button type="primary" @click="saveProjectSettings"
                      >保存设置</n-button
                    >
                  </template>
                </n-card>
              </n-grid-item>

              <n-grid-item>
                <n-card title="权限管理">
                  <n-empty description="权限管理功能开发中">
                    <template #icon>
                      <n-icon size="48" color="#d1d5db">
                        <LockClosedOutline />
                      </n-icon>
                    </template>
                    <template #extra>
                      <n-text depth="3">即将支持成员权限管理</n-text>
                    </template>
                  </n-empty>
                </n-card>
              </n-grid-item>
            </n-grid>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline,
  DocumentTextOutline,
  BarChartOutline,
  CheckmarkCircleOutline,
  PeopleOutline,
  TrendingUpOutline,
  FlashOutline,
  AddOutline,
  CreateOutline,
  DownloadOutline,
  StatsChartOutline,
  LockClosedOutline,
} from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

defineOptions({
  name: 'ProjectDetailPage',
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 当前激活的选项卡
const activeTab = ref('templates')

// 项目ID
const projectId = computed(() => route.params.id as string)

// 项目数据
const projectData = ref({
  id: '',
  name: '示例CRF项目',
  description: '这是一个示例的CRF项目，用于演示系统功能',
  status: 'active',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
})

// 项目统计
const stats = ref({
  templates: 5,
  instances: 128,
  completion: 78.5,
  participants: 15,
  todayNew: 8,
  activity: 92,
})

// 模板数据
const templates = ref([
  {
    id: '1',
    title: '患者基线信息采集模板',
    description: '用于收集患者基本信息、病史、体征等基线数据',
    status: 'published',
    icon_color: '#3b82f6',
    usage_count: 45,
  },
  {
    id: '2',
    title: '不良事件报告表',
    description: '专门用于记录临床试验中发生的不良事件',
    status: 'draft',
    icon_color: '#ef4444',
    usage_count: 12,
  },
  {
    id: '3',
    title: '实验室检查结果模板',
    description: '标准化的实验室检查结果记录表单',
    status: 'published',
    icon_color: '#10b981',
    usage_count: 67,
  },
])

// 实例数据
const instanceData = ref([])
const dataLoading = ref(false)

// 数据表格列配置
const dataColumns: DataTableColumns = [
  {
    title: '受试者ID',
    key: 'subject_id',
    width: 120,
  },
  {
    title: '表单模板',
    key: 'template_name',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'status',
    render: (row: any) => {
      const statusMap: Record<string, { text: string; type: string }> = {
        draft: { text: '草稿', type: 'default' },
        in_progress: { text: '进行中', type: 'warning' },
        completed: { text: '已完成', type: 'success' },
        locked: { text: '已锁定', type: 'error' },
      }
      const status = statusMap[row.status] || { text: '未知', type: 'default' }
      return h('n-tag', { type: status.type as any }, status.text)
    },
  },
  {
    title: '完成度',
    key: 'completion_percentage',
    render: (row: any) => `${row.completion_percentage}%`,
  },
  {
    title: '更新时间',
    key: 'updated_at',
    render: (row: any) => new Date(row.updated_at).toLocaleDateString(),
  },
]

// 分页配置
const dataPagination = ref({
  page: 1,
  pageSize: 5,
  showSizePicker: false,
  showQuickJumper: false,
})

// 状态选项
const statusOptions = ref([
  { label: '草稿', value: 'draft' },
  { label: '激活', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已归档', value: 'archived' },
])

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'default',
    active: 'success',
    completed: 'info',
    archived: 'warning',
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '激活',
    completed: '已完成',
    archived: '已归档',
  }
  return statusMap[status] || '未知'
}

// 获取模板状态类型
const getTemplateStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'warning',
  }
  return statusMap[status] || 'default'
}

// 获取模板状态文本
const getTemplateStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档',
  }
  return statusMap[status] || '未知'
}

// 返回上级页面
const goBack = () => {
  router.push('/projects')
}

// 编辑项目
const editProject = () => {
  message.info('编辑项目功能开发中')
}

// 创建模板
const createTemplate = () => {
  router.push('/templates/create')
}

// 跳转到模板详情
const goToTemplate = (templateId: string) => {
  router.push(`/templates/${templateId}/detail`)
}

// 编辑模板
const editTemplate = (templateId: string) => {
  router.push(`/templates/${templateId}/edit`)
}

// 使用模板
const useTemplate = (templateId: string) => {
  router.push(`/form-fill/edit/${templateId}`)
}

// 导出项目数据
const exportProjectData = () => {
  message.success('项目数据导出功能开发中')
}

// 跳转到数据收集页面
const goToDataCollection = () => {
  router.push(`/projects/${projectId.value}/data`)
}

// 跳转到数据分析页面
const goToAnalysis = () => {
  router.push(`/projects/${projectId.value}/analysis`)
}

// 保存项目设置
const saveProjectSettings = () => {
  message.success('项目设置已保存')
}

// 加载项目数据
const loadProjectData = async () => {
  try {
    // 模拟API调用
    console.log('加载项目数据:', projectId.value)

    // 加载实例数据
    const mockInstanceData = Array.from({ length: 15 }, (_, index) => ({
      id: index + 1,
      subject_id: `SUB${String(index + 1).padStart(4, '0')}`,
      template_name: templates.value[index % templates.value.length].title,
      status: ['draft', 'in_progress', 'completed', 'locked'][index % 4],
      completion_percentage: Math.floor(Math.random() * 100),
      updated_at: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
      ).toISOString(),
    }))

    instanceData.value = mockInstanceData.slice(0, 5) // 显示前5条
  } catch (error) {
    console.error('加载项目数据失败:', error)
    message.error('加载项目数据失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadProjectData()
})
</script>

<style lang="scss" scoped>
.project-detail-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.project-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .project-info {
      flex: 1;

      .project-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .back-btn {
          padding: 8px;
          border-radius: 6px;

          &:hover {
            background-color: #f3f4f6;
          }
        }

        h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          color: #1f2937;
        }
      }

      .project-description {
        margin-left: 44px;

        p {
          margin: 0;
          color: #6b7280;
          font-size: 16px;
        }
      }
    }

    .project-actions {
      margin-left: 24px;
    }
  }
}

.project-stats {
  margin-bottom: 32px;

  .stat-card {
    :deep(.n-card-body) {
      padding: 20px;
    }

    :deep(.n-statistic) {
      .n-statistic-label {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 8px;
      }

      .n-statistic-value {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

.project-content {
  :deep(.n-tabs) {
    .n-tabs-nav {
      margin-bottom: 24px;
    }

    .n-tab-pane {
      padding: 0;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .section-title {
      h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }

  .templates-list {
    .template-card {
      height: 100%;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .template-content {
        display: flex;
        gap: 12px;

        .template-icon {
          flex-shrink: 0;
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background: #f8fafc;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .template-info {
          flex: 1;
          min-width: 0;

          h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          p {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #6b7280;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .template-meta {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
      }
    }
  }

  .empty-state {
    padding: 60px 0;
  }

  .data-overview,
  .quick-analysis {
    .chart-placeholder {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

:deep(.n-card) {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .n-card-header {
    font-weight: 600;
    color: #1f2937;
  }
}

:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.n-input),
:deep(.n-select) {
  border-radius: 8px;
}

@media (max-width: 768px) {
  .project-header {
    .header-content {
      flex-direction: column;
      gap: 16px;

      .project-actions {
        margin-left: 0;
        align-self: stretch;
      }
    }
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
