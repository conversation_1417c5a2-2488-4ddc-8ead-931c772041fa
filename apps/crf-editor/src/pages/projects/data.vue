<template>
  <div class="project-data-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <n-button text @click="goBack" class="back-btn">
            <template #icon>
              <n-icon>
                <ArrowBackOutline />
              </n-icon>
            </template>
          </n-button>
          <div class="title-content">
            <h1>{{ projectName }} - 数据收集</h1>
            <n-text depth="3">管理项目中收集的所有数据</n-text>
          </div>
        </div>
        <div class="header-actions">
          <n-button-group>
            <n-button @click="exportData">
              <template #icon>
                <n-icon>
                  <DownloadOutline />
                </n-icon>
              </template>
              导出数据
            </n-button>
            <n-button type="primary" @click="goToAnalysis">
              <template #icon>
                <n-icon>
                  <BarChartOutline />
                </n-icon>
              </template>
              数据分析
            </n-button>
          </n-button-group>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="data-stats">
      <n-grid cols="2 s:3 m:4 l:5" responsive="screen" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="总数据条数" :value="stats.totalRecords">
              <template #prefix>
                <n-icon size="18" color="#3b82f6">
                  <DocumentTextOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="已完成" :value="stats.completedRecords">
              <template #prefix>
                <n-icon size="18" color="#10b981">
                  <CheckmarkCircleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="进行中" :value="stats.inProgressRecords">
              <template #prefix>
                <n-icon size="18" color="#f59e0b">
                  <TimeOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="草稿" :value="stats.draftRecords">
              <template #prefix>
                <n-icon size="18" color="#6b7280">
                  <CreateOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card class="stat-card">
            <n-statistic label="今日新增" :value="stats.todayNew">
              <template #prefix>
                <n-icon size="18" color="#ef4444">
                  <TrendingUpOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 数据表格 -->
    <div class="data-content">
      <n-card>
        <template #header>
          <div class="table-header">
            <div class="header-left">
              <h3>数据列表</h3>
            </div>
            <div class="header-right">
              <n-space>
                <n-input
                  v-model:value="searchKeyword"
                  placeholder="搜索受试者ID或模板名称"
                  clearable
                  style="width: 300px"
                >
                  <template #prefix>
                    <n-icon>
                      <SearchOutline />
                    </n-icon>
                  </template>
                </n-input>
                <n-select
                  v-model:value="statusFilter"
                  placeholder="筛选状态"
                  :options="statusOptions"
                  clearable
                  style="width: 150px"
                />
                <n-button @click="refreshData">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
              </n-space>
            </div>
          </div>
        </template>

        <n-data-table
          :columns="tableColumns"
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          striped
        />
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline,
  DocumentTextOutline,
  DownloadOutline,
  BarChartOutline,
  CheckmarkCircleOutline,
  TimeOutline,
  CreateOutline,
  TrendingUpOutline,
  SearchOutline,
  RefreshOutline,
} from '@vicons/ionicons5'
import type { DataTableColumns } from 'naive-ui'

defineOptions({
  name: 'ProjectDataPage',
})

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 项目ID和名称
const projectId = computed(() => route.params.id as string)
const projectName = ref('示例CRF项目')

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)

// 数据统计
const stats = ref({
  totalRecords: 156,
  completedRecords: 89,
  inProgressRecords: 45,
  draftRecords: 22,
  todayNew: 8,
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 状态选项
const statusOptions = ref([
  { label: '草稿', value: 'draft' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已锁定', value: 'locked' },
])

// 表格列配置
const tableColumns: DataTableColumns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
  },
  {
    title: '受试者ID',
    key: 'subject_id',
    width: 120,
  },
  {
    title: '表单模板',
    key: 'template_name',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: any) => {
      const statusMap: Record<string, { text: string; type: string }> = {
        draft: { text: '草稿', type: 'default' },
        in_progress: { text: '进行中', type: 'warning' },
        completed: { text: '已完成', type: 'success' },
        locked: { text: '已锁定', type: 'error' },
      }
      const status = statusMap[row.status] || { text: '未知', type: 'default' }
      return h('n-tag', { type: status.type as any }, status.text)
    },
  },
  {
    title: '完成度',
    key: 'completion_percentage',
    width: 100,
    render: (row: any) => `${row.completion_percentage}%`,
  },
  {
    title: '创建者',
    key: 'creator_name',
    width: 100,
  },
  {
    title: '更新时间',
    key: 'updated_at',
    width: 150,
    render: (row: any) => new Date(row.updated_at).toLocaleDateString(),
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row: any) => {
      return h('n-space', { size: 'small' }, [
        h(
          'n-button',
          {
            size: 'small',
            onClick: () => viewRecord(row),
          },
          '查看',
        ),
        h(
          'n-button',
          {
            size: 'small',
            type: 'primary',
            onClick: () => editRecord(row),
          },
          '编辑',
        ),
      ])
    },
  },
]

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.value.page = page
    loadData()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    loadData()
  },
})

// 返回项目详情
const goBack = () => {
  router.push(`/projects/${projectId.value}`)
}

// 导出数据
const exportData = () => {
  message.success('数据导出功能开发中')
}

// 跳转到数据分析
const goToAnalysis = () => {
  router.push(`/projects/${projectId.value}/analysis`)
}

// 查看记录
const viewRecord = (record: any) => {
  router.push(`/form-fill/view/${record.id}`)
}

// 编辑记录
const editRecord = (record: any) => {
  router.push(`/form-fill/edit/${record.id}`)
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 模拟数据
    const mockData = Array.from({ length: 156 }, (_, index) => ({
      id: index + 1,
      subject_id: `SUB${String(index + 1).padStart(4, '0')}`,
      template_name: [
        '患者基线信息采集模板',
        '不良事件报告表',
        '实验室检查结果模板',
      ][index % 3],
      status: ['draft', 'in_progress', 'completed', 'locked'][index % 4],
      completion_percentage: Math.floor(Math.random() * 100),
      creator_name: ['张医生', '李护士', '王研究员'][index % 3],
      updated_at: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
      ).toISOString(),
    }))

    // 应用筛选
    let filteredData = mockData

    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filteredData = filteredData.filter(
        (item) =>
          item.subject_id.toLowerCase().includes(keyword) ||
          item.template_name.toLowerCase().includes(keyword),
      )
    }

    if (statusFilter.value) {
      filteredData = filteredData.filter(
        (item) => item.status === statusFilter.value,
      )
    }

    const startIndex = (pagination.value.page - 1) * pagination.value.pageSize
    const endIndex = startIndex + pagination.value.pageSize

    tableData.value = filteredData.slice(startIndex, endIndex)
    pagination.value.itemCount = filteredData.length
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.project-data-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .page-title {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .back-btn {
        padding: 8px;
        border-radius: 6px;
        margin-top: 4px;

        &:hover {
          background-color: #f3f4f6;
        }
      }

      .title-content {
        h1 {
          margin: 0 0 4px 0;
          font-size: 28px;
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }
}

.data-stats {
  margin-bottom: 32px;

  .stat-card {
    :deep(.n-card-body) {
      padding: 20px;
    }

    :deep(.n-statistic) {
      .n-statistic-label {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 8px;
      }

      .n-statistic-value {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

.data-content {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

:deep(.n-card) {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .n-card-header {
    font-weight: 600;
    color: #1f2937;
  }
}

:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.n-input),
:deep(.n-select) {
  border-radius: 8px;
}

:deep(.n-data-table) {
  .n-data-table-th {
    background: #f9fafb;
    font-weight: 600;
  }
}

@media (max-width: 768px) {
  .page-header {
    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
  }

  .table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
