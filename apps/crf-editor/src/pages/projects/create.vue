<template>
  <div class="create-project-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">创建项目</h1>
        <p class="page-description">创建新的研究项目</p>
      </div>
      <div class="header-right">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="creating" @click="handleCreate">
          创建项目
        </n-button>
      </div>
    </div>

    <!-- 创建表单 -->
    <div class="create-form">
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="large"
      >
        <n-grid cols="24" :x-gap="24">
          <n-grid-item span="12">
            <n-form-item label="项目名称" path="name">
              <n-input
                v-model:value="form.name"
                placeholder="请输入项目名称"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item span="12">
            <n-form-item label="项目类型" path="type">
              <n-select
                v-model:value="form.type"
                placeholder="选择项目类型"
                style="width: 100%"
              >
                <n-option label="临床试验" value="clinical_trial" />
                <n-option label="队列研究" value="cohort_study" />
                <n-option label="病例对照研究" value="case_control" />
                <n-option label="横断面研究" value="cross_sectional" />
                <n-option label="真实世界研究" value="real_world" />
                <n-option label="其他" value="other" />
              </n-select>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-form-item label="项目描述" path="description">
          <n-input
            v-model:value="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入项目描述"
          />
        </n-form-item>

        <n-grid cols="24" :x-gap="24">
          <n-grid-item span="8">
            <n-form-item label="开始日期" path="start_date">
              <n-date-picker
                v-model:value="form.start_date"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item span="8">
            <n-form-item label="预计结束日期" path="end_date">
              <n-date-picker
                v-model:value="form.end_date"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item span="8">
            <n-form-item label="项目状态" path="status">
              <n-select
                v-model:value="form.status"
                placeholder="选择项目状态"
                style="width: 100%"
              >
                <n-option label="筹备中" value="preparing" />
                <n-option label="进行中" value="active" />
                <n-option label="已暂停" value="paused" />
                <n-option label="已完成" value="completed" />
              </n-select>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-grid cols="24" :x-gap="24">
          <n-grid-item span="12">
            <n-form-item label="主要研究者" path="principal_investigator">
              <n-input
                v-model:value="form.principal_investigator"
                placeholder="请输入主要研究者姓名"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item span="12">
            <n-form-item label="联系方式" path="contact_info">
              <n-input
                v-model:value="form.contact_info"
                placeholder="请输入联系方式"
                clearable
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-form-item label="研究机构">
          <n-input
            v-model:value="form.institution"
            placeholder="请输入研究机构名称"
            clearable
          />
        </n-form-item>

        <n-form-item label="项目设置">
          <n-grid cols="24" :x-gap="24">
            <n-grid-item span="8">
              <n-form-item label="目标样本量">
                <n-input-number
                  v-model:value="form.target_sample_size"
                  :min="1"
                  placeholder="目标样本量"
                  style="width: 100%"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item span="8">
              <n-form-item label="数据质量要求">
                <n-select
                  v-model:value="form.data_quality_level"
                  placeholder="数据质量要求"
                  style="width: 100%"
                >
                  <n-option label="基础" value="basic" />
                  <n-option label="标准" value="standard" />
                  <n-option label="严格" value="strict" />
                </n-select>
              </n-form-item>
            </n-grid-item>
            <n-grid-item span="8">
              <n-form-item label="数据导出权限">
                <n-select
                  v-model:value="form.export_permission"
                  placeholder="数据导出权限"
                  style="width: 100%"
                >
                  <n-option label="无限制" value="unlimited" />
                  <n-option label="需要审批" value="approval_required" />
                  <n-option label="禁止导出" value="restricted" />
                </n-select>
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </n-form-item>

        <n-form-item label="项目标签">
          <n-select
            v-model:value="form.tags"
            multiple
            filterable
            tag
            placeholder="添加项目标签"
            style="width: 100%"
          >
            <n-option
              v-for="tag in predefinedTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </n-select>
        </n-form-item>

        <n-form-item label="备注信息">
          <n-input
            v-model:value="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { type FormInst, type FormRules } from 'naive-ui'
import { projectAPI } from '@/api'
import { getErrorMessage } from '@/utils/error-messages'

defineOptions({
  name: 'CreateProjectPage',
})

const router = useRouter()
const message = useMessage()

// 表单引用
const formRef = ref<FormInst>()

// 状态
const creating = ref(false)

// 预定义标签
const predefinedTags = [
  '妇科',
  '产科',
  '肿瘤科',
  '心血管',
  '神经科',
  '呼吸科',
  '消化科',
  '内分泌',
  '免疫科',
  '儿科',
  '急诊科',
  '重症医学',
  '多中心',
  '前瞻性',
  '回顾性',
  '随机对照',
  'RCT',
  'RWS',
]

// 表单数据
const form = reactive({
  name: '',
  type: '',
  description: '',
  start_date: '',
  end_date: '',
  status: 'preparing',
  principal_investigator: '',
  contact_info: '',
  institution: '',
  target_sample_size: undefined as number | undefined,
  data_quality_level: 'standard',
  export_permission: 'approval_required',
  tags: [] as string[],
  notes: '',
})

// 验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    {
      min: 2,
      max: 100,
      message: '项目名称长度在 2 到 100 个字符',
      trigger: 'blur',
    },
  ],
  type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  description: [
    { required: true, message: '请输入项目描述', trigger: 'blur' },
    {
      min: 10,
      max: 1000,
      message: '项目描述长度在 10 到 1000 个字符',
      trigger: 'blur',
    },
  ],
  start_date: [
    { required: true, message: '请选择开始日期', trigger: 'change' },
  ],
  principal_investigator: [
    { required: true, message: '请输入主要研究者姓名', trigger: 'blur' },
  ],
}

// 创建项目
const handleCreate = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    creating.value = true

    // 构建项目数据
    const projectData = {
      name: form.name,
      description: form.description,
      settings: {
        type: form.type,
        start_date: form.start_date,
        end_date: form.end_date,
        status: form.status,
        principal_investigator: form.principal_investigator,
        contact_info: form.contact_info,
        institution: form.institution,
        target_sample_size: form.target_sample_size,
        data_quality_level: form.data_quality_level,
        export_permission: form.export_permission,
        tags: form.tags,
        notes: form.notes,
      },
    }

    const response = await projectAPI.createProject(projectData)
    if (response.success) {
      message.success('项目创建成功')
      router.push(`/projects/${response.data?.project?.id}`)
    }
  } catch (error: unknown) {
    console.error('创建项目失败:', error)
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      validation: '项目信息有误，请检查后重试',
      default: '创建项目失败，请稍后重试',
    })
    message.error(errorMessage)
  } finally {
    creating.value = false
  }
}

// 取消创建
const handleCancel = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.create-project-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--crf-text-color);
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: var(--crf-text-color-secondary);
        margin: 0;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .create-form {
    background: var(--crf-card-bg);
    border: 1px solid var(--crf-border-color);
    border-radius: 12px;
    padding: 32px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .create-project-page {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .create-form {
      padding: 20px;
    }
  }
}
</style>
