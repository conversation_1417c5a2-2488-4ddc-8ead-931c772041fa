// 对话框和弹窗样式
@use '@/styles/radius-config.scss';

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 60px 40px;

  .empty-content {
    text-align: center;
    max-width: 480px;

    .empty-icon {
      margin-bottom: 32px;
      opacity: 0.7;
      filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.1));
    }

    .empty-title {
      font-size: 24px;
      font-weight: 700;
      color: #1a202c;
      margin: 0 0 12px;
      line-height: 1.3;
    }

    .empty-description {
      font-size: 16px;
      color: #718096;
      margin: 0 0 32px;
      line-height: 1.6;
    }

    .empty-actions {
      .n-button {
        padding: 16px 32px;
        @include radius-config.rounded-small;
        border: none;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        font-size: 15px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
        }

        .n-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

// 创建表单对话框样式
:deep(.create-form-dialog) {
  .n-modal {
    @include radius-config.rounded-large;
    overflow: visible;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);

    .n-modal__header {
      margin: 0;
      padding: 24px;
      border-bottom: 1px solid #f0f0f0;
      background: #ffffff;

      .n-modal__title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }

      .n-modal__headerbtn {
        top: 24px;
        right: 24px;

        .n-modal__close {
          font-size: 18px;
          color: #9ca3af;

          &:hover {
            color: #6b7280;
          }
        }
      }
    }

    .n-modal__body {
      padding: 24px;
      background: #ffffff;
      overflow: visible;

      .n-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .n-form-item__label {
          font-weight: 500;
          color: #374151;
          padding-right: 12px;
        }

        .n-input__wrapper,
        .n-input-wrapper {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          transition: all 0.2s;

          &:hover {
            border-color: #d1d5db;
          }

          &.is-focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          }
        }

        .n-input__inner,
        .n-input__textarea-el {
          color: #1f2937;
          font-size: 14px;

          &::placeholder {
            color: #9ca3af;
          }
        }

        // 图标选择器特殊样式
        .icon-selector {
          position: relative;
          z-index: 1000;

          .icon-trigger {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            transition: all 0.2s;

            &:hover {
              border-color: #d1d5db;
            }

            &:focus-within {
              border-color: #3b82f6;
              box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            }
          }
        }
      }
    }

    .n-modal__footer {
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .n-button {
          min-width: 88px;
          @include radius-config.rounded-small;
          font-weight: 500;

          &--default {
            border-color: #e5e7eb;
            color: #374151;

            &:hover {
              border-color: #d1d5db;
              color: #1f2937;
            }
          }

          &--primary {
            background: #3b82f6;
            border-color: #3b82f6;

            &:hover {
              background: #2563eb;
              border-color: #2563eb;
            }

            &:active {
              background: #1d4ed8;
              border-color: #1d4ed8;
            }
          }
        }
      }
    }
  }
}

// 下拉菜单样式
:deep(.n-dropdown-menu) {
  border: 1px solid #e5e7eb;
  @include radius-config.rounded-medium;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 4px;
  min-width: 120px;

  .n-dropdown-menu__item {
    @include radius-config.rounded-small;
    margin: 2px;
    padding: 8px 12px;
    font-size: 14px;
    color: #374151;
    transition: all 0.2s ease;

    .n-icon {
      margin-right: 8px;
      font-size: 14px;
    }

    &:hover {
      background: #f3f4f6;
      color: #1f2937;
    }

    &.is-disabled {
      color: #9ca3af;
      cursor: not-allowed;

      &:hover {
        background: transparent;
      }
    }

    &.is-divided {
      border-top: 1px solid #f3f4f6;
      margin-top: 4px;
      padding-top: 12px;
    }
  }
}

// 快速上手对话框样式
:global(.tour-dialog) {
  .n-message-box__content {
    max-height: 70vh;
    overflow-y: auto;

    h3 {
      margin-top: 0;
    }

    h4 {
      color: var(--n-text-color-base);
      margin-bottom: 8px;
    }

    p {
      color: var(--n-text-color-base);
      margin-bottom: 0;
    }
  }
}