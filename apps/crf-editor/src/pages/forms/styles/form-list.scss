// 表单列表页面样式
@use '@/styles/radius-config.scss';

.forms-container {
  padding: 8px;
  background: #f8fafc;
  height: calc(100vh - 60px); /* 减去header高度，避免滚动条 */
  display: flex;
  flex-direction: column;
  gap: 6px; /* 减小数据总览和表单管理之间的间距 */
  overflow: hidden;
}

// 统计信息容器样式
.statistics-container {
  background: #ffffff;
  @include radius-config.rounded-small;
  padding: 16px 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;

  .statistics-header {
    margin-bottom: 16px;

    .statistics-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 4px 0;
      line-height: 1.3;
    }

    .statistics-subtitle {
      font-size: 14px;
      color: #6b7280;
      margin: 0;
      font-weight: 400;
    }
  }
}

// 表单管理容器样式
.forms-management-container {
  background: #ffffff;
  @include radius-config.rounded-small;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: calc(100vh - 200px); // 限制最大高度，为页面其他元素留出空间
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 8px;
    
    &:hover {
      background: rgba(148, 163, 184, 0.7);
    }
  }
  
  // Firefox 滚动条样式
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.5) rgba(241, 245, 249, 0.5);
}

// 页面头部样式
.forms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-right {
    display: flex;
    gap: 16px;
    align-items: center;
    
    .search-container {
      .n-input {
        .n-input__prefix {
          color: #9ca3af;
        }
        
        .n-input__input {
          font-size: 14px;
        }
        
        &:hover {
          border-color: #d1d5db;
        }
        
        &:focus-within {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }
    }

    .select-all-section {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: rgba(102, 126, 234, 0.05);
      @include radius-config.rounded-small;
      border: 1px solid rgba(102, 126, 234, 0.2);

      :deep(.n-checkbox) {
        .n-checkbox__label {
          font-size: 14px;
          font-weight: 500;
          color: #667eea;
        }
      }
    }

    .batch-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      @include radius-config.rounded-large;
      border: 1px solid #f59e0b;

      .selected-count {
        font-size: 14px;
        font-weight: 600;
        color: #92400e;
      }

      .n-button {
        height: 32px;
        padding: 0 12px;
        font-size: 13px;
        font-weight: 500;
      }
    }

    .n-button {
      height: 40px;
      padding: 0 20px;
      @include radius-config.rounded-small;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;

      &.flat-btn {
        border: 1px solid #e2e8f0;
        background: #ffffff;
        color: #374151;
        box-shadow: none;

        &:hover {
          border-color: #d1d5db;
          background: #f9fafb;
          transform: none;
        }

        &.n-button--primary {
          background: #667eea;
          color: white;
          border-color: #667eea;

          &:hover {
            background: #5a67d8;
            border-color: #5a67d8;
          }
        }

        &.n-button--error {
          background: #ef4444;
          color: white;
          border-color: #ef4444;

          &:hover {
            background: #dc2626;
            border-color: #dc2626;
          }
        }
      }

      &.create-form-btn {
        background: #667eea;
        color: white;
        border: 1px solid #667eea;
        box-shadow: none;

        &:hover {
          background: #5a67d8;
          border-color: #5a67d8;
          transform: none;
        }

        .n-icon {
          margin-right: 6px;
          font-size: 16px;
        }
      }
    }

    .layout-buttons {
      .layout-btn {
        width: 40px;
        height: 40px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 标签页过滤样式
.filter-tabs {
  margin-bottom: 24px;

  :deep(.n-tabs) {
    background: #f8fafc;
    @include radius-config.rounded-small;
    padding: 4px;
    border: 1px solid #e2e8f0;
  }

  :deep(.n-tabs__header) {
    margin-bottom: 0;

    .n-tabs__nav-wrap {
      &::after {
        display: none;
      }
    }

    .n-tabs__item {
      padding: 0 16px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 500;
      color: #6b7280;
      @include radius-config.rounded-small;
      margin: 0 2px;
      transition: all 0.3s ease;

      &.is-active {
        color: #667eea;
        background: #ffffff;
        font-weight: 600;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      &:hover {
        color: #667eea;
        background: rgba(255, 255, 255, 0.8);
      }
    }

    .n-tabs__active-bar {
      display: none;
    }
  }
}

// 表单列表容器样式
.forms-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
     border-radius: 8px;
     
     &:hover {
       background: rgba(148, 163, 184, 0.7);
     }
   }
 }
 
 // 骨架屏样式
.forms-grid-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
  justify-items: start; /* 左对齐，不居中 */
}

.form-card-skeleton {
  background: #ffffff;
  @include radius-config.rounded-small;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 200px;
  width: 100%;
  max-width: 300px;

  .skeleton-card {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .skeleton-header {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .skeleton-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    .skeleton-footer {
      margin-top: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

// 标签页切换加载样式
.tab-switching-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;

  .loading-bar {
    width: 100%;
    max-width: 400px;
  }

  .loading-text {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
  }
}
 
 // 加载状态样式
 .loading-state {
   width: 100%;
   height: 100%;
   display: flex;
   align-items: center;
   justify-content: center;
 
   .loading-content {
     text-align: center;
     color: #6b7280;
 
     .loading-icon {
       font-size: 32px;
       margin-bottom: 16px;
       color: #9ca3af;
 
       &.is-loading {
         animation: spin 1s linear infinite;
       }
     }
 
     .loading-text {
       font-size: 16px;
       font-weight: 500;
     }
   }
 }
 
 @keyframes spin {
   from {
     transform: rotate(0deg);
   }
   to {
     transform: rotate(360deg);
   }
 }

// 旋转加载动画
.is-loading {
  animation: rotate 1s linear infinite;
}

// 标签页切换骨架屏
.tab-switching-skeleton {
  .forms-grid-skeleton {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    padding: 16px 0;

    .form-card-skeleton {
      .skeleton-card {
        padding: 24px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

        .skeleton-header {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 16px;

          .skeleton-content {
            flex: 1;
          }
        }

        .skeleton-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;
        }
      }
    }
  }
}

// 顶部加载指示器
.top-loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  margin-bottom: 16px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  color: #409eff;
  font-size: 14px;

  .n-icon {
    font-size: 16px;
  }
}

// 表单网格容器
.forms-grid-container {
  position: relative;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 8px;
    
    &:hover {
      background: rgba(148, 163, 184, 0.7);
    }
  }
  
  // Firefox 滚动条样式
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.5) rgba(241, 245, 249, 0.5);
}

.forms-grid {
  position: relative;
  min-height: 400px;
}

.forms-grid-inner {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
  align-items: stretch;
  padding: 16px 0;
}

// 分页样式
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

// 动画定义
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 过渡动画
.form-card-fade-enter-active {
  transition: all 0.3s ease-out;
}

.form-card-fade-leave-active {
  transition: all 0.2s ease-in;
}

.form-card-fade-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.form-card-fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.form-card-fade-move {
  transition: none !important;
}

.loading-indicator-enter-active,
.loading-indicator-leave-active {
  transition: all 0.3s ease;
}

.loading-indicator-enter-from,
.loading-indicator-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}