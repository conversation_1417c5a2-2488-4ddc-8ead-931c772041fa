// 表单填写抽屉样式
@use '@/styles/radius-config.scss';

// 滑动表单填写界面
.form-fill-drawer {
  :deep(.n-drawer-body) {
    padding: 0;
    background: #f8fafc;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .form-fill-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    z-index: 10;
    position: relative;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        .n-button {
          border: none;
          background: transparent;
          color: #64748b;
          padding: 8px;
          @include radius-config.rounded-small;
          transition: all 0.2s ease;

          &:hover {
            background: #f1f5f9;
            color: #475569;
          }

          .n-icon {
            font-size: 18px;
          }
        }
      }

      .form-info {
        .form-title {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 4px;
          line-height: 1.2;
        }

        .form-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .form-status {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;

            &.published {
              background: #dcfce7;
              color: #166534;
            }

            &.draft {
              background: #fef3c7;
              color: #92400e;
            }
          }

          .auto-save-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #64748b;
            font-size: 12px;

            .save-icon {
              font-size: 14px;

              &.saving {
                animation: spin 1s linear infinite;
              }

              &.saved {
                color: #10b981;
              }
            }

            @keyframes spin {
              from {
                transform: rotate(0deg);
              }

              to {
                transform: rotate(360deg);
              }
            }
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .action-button {
        .n-button {
          @include radius-config.rounded-small;
          font-weight: 500;

          &--primary {
            background: #3b82f6;
            border-color: #3b82f6;

            &:hover {
              background: #2563eb;
              border-color: #2563eb;
            }
          }

          &--default {
            border-color: #e2e8f0;
            color: #64748b;

            &:hover {
              border-color: #cbd5e1;
              color: #475569;
            }
          }
        }
      }
    }
  }

  .form-fill-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .content-loading {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: white;

      .loading-content {
        text-align: center;

        .loading-spinner {
          margin-bottom: 16px;

          .n-spin {
            font-size: 32px;
            color: #3b82f6;
          }
        }

        .loading-text {
          color: #64748b;
          font-size: 14px;
        }
      }
    }

    .form-render-container {
      flex: 1;
      overflow-y: auto;
      background: white;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
      }

      &::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;

        &:hover {
          background: #94a3b8;
        }
      }

      // 表单内容区域
      .form-content {
        padding: 24px;
        max-width: 800px;
        margin: 0 auto;

        // 表单项间距
        .form-item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        // 表单标题
        .form-section-title {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin: 32px 0 16px;
          padding-bottom: 8px;
          border-bottom: 2px solid #e2e8f0;

          &:first-child {
            margin-top: 0;
          }
        }

        // 表单描述
        .form-description {
          color: #64748b;
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 24px;
          padding: 16px;
          background: #f8fafc;
          @include radius-config.rounded-medium;
          border-left: 4px solid #3b82f6;
        }
      }
    }
  }
}

// 旧的表单填写头部样式（备用）
.form-fill-header-old {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        .n-button {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
          }
        }
      }

      .form-info {
        .form-title {
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 4px;
        }

        .form-meta {
          display: flex;
          align-items: center;
          gap: 12px;
          opacity: 0.9;

          .form-status {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
          }

          .auto-save-indicator {
            font-size: 12px;
          }
        }
      }
    }

    .header-right {
      .action-button {
        .n-button {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
          }

          &--primary {
            background: white;
            color: #667eea;
            border-color: white;

            &:hover {
              background: #f8fafc;
              color: #5a67d8;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-fill-drawer {
    .form-fill-header {
      padding: 12px 16px;

      .header-left {
        gap: 12px;

        .form-info {
          .form-title {
            font-size: 16px;
          }

          .form-meta {
            gap: 8px;

            .form-status,
            .auto-save-indicator {
              font-size: 11px;
            }
          }
        }
      }

      .header-right {
        gap: 8px;

        .action-button {
          .n-button {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }

    .form-fill-content {
      .form-render-container {
        .form-content {
          padding: 16px;

          .form-item {
            margin-bottom: 20px;
          }

          .form-section-title {
            font-size: 15px;
            margin: 24px 0 12px;
          }

          .form-description {
            padding: 12px;
            font-size: 13px;
            margin-bottom: 20px;
          }
        }
      }
    }
  }
}