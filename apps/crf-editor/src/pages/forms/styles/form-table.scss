// 表格视图样式
.forms-table-container {
  .n-data-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .n-data-table-thead {
      background: #f8fafc;
      
      .n-data-table-th {
        font-weight: 600;
        color: #374151;
        border-bottom: 1px solid #e5e7eb;
      }
    }
    
    .n-data-table-tbody {
      .n-data-table-tr {
        transition: background-color 0.2s ease;
        
        &:hover {
          background: #f9fafb;
        }
        
        .n-data-table-td {
          border-bottom: 1px solid #f1f5f9;
          padding: 12px 16px;
        }
      }
    }
  }
}

// 表格中的表单信息样式
.table-form-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .table-form-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
  }
  
  .table-form-details {
    flex: 1;
    min-width: 0;
    
    .table-form-title {
      font-weight: 600;
      color: #1f2937;
      font-size: 14px;
      line-height: 1.4;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .table-form-description {
      color: #6b7280;
      font-size: 12px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 表格中的时间显示样式
.table-time {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 13px;
  
  .time-icon {
    font-size: 14px;
    color: #9ca3af;
  }
}

// 表格操作按钮样式
.table-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;

  .n-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .n-icon {
      font-size: 16px;
    }
  }

  .n-dropdown {
    .n-button {
      width: 32px;
      height: 32px;
    }
  }
}

// 版本标签样式
.version-tag {
  background: #dcfce7 !important;
  color: #166534 !important;
  border: 1px solid #bbf7d0 !important;
}

// 响应式设计
@media (max-width: 768px) {
  .forms-table-container {
    .n-data-table {
      font-size: 12px;
    }
    
    .table-form-info {
      .table-form-icon {
        width: 32px;
        height: 32px;
      }
      
      .table-form-details {
        .table-form-title {
          font-size: 13px;
        }
        
        .table-form-description {
          font-size: 11px;
        }
      }
    }
    
    .table-actions {
      gap: 4px;
      
      .n-button {
        height: 24px;
        padding: 0 6px;
        font-size: 11px;
      }
    }
  }
}