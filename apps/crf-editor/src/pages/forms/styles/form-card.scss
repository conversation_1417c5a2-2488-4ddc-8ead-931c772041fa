// 表单卡片样式
@use '@/styles/radius-config.scss';

.form-card {
  background: #ffffff;
  @include radius-config.rounded-small;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 320px; // 固定高度，确保所有卡片高度一致
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  z-index: 1;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #667eea;
    z-index: 2;
    
    // 确保悬浮时保持圆角
    border-radius: var(--radius-small);
    
    // 优化悬浮时的视觉效果
    .form-meta {
      border-bottom-left-radius: var(--radius-small);
      border-bottom-right-radius: var(--radius-small);
      background: linear-gradient(180deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 1) 100%);
    }
    
    .form-actions {
      border-bottom-left-radius: var(--radius-small);
      border-bottom-right-radius: var(--radius-small);
      background: linear-gradient(180deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 1) 100%);
    }
    
    // 增强卡片内容的视觉效果
    .form-card-content {
      transform: translateY(-1px);
    }
  }

  &:active {
    transform: translateY(-1px);
    transition-duration: 0.2s;
  }

  // 骨架屏样式
  &.form-card-skeleton {
    cursor: default;
    pointer-events: none;
    
    &:hover {
      transform: none;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border-color: #e2e8f0;
    }

    // 复选框占位样式
    .skeleton-checkbox {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 10;
      
      .n-skeleton {
        opacity: 0.3;
      }
    }

    .form-card-body,
    .form-actions {
      .n-skeleton {
        border-radius: 9999px; /* 半圆形 */
        
        &.skeleton-line {
          height: 14px; /* 默认高度 */
          margin-right: 8px; /* 横向间距 */
          margin-bottom: 0; /* 移除底部间距 */
          display: inline-block; /* 使其横向排列 */
          vertical-align: middle; /* 垂直对齐 */
        }

        &.skeleton-circle-line {
          margin-right: 8px; /* 横向间距 */
          margin-bottom: 0; /* 移除底部间距 */
          display: inline-block; /* 使其横向排列 */
          vertical-align: middle; /* 垂直对齐 */
        }

        &:last-child {
          margin-right: 0; /* 最后一个元素不需要右侧间距 */
        }
      }
    }

    .form-card-content {
      .form-info {
        .form-header {
          display: flex;
          align-items: center;
          gap: 8px; /* 使用gap替代margin */
          flex-wrap: wrap; /* 允许换行 */
          
          .n-skeleton {
            margin: 0; /* 移除所有margin */
            
            &.skeleton-line {
              flex-shrink: 0; /* 防止收缩 */
            }
          }
        }
        
        /* 描述文本行 */
        > .n-skeleton.skeleton-line {
          display: block; /* 描述文本独占一行 */
          margin: 8px 0 6px 0; /* 上下间距 */
        }
      }
    }

    .form-meta {
      display: flex;
      align-items: center;
      gap: 8px; /* 使用gap替代margin */
      
      .n-skeleton {
        margin: 0; /* 移除所有margin */
        flex-shrink: 0; /* 防止收缩 */
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 8px; /* 使用gap替代margin */
      
      .n-skeleton {
        margin: 0; /* 移除所有margin */
        flex-shrink: 0; /* 防止收缩 */
      }
    }
  }

  // 复选框容器样式
  .form-checkbox-container {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 10;
    padding: 6px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    cursor: pointer;
    
    &:hover {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 选中状态
  &.is-selected {
    border: 2px solid #3b82f6;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);

    .form-checkbox-container {
      opacity: 1;
    }
  }

  // 复制状态样式
  &.is-duplicating {
    position: relative;
    pointer-events: none;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
      z-index: 10;
      border-radius: 16px;
    }

    &::after {
      content: '复制中...';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 11;
      background: #3b82f6;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      animation: pulse-duplicate 1.5s ease-in-out infinite;
    }

    .form-card-content {
      opacity: 0.6;
    }

    .form-actions {
      opacity: 0.4;
    }
  }

  // 删除状态样式
  &.is-deleting {
    position: relative;
    pointer-events: none;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
      z-index: 10;
      border-radius: 16px;
    }

    &::after {
      content: '删除中...';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 11;
      background: #ef4444;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
      animation: pulse-delete 1.5s ease-in-out infinite;
    }

    .form-card-content {
      opacity: 0.6;
    }

    .form-actions {
      opacity: 0.4;
    }
  }

  // 高亮新创建表单的样式
  &.highlight-new-form {
    border: 2px solid #10b981;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.25);
    animation: highlight-pulse 2s ease-in-out;
    transform: translateY(-4px);
    z-index: 10;

    .form-icon {
      animation: icon-bounce 0.6s ease-out;
    }
  }
}

.form-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-card-content {
  padding: 24px;
  padding-right: 70px; // 增加右边距为复选框留出更多空间
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
  position: relative;
  padding-bottom: 50px;
}

// 表单图标容器样式
.form-icon-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表单图标样式
.form-icon {
  width: 48px;
  height: 48px;
  @include radius-config.rounded-large;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: -1px;
    border-radius: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    z-index: -1;
    opacity: 0.2;
    filter: blur(4px);
  }

  .icon {
    font-size: 22px;
    color: #ffffff;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 表单信息样式
.form-info {
  flex: 1;
  min-width: 0;
}

.form-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 8px;
}

.form-title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #667eea;
    transform: translateX(2px);
  }
}

.form-status {
  flex-shrink: 0;
  font-size: 11px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  position: relative;
  z-index: 3;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-description {
  margin: 8px 0 16px 0;
  font-size: 14px;
  color: #718096;
  line-height: 1.6;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #4a5568;
  }
}

// 表单元数据样式
.form-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 16px 15px;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(10px);
  z-index: 2;
  // 确保底部圆角
  border-bottom-left-radius: var(--radius-small);
  border-bottom-right-radius: var(--radius-small);
  overflow: hidden;

  .version-tag {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border: none !important;
    color: #ffffff !important;
    font-weight: 600;
    font-size: 10px;
    padding: 4px 12px;
    border-radius: 20px;
    height: auto;
    line-height: 1.2;
    flex-shrink: 0;
    position: relative;
    z-index: 3;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);

    &.n-tag--small {
      height: auto;
      padding: 4px 12px;
      font-size: 10px;
    }
  }

  .edit-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #a0aec0;
    white-space: nowrap;
    font-weight: 500;

    .time-icon {
      font-size: 12px;
      color: #cbd5e0;
    }
  }
}

// 表单操作按钮样式
.form-actions {
  padding: 12px 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  background: linear-gradient(180deg, rgba(248, 250, 252, 0.9) 0%, rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(12px);
  // 确保底部圆角
  border-bottom-left-radius: var(--radius-small);
  border-bottom-right-radius: var(--radius-small);
  overflow: hidden;
  transition: all 0.3s ease;

  .actions-row {
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    flex: 1;
    min-width: 0;
    flex-shrink: 1;
  }

  .n-button {
    height: 32px;
    padding: 4px 12px;
    color: #4a5568;
    border: 1px solid #e2e8f0;
    @include radius-config.rounded-small;
    background: #ffffff;
    font-weight: 500;
    font-size: 12px;
    flex-shrink: 1;
    min-width: 0;
    transition: all 0.3s ease;

    .n-icon {
      margin-right: 4px;
      font-size: 14px;
    }

    &:hover {
      color: #667eea;
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.05);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    }
  }

  // 更多按钮样式
  .more-dropdown {
    flex-shrink: 0;
    margin-left: auto;

    .more-button {
      width: 32px !important;
      height: 32px !important;
      padding: 6px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border: 1px solid #e2e8f0 !important;
      @include radius-config.rounded-small;
      background: #ffffff !important;
      color: #64748b !important;
      font-weight: 500 !important;
      font-size: 13px !important;
      transition: all 0.2s ease !important;
      min-width: 32px !important;

      .n-icon {
        margin-right: 0 !important;
        font-size: 13px !important;
      }

      &:hover {
        color: #3b82f6 !important;
        border-color: #3b82f6 !important;
        background: #f8fafc !important;
      }

      &:focus {
        outline: none !important;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
      }
    }
  }
}

// 未发布状态的填写按钮样式
.unpublished-fill-btn {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #f59e0b, #f97316);
    border-radius: 6px;
    z-index: -1;
    animation: warning-glow 2s ease-in-out infinite;
  }

  &:hover::before {
    animation-play-state: paused;
  }

  .warning-icon {
    margin-left: 4px;
    color: #f59e0b;
    animation: warning-pulse 1.5s ease-in-out infinite;
  }
}

// 新表单高亮效果
.highlight-new-form {
  position: relative;
  animation: highlight-scale-pulse 2s ease-in-out;
  border-color: #3b82f6 !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3) !important;
  z-index: 10;
}

// 动画定义
@keyframes pulse-duplicate {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 0.9;
  }
}

@keyframes pulse-delete {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 0.9;
  }
}

@keyframes highlight-scale-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }

  25% {
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
  }

  50% {
    transform: scale(1.02);
    box-shadow: 0 10px 28px rgba(59, 130, 246, 0.35);
  }

  75% {
    transform: scale(1.03);
    box-shadow: 0 11px 29px rgba(59, 130, 246, 0.37);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
  }
}

@keyframes warning-glow {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes warning-pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes highlight-pulse {
  0% {
    transform: translateY(-4px) scale(1);
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.25);
  }

  25% {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 12px 40px rgba(16, 185, 129, 0.35);
  }

  50% {
    transform: translateY(-5px) scale(1.01);
    box-shadow: 0 10px 36px rgba(16, 185, 129, 0.3);
  }

  75% {
    transform: translateY(-5px) scale(1.01);
    box-shadow: 0 11px 38px rgba(16, 185, 129, 0.32);
  }

  100% {
    transform: translateY(-2px) scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

@keyframes icon-bounce {
  0% {
    transform: scale(1);
  }

  30% {
    transform: scale(1.15);
  }

  60% {
    transform: scale(0.95);
  }

  100% {
    transform: scale(1);
  }
}