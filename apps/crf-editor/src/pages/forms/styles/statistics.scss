// 统计信息卡片样式
@use '@/styles/radius-config.scss';

.statistics-cards {
    display: grid;
    grid-template-columns: repeat(4, minmax(180px, 220px));
    gap: 12px;
    justify-content: start;

    @media (max-width: 1024px) {
        grid-template-columns: repeat(2, minmax(180px, 220px));
        gap: 10px;
    }

    @media (max-width: 640px) {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

.stat-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    @include radius-config.rounded-small;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-shadow: none;
    min-height: 72px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
        border-color: #d1d5db;
    }

    &.active {
        border-color: #3b82f6;
        background: #ffffff;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.08);
        transform: translateY(-1px);

        .stat-icon {
            background: #3b82f6;
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        }

        .stat-number {
            color: #3b82f6;
            font-weight: 800;
        }

        .stat-label {
            color: #3b82f6;
            font-weight: 600;
        }
    }

    // 不同类型的统计卡片颜色
    &.all-forms {
        &.active {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.08);

            .stat-icon {
                background: #ef4444;
                box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25);
            }

            .stat-number {
                color: #ef4444;
            }

            .stat-label {
                color: #ef4444;
            }
        }

        &:hover:not(.active) {
            .stat-icon {
                background: #fef2f2;
                color: #ef4444;
            }
        }
    }

    &.published-forms {
        &.active {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.08);

            .stat-icon {
                background: #10b981;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
            }

            .stat-number {
                color: #10b981;
            }

            .stat-label {
                color: #10b981;
            }
        }

        &:hover:not(.active) {
            .stat-icon {
                background: #f0fdf4;
                color: #10b981;
            }
        }
    }

    &.unpublished-forms {
        &.active {
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.08);

            .stat-icon {
                background: #f59e0b;
                box-shadow: 0 4px 12px rgba(245, 158, 11, 0.25);
            }

            .stat-number {
                color: #f59e0b;
            }

            .stat-label {
                color: #f59e0b;
            }
        }

        &:hover:not(.active) {
            .stat-icon {
                background: #fffbeb;
                color: #f59e0b;
            }
        }
    }
}

.stat-icon {
    width: 40px;
    height: 40px;
    @include radius-config.rounded-small;
    background: #f9fafb;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;

    .n-icon {
        font-size: 18px;
        color: #6b7280;
        transition: all 0.2s ease;
    }
}

.stat-info {
    flex: 1;
    min-width: 0;

    .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        line-height: 1;
        margin-bottom: 2px;
        transition: all 0.2s ease;
    }

    .stat-label {
        font-size: 13px;
        color: #6b7280;
        font-weight: 500;
        line-height: 1.2;
        transition: all 0.2s ease;
    }
}

// 响应式调整
@media (max-width: 640px) {
    .stat-card {
        padding: 14px;
        min-height: 64px;
        gap: 10px;

        .stat-icon {
            width: 36px;
            height: 36px;

            .n-icon {
                font-size: 16px;
            }
        }

        .stat-info {
            .stat-number {
                font-size: 20px;
            }

            .stat-label {
                font-size: 12px;
            }
        }
    }
}