// RBAC类型定义
export interface User {
  id: string
  username: string
  email: string
  full_name?: string
  avatar_url?: string
  phone?: string
  department?: string
  position?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Role {
  id: string
  code: string
  name: string
  display_name: string
  description?: string
  is_system: boolean
  is_active: boolean
  created_at: string
  updated_at: string
  permissions?: Permission[]
}

export interface Permission {
  id: string
  resource: string
  action: string
  scope: string
  description?: string
  is_system: boolean
  created_at: string
}

export interface UserRole {
  id: string
  user_id: string
  role_id: string
  project_id?: string
  assigned_by: string
  assigned_at: string
  expires_at?: string
  created_at: string
  updated_at: string

  // 关联数据
  user?: User
  role?: Role
  project?: {
    id: string
    name: string
  }
  assigner?: User
}

export interface UserRoleInfo {
  user_id: string
  username: string
  email: string
  full_name: string
  roles: RoleInfo[]
  global_roles: RoleInfo[]
}

export interface RoleInfo {
  id: string
  name: string
  display_name: string
  project_id?: string
  project_name?: string
  assigned_at: string
  expires_at?: string
}

export interface PermissionResult {
  has_permission: boolean
  reason?: string
  source?: string
}

export interface PermissionCheck {
  user_id: string
  resource: string
  action: string
  project_id?: string
  resource_id?: string
}

export interface RoleAssignmentRequest {
  user_id: string
  role_id: string
  project_id?: string
  expires_at?: string
}

export interface RolePermissionRequest {
  role_id: string
  permission_ids: string[]
}

// 权限资源常量
export const RESOURCES = {
  USER: 'user',
  ROLE: 'role',
  PROJECT: 'project',
  TEMPLATE: 'template',
  INSTANCE: 'instance',
  DATA: 'data',
  SYSTEM: 'system',
} as const

// 权限操作常量
export const ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  PUBLISH: 'publish',
  SUBMIT: 'submit',
  APPROVE: 'approve',
  LOCK: 'lock',
  EXPORT: 'export',
  ANALYZE: 'analyze',
  ASSIGN_ROLE: 'assign_role',
  ASSIGN_PERMISSION: 'assign_permission',
  CONFIG: 'config',
  MONITOR: 'monitor',
  BACKUP: 'backup',
  AUDIT: 'audit',
} as const

// 权限范围常量
export const SCOPES = {
  GLOBAL: 'global',
  PROJECT: 'project',
  OWN: 'own',
} as const

// 系统角色常量
export const SYSTEM_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  RESEARCHER: 'researcher',
  DATA_ENTRY: 'data_entry',
  REVIEWER: 'reviewer',
  VIEWER: 'viewer',
} as const

export type ResourceType = (typeof RESOURCES)[keyof typeof RESOURCES]
export type ActionType = (typeof ACTIONS)[keyof typeof ACTIONS]
export type ScopeType = (typeof SCOPES)[keyof typeof SCOPES]
export type SystemRoleType = (typeof SYSTEM_ROLES)[keyof typeof SYSTEM_ROLES]
