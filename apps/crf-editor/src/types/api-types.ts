/**
 * API 类型定义 - 解决类型安全问题
 */

// 基础API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data: T
  message?: string
  error?: string
}

// 分页响应类型
export interface PaginationInfo {
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface PaginatedResponse<T = unknown> {
  success: boolean
  data: T[]
  pagination: PaginationInfo
  message?: string
}

// 用户类型
export interface User {
  id: string
  username: string
  full_name: string
  email: string
  role: string
  created_at: string
  updated_at: string
}

// 项目类型
export interface Project {
  id: string
  name: string
  description?: string
  status: 'active' | 'inactive' | 'archived'
  created_at: string
  updated_at: string
  creator?: User
  created_by?: string
  template_count?: number
  instance_count?: number
  member_count?: number
}

// 模板类型
export interface Template {
  id: string
  name: string
  template_name: string
  title?: string
  description?: string
  status: 'draft' | 'published' | 'archived'
  version: string
  template_data: Record<string, unknown>
  created_at: string
  updated_at: string
  published_at?: string
  creator?: User
  template_type?: string
  source_type?: string
}

// 实例类型
export interface Instance {
  id: string
  instance_name: string
  template_name: string
  status: 'draft' | 'submitted' | 'approved' | 'rejected'
  template: Template
  creator?: User
  submitted_at?: string
  created_at: string
  updated_at: string
  completion_percentage?: number
  form_data?: Record<string, unknown>
}

// 发布类型
export interface Publish {
  id: string
  template_name: string
  status: 'pending' | 'published' | 'failed'
  environment: 'development' | 'staging' | 'production'
  published_at?: string
  created_at: string
  updated_at: string
}

// 历史记录类型
export interface HistoryItem {
  id: string
  action: 'create' | 'update' | 'delete' | 'submit' | 'approve' | 'reject'
  user?: User
  created_at: string
  details?: Record<string, unknown>
}

// 患者信息类型
export interface PatientInfo {
  patientId: string
  patientName: string
  gender: 'male' | 'female'
  age: number
  visitDate?: string
  remarks?: string
}

// 错误类型扩展
export interface ExtendedError extends Error {
  title?: string
  code?: string
  details?: Record<string, unknown>
}

// 状态类型映射
export type StatusType =
  | 'default'
  | 'primary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
export type EnvironmentType = 'default' | 'primary' | 'warning' | 'error'

// 表格列类型
export interface TableColumn {
  title: string
  key: string
  minWidth?: number
  width?: number
  fixed?: 'left' | 'right'
  render?: (row: Record<string, unknown>) => unknown
}

// 下拉菜单选项类型
export interface DropdownOption {
  label: string
  key: string
  icon?: () => unknown
  disabled?: boolean
  type?: 'divider'
  props?: Record<string, unknown>
}

// 表单数据类型
export interface FormData {
  [key: string]: unknown
}

// 导出配置类型
export interface ExportConfig {
  format: 'json' | 'excel' | 'pdf'
  includeData: string[]
  filename?: string
}

// 导入配置类型
export interface ImportConfig {
  mode: 'replace' | 'merge'
  includeData: string[]
  conflictResolution: 'overwrite' | 'keep' | 'prompt'
}

// 编辑器相关类型
export interface ComponentInstance {
  id: string
  type: string
  title?: string
  props: Record<string, unknown>
  children?: ComponentInstance[]
}

export interface HistoryEntry {
  id: string
  action: string
  timestamp: string
  data: Record<string, unknown>
}

export interface FormSchema {
  id: string
  name: string
  version: string
  fields: FormField[]
}

export interface FormField {
  id: string
  name: string
  type: string
  label: string
  required?: boolean
  validation?: ValidationRule[]
}

export interface ValidationRule {
  type: string
  value?: unknown
  message: string
}

export interface ValidationState {
  isValid: boolean
  errors: ValidationError[]
  warnings?: string[]
  results?: Map<string, any>
}

export interface ValidationError {
  field: string
  message: string
}

// 权限相关类型
export interface Role {
  id: string
  name: string
  description?: string
  permissions: Permission[]
}

export interface Permission {
  id: string
  resource: string
  action: string
  scope?: string
}

export interface UserRole {
  id: string
  user_id: string
  role_id: string
  role?: Role
}

// 错误类型
export interface ErrorInfo {
  code: string
  message: string
  details?: Record<string, unknown>
}

export interface ExtendedError extends Error {
  code?: string
  details?: Record<string, unknown>
}

// CRF模板类型
export interface CRFTemplate {
  id: string
  title: string
  description?: string
  template_data: {
    sections: any[]
  }
  template_type?: string
  source_type?: string
}

// 表格行键类型
export type RowKey = string | number

// 下拉选项类型
export interface DropdownOption {
  label: string
  key: string
  type?: 'divider'
}
