/**
 * 统一表单渲染器类型定义
 * 解决填报页面和预览页面数据结构差异问题
 */

// 表单渲染模式
export enum FormRenderMode {
  EDIT = 'edit', // 编辑模式（编辑器中）
  FILL = 'fill', // 填写模式（实际数据录入）
  VIEW = 'view', // 查看模式（只读已提交数据）
  PREVIEW = 'preview', // 预览模式（设计时预览）
}

// 表单配置来源
export enum FormDataSource {
  TEMPLATE = 'template', // 来自模板数据
  EDITOR = 'editor', // 来自编辑器状态
  INSTANCE = 'instance', // 来自实例数据
}

// 统一的字段配置接口
export interface UnifiedFieldConfig {
  id: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  hidden?: boolean
  validation?: ValidationRule[]
  options?: unknown[]
  defaultValue?: unknown
  customProps?: Record<string, unknown>
}

// 统一的分组配置接口
export interface UnifiedSectionConfig {
  id: string
  title: string
  description?: string
  fields: UnifiedFieldConfig[]
  collapsed?: boolean
  collapsible?: boolean
  readonly?: boolean
  hidden?: boolean
}

// 统一的表单配置接口
export interface UnifiedFormConfig {
  id: string
  title: string
  description?: string
  sections: UnifiedSectionConfig[]
  metadata?: Record<string, unknown>
}

// 验证规则接口
export interface ValidationRule {
  type: string
  value?: unknown
  message: string
  trigger?: 'blur' | 'change' | 'submit'
  validator?: (value: unknown, field: UnifiedFieldConfig) => boolean | string
}

// 表单渲染器配置
export interface FormRendererConfig {
  mode: FormRenderMode
  dataSource: FormDataSource
  enableValidation: boolean
  enableAutoSave: boolean
  autoSaveInterval?: number
  readonly?: boolean
  showProgress?: boolean
  showMedicalInfo?: boolean
  customValidators?: Record<string, (value: unknown) => boolean | string>
  onFieldChange?: (fieldId: string, value: unknown) => void
  onSectionChange?: (sectionId: string, data: Record<string, unknown>) => void
  onValidationChange?: (errors: Record<string, string>) => void
  onAutoSave?: (data: Record<string, unknown>) => void
}

// 数据适配器接口
export interface FormDataAdapter {
  // 将原始数据转换为统一格式
  toUnifiedFormat(data: unknown): UnifiedFormConfig
  // 将统一格式转换为目标格式
  fromUnifiedFormat(config: UnifiedFormConfig): unknown
  // 获取表单数据
  getFormData(): Record<string, unknown>
  // 设置表单数据
  setFormData(data: Record<string, unknown>): void
}

// 表单渲染器上下文
export interface FormRendererContext {
  config: FormRendererConfig
  formConfig: UnifiedFormConfig
  formData: Record<string, unknown>
  validationErrors: Record<string, string>
  isLoading: boolean
  isDirty: boolean
  canSubmit: boolean
  completion: number
}

// 表单渲染器事件
export interface FormRendererEvents {
  'field-change': (fieldId: string, value: unknown) => void
  'validation-change': (errors: Record<string, string>) => void
  'form-submit': (data: Record<string, unknown>) => void
  'auto-save': (data: Record<string, unknown>) => void
  'section-toggle': (sectionId: string, collapsed: boolean) => void
  'progress-change': (percentage: number) => void
}

// 组件注册接口
export interface ComponentRegistry {
  register(type: string, component: unknown): void
  get(type: string): unknown
  has(type: string): boolean
  list(): string[]
}

// 表单渲染器选项
export interface FormRendererOptions {
  componentRegistry?: ComponentRegistry
  validateOnMount?: boolean
  validateOnChange?: boolean
  debounceTime?: number
  autoSaveOptions?: {
    enabled: boolean
    interval: number
    endpoint?: string
  }
}
