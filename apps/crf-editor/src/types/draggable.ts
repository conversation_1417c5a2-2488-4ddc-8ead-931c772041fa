// vuedraggable 相关类型定义

/**
 * 拖拽上下文信息
 */
export interface DraggedContext {
  /** 被拖拽元素的索引 */
  index: number
  /** 被拖拽的元素数据 */
  element: {
    /** 是否为嵌套元素 */
    nested?: boolean
    [key: string]: unknown
  }
  /** 如果拖拽操作成功，元素的未来索引 */
  futureIndex: number
}

/**
 * 拖拽目标上下文信息
 */
export interface RelatedContext {
  /** 目标元素索引 */
  index: number
  /** 目标元素数据 */
  element: Record<string, unknown>
  /** 目标列表 */
  list: Record<string, unknown>[]
  /** 目标 Vue 组件实例 */
  component: Record<string, unknown>
}

/**
 * vuedraggable move 事件参数
 */
export interface VueDraggableMoveEvent {
  /** 拖拽目标 DOM 元素 */
  to: HTMLElement
  /** 拖拽源 DOM 元素 */
  from: HTMLElement
  /** 被拖拽的 DOM 元素 */
  dragged: HTMLElement
  /** 相关的 DOM 元素 */
  related: HTMLElement
  /** 是否为克隆操作 */
  willInsertAfter: boolean
  /** 原生 DOM 事件 */
  originalEvent: Event

  /** 被拖拽元素的上下文信息 */
  draggedContext: DraggedContext
  /** 目标元素的上下文信息 */
  relatedContext: RelatedContext
}

/**
 * vuedraggable move 回调函数类型
 */
export type VueDraggableMoveCallback = (evt: VueDraggableMoveEvent) => boolean
