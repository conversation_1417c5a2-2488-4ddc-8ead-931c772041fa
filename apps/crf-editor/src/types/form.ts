// 表单相关类型定义

export interface FormTemplate {
  id: string
  project_id: string
  name: string
  title: string
  description?: string
  icon?: string
  iconColor?: string
  keyword?: string
  version: string
  status: 'draft' | 'published'
  template_type: 'custom_form' | 'system_template'
  source_type: 'user_created' | 'system_default'
  template_data: TemplateData
  permissions: Record<string, unknown>
  created_at: string
  updated_at: string
  submitted_at?: string
}

export interface TemplateData {
  page_config: Record<string, unknown>
  form_structure: FormComponent[]
  component_configs: Record<string, unknown>
  validation_rules: Record<string, unknown>
  style_config: Record<string, unknown>
}

export interface FormComponent {
  id: string
  type: string
  label: string
  required?: boolean
  props?: Record<string, unknown>
  validation?: ValidationRule[]
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: unknown
  message: string
}

export interface FormInstance {
  id: string
  template_id: string
  status: 'draft' | 'submitted' | 'completed'
  data: Record<string, unknown>
  created_at: string
  updated_at: string
  submitted_at?: string
}

export interface CreateFormData {
  project_id: string
  name: string
  title: string
  description?: string
  icon: string
  iconColor: string
  keyword?: string
  version: string
  template_type: 'custom_form'
  source_type: 'user_created'
  template_data: TemplateData
  permissions: Record<string, unknown>
}

export interface FormListState {
  forms: FormTemplate[]
  loading: boolean
  pagination: {
    current: number
    size: number
    total: number
  }
  searchQuery: string
}

export interface FormOperationState {
  duplicatingFormIds: Set<string>
  deletingFormIds: Set<string>
  selectedFormIds: Set<string>
  batchDeleting: boolean
  creating: boolean
  tabSwitching: boolean
  operationLoading: boolean
}

export type FormMode = 'edit' | 'fill' | 'view' | 'preview'

export type FormStatus = 'draft' | 'published'

export type TabType = 'published' | 'unpublished' | 'all'

export interface FormActionCommand {
  command: 'fill' | 'duplicate' | 'delete'
  form: FormTemplate
}

export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  code?: number
}

export interface PaginationParams {
  limit: number
  offset: number
}

export interface FormListResponse {
  templates: FormTemplate[]
  pagination: {
    total: number
    limit: number
    offset: number
  }
}
