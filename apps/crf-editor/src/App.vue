<script lang="ts" setup>
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  NLoadingBarProvider,
  zhCN,
  dateZhCN,
} from 'naive-ui'
import { createCustomThemeOverrides } from '@/composables/useNaive'
import { initNetworkManager } from '@/api'
import { useUserStore } from '@/stores/user-store'
import { onMounted } from 'vue'

// 创建自定义主题覆盖配置
const themeOverrides = createCustomThemeOverrides()

// 初始化网络管理器
initNetworkManager()

// 在组件挂载后初始化用户认证状态
onMounted(async () => {
  const userStore = useUserStore()
  await userStore.initAuth()
  console.log('用户认证状态初始化完成')
})

// 使用路由视图
</script>

<template>
  <n-config-provider
    :locale="zhCN"
    :date-locale="dateZhCN"
    :theme-overrides="themeOverrides"
  >
    <n-loading-bar-provider>
      <n-message-provider>
        <n-dialog-provider>
          <n-notification-provider>
            <router-view />
          </n-notification-provider>
        </n-dialog-provider>
      </n-message-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<style lang="scss" scoped></style>
