// 拖拽动画样式

// 拖拽鬼影样式
.drag-ghost {
  opacity: 0.5 !important;
  background: var(--primary-color) !important;
  border: 2px dashed var(--primary-hover) !important;
  border-radius: 8px !important;
  transform: rotate(3deg) !important;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.25) !important;

  * {
    opacity: 0.7;
  }
}

// 选中状态样式
.drag-chosen {
  transform: rotate(1deg) scale(1.02) !important;
  z-index: 1000 !important;
  box-shadow: 0 12px 32px rgba(37, 99, 235, 0.2) !important;
  border: 2px solid var(--primary-color) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

// 拖拽中样式
.drag-moving {
  background: white !important;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2) !important;
  border-radius: 12px !important;
  transform: rotate(5deg) scale(1.05) !important;
  z-index: 9999 !important;
  transition: none !important;
}

// 放置区域高亮
.drop-zone {
  position: relative;

  &.drag-over {
    background: rgba(37, 99, 235, 0.05) !important;
    border: 2px dashed var(--primary-color) !important;
    border-radius: 8px !important;

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, var(--primary-color), var(--primary-hover));
      border-radius: 10px;
      z-index: -1;
      animation: borderGlow 1.5s ease-in-out infinite alternate;
    }

    &::after {
      content: '松手放置组件';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--primary-color);
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
      animation: dropHint 0.6s ease-out;
      z-index: 1000;
    }
  }
}

// 全局拖拽状态样式
body.dragging {
  cursor: grabbing !important;

  // 显示所有可放置区域
  .section-drop-zone {
    background: rgba(59, 130, 246, 0.02) !important;
    border: 2px dashed rgba(59, 130, 246, 0.3) !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
      border-radius: 10px;
      z-index: -1;
      animation: dropZoneGlow 2s ease-in-out infinite alternate;
    }

    &::after {
      content: '可放置区域';
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 500;
      animation: dropZoneLabel 0.3s ease-out;
      z-index: 1000;
    }
  }

  // 隐藏空状态提示
  .empty-section-hint {
    opacity: 0.3 !important;
    transform: scale(0.95) !important;
  }

  // 高亮章节标题
  .section-title {
    color: #3b82f6 !important;
    font-weight: 600 !important;
  }

  // 非拖拽元素变暗
  *:not(.drag-chosen):not(.drag-moving):not(.section-drop-zone):not(.section-title) {
    opacity: 0.8;
  }
}

// 新增拖拽引导指示器
.drag-guide {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10000;
  animation: guideSlideIn 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  .guide-icon {
    margin-right: 8px;
    display: inline-block;
    animation: guideIcon 1s ease-in-out infinite;
  }
}

// 边框发光动画
@keyframes borderGlow {
  0% {
    opacity: 0.3;
    filter: blur(0px);
  }

  100% {
    opacity: 0.6;
    filter: blur(1px);
  }
}

// 放置区域发光动画
@keyframes dropZoneGlow {
  0% {
    opacity: 0.2;
  }

  100% {
    opacity: 0.4;
  }
}

// 放置提示动画
@keyframes dropHint {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

// 放置区域标签动画
@keyframes dropZoneLabel {
  0% {
    opacity: 0;
    transform: translateY(-4px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 引导滑入动画
@keyframes guideSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 引导图标动画
@keyframes guideIcon {

  0%,
  100% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(5deg);
  }
}

// 组件拖拽预览样式
.component-drag-preview {
  position: fixed;
  pointer-events: none;
  z-index: 10000;
  background: white;
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
  transform: rotate(3deg);
  animation: dragFloat 0.3s ease-out;

  .component-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    color: var(--primary-color);
  }

  .component-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
  }
}

// 拖拽浮动动画
@keyframes dragFloat {
  0% {
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }

  100% {
    opacity: 1;
    transform: rotate(3deg) scale(1);
  }
}

// 拖拽进入效果
.drag-enter {
  animation: dragEnter 0.3s ease-out;
}

@keyframes dragEnter {
  0% {
    transform: scale(0.95);
    background: rgba(37, 99, 235, 0.1);
  }

  50% {
    transform: scale(1.02);
    background: rgba(37, 99, 235, 0.15);
  }

  100% {
    transform: scale(1);
    background: rgba(37, 99, 235, 0.05);
  }
}

// 拖拽离开效果
.drag-leave {
  animation: dragLeave 0.2s ease-in;
}

@keyframes dragLeave {
  0% {
    background: rgba(37, 99, 235, 0.05);
  }

  100% {
    background: transparent;
  }
}

// 插入位置指示器
.insert-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
  border-radius: 2px;
  z-index: 1000;
  animation: insertPulse 1s ease-in-out infinite alternate;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    top: -4px;
  }

  &::before {
    left: -6px;
  }

  &::after {
    right: -6px;
  }
}

@keyframes insertPulse {
  0% {
    opacity: 0.6;
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
  }

  100% {
    opacity: 1;
    box-shadow: 0 0 0 8px rgba(37, 99, 235, 0);
  }
}

// 拖拽触发区域样式
.drag-handle {
  cursor: grab;
  color: var(--edit-disabled-text-color);
  transition: all 0.2s ease;

  &:hover {
    color: var(--primary-color);
    transform: scale(1.1);
  }

  &:active {
    cursor: grabbing;
    transform: scale(0.95);
  }
}

// 禁用拖拽状态
.drag-disabled {
  cursor: not-allowed !important;
  opacity: 0.5;

  .drag-handle {
    cursor: not-allowed !important;
    color: var(--edit-disabled-text-color) !important;

    &:hover {
      transform: none !important;
    }
  }
}

// 拖拽层遮罩
.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
  animation: overlayFadeIn 0.2s ease-out;
}

@keyframes overlayFadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .drag-chosen {
    transform: scale(1.05) !important;
  }

  .drag-moving {
    transform: scale(1.1) !important;
  }

  .component-drag-preview {
    transform: none;
    padding: 8px;
  }

  .drop-zone.drag-over::after {
    font-size: 12px;
    padding: 6px 12px;
  }

  .drag-guide {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    font-size: 12px;
  }
}

// 减少动画（用户偏好设置）
@media (prefers-reduced-motion: reduce) {

  .drag-chosen,
  .drag-moving,
  .drag-ghost,
  .component-drag-preview,
  .insert-indicator {
    animation: none !important;
    transition: none !important;
  }
}

// 高对比度模式适配
@media (prefers-contrast: high) {
  .drag-ghost {
    border-width: 3px !important;
    background: #000 !important;
  }

  .drag-chosen {
    border-width: 3px !important;
    border-color: #000 !important;
  }

  .drop-zone.drag-over {
    border-width: 3px !important;
    border-color: #000 !important;
  }

  .insert-indicator {
    background: #000 !important;
    height: 6px;

    &::before,
    &::after {
      background: #000 !important;
      width: 16px;
      height: 16px;
    }
  }
}

@keyframes ghostEffect {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }

  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.ghost {
  animation: ghostEffect 1.5s infinite;
  border: 1px dashed #94a3b8;
  background-color: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
}