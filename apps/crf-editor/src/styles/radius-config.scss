// 全局圆角配置
// 统一管理按钮和卡片的圆角设计，与登录页面保持一致

// 基础圆角配置
// 与登录页面保持一致，使用较小的圆角弧度
:root {
    // 小圆角 - 用于按钮、卡片等元素（与登录页面统一）
    --radius-small: 6px;

    // 中等圆角 - 用于较大的表单元素
    --radius-medium: 8px;

    // 大圆角 - 用于容器和面板
    --radius-large: 10px;

    // 超大圆角 - 用于特殊装饰
    --radius-xl: 12px;

    // 圆形 - 用于头像等
    --radius-full: 50%;
}

// SCSS变量形式（兼容现有代码）
$radius-small: var(--radius-small);
$radius-medium: var(--radius-medium);
$radius-large: var(--radius-large);
$radius-xl: var(--radius-xl);
$radius-full: var(--radius-full);

// 混合器（Mixin）便于快速应用
@mixin rounded-small {
    border-radius: var(--radius-small);
}

@mixin rounded-medium {
    border-radius: var(--radius-medium);
}

@mixin rounded-large {
    border-radius: var(--radius-large);
}

@mixin rounded-xl {
    border-radius: var(--radius-xl);
}

@mixin rounded-full {
    border-radius: var(--radius-full);
}

// 按钮圆角样式（与登录页面保持一致）
.btn-radius {
    @include rounded-small;
}

// 卡片圆角样式
.card-radius {
    @include rounded-medium;
}

// 容器圆角样式
.container-radius {
    @include rounded-large;
}

// 面板圆角样式
.panel-radius {
    @include rounded-xl;
}

// 工具类
.rounded-sm {
    @include rounded-small;
}

.rounded-md {
    @include rounded-medium;
}

.rounded-lg {
    @include rounded-large;
}

.rounded-xl {
    @include rounded-xl;
}

.rounded-full {
    @include rounded-full;
}