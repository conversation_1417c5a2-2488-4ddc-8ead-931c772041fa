<template>
  <div class="unified-form-renderer">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <n-skeleton text :repeat="3">
        <template #template>
          <div class="skeleton-section" v-for="i in 3" :key="i">
            <n-skeleton-item
              type="text"
              style="width: 200px; margin-bottom: 16px; height: 24px"
            />
            <n-skeleton-item
              type="text"
              style="width: 100%; margin-bottom: 8px"
            />
            <n-skeleton-item
              type="text"
              style="width: 80%; margin-bottom: 16px"
            />
          </div>
        </template>
      </n-skeleton>
    </div>

    <!-- 表单内容 -->
    <div v-else-if="unifiedConfig" class="form-content">
      <!-- 表单头部信息 -->
      <div v-if="showHeader" class="form-header">
        <h2 class="form-title">{{ unifiedConfig.title }}</h2>
        <p v-if="unifiedConfig.description" class="form-description">
          {{ unifiedConfig.description }}
        </p>

        <!-- 医疗信息栏 -->
        <div v-if="config.showMedicalInfo" class="medical-info-bar">
          <div class="medical-meta">
            <div class="meta-item">
              <span class="label">患者ID:</span>
              <span class="value">{{
                unifiedConfig.metadata?.patientId || 'N/A'
              }}</span>
            </div>
            <div class="meta-item">
              <span class="label">表单状态:</span>
              <span class="value">{{
                getStatusLabel(unifiedConfig.metadata?.status)
              }}</span>
            </div>
            <div v-if="config.showProgress" class="meta-item">
              <span class="label">完成度:</span>
              <span class="value">{{ Math.round(completion) }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单章节 -->
      <div class="form-sections">
        <div
          v-for="(section, sectionIndex) in unifiedConfig.sections"
          :key="section.id || sectionIndex"
          class="form-section"
          :class="{
            'has-error': hasSectionError(section),
            collapsed: section.collapsed,
            readonly: section.readonly || config.readonly,
          }"
          v-show="!section.hidden"
        >
          <!-- 章节头部 -->
          <div
            v-if="section.title"
            class="section-header"
            @click="toggleSection(section.id)"
          >
            <h3 class="section-title">{{ section.title }}</h3>
            <p v-if="section.description" class="section-description">
              {{ section.description }}
            </p>
            <n-icon v-if="section.collapsible" class="collapse-icon">
              <arrow-down v-if="!section.collapsed" />
              <arrow-right v-else />
            </n-icon>
          </div>

          <!-- 章节内容 -->
          <div
            v-show="!section.collapsed"
            class="section-content"
            :class="{ readonly: section.readonly || config.readonly }"
          >
            <div
              v-for="(field, fieldIndex) in section.fields"
              :key="field.id || fieldIndex"
              class="form-field"
              :class="{
                'has-error': hasFieldError(field.id),
                required: field.required,
                readonly: field.readonly || section.readonly || config.readonly,
              }"
              v-show="!field.hidden"
            >
              <!-- 动态组件渲染 -->
              <component
                :is="getFormComponent(field.type)"
                v-bind="getFormProps(field)"
                :model-value="formData[field.id]"
                :readonly="
                  field.readonly || section.readonly || config.readonly
                "
                :disabled="
                  field.readonly || section.readonly || config.readonly
                "
                :error="validationErrors[field.id]"
                @update:model-value="handleFieldChange(field.id, $event)"
                @blur="validateField(field.id)"
                @focus="handleFieldFocus(field.id)"
              />

              <!-- 字段错误信息 -->
              <div v-if="hasFieldError(field.id)" class="field-error">
                <div class="error-message">
                  {{ validationErrors[field.id] }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单底部 -->
      <div v-if="showFooter" class="form-footer">
        <div class="footer-info">
          <span v-if="config.showProgress" class="completion-info">
            完成度: {{ Math.round(completion) }}%
          </span>
          <span v-if="isDirty" class="dirty-indicator"> 有未保存的更改 </span>
          <span v-if="lastAutoSave" class="autosave-info">
            自动保存于: {{ formatTime(lastAutoSave) }}
          </span>
        </div>

        <div class="footer-actions">
          <slot name="actions" :context="rendererContext">
            <n-button v-if="config.mode === 'fill'" @click="saveDraft">
              保存草稿
            </n-button>
            <n-button
              v-if="config.mode === 'fill'"
              type="primary"
              :disabled="!canSubmit"
              @click="submitForm"
            >
              提交表单
            </n-button>
          </slot>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <n-empty description="暂无表单内容" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import {
  ChevronDownOutline as ArrowDown,
  ChevronForwardOutline as ArrowRight,
} from '@vicons/ionicons5'
import {
  FormRenderMode,
  FormDataSource,
  type FormRendererConfig,
  type FormRendererContext,
  type FormRendererEvents,
  type UnifiedFormConfig,
} from '@/types/form-renderer'
import { AdapterFactory, DataConverter } from '@/utils/form-data-adapter'
import { useFormValidator } from '@/composables/useFormValidator'
import { useAutoSave } from '@/composables/useAutoSave'
import { useComponentRegistry } from '@/composables/useComponentRegistry'

interface Props {
  // 原始数据
  data: unknown
  // 渲染器配置
  config: FormRendererConfig
  // 是否显示头部
  showHeader?: boolean
  // 是否显示底部
  showFooter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  showFooter: true,
})

const message = useMessage()

const emit = defineEmits<{
  'field-change': [fieldId: string, value: unknown]
  'validation-change': [errors: Record<string, string>]
  'form-submit': [data: Record<string, unknown>]
  'auto-save': [data: Record<string, unknown>]
  'section-toggle': [sectionId: string, collapsed: boolean]
  'progress-change': [percentage: number]
}>()

// 响应式数据
const isLoading = ref(false)
const unifiedConfig = ref<UnifiedFormConfig | null>(null)
const formData = reactive<Record<string, unknown>>({})
const validationErrors = reactive<Record<string, string>>({})
const isDirty = ref(false)
const lastAutoSave = ref<Date | null>(null)

// 组件注册表
const componentRegistry = useComponentRegistry()

// 表单验证器
const validator = useFormValidator(unifiedConfig, formData, validationErrors)

// 自动保存 - 先创建简单的自动保存对象
const autoSaveObj = {
  trigger: () => {
    if (props.config.enableAutoSave && props.config.onAutoSave) {
      props.config.onAutoSave(formData)
    }
  },
  setup: () => {
    // 简单的自动保存设置
  },
}

// 计算属性
const completion = computed(() => {
  if (!unifiedConfig.value) return 0

  const totalFields = unifiedConfig.value.sections.reduce((total, section) => {
    return total + section.fields.length
  }, 0)

  if (totalFields === 0) return 0

  const filledFields = Object.keys(formData).filter((key) => {
    const value = formData[key]
    return value !== null && value !== undefined && value !== ''
  }).length

  return (filledFields / totalFields) * 100
})

const canSubmit = computed(() => {
  return Object.keys(validationErrors).length === 0 && completion.value >= 80
})

const rendererContext = computed<FormRendererContext>(() => ({
  config: props.config,
  formConfig: unifiedConfig.value!,
  formData,
  validationErrors,
  isLoading: isLoading.value,
  isDirty: isDirty.value,
  canSubmit: canSubmit.value,
  completion: completion.value,
}))

// 初始化
onMounted(async () => {
  await initializeForm()
  setupAutoSave()
})

// 初始化表单
const initializeForm = async () => {
  try {
    isLoading.value = true

    // 创建数据适配器
    const adapter = AdapterFactory.createAdapter(
      props.config.dataSource,
      props.data,
    )

    // 转换为统一格式
    unifiedConfig.value = adapter.toUnifiedFormat(props.data)

    // 初始化表单数据
    const initialData = adapter.getFormData()
    Object.assign(formData, initialData)

    // 初始化验证
    if (props.config.enableValidation) {
      await nextTick()
      validateAllFields()
    }
  } catch (error) {
    console.error('初始化表单失败:', error)
    message.error('表单初始化失败')
  } finally {
    isLoading.value = false
  }
}

// 设置自动保存
const setupAutoSave = () => {
  if (props.config.enableAutoSave) {
    autoSaveObj.setup()
  }
}

// 获取表单组件
const getFormComponent = (fieldType: string) => {
  return componentRegistry.getComponent(fieldType)
}

// 获取表单属性
const getFormProps = (field: UnifiedFieldConfig) => {
  return {
    ...field.customProps,
    id: field.id,
    label: field.label,
    placeholder: field.placeholder,
    required: field.required,
    options: field.options,
    validation: field.validation,
  }
}

// 处理字段变更
const handleFieldChange = (fieldId: string, value: unknown) => {
  formData[fieldId] = value
  isDirty.value = true

  // 验证字段
  if (props.config.enableValidation) {
    validateField(fieldId)
  }

  // 触发事件
  emit('field-change', fieldId, value)

  // 自动保存
  if (props.config.enableAutoSave) {
    autoSaveObj.trigger()
  }
}

// 处理字段焦点
const handleFieldFocus = (fieldId: string) => {
  // 清除该字段的错误
  if (validationErrors[fieldId]) {
    delete validationErrors[fieldId]
  }
}

// 验证字段
const validateField = (fieldId: string) => {
  if (!props.config.enableValidation) return

  const field = findFieldById(fieldId)
  if (!field) return

  const value = formData[fieldId]
  const error = validator.validateField(field, value)

  if (error) {
    validationErrors[fieldId] = error
  } else {
    delete validationErrors[fieldId]
  }

  emit('validation-change', { ...validationErrors })
}

// 验证所有字段
const validateAllFields = () => {
  if (!unifiedConfig.value) return

  for (const section of unifiedConfig.value.sections) {
    for (const field of section.fields) {
      validateField(field.id)
    }
  }
}

// 查找字段
const findFieldById = (fieldId: string) => {
  if (!unifiedConfig.value) return null

  for (const section of unifiedConfig.value.sections) {
    const field = section.fields.find((f) => f.id === fieldId)
    if (field) return field
  }
  return null
}

// 检查字段是否有错误
const hasFieldError = (fieldId: string) => {
  return !!validationErrors[fieldId]
}

// 检查章节是否有错误
const hasSectionError = (section: UnifiedSectionConfig) => {
  return section.fields.some((field: UnifiedFieldConfig) =>
    hasFieldError(field.id),
  )
}

// 切换章节展开状态
const toggleSection = (sectionId: string) => {
  if (!unifiedConfig.value) return

  const section = unifiedConfig.value.sections.find((s) => s.id === sectionId)
  if (section && section.collapsible) {
    section.collapsed = !section.collapsed
    emit('section-toggle', sectionId, section.collapsed)
  }
}

// 处理自动保存
const handleAutoSave = async (data: Record<string, unknown>) => {
  try {
    if (props.config.onAutoSave) {
      await props.config.onAutoSave(data)
    }
    lastAutoSave.value = new Date()
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

// 保存草稿
const saveDraft = async () => {
  try {
    await handleAutoSave(formData)
    message.success('草稿保存成功')
  } catch (error) {
    message.error('草稿保存失败')
  }
}

// 提交表单
const submitForm = async () => {
  try {
    // 验证所有字段
    validateAllFields()

    if (Object.keys(validationErrors).length > 0) {
      message.error('请修正表单中的错误')
      return
    }

    emit('form-submit', formData)
  } catch (error) {
    console.error('表单提交失败:', error)
    message.error('表单提交失败')
  }
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    reviewed: '已审核',
    approved: '已通过',
    rejected: '已拒绝',
  }
  return statusMap[status] || status
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      initializeForm()
    }
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
.unified-form-renderer {
  .loading-container {
    .skeleton-section {
      margin-bottom: 32px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
    }
  }

  .form-content {
    .form-header {
      margin-bottom: 32px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .form-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
      }

      .form-description {
        margin: 0 0 16px 0;
        color: #6b7280;
        line-height: 1.6;
      }

      .medical-info-bar {
        .medical-meta {
          display: flex;
          gap: 24px;
          flex-wrap: wrap;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;

            .label {
              font-weight: 500;
              color: #374151;
            }

            .value {
              color: #6b7280;
            }
          }
        }
      }
    }

    .form-sections {
      .form-section {
        margin-bottom: 32px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &.has-error {
          border-color: #f87171;

          .section-header {
            background: #fef2f2;
            border-bottom-color: #f87171;
          }
        }

        &.readonly {
          background: #f9fafb;
          border-color: #d1d5db;
        }

        .section-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 20px;
          background: #ffffff;
          border-bottom: 1px solid #e5e7eb;
          border-radius: 8px 8px 0 0;
          cursor: pointer;

          .section-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
          }

          .section-description {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #6b7280;
          }

          .collapse-icon {
            transition: transform 0.2s ease;
          }
        }

        .section-content {
          padding: 20px;

          &.readonly {
            background: #f9fafb;
          }

          .form-field {
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            &.has-error {
              :deep(.n-input__wrapper) {
                border-color: #f87171;
              }
            }

            &.required {
              :deep(.n-form-item__label) {
                &::before {
                  content: '*';
                  color: #f87171;
                  margin-right: 4px;
                }
              }
            }

            &.readonly {
              :deep(.n-input__wrapper) {
                background: #f9fafb;
              }
            }

            .field-error {
              margin-top: 4px;

              .error-message {
                color: #f87171;
                font-size: 12px;
                line-height: 1.4;
              }
            }
          }
        }
      }
    }

    .form-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 32px;
      padding: 16px 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .footer-info {
        display: flex;
        gap: 16px;
        font-size: 14px;
        color: #6b7280;

        .completion-info {
          color: #059669;
          font-weight: 500;
        }

        .dirty-indicator {
          color: #d97706;
        }

        .autosave-info {
          color: #6b7280;
        }
      }

      .footer-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
}
</style>
