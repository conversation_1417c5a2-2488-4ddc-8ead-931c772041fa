<template>
  <Teleport to="body">
    <div v-if="showOverlay" class="editor-overlay" :class="overlayClass">
      <div class="overlay-content">
        <div class="overlay-icon">
          <crf-icon :icon="overlayIcon" :color="overlayIconColor" size="48" />
        </div>
        <div class="overlay-message">
          {{ overlayMessage }}
        </div>
        <div class="overlay-actions" v-if="showActions">
          <n-button v-if="canExit" type="primary" @click="handleExit">
            退出{{ modeText }}
          </n-button>
          <n-button v-if="canContinue" @click="handleContinue"> 继续 </n-button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { CrfIcon } from '@crf/ui-components'
import { useEditorStore } from '@/stores/editor-store'
import { OverlayType } from '@/types/editor'
import { EditorMode } from '@crf/type-definitions/core'

// Props
interface Props {
  showOverlay: boolean
  overlayMessage: string
  overlayType?: OverlayType
  canExit?: boolean
  canContinue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  overlayType: OverlayType.WARNING,
  canExit: true,
  canContinue: false,
})

// Emits
const emit = defineEmits<{
  exit: []
  continue: []
}>()

// Store
const editorStore = useEditorStore()

// 计算属性
const overlayClass = computed(() => {
  return {
    'overlay-preview': editorStore.mode === 'preview',
    'overlay-publish': editorStore.mode === 'publish',
    'overlay-loading': props.overlayType === OverlayType.LOADING,
    'overlay-error': props.overlayType === OverlayType.ERROR,
  }
})

const overlayIcon = computed(() => {
  switch (props.overlayType) {
    case OverlayType.WARNING:
      return 'material-symbols:preview'
    case OverlayType.SUCCESS:
      return 'material-symbols:publish'
    case OverlayType.LOADING:
      return 'material-symbols:sync'
    case OverlayType.ERROR:
      return 'material-symbols:error'
    default:
      return 'material-symbols:info'
  }
})

const overlayIconColor = computed(() => {
  switch (props.overlayType) {
    case OverlayType.WARNING:
      return '#1890ff'
    case OverlayType.SUCCESS:
      return '#52c41a'
    case OverlayType.LOADING:
      return '#faad14'
    case OverlayType.ERROR:
      return '#ff4d4f'
    default:
      return '#666666'
  }
})

const modeText = computed(() => {
  if (editorStore.mode === 'preview') return '预览'
  if (editorStore.mode === 'publish') return '发布'
  return ''
})

const showActions = computed(() => {
  return props.canExit || props.canContinue
})

// 方法
const handleExit = () => {
  emit('exit')
  editorStore.setMode(EditorMode.EDIT)
}

const handleContinue = () => {
  emit('continue')
}
</script>

<style lang="scss" scoped>
.editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;

  &.overlay-preview {
    background-color: rgba(24, 144, 255, 0.1);
  }

  &.overlay-publish {
    background-color: rgba(82, 196, 26, 0.1);
  }

  &.overlay-loading {
    background-color: rgba(250, 173, 20, 0.1);
  }

  &.overlay-error {
    background-color: rgba(255, 77, 79, 0.1);
  }
}

.overlay-content {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 90%;
  animation: slideUp 0.3s ease-out;
}

.overlay-icon {
  margin-bottom: 20px;

  :deep(.crf-icon) {
    animation: pulse 2s infinite;
  }
}

.overlay-message {
  font-size: 16px;
  color: #333;
  margin-bottom: 30px;
  line-height: 1.5;
}

.overlay-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overlay-content {
    padding: 30px 20px;
    margin: 20px;
  }

  .overlay-message {
    font-size: 14px;
  }

  .overlay-actions {
    flex-direction: column;
  }
}
</style>
