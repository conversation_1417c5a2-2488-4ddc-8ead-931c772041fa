<template>
  <div class="login-left">
    <!-- 背景装饰元素 -->
    <div class="background-decorations">
      <div class="decoration-circle decoration-1"></div>
      <div class="decoration-circle decoration-2"></div>
      <div class="decoration-circle decoration-3"></div>
      <div class="floating-elements">
        <div
          class="floating-element"
          v-for="i in 6"
          :key="i"
          :class="`element-${i}`"
        ></div>
      </div>
    </div>

    <!-- 品牌标识 -->
    <div class="brand-container">
      <div class="brand-logo">
        <BrandLogo size="64" :show-ring="true" />
      </div>

      <!-- 品牌信息 -->
      <div class="brand-info">
        <h1 class="brand-title">
          <span class="title-main">临床研究表单系统</span>
          <span class="title-sub">Clinical Research Forms</span>
        </h1>
        <p class="brand-description">专业的临床研究数据采集与管理平台</p>
        <div class="brand-features">
          <div
            class="feature-item"
            v-for="feature in features"
            :key="feature.text"
          >
            <div class="feature-icon">
              <StarIcon
                size="20"
                :variant="feature.variant"
                color="rgba(255, 255, 255, 0.9)"
              />
            </div>
            <span>{{ feature.text }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部标签 -->
    <div class="medical-tags">
      <div class="tag-item" v-for="tag in tags" :key="tag">{{ tag }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BrandLogo, StarIcon } from '@/assets/icons'

interface Feature {
  variant: 'star1' | 'star2' | 'star3'
  text: string
}

const features: Feature[] = [
  {
    variant: 'star1',
    text: '专业可靠',
  },
  {
    variant: 'star2',
    text: '安全合规',
  },
  {
    variant: 'star3',
    text: '高效便捷',
  },
]

const tags: string[] = ['临床研究', '数据采集', 'GCP规范']
</script>

<style lang="scss" scoped>
@use 'sass:math';

// 左侧品牌区域
.login-left {
  flex: 1.1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 40px;
  color: white;
  min-width: 500px;
  overflow: hidden;

  // 背景装饰元素
  .background-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 1;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);

      &.decoration-1 {
        width: 200px;
        height: 200px;
        top: -50px;
        right: -50px;
        animation: float 6s ease-in-out infinite;
      }

      &.decoration-2 {
        width: 150px;
        height: 150px;
        bottom: 100px;
        left: -30px;
        animation: float 8s ease-in-out infinite reverse;
      }

      &.decoration-3 {
        width: 100px;
        height: 100px;
        top: 30%;
        left: 10%;
        animation: float 5s ease-in-out infinite;
      }
    }

    .floating-elements {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .floating-element {
        position: absolute;
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;

        @for $i from 1 through 6 {
          &.element-#{$i} {
            top: math.random(100) * 1%;
            left: math.random(100) * 1%;
            animation: twinkle #{2 + math.random(3)}s ease-in-out infinite;
            animation-delay: #{math.random(2)}s;
          }
        }
      }
    }
  }

  // 品牌容器
  .brand-container {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 400px;
  }

  // 品牌Logo
  .brand-logo {
    position: relative;
    margin-bottom: 40px;
    display: flex;
    justify-content: center;
    align-items: center;

    .main-logo {
      position: relative;
      z-index: 2;
      filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
    }

    .logo-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      animation: rotate 30s linear infinite;
    }
  }

  // 品牌信息
  .brand-info {
    .brand-title {
      margin-bottom: 20px;

      .title-main {
        display: block;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -0.025em;
      }

      .title-sub {
        display: block;
        font-size: 1.1rem;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.8);
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
    }

    .brand-description {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 40px;
      line-height: 1.6;
    }

    .brand-features {
      display: flex;
      gap: 30px;
      justify-content: center;

      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        color: rgba(255, 255, 255, 0.9);

        .feature-icon {
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);

          svg {
            color: rgba(255, 255, 255, 0.9);
          }
        }

        span {
          font-size: 0.9rem;
          font-weight: 500;
        }
      }
    }
  }

  // 医疗标签
  .medical-tags {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 16px;
    z-index: 2;

    .tag-item {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }
  }
}

// 动画
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-left {
    min-width: 0;
    min-height: 300px;
    padding: 40px 20px;

    .brand-info .title-main {
      font-size: 2rem;
    }

    .brand-features {
      gap: 16px;
    }

    .medical-tags {
      position: relative;
      bottom: auto;
      margin-top: 20px;
      transform: none;

      .tag-item {
        font-size: 0.8rem;
        padding: 6px 12px;
      }
    }
  }
}

@media (max-width: 640px) {
  .login-left {
    padding: 30px 15px;

    .brand-info .title-main {
      font-size: 1.8rem;
    }

    .brand-features {
      flex-direction: column;
      gap: 16px;
    }

    .medical-tags {
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
