<template>
  <div class="login-form-container">
    <!-- 登录表单卡片 -->
    <div class="login-card">
      <!-- 表单头部 -->
      <div class="form-header">
        <h2 class="form-title">
          {{ formMode === 'login' ? '欢迎登录' : '注册账号' }}
        </h2>
      </div>

      <!-- 登录方式切换 -->
      <div class="login-tabs">
        <button
          :class="['tab-button', { active: loginType === 'password' }]"
          @click="loginType = 'password'"
        >
          <TabIcon type="lock" size="16" />
          密码登录
        </button>
        <button
          :class="['tab-button', { active: loginType === 'sms' }]"
          @click="loginType = 'sms'"
        >
          <TabIcon type="chat" size="16" />
          短信验证码
        </button>
      </div>

      <!-- 登录表单 -->
      <n-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <!-- 用户名/手机号输入 (密码登录) -->
        <n-form-item
          v-if="loginType === 'password'"
          path="username"
          :show-label="false"
        >
          <n-input
            v-model:value="loginForm.username"
            size="large"
            :placeholder="
              formMode === 'login' ? '请输入用户名/手机号' : '请输入手机号'
            "
            clearable
            class="form-input"
          >
            <template #prefix>
              <UserIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 手机号输入 (短信登录) -->
        <n-form-item
          v-if="loginType === 'sms'"
          path="phone"
          :show-label="false"
        >
          <n-input
            v-model:value="loginForm.phone"
            size="large"
            placeholder="请输入手机号"
            clearable
            class="form-input"
          >
            <template #prefix>
              <PhoneIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 密码输入 -->
        <n-form-item
          v-if="loginType === 'password'"
          path="password"
          :show-label="false"
        >
          <n-input
            v-model:value="loginForm.password"
            type="password"
            size="large"
            placeholder="请输入密码"
            show-password-on="click"
            clearable
            class="form-input"
            @keyup.enter="handleSubmit"
          >
            <template #prefix>
              <LockIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 确认密码输入 (注册模式) -->
        <n-form-item
          v-if="formMode === 'register' && loginType === 'password'"
          path="confirmPassword"
          :show-label="false"
        >
          <n-input
            v-model:value="loginForm.confirmPassword"
            type="password"
            size="large"
            placeholder="请确认密码"
            show-password-on="click"
            clearable
            class="form-input"
            @keyup.enter="handleSubmit"
          >
            <template #prefix>
              <LockIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 短信验证码输入 -->
        <n-form-item
          v-if="loginType === 'sms'"
          path="smsCode"
          :show-label="false"
        >
          <n-input
            v-model:value="loginForm.smsCode"
            size="large"
            placeholder="请输入6位验证码"
            clearable
            class="form-input sms-input"
          >
            <template #prefix>
              <MailIcon size="18" color="#303133" />
            </template>
            <template #suffix>
              <n-button
                type="primary"
                size="small"
                text
                @click="sendSmsCode"
                class="sms-button"
                :disabled="smsCountdown > 0"
              >
                {{ smsCodeText }}
              </n-button>
            </template>
          </n-input>
        </n-form-item>

        <!-- 登录选项 -->
        <div class="form-options">
          <n-checkbox v-model:checked="rememberMe" class="remember-checkbox">
            记住登录状态
          </n-checkbox>
          <n-a @click="handleForgotPassword" class="forgot-link">
            忘记密码？
          </n-a>
        </div>

        <!-- 登录/注册按钮 -->
        <n-button
          type="primary"
          size="large"
          :loading="loading"
          class="login-button"
          @click="handleSubmit"
          block
        >
          <template v-if="!loading">
            <n-icon :size="20" style="margin-right: 6px">
              <ArrowIcon direction="right" />
            </n-icon>
            <span>{{ formMode === 'login' ? '立即登录' : '立即注册' }}</span>
          </template>
          <template v-else>
            <span>{{ formMode === 'login' ? '登录中...' : '注册中...' }}</span>
          </template>
        </n-button>
      </n-form>

      <!-- 底部信息 -->
      <div class="login-footer">
        <!-- 注册链接 -->
        <div v-if="formMode === 'login'" class="register-section">
          <span class="register-text">还没有账号？</span>
          <n-a @click="formMode = 'register'" class="register-link">
            立即注册
          </n-a>
        </div>
        <div v-else class="register-section">
          <span class="register-text">已有账号？</span>
          <n-a @click="formMode = 'login'" class="register-link">
            立即登录
          </n-a>
        </div>

        <!-- 版权信息 -->
        <div class="copyright-section">
          <div class="divider"></div>
          <p class="copyright-text">
            © {{ new Date().getFullYear() }} 九章数源 版权所有 V1.0.0
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, h } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NCheckbox,
  NIcon,
  NA,
  type FormInst,
  type FormRules,
  type FormItemRule,
} from 'naive-ui'
import { useMessage, useDialog } from '@/composables/useNaive'
import { authAPI } from '@/api'
import { useUserStore } from '@/stores/user-store'
import { getErrorMessage } from '@/utils/error-messages'
import {
  UserIcon,
  LockIcon,
  PhoneIcon,
  MailIcon,
  TabIcon,
  ArrowIcon,
} from '@/assets/icons'

// Props 和 Emits 定义
interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'login', data: Record<string, unknown>): void
  (e: 'register', data: Record<string, unknown>): void
  (e: 'update:loading', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()
const message = useMessage()
const dialog = useDialog()

const loginFormRef = ref<FormInst>()
const formMode = ref<'login' | 'register'>('login')
const loginType = ref<'password' | 'sms'>('password')
const smsCodeSending = ref(false)
const smsCodeText = ref('获取验证码')
const smsCountdown = ref(0)
const rememberMe = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  phone: '',
  smsCode: '',
})

// 表单验证规则 - 动态计算
const loginRules = computed((): FormRules => {
  const baseRules: FormRules = {}

  if (loginType.value === 'password') {
    if (formMode.value === 'login') {
      // 登录模式：支持用户名或手机号
      baseRules.username = [
        { required: true, message: '请输入用户名或手机号', trigger: 'blur' },
        { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' },
      ]
    } else {
      // 注册模式：只支持手机号
      baseRules.username = [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        {
          validator: (rule: FormItemRule, value: string) => {
            if (!value) return false
            return /^1[3-9]\d{9}$/.test(value)
          },
          message: '请输入正确的手机号格式',
          trigger: 'blur',
        },
      ]
    }

    baseRules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      {
        min: 6,
        max: 20,
        message: '密码长度在 6 到 20 个字符',
        trigger: 'blur',
      },
    ]

    // 注册模式的确认密码验证
    if (formMode.value === 'register') {
      baseRules.confirmPassword = [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule: FormItemRule, value: string) => {
            return value === loginForm.password
          },
          message: '两次输入的密码不一致',
          trigger: 'blur',
        },
      ]
    }
  } else if (loginType.value === 'sms') {
    baseRules.phone = [
      {
        validator: (rule: FormItemRule, value: string) => {
          if (!value) return false
          return /^1[3-9]\d{9}$/.test(value)
        },
        message: '请输入正确的手机号格式',
        trigger: 'blur',
      },
    ]
    baseRules.smsCode = [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { len: 6, message: '验证码为6位数字', trigger: 'blur' },
    ]
  }

  return baseRules
})

// 处理表单提交（登录或注册）
const handleSubmit = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    emit('update:loading', true)

    if (formMode.value === 'login') {
      handleLogin()
    } else {
      handleRegister()
    }
  } catch (error: unknown) {
    console.error('表单验证失败:', error)
  }
}

// 处理登录
const handleLogin = async () => {
  let loginData: Record<string, unknown> = {
    login_type: loginType.value,
    remember_me: rememberMe.value,
  }

  // 根据登录类型构建请求数据
  if (loginType.value === 'password') {
    loginData = {
      ...loginData,
      username: loginForm.username,
      password: loginForm.password,
    }
  } else if (loginType.value === 'sms') {
    loginData = {
      ...loginData,
      phone: loginForm.phone,
      phone_code: loginForm.smsCode,
    }
  }

  emit('login', loginData)
}

// 处理注册
const handleRegister = async () => {
  let registerData: Record<string, unknown> = {
    register_type: loginType.value,
  }

  // 根据注册类型构建请求数据
  if (loginType.value === 'password') {
    registerData = {
      ...registerData,
      phone: loginForm.username, // 注册时用户名字段实际是手机号
      password: loginForm.password,
      confirm_password: loginForm.confirmPassword,
    }
  } else if (loginType.value === 'sms') {
    registerData = {
      ...registerData,
      phone: loginForm.phone,
      phone_code: loginForm.smsCode,
    }
  }

  emit('register', registerData)
}

// 处理忘记密码
const handleForgotPassword = () => {
  let emailValue = ''

  const dialogInstance = dialog.create({
    title: '重置密码',
    content: () => {
      return h('div', [
        h(
          'p',
          { style: 'margin-bottom: 10px' },
          '请输入您的邮箱地址，我们将发送重置密码链接',
        ),
        h(NInput, {
          defaultValue: emailValue,
          onUpdateValue: (v: string) => {
            emailValue = v
          },
          placeholder: '请输入邮箱地址',
        }),
      ])
    },
    positiveText: '发送邮件',
    negativeText: '取消',
    onPositiveClick: () => {
      // 验证邮箱逻辑
      if (!emailValue) {
        message.warning('请输入邮箱地址')
        return false
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
        message.warning('请输入有效的邮箱地址')
        return false
      }
      message.success(`重置密码邮件已发送到 ${emailValue}`)
      return true
    },
  })
}

// 短信验证码发送
const sendSmsCode = async () => {
  if (!loginForm.phone) {
    message.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.phone)) {
    message.warning('请输入正确的手机号格式')
    return
  }

  if (smsCodeSending.value) return

  try {
    smsCodeSending.value = true

    // 这里调用发送短信验证码的API
    // const response = await authAPI.sendSmsCode({ phone: loginForm.phone })

    // 模拟发送成功
    message.success('验证码发送成功，请注意查收')

    // 开始倒计时
    startSmsCountdown()
  } catch (error: unknown) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '验证码发送失败，请重试',
    })
    message.error(errorMessage)
  } finally {
    smsCodeSending.value = false
  }
}

// 短信验证码倒计时
const startSmsCountdown = () => {
  smsCountdown.value = 60
  smsCodeText.value = `${smsCountdown.value}s后重发`

  const timer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value > 0) {
      smsCodeText.value = `${smsCountdown.value}s后重发`
    } else {
      smsCodeText.value = '获取验证码'
      clearInterval(timer)
    }
  }, 1000)
}
</script>

<style lang="scss" scoped>
// 右侧表单区域
.login-form-container {
  width: 100%;
  max-width: 520px;

  // 登录卡片
  .login-card {
    background: #ffffff;
    border-radius: 0 0 8px 8px;
    padding: 40px;
    min-height: 580px;
    display: flex;
    flex-direction: column;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 20px 25px -5px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    backdrop-filter: blur(20px);
    transform: translateY(0);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05),
        0 25px 30px -10px rgba(0, 0, 0, 0.08);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      border-radius: 0;
    }
  }

  // 表单头部
  .form-header {
    text-align: center;
    margin-bottom: 32px;

    .form-title {
      font-size: 2rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 32px;
    }
  }

  // 登录标签页
  .login-tabs {
    display: flex;
    background: #f7fafc;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 32px;
    gap: 4px;

    .tab-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      border: none;
      background: transparent;
      border-radius: 8px;
      color: #4a5568;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      svg {
        color: currentColor;
      }

      &:hover {
        color: #2d3748;
        background: rgba(255, 255, 255, 0.5);
      }

      &.active {
        background: white;
        color: #667eea;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // 表单组
  .login-form {
    :deep(.n-form-item) {
      margin-bottom: 24px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    // 移除输入框样式覆盖，使用全局主题配置
  }

  // 表单选项
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .remember-checkbox {
      :deep(.n-checkbox__label) {
        color: #4a5568;
        font-size: 0.9rem;
      }
    }

    .forgot-link {
      font-size: 0.9rem;
      text-decoration: none;
      color: #667eea;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 登录按钮
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 6px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: #ffffff !important;
    transition: all 0.3s ease;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #5a6fd8 0%, #6b4190 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);

      &::before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }

    :deep(.n-button__content) {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      position: relative;
      z-index: 1;
    }
  }

  // 底部信息
  .login-footer {
    margin-top: auto;
    padding-top: 20px;

    .register-section {
      text-align: center;
      margin-bottom: 20px;

      .register-text {
        color: #718096;
        font-size: 0.9rem;
        margin-right: 8px;
      }

      .register-link {
        color: #667eea;
        font-size: 0.9rem;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          color: #5a67d8;
          text-decoration: underline;
        }
      }
    }

    .copyright-section {
      text-align: center;

      .divider {
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          #e2e8f0 50%,
          transparent 100%
        );
        margin: 20px 0 16px;
      }

      .copyright-text {
        color: #a0aec0;
        font-size: 0.875rem;
        margin: 0;
        font-weight: 400;
      }
    }
  }
}

// 短信按钮样式
.sms-button {
  --n-height-small: 32px;
  --n-font-size-small: 0.85rem;
  --n-text-color: #667eea !important;
  --n-text-color-hover: #5a67d8 !important;

  &:disabled {
    --n-text-color: #909399 !important;
    cursor: not-allowed;
  }
}
</style>
