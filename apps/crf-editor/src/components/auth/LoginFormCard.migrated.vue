<template>
  <div class="w-full max-w-[520px]">
    <!-- 登录表单卡片 -->
    <div
      class="login-card bg-white rounded-b-lg p-10 min-h-[580px] flex flex-col"
    >
      <!-- 表单头部 -->
      <div class="text-center mb-8">
        <h2 class="text-4xl font-bold text-gray-800 mb-8">
          {{ formMode === 'login' ? '欢迎登录' : '注册账号' }}
        </h2>
      </div>

      <!-- 登录方式切换 -->
      <div class="flex bg-gray-50 rounded-xl p-1 mb-8 gap-1">
        <button
          :class="[
            'flex-1 flex items-center justify-center gap-2 px-3 py-3 border-none bg-transparent rounded-lg text-gray-600 font-medium cursor-pointer transition-all duration-300',
            { 'tab-active': loginType === 'password' },
          ]"
          @click="loginType = 'password'"
        >
          <TabIcon type="lock" size="16" />
          密码登录
        </button>
        <button
          :class="[
            'flex-1 flex items-center justify-center gap-2 px-3 py-3 border-none bg-transparent rounded-lg text-gray-600 font-medium cursor-pointer transition-all duration-300',
            { 'tab-active': loginType === 'sms' },
          ]"
          @click="loginType = 'sms'"
        >
          <TabIcon type="chat" size="16" />
          短信验证码
        </button>
      </div>

      <!-- 登录表单 -->
      <n-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <!-- 用户名/手机号输入 (密码登录) -->
        <n-form-item
          v-if="loginType === 'password'"
          path="username"
          :show-label="false"
          class="mb-6"
        >
          <n-input
            v-model:value="loginForm.username"
            size="large"
            :placeholder="
              formMode === 'login' ? '请输入用户名/手机号' : '请输入手机号'
            "
            clearable
            class="form-input"
          >
            <template #prefix>
              <UserIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 手机号输入 (短信登录) -->
        <n-form-item
          v-if="loginType === 'sms'"
          path="phone"
          :show-label="false"
          class="mb-6"
        >
          <n-input
            v-model:value="loginForm.phone"
            size="large"
            placeholder="请输入手机号"
            clearable
            class="form-input"
          >
            <template #prefix>
              <PhoneIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 密码输入 -->
        <n-form-item
          v-if="loginType === 'password'"
          path="password"
          :show-label="false"
          class="mb-6"
        >
          <n-input
            v-model:value="loginForm.password"
            type="password"
            size="large"
            placeholder="请输入密码"
            show-password-on="click"
            clearable
            class="form-input"
            @keyup.enter="handleSubmit"
          >
            <template #prefix>
              <LockIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 确认密码输入 (注册模式) -->
        <n-form-item
          v-if="formMode === 'register' && loginType === 'password'"
          path="confirmPassword"
          :show-label="false"
          class="mb-6"
        >
          <n-input
            v-model:value="loginForm.confirmPassword"
            type="password"
            size="large"
            placeholder="请确认密码"
            show-password-on="click"
            clearable
            class="form-input"
            @keyup.enter="handleSubmit"
          >
            <template #prefix>
              <LockIcon size="18" color="#303133" />
            </template>
          </n-input>
        </n-form-item>

        <!-- 短信验证码输入 -->
        <n-form-item
          v-if="loginType === 'sms'"
          path="smsCode"
          :show-label="false"
          class="mb-6"
        >
          <n-input
            v-model:value="loginForm.smsCode"
            size="large"
            placeholder="请输入6位验证码"
            clearable
            class="form-input sms-input"
          >
            <template #prefix>
              <MailIcon size="18" color="#303133" />
            </template>
            <template #suffix>
              <n-button
                type="primary"
                size="small"
                text
                @click="sendSmsCode"
                class="sms-button"
                :disabled="smsCountdown > 0"
              >
                {{ smsCodeText }}
              </n-button>
            </template>
          </n-input>
        </n-form-item>

        <!-- 登录选项 -->
        <div class="flex justify-between items-center mb-8">
          <n-checkbox v-model:checked="rememberMe" class="remember-checkbox">
            <span class="text-gray-600 text-sm">记住登录状态</span>
          </n-checkbox>
          <n-a
            @click="handleForgotPassword"
            class="text-sm text-primary hover:underline cursor-pointer"
          >
            忘记密码？
          </n-a>
        </div>

        <!-- 登录/注册按钮 -->
        <n-button
          type="primary"
          size="large"
          :loading="loading"
          class="login-button w-full h-12 text-base font-semibold rounded-md mb-6"
          @click="handleSubmit"
          block
        >
          <template v-if="!loading">
            <n-icon :size="20" class="mr-2">
              <ArrowIcon direction="right" />
            </n-icon>
            <span>{{ formMode === 'login' ? '立即登录' : '立即注册' }}</span>
          </template>
          <template v-else>
            <span>{{ formMode === 'login' ? '登录中...' : '注册中...' }}</span>
          </template>
        </n-button>
      </n-form>

      <!-- 底部信息 -->
      <div class="mt-auto pt-5">
        <!-- 注册链接 -->
        <div class="text-center mb-5">
          <template v-if="formMode === 'login'">
            <span class="text-gray-500 text-sm mr-2">还没有账号？</span>
            <n-a
              @click="formMode = 'register'"
              class="text-primary text-sm font-medium hover:text-primary-hover hover:underline cursor-pointer transition-all duration-300"
            >
              立即注册
            </n-a>
          </template>
          <template v-else>
            <span class="text-gray-500 text-sm mr-2">已有账号？</span>
            <n-a
              @click="formMode = 'login'"
              class="text-primary text-sm font-medium hover:text-primary-hover hover:underline cursor-pointer transition-all duration-300"
            >
              立即登录
            </n-a>
          </template>
        </div>

        <!-- 版权信息 -->
        <div class="text-center">
          <div
            class="divider h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent my-5"
          ></div>
          <p class="text-gray-400 text-sm m-0 font-normal">
            © {{ new Date().getFullYear() }} 九章数源 版权所有 V1.0.0
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, h } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NCheckbox,
  NIcon,
  NA,
  type FormInst,
  type FormRules,
  type FormItemRule,
} from 'naive-ui'
import { useMessage, useDialog } from 'naive-ui'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { getErrorMessage } from '@/utils/error'

// 图标组件
import UserIcon from '@/components/icons/UserIcon.vue'
import LockIcon from '@/components/icons/LockIcon.vue'
import PhoneIcon from '@/components/icons/PhoneIcon.vue'
import MailIcon from '@/components/icons/MailIcon.vue'
import ArrowIcon from '@/components/icons/ArrowIcon.vue'
import TabIcon from '@/components/icons/TabIcon.vue'

// 组合式API
const message = useMessage()
const dialog = useDialog()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loginFormRef = ref<FormInst | null>(null)
const loading = ref(false)
const formMode = ref<'login' | 'register'>('login')
const loginType = ref<'password' | 'sms'>('password')
const rememberMe = ref(false)
const smsCountdown = ref(0)
const smsCodeSending = ref(false)

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  phone: '',
  smsCode: '',
})

// 短信验证码文本
const smsCodeText = computed(() => {
  return smsCountdown.value > 0 ? `${smsCountdown.value}s后重发` : '获取验证码'
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名或手机号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: FormItemRule, value: string) => {
        return value === loginForm.password
      },
      message: '两次输入的密码不一致',
      trigger: 'blur',
    },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号格式',
      trigger: 'blur',
    },
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' },
  ],
}

// 提交处理
const handleSubmit = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    await handleLogin()
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 登录处理
const handleLogin = async () => {
  if (loading.value) return

  try {
    loading.value = true

    const loginData =
      loginType.value === 'password'
        ? {
            username: loginForm.username,
            password: loginForm.password,
            type: 'password' as const,
          }
        : {
            phone: loginForm.phone,
            smsCode: loginForm.smsCode,
            type: 'sms' as const,
          }

    if (formMode.value === 'login') {
      await authStore.login(loginData)
      message.success('登录成功')
    } else {
      await authStore.register({
        ...loginData,
        confirmPassword: loginForm.confirmPassword,
      })
      message.success('注册成功')
    }

    // 登录成功后跳转
    const redirect = router.currentRoute.value.query.redirect as string
    await router.push(redirect || '/dashboard')
  } catch (error: unknown) {
    const errorMessage = getErrorMessage(error, {
      401: '用户名或密码错误',
      403: '账号已被禁用，请联系管理员',
      429: '登录尝试次数过多，请稍后再试',
      network: '网络连接失败，请检查网络设置',
      default:
        formMode.value === 'login' ? '登录失败，请重试' : '注册失败，请重试',
    })
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 忘记密码处理
const handleForgotPassword = () => {
  dialog.create({
    title: '重置密码',
    content: () =>
      h('div', [
        h(
          'p',
          { style: 'margin-bottom: 16px; color: #666;' },
          '请输入您的邮箱地址，我们将发送重置密码的链接到您的邮箱。',
        ),
        h(NInput, {
          placeholder: '请输入邮箱地址',
          onInput: (value: string) => {
            emailValue = value
          },
        }),
      ]),
    positiveText: '发送',
    negativeText: '取消',
    onPositiveClick: () => {
      if (!emailValue) {
        message.warning('请输入邮箱地址')
        return false
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
        message.warning('请输入有效的邮箱地址')
        return false
      }
      message.success(`重置密码邮件已发送到 ${emailValue}`)
      return true
    },
  })
}

let emailValue = ''

// 短信验证码发送
const sendSmsCode = async () => {
  if (!loginForm.phone) {
    message.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.phone)) {
    message.warning('请输入正确的手机号格式')
    return
  }

  if (smsCodeSending.value) return

  try {
    smsCodeSending.value = true

    // 这里调用发送短信验证码的API
    // const response = await authAPI.sendSmsCode({ phone: loginForm.phone })

    // 模拟发送成功
    message.success('验证码发送成功，请注意查收')

    // 开始倒计时
    startSmsCountdown()
  } catch (error: unknown) {
    const errorMessage = getErrorMessage(error, {
      network: '网络连接失败，请检查网络设置',
      default: '验证码发送失败，请重试',
    })
    message.error(errorMessage)
  } finally {
    smsCodeSending.value = false
  }
}

// 短信验证码倒计时
const startSmsCountdown = () => {
  smsCountdown.value = 60

  const timer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}
</script>

<style lang="scss" scoped>
// 保留复杂样式使用SCSS
.login-card {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 20px 25px -5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  backdrop-filter: blur(20px);
  transform: translateY(0);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 25px 30px -10px rgba(0, 0, 0, 0.08);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 0;
  }
}

// 标签页激活状态
.tab-active {
  background: white !important;
  color: #667eea !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 登录按钮渐变效果
.login-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: #ffffff !important;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #5a6fd8 0%, #6b4190 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  :deep(.n-button__content) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    z-index: 1;
  }
}

// 短信按钮样式
.sms-button {
  --n-height-small: 32px;
  --n-font-size-small: 0.85rem;
  --n-text-color: #667eea !important;
  --n-text-color-hover: #5a67d8 !important;

  &:disabled {
    --n-text-color: #909399 !important;
    cursor: not-allowed;
  }
}

// 表单项间距
.login-form {
  :deep(.n-form-item:last-of-type) {
    margin-bottom: 0;
  }
}
</style>
