<template>
  <div
    class="flex justify-center items-center min-h-100 p-10 bg-white rounded-3 border border-gray-200 md:min-h-75 md:p-5"
  >
    <div class="text-center max-w-100">
      <div class="mb-6 opacity-60 animate-float">
        <slot name="icon">
          <EmptyFormIcon size="120" />
        </slot>
      </div>
      <h3 class="m-0 mb-3 text-5 font-semibold text-gray-700 md:text-4.5">
        <slot name="title">还没有任何表单</slot>
      </h3>
      <p class="m-0 mb-8 text-4 text-gray-500 leading-6 md:text-3.5">
        <slot name="description">创建您的第一个表单，开始收集和管理数据</slot>
      </p>
      <div
        class="flex justify-center gap-3 flex-wrap md:flex-col md:items-center"
      >
        <slot name="actions">
          <n-button
            round
            type="primary"
            size="large"
            @click="emit('create-form')"
            class="md:w-full md:max-w-50 m-t-20px"
          >
            <n-icon><AddOutline /></n-icon>
            创建第一个表单
          </n-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui'
import { AddOutline } from '@vicons/ionicons5'
import { EmptyFormIcon } from '@/assets/icons'

const emit = defineEmits<{
  'create-form': []
}>()
</script>
