<template>
  <div class="statistics-container">
    <div class="statistics-header">
      <h2 class="statistics-title">数据总览</h2>
    </div>
    <div class="statistics-cards">
      <div
        class="stat-card published-forms"
        :class="{ active: activeTab === 'published' }"
        @click="$emit('tab-change', 'published')"
      >
        <div class="stat-icon">
          <n-icon><Check /></n-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ publishedCount }}</div>
          <div class="stat-label">已发布</div>
        </div>
      </div>

      <div
        class="stat-card unpublished-forms"
        :class="{ active: activeTab === 'unpublished' }"
        @click="$emit('tab-change', 'unpublished')"
      >
        <div class="stat-icon">
          <n-icon><Edit /></n-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ unpublishedCount }}</div>
          <div class="stat-label">未发布</div>
        </div>
      </div>

      <div
        class="stat-card deleted-forms"
        :class="{ active: activeTab === 'deleted' }"
        @click="$emit('tab-change', 'deleted')"
      >
        <div class="stat-icon">
          <n-icon><Delete /></n-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ deletedCount }}</div>
          <div class="stat-label">已删除</div>
        </div>
      </div>

      <div
        class="stat-card all-forms"
        :class="{ active: activeTab === 'all' }"
        @click="$emit('tab-change', 'all')"
      >
        <div class="stat-icon">
          <n-icon><Archive /></n-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ totalCount }}</div>
          <div class="stat-label">全部</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NIcon } from 'naive-ui'
import {
  CreateOutline as Edit,
  CheckmarkOutline as Check,
  ArchiveOutline as Archive,
  TrashOutline as Delete,
} from '@vicons/ionicons5'

interface Props {
  activeTab: string
  publishedCount: number
  unpublishedCount: number
  deletedCount: number
  totalCount: number
}

defineProps<Props>()

defineEmits<{
  'tab-change': [tabName: string]
}>()
</script>

<style lang="scss" scoped>
.statistics-container {
  background: white;
  border-radius: 12px;
  padding: 2px;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f2f5;
}

.statistics-header {
  margin-bottom: 20px;
}

.statistics-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f5f9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  &.active {
    background: #eff6ff;
    border-color: #3b82f6;

    .stat-icon {
      background: #3b82f6;
      color: white;
    }

    .stat-number {
      color: #3b82f6;
    }
  }
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #e2e8f0;
  color: #64748b;
  margin-right: 16px;
  transition: all 0.2s ease;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
  transition: color 0.2s ease;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

// 特定卡片的主题色
.published-forms.active {
  background: #f0fdf4;
  border-color: #22c55e;

  .stat-icon {
    background: #22c55e;
  }

  .stat-number {
    color: #22c55e;
  }
}

.unpublished-forms.active {
  background: #fffbeb;
  border-color: #f59e0b;

  .stat-icon {
    background: #f59e0b;
  }

  .stat-number {
    color: #f59e0b;
  }
}

.deleted-forms.active {
  background: #fef2f2;
  border-color: #ef4444;

  .stat-icon {
    background: #ef4444;
  }

  .stat-number {
    color: #ef4444;
  }
}

.all-forms.active {
  background: #f8fafc;
  border-color: #64748b;

  .stat-icon {
    background: #64748b;
  }

  .stat-number {
    color: #64748b;
  }
}
</style>
