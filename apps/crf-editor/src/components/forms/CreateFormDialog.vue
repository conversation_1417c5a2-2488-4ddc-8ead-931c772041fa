<template>
  <n-modal
    :show="visible"
    preset="dialog"
    title="创建新表单"
    style="width: 520px"
    :mask-closable="false"
    @update:show="handleVisibilityChange"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="90px"
      label-placement="left"
      @keyup.enter="handleSubmit"
      class="m-t-20px"
    >
      <n-form-item label="表单名称" path="name">
        <n-input
          ref="nameInputRef"
          v-model:value="formData.name"
          placeholder="请输入表单名称"
          clearable
          @keyup.enter="handleSubmit"
        />
      </n-form-item>

      <n-form-item label="表单图标" path="icon">
        <icon-selector
          v-model="formData.icon"
          v-model:color="formData.iconColor"
        />
      </n-form-item>

      <n-form-item label="表单描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入表单描述（可选）"
          :resizable="false"
        />
      </n-form-item>
    </n-form>

    <template #action>
      <div class="flex justify-end gap-3">
        <n-button @click="handleCancel" class="min-w-20">取消</n-button>
        <n-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          class="min-w-20"
        >
          {{ loading ? '创建中...' : '创建表单' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { NModal, NForm, NFormItem, NInput, NButton } from 'naive-ui'
import IconSelector from '@/components/icon-selector/IconSelector.vue'

interface FormData {
  name: string
  description: string
  icon: string
  iconColor: string
}

interface Props {
  visible: boolean
  loading?: boolean
  existingForms?: Array<{ name?: string; title?: string; status?: string }>
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  existingForms: () => [],
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  submit: [formData: FormData]
  cancel: []
}>()

// 响应式数据
const formRef = ref<any>()
const nameInputRef = ref<any>()

const formData = reactive<FormData>({
  name: '',
  description: '',
  icon: 'FormOutlined',
  iconColor: '#3b82f6',
})

// 表单验证规则
const formRules = computed(() => ({
  name: [
    { required: true, message: '请输入表单名称', trigger: 'blur' },
    {
      min: 1,
      max: 50,
      message: '表单名称长度在 1 到 50 个字符',
      trigger: 'blur',
    },
    { validator: validateFormName, trigger: 'blur' },
  ],
}))

// 检查表单名称是否重复的验证函数
const validateFormName = (rule: any, value: string) => {
  if (!value || !value.trim()) {
    return Promise.resolve()
  }

  const trimmedValue = value.trim()
  const existingForm = props.existingForms.find((form) => {
    const formName = (form.name || form.title || '').trim()
    return formName.toLowerCase() === trimmedValue.toLowerCase()
  })

  if (existingForm) {
    const statusText = existingForm.status === 'published' ? '已发布' : '草稿'
    return Promise.reject(
      new Error(
        `表单名称"${trimmedValue}"已存在（${statusText}状态），请使用其他名称`,
      ),
    )
  }

  return Promise.resolve()
}

// 处理可见性变化
const handleVisibilityChange = (visible: boolean) => {
  emit('update:visible', visible)

  if (visible) {
    // 对话框打开时，聚焦到名称输入框
    nextTick(() => {
      if (nameInputRef.value?.focus) {
        nameInputRef.value.focus()
      }
    })
  } else {
    // 对话框关闭时重置表单
    resetForm()
  }
}

// 重置表单数据
const resetForm = () => {
  formData.name = ''
  formData.description = ''
  formData.icon = 'FormOutlined'
  formData.iconColor = '#3b82f6'

  // 清除验证状态
  if (formRef.value?.restoreValidation) {
    formRef.value.restoreValidation()
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 验证表单
    if (formRef.value.validate) {
      await formRef.value.validate()
    }

    // 再次检查名称重复（防止在验证和提交之间有其他用户创建了同名表单）
    const trimmedName = formData.name.trim()
    const existingForm = props.existingForms.find((form) => {
      const formName = (form.name || form.title || '').trim()
      return formName.toLowerCase() === trimmedName.toLowerCase()
    })

    if (existingForm) {
      const statusText = existingForm.status === 'published' ? '已发布' : '草稿'
      throw new Error(
        `表单名称"${trimmedName}"已存在（${statusText}状态），请使用其他名称`,
      )
    }

    // 发送提交事件
    emit('submit', {
      name: formData.name.trim(),
      description: formData.description.trim(),
      icon: formData.icon,
      iconColor: formData.iconColor,
    })
  } catch (error) {
    // 验证失败，不关闭对话框
    console.warn('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

// 监听visible变化，自动聚焦
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      nextTick(() => {
        if (nameInputRef.value?.focus) {
          nameInputRef.value.focus()
        }
      })
    }
  },
)
</script>
