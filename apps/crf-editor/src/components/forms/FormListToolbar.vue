<template>
  <div class="forms-header">
    <div class="header-left">
      <!-- 新建表单按钮 -->
      <n-button
        strong
        round
        type="primary"
        class="create-form-btn"
        @click="$emit('create-form')"
      >
        <template #icon>
          <n-icon><Plus /></n-icon>
        </template>
        新建表单
      </n-button>

      <!-- 全选复选框 - 当有表单时且为网格视图时显示 -->
      <div v-if="showSelectAll" class="select-all-section">
        <n-checkbox
          :checked="isAllSelected"
          :indeterminate="isIndeterminate"
          @update:checked="$emit('select-all', $event)"
        >
          全选
        </n-checkbox>
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedCount > 0" class="batch-actions">
        <n-button
          quaternary
          type="error"
          size="small"
          :loading="batchDeleting"
          @click="$emit('batch-delete')"
        >
          批量删除
        </n-button>
        <n-button quaternary size="small" @click="$emit('clear-selection')">
          取消选择
        </n-button>
        <span class="selected-count">已选择 {{ selectedCount }} 项</span>
      </div>
    </div>

    <div class="header-right">
      <!-- 搜索框 -->
      <div class="search-container">
        <n-input
          :value="searchKeyword"
          placeholder="搜索表单名称或描述"
          clearable
          size="medium"
          style="width: 240px"
          @update:value="$emit('search', $event)"
        >
          <template #prefix>
            <n-icon><Search /></n-icon>
          </template>
        </n-input>
      </div>

      <!-- 布局样式按钮 -->
      <div class="layout-buttons">
        <n-button-group>
          <n-tooltip trigger="hover" placement="bottom">
            <template #trigger>
              <n-button
                class="layout-btn flat-btn"
                :type="viewMode === 'grid' ? 'primary' : 'default'"
                @click="$emit('view-mode-change', 'grid')"
              >
                <n-icon><Grid /></n-icon>
              </n-button>
            </template>
            卡片布局
          </n-tooltip>
          <n-tooltip trigger="hover" placement="bottom">
            <template #trigger>
              <n-button
                class="layout-btn flat-btn"
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="$emit('view-mode-change', 'list')"
              >
                <n-icon><List /></n-icon>
              </n-button>
            </template>
            列表布局
          </n-tooltip>
        </n-button-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  NButton,
  NIcon,
  NCheckbox,
  NInput,
  NButtonGroup,
  NTooltip,
} from 'naive-ui'
import {
  AddOutline as Plus,
  SearchOutline as Search,
  GridOutline as Grid,
  ListOutline as List,
} from '@vicons/ionicons5'

interface Props {
  viewMode: 'grid' | 'list'
  searchKeyword: string
  selectedCount: number
  totalCount: number
  isAllSelected: boolean
  isIndeterminate: boolean
  batchDeleting?: boolean
}

const props = defineProps<Props>()

defineEmits<{
  'create-form': []
  'select-all': [checked: boolean]
  'batch-delete': []
  'clear-selection': []
  search: [keyword: string]
  'view-mode-change': [mode: 'grid' | 'list']
}>()

// 计算属性
const showSelectAll = computed(() => {
  return props.totalCount > 0 && props.viewMode === 'grid'
})
</script>

<style lang="scss" scoped>
.forms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.create-form-btn {
  min-width: 120px;
  height: 40px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  }
}

.select-all-section {
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;

  :deep(.n-checkbox) {
    .n-checkbox__label {
      font-weight: 500;
      color: #475569;
    }
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border-radius: 8px;
  border: 1px solid #fecaca;

  .selected-count {
    font-size: 13px;
    color: #dc2626;
    font-weight: 500;
    margin-left: 4px;
  }
}

.search-container {
  :deep(.n-input) {
    border-radius: 8px;

    &.n-input--focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
}

.layout-buttons {
  .layout-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &.flat-btn {
      border: 1px solid #d1d5db;
      background: white;

      &:hover {
        background: #f9fafb;
        border-color: #9ca3af;
      }

      &.n-button--primary-type {
        background: #3b82f6;
        border-color: #3b82f6;
        color: white;

        &:hover {
          background: #2563eb;
          border-color: #2563eb;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .forms-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-left,
  .header-right {
    justify-content: space-between;
  }

  .search-container {
    flex: 1;

    :deep(.n-input) {
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .header-left {
    flex-wrap: wrap;
    gap: 12px;
  }

  .header-right {
    flex-wrap: wrap;
    gap: 8px;
  }

  .create-form-btn {
    min-width: 100px;
    height: 36px;
  }

  .batch-actions {
    flex-wrap: wrap;
    gap: 6px;
  }

  .selected-count {
    width: 100%;
    text-align: center;
    margin-top: 4px;
  }
}

// 动画效果
.batch-actions {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
