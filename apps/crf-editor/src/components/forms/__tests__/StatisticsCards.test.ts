import { describe, test, expect, vi, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import StatisticsCards from '@/components/forms/StatisticsCards.vue'

// 类型定义
interface StatisticsCardsProps {
  activeTab: string
  publishedCount: number
  unpublishedCount: number
  deletedCount: number
  totalCount: number
}

describe('StatisticsCards.vue', () => {
  let wrapper: VueWrapper<any>

  const defaultProps: StatisticsCardsProps = {
    activeTab: 'published',
    publishedCount: 10,
    unpublishedCount: 5,
    deletedCount: 2,
    totalCount: 17
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  test('应该正确渲染统计数据', () => {
    wrapper = mount(StatisticsCards, {
      props: defaultProps
    })

    // 检查标题
    expect(wrapper.find('.statistics-title').text()).toBe('数据总览')

    // 检查统计数字
    const statNumbers = wrapper.findAll('.stat-number')
    expect(statNumbers[0].text()).toBe('10') // 已发布
    expect(statNumbers[1].text()).toBe('5')  // 未发布
    expect(statNumbers[2].text()).toBe('2')  // 已删除
    expect(statNumbers[3].text()).toBe('17') // 全部

    // 检查标签
    const statLabels = wrapper.findAll('.stat-label')
    expect(statLabels[0].text()).toBe('已发布')
    expect(statLabels[1].text()).toBe('未发布')
    expect(statLabels[2].text()).toBe('已删除')
    expect(statLabels[3].text()).toBe('全部')
  })

  test('应该正确显示活动状态', () => {
    wrapper = mount(StatisticsCards, {
      props: {
        ...defaultProps,
        activeTab: 'unpublished'
      }
    })

    const statCards = wrapper.findAll('.stat-card')
    
    // 第一个卡片（已发布）不应该有active类
    expect(statCards[0].classes()).not.toContain('active')
    
    // 第二个卡片（未发布）应该有active类
    expect(statCards[1].classes()).toContain('active')
    
    // 其他卡片不应该有active类
    expect(statCards[2].classes()).not.toContain('active')
    expect(statCards[3].classes()).not.toContain('active')
  })

  test('应该在点击时触发tab-change事件', async () => {
    wrapper = mount(StatisticsCards, {
      props: defaultProps
    })

    const statCards = wrapper.findAll('.stat-card')

    // 点击未发布卡片
    await statCards[1].trigger('click')
    expect(wrapper.emitted('tab-change')).toBeTruthy()
    expect(wrapper.emitted('tab-change')![0]).toEqual(['unpublished'])

    // 点击已删除卡片
    await statCards[2].trigger('click')
    expect(wrapper.emitted('tab-change')![1]).toEqual(['deleted'])

    // 点击全部卡片
    await statCards[3].trigger('click')
    expect(wrapper.emitted('tab-change')![2]).toEqual(['all'])
  })

  test('应该正确应用特定卡片的主题色', () => {
    // 测试已发布卡片的主题色
    wrapper = mount(StatisticsCards, {
      props: {
        ...defaultProps,
        activeTab: 'published'
      }
    })

    const publishedCard = wrapper.findAll('.stat-card')[0]
    expect(publishedCard.classes()).toContain('published-forms')
    expect(publishedCard.classes()).toContain('active')

    // 测试未发布卡片的主题色
    wrapper.unmount()
    wrapper = mount(StatisticsCards, {
      props: {
        ...defaultProps,
        activeTab: 'unpublished'
      }
    })

    const unpublishedCard = wrapper.findAll('.stat-card')[1]
    expect(unpublishedCard.classes()).toContain('unpublished-forms')
    expect(unpublishedCard.classes()).toContain('active')

    // 测试已删除卡片的主题色
    wrapper.unmount()
    wrapper = mount(StatisticsCards, {
      props: {
        ...defaultProps,
        activeTab: 'deleted'
      }
    })

    const deletedCard = wrapper.findAll('.stat-card')[2]
    expect(deletedCard.classes()).toContain('deleted-forms')
    expect(deletedCard.classes()).toContain('active')
  })

  test('应该正确处理零值统计', () => {
    wrapper = mount(StatisticsCards, {
      props: {
        activeTab: 'published',
        publishedCount: 0,
        unpublishedCount: 0,
        deletedCount: 0,
        totalCount: 0
      }
    })

    const statNumbers = wrapper.findAll('.stat-number')
    statNumbers.forEach(number => {
      expect(number.text()).toBe('0')
    })
  })

  test('应该正确处理大数值统计', () => {
    wrapper = mount(StatisticsCards, {
      props: {
        activeTab: 'published',
        publishedCount: 1234,
        unpublishedCount: 567,
        deletedCount: 89,
        totalCount: 1890
      }
    })

    const statNumbers = wrapper.findAll('.stat-number')
    expect(statNumbers[0].text()).toBe('1234')
    expect(statNumbers[1].text()).toBe('567')
    expect(statNumbers[2].text()).toBe('89')
    expect(statNumbers[3].text()).toBe('1890')
  })

  test('应该包含正确的图标', () => {
    wrapper = mount(StatisticsCards, {
      props: defaultProps
    })

    const statIcons = wrapper.findAll('.stat-icon n-icon')
    expect(statIcons).toHaveLength(4)
    
    // 每个卡片都应该有图标
    statIcons.forEach(icon => {
      expect(icon.exists()).toBe(true)
    })
  })

  test('快照测试', () => {
    wrapper = mount(StatisticsCards, {
      props: defaultProps
    })
    
    expect(wrapper.html()).toMatchSnapshot()
  })
})