<template>
  <div class="page-import-export">
    <!-- 导出按钮 -->
    <n-button type="primary" @click="showExportDialog">
      <template #icon>
        <n-icon><Download /></n-icon>
      </template>
      导出页面
    </n-button>

    <!-- 导入按钮 -->
    <n-button @click="showImportDialog">
      <template #icon>
        <n-icon><Upload /></n-icon>
      </template>
      导入页面
    </n-button>

    <!-- 导出对话框 -->
    <n-modal
      v-model:show="exportDialogVisible"
      title="导出页面数据"
      style="width: 600px"
      :mask-closable="false"
    >
      <n-form :model="exportForm" label-width="120">
        <n-form-item label="页面标题">
          <n-input
            v-model:value="exportForm.pageTitle"
            placeholder="导出文件的标题"
          />
        </n-form-item>

        <n-form-item label="包含数据">
          <n-checkbox-group v-model:value="exportForm.includeOptions">
            <n-checkbox value="formData">表单数据</n-checkbox>
            <n-checkbox value="validation">验证结果</n-checkbox>
            <n-checkbox value="metadata">元数据</n-checkbox>
          </n-checkbox-group>
        </n-form-item>

        <n-form-item label="选择章节">
          <div class="section-selection">
            <n-checkbox
              v-model:checked="exportForm.selectAllSections"
              @update:checked="handleSelectAllSections"
              :indeterminate="exportForm.sectionIndeterminate"
            >
              全选章节
            </n-checkbox>

            <n-tree
              ref="sectionTreeRef"
              :data="sectionTreeData"
              checkable
              key-field="id"
              label-field="name"
              children-field="children"
              @update:checked-keys="handleSectionCheck"
              class="section-tree"
            />
          </div>
        </n-form-item>

        <n-form-item label="导出格式">
          <n-radio-group v-model:value="exportForm.format">
            <n-radio value="json">JSON格式</n-radio>
            <n-radio value="compressed">压缩格式</n-radio>
          </n-radio-group>
        </n-form-item>
      </n-form>

      <template #action>
        <n-button @click="exportDialogVisible = false">取消</n-button>
        <n-button type="primary" @click="handleExport" :loading="exporting">
          确认导出
        </n-button>
      </template>
    </n-modal>

    <!-- 导入对话框 -->
    <n-modal
      v-model:show="importDialogVisible"
      title="导入页面数据"
      style="width: 600px"
      :mask-closable="false"
    >
      <div class="import-container">
        <!-- 文件上传 -->
        <n-upload
          ref="uploadRef"
          :default-upload="false"
          :show-file-list="false"
          @change="handleFileChange"
          accept=".json"
          :trigger-style="{ width: '100%' }"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <UploadFilled />
              </n-icon>
            </div>
            <n-text style="font-size: 16px">
              将JSON文件拖拽到此处，或点击选择文件
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
              只支持JSON格式的CRF页面数据文件
            </n-p>
          </n-upload-dragger>
        </n-upload>

        <!-- 文件信息 -->
        <div v-if="selectedFile" class="file-info">
          <n-card>
            <h4>已选择文件</h4>
            <p><strong>文件名:</strong> {{ selectedFile.name }}</p>
            <p>
              <strong>大小:</strong> {{ formatFileSize(selectedFile.size) }}
            </p>
            <p>
              <strong>修改时间:</strong>
              {{ formatDate(selectedFile.lastModified) }}
            </p>
          </n-card>
        </div>

        <!-- 预览数据 -->
        <div v-if="previewData" class="preview-container">
          <n-card>
            <template #header>
              <span>数据预览</span>
            </template>

            <n-descriptions :column="2" bordered>
              <n-descriptions-item label="标题">
                {{ previewData.metadata?.title || '未知' }}
              </n-descriptions-item>
              <n-descriptions-item label="版本">
                {{ previewData.metadata?.version || '未知' }}
              </n-descriptions-item>
              <n-descriptions-item label="导出时间">
                {{ formatDate(previewData.metadata?.exportTime) }}
              </n-descriptions-item>
              <n-descriptions-item label="章节数量">
                {{ previewData.sections?.length || 0 }}
              </n-descriptions-item>
              <n-descriptions-item label="组件数量">
                {{ getTotalComponentCount(previewData.sections) }}
              </n-descriptions-item>
              <n-descriptions-item label="表单数据">
                {{ Object.keys(previewData.formData || {}).length }} 条
              </n-descriptions-item>
            </n-descriptions>

            <!-- 警告信息 -->
            <div v-if="importWarnings.length > 0" class="warnings">
              <n-alert
                title="导入警告"
                type="warning"
                :closable="false"
                show-icon
              >
                <ul>
                  <li v-for="warning in importWarnings" :key="warning">
                    {{ warning }}
                  </li>
                </ul>
              </n-alert>
            </div>
          </n-card>
        </div>

        <!-- 导入选项 -->
        <div v-if="previewData" class="import-options">
          <n-card>
            <template #header>
              <span>导入选项</span>
            </template>

            <n-form :model="importForm" label-width="120">
              <n-form-item label="导入模式">
                <n-radio-group v-model:value="importForm.mode">
                  <n-radio value="replace">替换当前内容</n-radio>
                  <n-radio value="merge">合并到当前内容</n-radio>
                </n-radio-group>
              </n-form-item>

              <n-form-item label="包含数据">
                <n-checkbox-group v-model:value="importForm.includeData">
                  <n-checkbox value="sections">章节结构</n-checkbox>
                  <n-checkbox value="formData">表单数据</n-checkbox>
                  <n-checkbox value="validation">验证结果</n-checkbox>
                </n-checkbox-group>
              </n-form-item>

              <n-form-item v-if="importForm.mode === 'merge'" label="冲突处理">
                <n-radio-group v-model:value="importForm.conflictResolution">
                  <n-radio value="overwrite">覆盖现有数据</n-radio>
                  <n-radio value="keep">保留现有数据</n-radio>
                  <n-radio value="prompt">逐个询问</n-radio>
                </n-radio-group>
              </n-form-item>
            </n-form>
          </n-card>
        </div>
      </div>

      <template #action>
        <n-button @click="importDialogVisible = false">取消</n-button>
        <n-button
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!previewData"
        >
          确认导入
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { NTree } from 'naive-ui'
import { CrfIcon } from '@crf/ui-components'
import { useEditorStore } from '@/stores/editor-store'
import {
  PageDataManager,
  type CompletePageData,
  type ExportPageOptions,
} from '@/utils/page-data-manager'

// Props
interface Props {
  disabled?: boolean
}

withDefaults(defineProps<Props>(), {
  disabled: false,
})

// Emits
const emit = defineEmits<{
  exportSuccess: [data: CompletePageData]
  importSuccess: [data: CompletePageData]
  exportError: [error: string]
  importError: [error: string]
}>()

// Store
const editorStore = useEditorStore()
const message = useMessage()
const dialog = useDialog()

// 引用
const sectionTreeRef = ref<InstanceType<typeof ElTree>>()
const uploadRef = ref()

// 导出相关状态
const exportDialogVisible = ref(false)
const exporting = ref(false)
const exportForm = ref({
  pageTitle: '',
  includeOptions: ['formData', 'validation', 'metadata'],
  selectedSections: [] as string[],
  selectAllSections: true,
  sectionIndeterminate: false,
  format: 'json' as 'json' | 'compressed',
})

// 导入相关状态
const importDialogVisible = ref(false)
const importing = ref(false)
const selectedFile = ref<File | null>(null)
const previewData = ref<CompletePageData | null>(null)
const importWarnings = ref<string[]>([])

const importForm = ref({
  mode: 'replace' as 'replace' | 'merge',
  includeData: ['sections', 'formData', 'validation'],
  conflictResolution: 'overwrite' as 'overwrite' | 'keep' | 'prompt',
})

// 计算属性
const sectionTreeData = computed(() => {
  const convertSection = (section: Record<string, unknown>) => ({
    id: section.id,
    name: section.name,
    children: section.children?.map(convertSection) || [],
  })

  return editorStore.sections.map(convertSection)
})

// 方法
const showExportDialog = () => {
  // 重置表单
  exportForm.value = {
    pageTitle: '我的CRF页面',
    includeOptions: ['formData', 'validation', 'metadata'],
    selectedSections: [],
    selectAllSections: true,
    sectionIndeterminate: false,
    format: 'json',
  }

  exportDialogVisible.value = true

  // 默认选中所有章节
  nextTick(() => {
    if (sectionTreeRef.value) {
      const allSectionIds = getAllSectionIds(editorStore.sections)
      sectionTreeRef.value.setCheckedKeys(allSectionIds)
      exportForm.value.selectedSections = allSectionIds
    }
  })
}

const showImportDialog = () => {
  // 重置状态
  selectedFile.value = null
  previewData.value = null
  importWarnings.value = []
  importForm.value = {
    mode: 'replace',
    includeData: ['sections', 'formData', 'validation'],
    conflictResolution: 'overwrite',
  }

  importDialogVisible.value = true
}

const getAllSectionIds = (sections: Record<string, unknown>[]): string[] => {
  const ids: string[] = []
  const collect = (section: Record<string, unknown>) => {
    ids.push(section.id)
    if (section.children) {
      section.children.forEach(collect)
    }
  }
  sections.forEach(collect)
  return ids
}

const handleSelectAllSections = (checked: boolean) => {
  if (sectionTreeRef.value) {
    const allIds = getAllSectionIds(editorStore.sections)
    if (checked) {
      sectionTreeRef.value.setCheckedKeys(allIds)
      exportForm.value.selectedSections = allIds
    } else {
      sectionTreeRef.value.setCheckedKeys([])
      exportForm.value.selectedSections = []
    }
  }
  exportForm.value.sectionIndeterminate = false
}

const handleSectionCheck = () => {
  if (sectionTreeRef.value) {
    const checkedKeys = sectionTreeRef.value.getCheckedKeys() as string[]
    const allIds = getAllSectionIds(editorStore.sections)

    exportForm.value.selectedSections = checkedKeys
    exportForm.value.selectAllSections = checkedKeys.length === allIds.length
    exportForm.value.sectionIndeterminate =
      checkedKeys.length > 0 && checkedKeys.length < allIds.length
  }
}

const handleExport = async () => {
  try {
    exporting.value = true

    const options: ExportPageOptions = {
      includeFormData: exportForm.value.includeOptions.includes('formData'),
      includeValidation: exportForm.value.includeOptions.includes('validation'),
      selectedSections: exportForm.value.selectedSections,
      format: exportForm.value.format,
    }

    const pageConfig = {
      title: exportForm.value.pageTitle,
      description: '通过CRF编辑器导出的页面数据',
    }

    const exportData = PageDataManager.exportPageData(
      editorStore.sections,
      editorStore.formData,
      editorStore.validationResults,
      pageConfig,
      options,
    )

    // 生成文件名并下载
    const fileName = PageDataManager.generateFileName(
      exportForm.value.pageTitle,
      exportForm.value.format,
    )

    const dataStr =
      exportForm.value.format === 'compressed'
        ? PageDataManager.compressData(exportData)
        : JSON.stringify(exportData, null, 2)

    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    exportDialogVisible.value = false
    message.success('页面数据导出成功！')
    emit('exportSuccess', exportData)
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '导出失败'
    message.error(errorMsg)
    emit('exportError', errorMsg)
  } finally {
    exporting.value = false
  }
}

const handleFileChange = (file: Record<string, unknown>) => {
  selectedFile.value = file.raw
  previewData.value = null
  importWarnings.value = []

  if (file.raw) {
    parseFile(file.raw)
  }
}

const parseFile = async (file: File) => {
  try {
    const text = await file.text()
    const data = JSON.parse(text)

    const result = PageDataManager.importPageData(data)

    if (result.success && result.data) {
      previewData.value = result.data
      importWarnings.value = result.warnings || []
    } else {
      message.error(result.error || '文件解析失败')
    }
  } catch (error) {
    message.error('文件格式错误，请选择有效的JSON文件')
  }
}

const handleImport = async () => {
  if (!previewData.value) {
    message.error('没有可导入的数据')
    return
  }

  try {
    importing.value = true

    // 根据导入模式处理数据
    if (importForm.value.mode === 'replace') {
      // 替换模式：确认操作
      await new Promise((resolve, reject) => {
        dialog.warning({
          title: '确认替换',
          content: '此操作将替换当前所有内容，是否继续？',
          positiveText: '确定',
          negativeText: '取消',
          onPositiveClick: () => resolve(true),
          onNegativeClick: () => reject('cancel'),
        })
      })

      // 导入数据
      if (importForm.value.includeData.includes('sections')) {
        editorStore.sections.splice(
          0,
          editorStore.sections.length,
          ...previewData.value.sections,
        )
      }

      if (importForm.value.includeData.includes('formData')) {
        Object.assign(editorStore.formData, previewData.value.formData)
      }

      if (importForm.value.includeData.includes('validation')) {
        Object.assign(
          editorStore.validationResults,
          previewData.value.validationResults,
        )
      }
    } else {
      // 合并模式：处理冲突
      await mergeImportData(previewData.value)
    }

    importDialogVisible.value = false
    message.success('页面数据导入成功！')
    emit('importSuccess', previewData.value)
  } catch (error) {
    if (error !== 'cancel') {
      const errorMsg = error instanceof Error ? error.message : '导入失败'
      message.error(errorMsg)
      emit('importError', errorMsg)
    }
  } finally {
    importing.value = false
  }
}

const mergeImportData = async (data: CompletePageData) => {
  // 简单的合并逻辑，实际项目中可能需要更复杂的冲突处理
  if (importForm.value.includeData.includes('sections')) {
    // 合并章节（避免ID冲突）
    const existingIds = new Set(
      editorStore.sections.map((s: Record<string, unknown>) => s.id),
    )
    const newSections = data.sections.filter(
      (s: Record<string, unknown>) => !existingIds.has(s.id),
    )
    editorStore.sections.push(...newSections)
  }

  if (importForm.value.includeData.includes('formData')) {
    Object.assign(editorStore.formData, data.formData)
  }

  if (importForm.value.includeData.includes('validation')) {
    Object.assign(editorStore.validationResults, data.validationResults)
  }
}

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (timestamp: string | number | undefined): string => {
  if (!timestamp) return '未知'
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getTotalComponentCount = (
  sections: Record<string, unknown>[] | undefined,
): number => {
  if (!sections) return 0
  let count = 0
  const countComponents = (section: Record<string, unknown>) => {
    count += section.blocks?.length || 0
    if (section.children) {
      section.children.forEach(countComponents)
    }
  }
  sections.forEach(countComponents)
  return count
}
</script>

<style lang="scss" scoped>
.page-import-export {
  display: flex;
  gap: 12px;
}

.section-selection {
  .section-tree {
    margin-top: 12px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 8px;
  }
}

.import-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-info {
  .n-card {
    background: #f8f9fa;
  }

  h4 {
    margin: 0 0 12px 0;
    color: #303133;
  }

  p {
    margin: 4px 0;
    color: #606266;
  }
}

.preview-container {
  .warnings {
    margin-top: 16px;

    ul {
      margin: 8px 0 0 0;
      padding-left: 20px;

      li {
        margin: 4px 0;
      }
    }
  }
}

.import-options {
  .n-form {
    margin: 0;
  }
}

:deep(.n-upload-dragger) {
  padding: 40px;
}

:deep(.n-descriptions-body) {
  background: #fafafa;
}
</style>
