<template>
  <div class="form-data-entry">
    <!-- 表单头部 -->
    <div class="entry-header">
      <div class="entry-title">
        <h2>
          {{
            entry.templateData?.value?.name ||
            entry.templateData?.value?.title ||
            '表单填写'
          }}
        </h2>
        <div class="entry-meta">
          <span class="completion"
            >完成度: {{ entry.completionPercentage }}%</span
          >
          <span class="status-badge" :class="entry.getFormStatus()">
            {{ getStatusText(entry.getFormStatus()) }}
          </span>
          <span v-if="entry.lastSaveTime?.value" class="last-save">
            最后保存: {{ formatTime(entry.lastSaveTime.value) }}
          </span>
        </div>
      </div>

      <div class="entry-actions">
        <n-button-group>
          <n-button @click="handleSave" size="small" :loading="entry.isSaving">
            <template #icon>
              <n-icon><SaveOutlined /></n-icon>
            </template>
            保存
          </n-button>
          <n-button @click="handleReset" size="small">
            <template #icon>
              <n-icon><ReloadOutlined /></n-icon>
            </template>
            重置
          </n-button>
          <n-button
            v-if="entry.canSubmit"
            @click="handleSubmit"
            type="primary"
            size="small"
            :loading="entry.isSubmitting"
          >
            <template #icon>
              <n-icon><CheckOutlined /></n-icon>
            </template>
            提交
          </n-button>
        </n-button-group>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="entry-content">
      <div v-if="entry.isLoading" class="loading-state">
        <n-skeleton :repeat="6" text />
      </div>

      <form-renderer
        v-else-if="entry.previewData?.value"
        :schema="entry.previewData.value.sections"
        :form-data="entry.currentFormData.value"
        :validation-errors="entry.validationErrors.value"
        :readonly="entry.isViewMode.value"
        :show-validation="true"
        @update-field="handleUpdateField"
        @validate-field="handleValidateField"
      />

      <div v-else class="empty-state">
        <n-empty description="暂无表单内容" />
      </div>
    </div>

    <!-- 验证错误汇总 -->
    <div v-if="entry.hasValidationErrors" class="validation-summary">
      <n-alert title="表单验证失败" type="error" show-icon :closable="false">
        <template #default>
          <ul class="error-list">
            <li
              v-for="(errors, fieldId) in entry.validationErrors.value"
              :key="fieldId"
            >
              <strong>{{ getFieldName(String(fieldId)) }}:</strong>
              <span v-for="(error, index) in errors" :key="index">{{
                error
              }}</span>
            </li>
          </ul>
        </template>
      </n-alert>
    </div>

    <!-- 自动保存提示 -->
    <div v-if="entry.hasUnsavedChanges" class="unsaved-changes">
      <n-alert
        title="有未保存的更改"
        type="warning"
        show-icon
        :closable="false"
      >
        <template #default>
          <span>请点击保存按钮保存更改</span>
        </template>
      </n-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import {
  useMessage,
  useDialog,
  NButtonGroup,
  NButton,
  NIcon,
  NSkeleton,
  NEmpty,
  NAlert,
} from 'naive-ui'
import { SaveOutlined, ReloadOutlined, CheckOutlined } from '@vicons/antd'
import { useFormDataEntry } from '@/composables/useFormDataEntry'
import FormRenderer from '@/components/preview/FormRenderer.vue'

interface Props {
  templateId: string
  instanceId?: string
  mode?: 'create' | 'edit' | 'view'
  autoSave?: boolean
  autoSaveInterval?: number
  enableValidation?: boolean
  enableSubmit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  autoSave: true,
  autoSaveInterval: 5000,
  enableValidation: true,
  enableSubmit: true,
})

// 初始化 Naive UI 消息和对话框
const message = useMessage()
const dialog = useDialog()

// 初始化表单填报功能
const entry = useFormDataEntry({
  templateId: props.templateId,
  instanceId: props.instanceId,
  mode: props.mode,
  autoSave: props.autoSave,
  autoSaveInterval: props.autoSaveInterval,
  enableValidation: props.enableValidation,
  enableSubmit: props.enableSubmit,
})

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已批准',
    rejected: '已拒绝',
    unknown: '未知',
  }
  return statusMap[status] || status
}

// 格式化时间
const formatTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(date)
}

// 获取字段名称
const getFieldName = (fieldId: string) => {
  const field = findFieldById(fieldId)
  return field?.title || field?.name || fieldId
}

// 在表单结构中查找字段
const findFieldById = (fieldId: string) => {
  if (!entry.previewData.value) return null

  const searchInSections = (
    sections: Record<string, unknown>[],
  ): Record<string, unknown> | null => {
    for (const section of sections) {
      if (section.blocks) {
        const field = (section as Record<string, unknown>).blocks?.find(
          (block: Record<string, unknown>) => block.id === fieldId,
        )
        if (field) return field
      }
      if (section.children) {
        const found = searchInSections(section.children)
        if (found) return found
      }
    }
    return null
  }

  return searchInSections(entry.previewData.value.sections)
}

// 处理字段更新
const handleUpdateField = (fieldId: string, value: unknown) => {
  entry.updateField(fieldId, value)
}

// 处理字段验证
const handleValidateField = (fieldId: string) => {
  entry.validateField(fieldId)
}

// 处理保存
const handleSave = async () => {
  try {
    const success = await entry.saveFormData(true)
    if (success) {
      message.success('表单已保存')
    }
  } catch (error) {
    message.error('保存失败')
  }
}

// 处理重置
const handleReset = async () => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '提示',
        content: '确定要重置表单吗？所有修改将丢失。',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject(new Error('用户取消')),
      })
    })

    await entry.resetForm()
  } catch {
    // 用户取消
  }
}

// 处理提交
const handleSubmit = async () => {
  try {
    const success = await entry.submitForm()
    if (success) {
      // 触发提交成功事件
      emit('submit-success')
    }
  } catch (error) {
    message.error('提交失败')
  }
}

// 定义事件
const emit = defineEmits<{
  'submit-success': []
  'save-success': []
  reset: []
}>()

// 组件挂载时初始化
onMounted(async () => {
  try {
    await entry.initializeForm()
    entry.startAutoSave()
  } catch (error) {
    console.error('初始化表单失败:', error)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  entry.cleanup()
})

// 暴露方法给父组件
defineExpose({
  save: handleSave,
  submit: handleSubmit,
  reset: handleReset,
  exportData: entry.exportFormData,
  getFormData: () => entry.currentFormData.value,
  getValidationErrors: () => entry.validationErrors.value,
})
</script>

<style lang="scss" scoped>
.form-data-entry {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f9f9f9;

  .entry-header {
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .entry-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }

      .entry-meta {
        display: flex;
        gap: 12px;
        align-items: center;

        .completion {
          font-size: 12px;
          color: #6b7280;
          padding: 4px 8px;
          background: #f3f4f6;
          border-radius: 4px;
        }

        .status-badge {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;

          &.draft {
            background: #fef3c7;
            color: #92400e;
          }

          &.submitted {
            background: #dbeafe;
            color: #1d4ed8;
          }

          &.approved {
            background: #d1fae5;
            color: #065f46;
          }

          &.rejected {
            background: #fee2e2;
            color: #991b1b;
          }
        }

        .last-save {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }

    .entry-actions {
      :deep(.n-button-group) {
        // 使用 Naive UI 原始圆角配置
      }
    }
  }

  .entry-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .loading-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      min-height: 200px;
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      min-height: 200px;
    }
  }

  .validation-summary {
    padding: 16px 20px;
    border-top: 1px solid #e8e8e8;
    background: white;
    flex-shrink: 0;

    .error-list {
      margin: 8px 0 0 0;
      padding: 0;
      list-style: none;

      li {
        padding: 4px 0;

        strong {
          color: #dc2626;
          margin-right: 8px;
        }

        span {
          color: #6b7280;

          &:not(:last-child)::after {
            content: '; ';
          }
        }
      }
    }
  }

  .unsaved-changes {
    padding: 12px 20px;
    border-top: 1px solid #e8e8e8;
    background: white;
    flex-shrink: 0;
  }

  // 主题样式
  &.preview-theme-compact {
    .entry-header {
      padding: 12px 16px;

      .entry-title h2 {
        font-size: 16px;
      }
    }

    .entry-content {
      padding: 12px;
    }
  }

  &.preview-theme-clinical {
    background: #fbfbfb;

    .entry-header {
      background: #f8fafc;
      border-bottom: 2px solid #e2e8f0;

      .entry-title h2 {
        color: #1e293b;
        font-weight: 700;
      }
    }

    .entry-content {
      :deep(.form-section) {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin-bottom: 16px;
        padding: 16px;
      }
    }
  }
}
</style>
