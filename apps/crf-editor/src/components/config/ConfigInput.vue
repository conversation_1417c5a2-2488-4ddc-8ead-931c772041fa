<template>
  <n-form-item :label="fieldLabel" :path="fieldKey" :rule="validationRules">
    <n-input
      v-model:value="fieldValue"
      :placeholder="data.placeholder || '请输入内容'"
      :maxlength="data.maxlength || data.maxLength"
      :minlength="data.minlength || data.minLength"
      :show-count="data.showWordLimit"
      :clearable="data.clearable !== false"
      :disabled="data.disabled"
      :readonly="data.readonly"
      :size="data.size || 'medium'"
      @input="handleInput"
      @update:value="handleChange"
      @blur="handleBlur"
    >
      <template v-if="data.prefixIcon" #prefix>
        <i :class="data.prefixIcon" />
      </template>
      <template v-if="data.suffixIcon" #suffix>
        <i :class="data.suffixIcon" />
      </template>
    </n-input>
  </n-form-item>
</template>

<script lang="ts" setup>
// Vue 3 编译器宏不需要导入
import { useFormConfig } from './mixins/useFormConfig'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  handleInput,
  handleChange,
  handleBlur,
} = useFormConfig(props, emit)
</script>
