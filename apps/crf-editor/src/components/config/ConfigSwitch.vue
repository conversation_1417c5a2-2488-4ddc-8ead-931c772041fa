<template>
  <div class="config-switch-inline">
    <span class="config-switch-label">{{ data.label || '开关' }}</span>
    <n-switch
      v-model:value="switchValue"
      :checked-value="data.activeValue !== undefined ? data.activeValue : true"
      :unchecked-value="
        data.inactiveValue !== undefined ? data.inactiveValue : false
      "
      :disabled="data.disabled"
      :size="data.size || 'medium'"
      @update:value="handleChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, watch } from 'vue'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const { data } = toRefs(props)

// 计算属性：从data中获取key
const fieldKey = computed(() => data.value?.key || '')

// 获取初始值
const getInitialValue = () => {
  const formValue = data.value.formData?.value
  const defaultValue = data.value.default
  // const activeValue = data.value.activeValue !== undefined ? data.value.activeValue : true
  const inactiveValue =
    data.value.inactiveValue !== undefined ? data.value.inactiveValue : false

  // 如果有表单值
  if (formValue !== undefined) {
    return formValue
  }

  // 如果有默认值
  if (defaultValue !== undefined) {
    return defaultValue
  }

  // 返回 inactive 状态作为默认值
  return inactiveValue
}

// 初始化开关值
const switchValue = ref(getInitialValue())

// 处理开关变化事件
const handleChange = (value: string | number | boolean) => {
  switchValue.value = value
  emit('update', {
    [fieldKey.value]: value,
  })
}

// 监听data变化，更新内部值
watch(
  () => data.value.formData?.value,
  (newValue) => {
    if (newValue !== undefined) {
      switchValue.value = newValue
    }
  },
  { immediate: true },
)

// 监听默认值变化
watch(
  () => data.value?.default,
  (newValue) => {
    if (newValue !== undefined && data.value.formData?.value === undefined) {
      switchValue.value = newValue
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.config-switch-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  margin-bottom: 20px;

  .config-switch-label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
    /* 移除 flex: 1，让标题自然宽度，Switch紧贴标题 */
  }
}
</style>
