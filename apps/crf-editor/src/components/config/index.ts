/**
 * 配置组件统一导出
 */

import type { App } from 'vue'
import ConfigBase from './ConfigBase.vue'
import ConfigInput from './ConfigInput.vue'
import ConfigNumber from './ConfigNumber.vue'
import ConfigText from './ConfigText.vue'
import ConfigTextarea from './ConfigTextarea.vue'
import ConfigSelect from './ConfigSelect.vue'
import ConfigRadio from './ConfigRadio.vue'
import ConfigCheckbox from './ConfigCheckbox.vue'
import ConfigSwitch from './ConfigSwitch.vue'
import ConfigSlider from './ConfigSlider.vue'
import ConfigIcon from './ConfigIcon.vue'
import ConfigFiles from './ConfigFiles.vue'

// 新增的配置组件
import ConfigOptions from './ConfigOptions.vue'
import ConfigShortcuts from './ConfigShortcuts.vue'

// 配置组件映射
export const CONFIG_COMPONENTS: Record<string, unknown> = {
  'config-base': ConfigBase,
  'config-input': ConfigInput,
  'config-number': ConfigNumber,
  'config-text': ConfigText,
  'config-textarea': ConfigTextarea,
  'config-select': ConfigSelect,
  'config-radio': ConfigRadio,
  'config-checkbox': ConfigCheckbox,
  'config-switch': ConfigSwitch,
  'config-slider': ConfigSlider,
  'config-icon': ConfigIcon,
  'config-files': ConfigFiles,
  // 新增的配置组件
  'config-options': ConfigOptions,
  'config-shortcuts': ConfigShortcuts,
}

// 获取配置组件
export function getConfigComponent(code: string): unknown {
  return CONFIG_COMPONENTS[code as keyof typeof CONFIG_COMPONENTS]
}

// 注册配置组件到Vue应用
export function registerConfigComponents(app: App) {
  Object.entries(CONFIG_COMPONENTS).forEach(([code, component]) => {
    app.component(code, component)
  })
}

// 导出所有配置组件
export {
  ConfigBase,
  ConfigInput,
  ConfigNumber,
  ConfigText,
  ConfigTextarea,
  ConfigSelect,
  ConfigRadio,
  ConfigCheckbox,
  ConfigSwitch,
  ConfigSlider,
  ConfigIcon,
  ConfigFiles,
  ConfigOptions,
  ConfigShortcuts,
}

// 默认导出
export default CONFIG_COMPONENTS
