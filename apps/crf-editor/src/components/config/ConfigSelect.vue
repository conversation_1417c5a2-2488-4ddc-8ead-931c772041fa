<template>
  <n-form-item :label="fieldLabel" :path="fieldKey" :rule="validationRules">
    <n-select
      v-model:value="fieldValue"
      :placeholder="data.placeholder || '请选择'"
      :multiple="data.multiple || false"
      :disabled="data.disabled"
      :clearable="data.clearable !== false"
      :filterable="data.filterable || false"
      :size="data.size || 'medium'"
      :max-tag-count="data.maxCollapseTags || 1"
      @update:value="handleChange"
    >
      <n-option
        v-for="option in options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      />
    </n-select>
  </n-form-item>
</template>

<script lang="ts" setup>
// Vue 3 编译器宏不需要导入
import { useFormConfig } from './mixins/useFormConfig'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const {
  fieldKey,
  fieldLabel,
  fieldValue,
  validationRules,
  options,
  handleChange,
} = useFormConfig(props, emit)
</script>
