<template>
  <n-form-item :label="data.label || '文件上传'" :path="data.key">
    <n-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="data.action || '#'"
      :multiple="data.multiple || false"
      :directory-dnd="data.drag || false"
      :accept="data.accept"
      :max="data.limit"
      :disabled="data.disabled"
      :show-file-list="data.showFileList !== false"
      :list-type="data.listType || 'text'"
      :default-upload="data.autoUpload !== false"
      :before-upload="beforeUpload"
      :on-finish="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :custom-request="customUpload"
      class="config-files-upload"
    >
      <template v-if="data.drag">
        <n-upload-dragger>
          <div class="upload-drag-content">
            <div class="upload-icon">📁</div>
            <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
            <div v-if="data.tip" class="upload-tip">
              {{ data.tip }}
            </div>
          </div>
        </n-upload-dragger>
      </template>

      <template v-else-if="data.listType === 'image-card'">
        <div class="upload-plus">+</div>
      </template>

      <template v-else>
        <n-button type="primary" :size="data.size || 'medium'">
          📁 {{ data.buttonText || '选择文件' }}
        </n-button>
        <div v-if="data.tip" class="config-files-tip">
          {{ data.tip }}
        </div>
      </template>
    </n-upload>

    <!-- 输入提示信息 -->
    <div v-if="data.description" class="config-files-desc">
      {{ data.description }}
    </div>
  </n-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, watch } from 'vue'

const emit = defineEmits(['update', 'upload-success', 'upload-error'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const { data } = toRefs(props)

// 计算属性：从data中获取key
const fieldKey = computed(() => data.value?.key || '')

// 上传组件引用
const uploadRef = ref()

// 文件列表
const fileList = ref<Record<string, unknown>[]>([])

// 获取初始文件列表
const getInitialFiles = () => {
  const formValue = data.value.formData?.value
  const defaultValue = data.value.default

  // 如果有表单值
  if (formValue) {
    if (Array.isArray(formValue)) {
      return formValue.map((url, index) => ({ name: `文件${index + 1}`, url }))
    }
    if (typeof formValue === 'string') {
      return [{ name: '文件', url: formValue }]
    }
  }

  // 如果有默认值
  if (defaultValue) {
    if (Array.isArray(defaultValue)) {
      return defaultValue.map((url, index) => ({
        name: `文件${index + 1}`,
        url,
      }))
    }
    if (typeof defaultValue === 'string') {
      return [{ name: '文件', url: defaultValue }]
    }
  }

  return []
}

// 初始化文件列表
fileList.value = getInitialFiles()

// 上传前检查
const beforeUpload = (file: File) => {
  // 文件类型检查
  if (data.value.accept) {
    const acceptTypes = data.value.accept
      .split(',')
      .map((type: string) => type.trim())
    const fileType = file.type
    const fileName = file.name
    const fileExtension = '.' + fileName.split('.').pop()?.toLowerCase()

    const isValidType = acceptTypes.some(
      (accept: string) =>
        fileType.includes(accept.replace('*', '')) || accept === fileExtension,
    )

    if (!isValidType) {
      console.warn(`文件类型不支持，仅支持 ${data.value.accept}`)
      return false
    }
  }

  // 文件大小检查
  if (data.value.maxSize) {
    const maxSize = data.value.maxSize * 1024 * 1024 // MB to bytes
    if (file.size > maxSize) {
      console.warn(`文件大小不能超过 ${data.value.maxSize}MB`)
      return false
    }
  }

  return true
}

// 自定义上传
const customUpload = (options: Record<string, unknown>) => {
  // 如果提供了自定义上传函数
  if (
    data.value.customUpload &&
    typeof data.value.customUpload === 'function'
  ) {
    return data.value.customUpload(options)
  }

  // 默认行为：模拟上传成功
  setTimeout(() => {
    const file = options.file
    const response = {
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size,
    }
    handleSuccess(response, file, fileList.value)
  }, 1000)
}

// 上传成功处理
const handleSuccess = (
  response: Record<string, unknown>,
  file: Record<string, unknown>,
  fileListParam: Record<string, unknown>[],
) => {
  // 更新文件列表
  fileList.value = fileListParam

  // 更新表单数据
  updateFormData()

  emit('upload-success', response, file, fileListParam)
}

// 上传失败处理
const handleError = (
  error: unknown,
  file: Record<string, unknown>,
  fileListParam: Record<string, unknown>[],
) => {
  console.error('文件上传失败', error)
  emit('upload-error', error, file, fileListParam)
}

// 文件移除处理
const handleRemove = (
  _file: Record<string, unknown>,
  fileListParam: Record<string, unknown>[],
) => {
  fileList.value = fileListParam
  updateFormData()
}

// 文件超出限制处理
const handleExceed = (
  _files: File[],
  _fileListParam: Record<string, unknown>[],
) => {
  console.warn(`最多只能上传 ${data.value.limit} 个文件`)
}

// 更新表单数据
const updateFormData = () => {
  let value

  if (data.value.multiple) {
    // 多文件：返回URL数组
    value = fileList.value
      .map((file) => file.url || file.response?.url)
      .filter(Boolean)
  } else {
    // 单文件：返回单个URL
    const file = fileList.value[0]
    value = file ? file.url || file.response?.url : ''
  }

  emit('update', {
    [fieldKey.value]: value,
  })
}

// 监听data变化，更新内部值
watch(
  () => data.value.formData?.value,
  (newValue) => {
    if (newValue !== undefined) {
      fileList.value = getInitialFiles()
    }
  },
  { immediate: true },
)

// 监听默认值变化
watch(
  () => data.value?.default,
  (newValue) => {
    if (newValue !== undefined && data.value.formData?.value === undefined) {
      fileList.value = getInitialFiles()
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.config-files-upload {
  width: 100%;
}

.upload-drag-content {
  text-align: center;
  padding: 20px;

  .upload-icon {
    font-size: 48px;
    margin-bottom: 8px;
  }
}

.upload-plus {
  font-size: 24px;
  color: #8c939d;
  text-align: center;
  padding: 20px;
}

.config-files-tip {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
}

.config-files-desc {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}
</style>
