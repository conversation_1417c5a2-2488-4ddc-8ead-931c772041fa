<template>
  <div class="config-text">
    <!-- 医疗CRF专用配置面板 -->
    <div class="config-sections">
      <!-- 基础设置 -->
      <div class="config-section">
        <h3 class="section-title">基础设置</h3>

        <div class="config-item">
          <label class="field-label">
            字段标题 <span class="required-mark">*</span>
          </label>
          <n-input
            v-model:value="formData.title"
            placeholder="请输入字段标题"
            @update:value="handleFieldChange('title', $event)"
          />
        </div>

        <div class="config-item">
          <label class="field-label">输入框暗显示</label>
          <n-input
            v-model:value="formData.placeholder"
            placeholder="请输入内容"
            @update:value="handleFieldChange('placeholder', $event)"
          />
        </div>

        <div class="config-item">
          <label class="field-label">描述/备注</label>
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :rows="3"
            :maxlength="200"
            show-count
            placeholder="请输入描述"
            @update:value="handleFieldChange('description', $event)"
          />
        </div>
      </div>

      <!-- 验证设置 -->
      <div class="config-section">
        <h3 class="section-title">验证设置</h3>

        <div class="config-item">
          <label class="field-label">必填</label>
          <n-switch
            v-model:value="formData.required"
            @update:value="handleFieldChange('required', $event)"
          />
        </div>

        <div class="config-item">
          <label class="field-label">格式验证</label>
          <n-select
            v-model:value="formData.format"
            placeholder="请选择格式"
            :options="formatOptions"
            @update:value="handleFieldChange('format', $event)"
          />
        </div>

        <div class="config-item">
          <label class="field-label">最少字符数</label>
          <n-input-number
            v-model:value="formData.minLength"
            :min="0"
            :max="1000"
            @update:value="handleFieldChange('minLength', $event)"
          />
        </div>

        <div class="config-item">
          <label class="field-label">最多字符数</label>
          <n-input-number
            v-model:value="formData.maxLength"
            :min="1"
            :max="5000"
            @update:value="handleFieldChange('maxLength', $event)"
          />
        </div>

        <div class="config-item">
          <label class="field-label">自定义错误提示</label>
          <n-input
            v-model:value="formData.customErrorMessage"
            placeholder="请输入自定义错误信息"
            @update:value="handleFieldChange('customErrorMessage', $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch } from 'vue'
import { NInput, NSelect, NInputNumber, NSwitch } from 'naive-ui'
import type { ComponentConfig } from '@crf/type-definitions'

interface Props {
  componentConfig: ComponentConfig
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:componentConfig': [ComponentConfig]
}>()

// 格式选项
const formatOptions = [
  { label: '无', value: 'none' },
  { label: '邮箱', value: 'email' },
  { label: '手机号', value: 'phone' },
  { label: '网址', value: 'url' },
  { label: '数字', value: 'number' },
  { label: '患者ID', value: 'patientId' },
  { label: '身份证号', value: 'idCard' },
]

// 表单数据
const formData = reactive<Record<string, unknown>>({
  title: (props.componentConfig as Record<string, unknown>).title || '',
  description:
    (props.componentConfig as Record<string, unknown>).description || '',
  required:
    (props.componentConfig as Record<string, unknown>).required || false,
  format: (props.componentConfig as Record<string, unknown>).format || 'none',
  minLength:
    (props.componentConfig as Record<string, unknown>).minLength || undefined,
  maxLength:
    (props.componentConfig as Record<string, unknown>).maxLength || undefined,
  customErrorMessage:
    (props.componentConfig as Record<string, unknown>).customErrorMessage || '',
  placeholder:
    (props.componentConfig as Record<string, unknown>).placeholder || '',
})

// 处理字段值变更
const handleFieldChange = (key: string, value: unknown) => {
  formData[key] = value
  updateComponentConfig()
}

// 更新组件配置
const updateComponentConfig = () => {
  const updatedConfig: ComponentConfig = {
    ...props.componentConfig,
    ...formData,
  }

  emit('update:componentConfig', updatedConfig)
}

// 监听props变化，同步到formData
watch(
  () => props.componentConfig,
  (newConfig) => {
    Object.keys(formData).forEach((key) => {
      if (key in newConfig) {
        formData[key] = newConfig[key as keyof ComponentConfig]
      }
    })
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
.config-text {
  .config-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .config-section {
    background: #fafafa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e4e7ed;
  }

  .section-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .config-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .field-label {
    display: block;
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .required-mark {
    color: #f56c6c;
    margin-left: 2px;
  }

  /* 添加通用样式类 */
  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    display: block;
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
    font-weight: 500;
  }
}
</style>
