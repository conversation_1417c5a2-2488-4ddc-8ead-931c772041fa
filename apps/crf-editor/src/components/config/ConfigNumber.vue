<template>
  <n-form-item :label="data.label || '数字输入'" :path="data.key">
    <n-input-number
      v-model:value="numberValue"
      :placeholder="data.placeholder || '请输入数字'"
      :min="data.min"
      :max="data.max"
      :step="data.step || 1"
      :precision="data.precision"
      :disabled="data.disabled"
      :size="data.size || 'medium'"
      :show-button="data.controls !== false"
      :button-placement="data.controlsPosition === 'right' ? 'right' : 'both'"
      style="width: 100%"
      @update:value="handleChange"
    />
  </n-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, watch } from 'vue'

const emit = defineEmits(['update'])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const { data } = toRefs(props)

// 计算属性：从data中获取key
const fieldKey = computed(() => data.value?.key || '')

// 获取初始值
const getInitialValue = () => {
  const formValue = data.value.formData?.value
  const defaultValue = data.value.default

  // 如果有表单值
  if (formValue !== undefined) {
    const num = Number(formValue)
    return isNaN(num) ? undefined : num
  }

  // 如果有默认值
  if (defaultValue !== undefined) {
    const num = Number(defaultValue)
    return isNaN(num) ? undefined : num
  }

  // 返回undefined让 n-input-number 显示空值
  return undefined
}

// 初始化数字值
const numberValue = ref(getInitialValue())

// 处理数字变化事件
const handleChange = (value: number | undefined) => {
  numberValue.value = value
  emit('update', {
    [fieldKey.value]: value,
  })
}

// 监听data变化，更新内部值
watch(
  () => data.value.formData?.value,
  (newValue) => {
    if (newValue !== undefined) {
      const num = Number(newValue)
      numberValue.value = isNaN(num) ? undefined : num
    }
  },
  { immediate: true },
)

// 监听默认值变化
watch(
  () => data.value?.default,
  (newValue) => {
    if (newValue !== undefined && data.value.formData?.value === undefined) {
      const num = Number(newValue)
      numberValue.value = isNaN(num) ? undefined : num
    }
  },
  { immediate: true },
)
</script>
