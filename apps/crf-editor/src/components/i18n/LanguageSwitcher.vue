<template>
  <div class="language-switcher">
    <!-- 简单下拉选择器 -->
    <n-dropdown
      v-if="mode === 'dropdown'"
      @select="handleLanguageChange"
      placement="bottom-start"
      trigger="click"
      :disabled="disabled"
      :options="dropdownOptions"
      class="language-dropdown"
    >
      <n-button
        :size="size"
        :type="buttonType"
        :loading="switching"
        class="language-trigger"
      >
        <span class="flag">{{ currentLocaleConfig.flag }}</span>
        <span v-if="showLabel" class="label">
          {{
            showNative
              ? currentLocaleConfig.nativeName
              : currentLocaleConfig.name
          }}
        </span>
        <n-icon class="arrow"><ArrowDownOutlined /></n-icon>
      </n-button>
    </n-dropdown>

    <!-- 选择器模式 -->
    <n-select
      v-else-if="mode === 'select'"
      v-model:value="currentLocale"
      @update:value="handleLanguageChange"
      :size="size"
      :disabled="disabled || switching"
      :loading="switching"
      placement="bottom-start"
      :options="selectOptions"
      class="language-select"
    />

    <!-- 按钮组模式 -->
    <n-button-group v-else-if="mode === 'buttons'" class="language-buttons">
      <n-button
        v-for="locale in limitedLocales"
        :key="locale.code"
        :type="locale.code === currentLocale ? 'primary' : 'default'"
        :size="size"
        :disabled="disabled || switching"
        :loading="switching && locale.code === switchingTo"
        @click="handleLanguageChange(locale.code)"
        class="language-button"
      >
        <span class="flag">{{ locale.flag }}</span>
        <span v-if="showLabel" class="label">
          {{
            compact
              ? (locale.code.split('-')[0] || locale.code).toUpperCase()
              : locale.nativeName
          }}
        </span>
      </n-button>
    </n-button-group>

    <!-- 弹窗模式 -->
    <div v-else-if="mode === 'modal'">
      <n-button
        :size="size"
        :type="buttonType"
        :disabled="disabled"
        @click="showModal = true"
        class="language-trigger"
      >
        <span class="flag">{{ currentLocaleConfig.flag }}</span>
        <span v-if="showLabel" class="label">
          {{
            showNative
              ? currentLocaleConfig.nativeName
              : currentLocaleConfig.name
          }}
        </span>
      </n-button>

      <n-modal
        v-model:show="showModal"
        :title="$t('common.actions.selectLanguage')"
        style="width: 400px"
        class="language-modal"
      >
        <div class="language-grid">
          <div
            v-for="locale in availableLocales"
            :key="locale.code"
            class="language-card"
            :class="{ selected: locale.code === currentLocale }"
            @click="selectLanguage(locale.code)"
          >
            <div class="card-content">
              <span class="flag large">{{ locale.flag }}</span>
              <div class="names">
                <div class="native-name">{{ locale.nativeName }}</div>
                <div class="english-name">{{ locale.name }}</div>
              </div>
              <n-icon v-if="locale.code === currentLocale" class="check-icon">
                <CheckOutlined />
              </n-icon>
            </div>
          </div>
        </div>

        <template #action>
          <n-button @click="showModal = false">
            {{ $t('common.buttons.cancel') }}
          </n-button>
          <n-button
            type="primary"
            @click="confirmLanguageChange"
            :disabled="!selectedLanguage || selectedLanguage === currentLocale"
            :loading="switching"
          >
            {{ $t('common.buttons.confirm') }}
          </n-button>
        </template>
      </n-modal>
    </div>

    <!-- 切换提示 -->
    <n-alert
      v-if="showSwitchingTip && switching"
      :title="$t('common.status.switchingLanguage')"
      type="info"
      :closable="false"
      show-icon
      class="switching-tip"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useI18n, type SupportedLocale } from '@/composables/useI18n'
import { useMessage } from 'naive-ui'
import { ArrowDownOutlined, CheckOutlined } from '@vicons/antd'

// Props
interface Props {
  mode?: 'dropdown' | 'select' | 'buttons' | 'modal'
  size?: 'large' | 'default' | 'small'
  buttonType?:
    | 'primary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'text'
    | 'default'
  disabled?: boolean
  showLabel?: boolean
  showNative?: boolean
  showSecondaryName?: boolean
  showSwitchingTip?: boolean
  compact?: boolean
  maxButtons?: number
  placement?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'dropdown',
  size: 'default',
  buttonType: 'default',
  disabled: false,
  showLabel: true,
  showNative: true,
  showSecondaryName: false,
  showSwitchingTip: false,
  compact: false,
  maxButtons: 5,
  placement: 'bottom-end',
})

// Emits
const emit = defineEmits<{
  'before-change': [oldLocale: SupportedLocale, newLocale: SupportedLocale]
  change: [locale: SupportedLocale]
  'after-change': [locale: SupportedLocale]
}>()

// Composables
const {
  currentLocale,
  currentLocaleConfig,
  setLocale,
  getAvailableLocales,
  t: $t,
} = useI18n()
const message = useMessage()

// 本地状态
const switching = ref(false)
const switchingTo = ref<SupportedLocale | null>(null)
const showModal = ref(false)
const selectedLanguage = ref<SupportedLocale | null>(null)

// 计算属性
const availableLocales = computed(() => getAvailableLocales())

const limitedLocales = computed(() => {
  if (
    props.mode === 'buttons' &&
    props.maxButtons < availableLocales.value.length
  ) {
    // 优先显示当前语言和常用语言
    const priority: SupportedLocale[] = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR']
    const priorityLocales = priority.filter((code) =>
      availableLocales.value.some((locale) => locale.code === code),
    )

    const current = currentLocale.value
    const result = [current]

    priorityLocales.forEach((code) => {
      if (code !== current && result.length < props.maxButtons) {
        result.push(code)
      }
    })

    // 填充其他语言
    availableLocales.value.forEach((locale) => {
      if (!result.includes(locale.code) && result.length < props.maxButtons) {
        result.push(locale.code)
      }
    })

    return result.map(
      (code) => availableLocales.value.find((locale) => locale.code === code)!,
    )
  }

  return availableLocales.value
})

const selectOptions = computed(() => {
  return availableLocales.value.map((locale) => ({
    label: locale.nativeName,
    value: locale.code,
    render: () => {
      return h('div', { class: 'language-option' }, [
        h('span', { class: 'flag' }, locale.flag),
        h('div', { class: 'names' }, [
          h('span', { class: 'native-name' }, locale.nativeName),
          props.showSecondaryName
            ? h('span', { class: 'english-name' }, locale.name)
            : null,
        ]),
      ])
    },
  }))
})

// 方法
async function handleLanguageChange(locale: SupportedLocale | string) {
  const newLocale = locale as SupportedLocale

  if (newLocale === currentLocale.value || switching.value) {
    return
  }

  try {
    emit('before-change', currentLocale.value, newLocale)

    switching.value = true
    switchingTo.value = newLocale

    await setLocale(newLocale)

    emit('change', newLocale)
    emit('after-change', newLocale)

    message.success($t('common.messages.languageChanged'))
  } catch (error) {
    console.error('Language switch failed:', error)
    message.error($t('common.messages.languageChangeFailed'))
  } finally {
    switching.value = false
    switchingTo.value = null
  }
}

function selectLanguage(locale: SupportedLocale) {
  selectedLanguage.value = locale
}

async function confirmLanguageChange() {
  if (selectedLanguage.value) {
    await handleLanguageChange(selectedLanguage.value)
    showModal.value = false
    selectedLanguage.value = null
  }
}

// 监听弹窗状态
watch(showModal, (visible) => {
  if (visible) {
    selectedLanguage.value = currentLocale.value
  } else {
    selectedLanguage.value = null
  }
})

// 键盘快捷键支持
function handleKeydown(event: KeyboardEvent) {
  if (event.altKey && event.key === 'l') {
    event.preventDefault()
    if (props.mode === 'modal') {
      showModal.value = true
    }
  }
}

// 添加键盘事件监听
document.addEventListener('keydown', handleKeydown)

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped lang="scss">
.language-switcher {
  position: relative;

  .flag {
    font-size: 16px;
    margin-right: 6px;

    &.large {
      font-size: 24px;
    }
  }

  .label {
    margin-left: 2px;
    font-size: 13px;
  }

  .arrow {
    margin-left: 4px;
    font-size: 12px;
    transition: transform 0.3s ease;
  }
}

// 下拉菜单样式
.language-dropdown {
  .language-trigger {
    display: flex;
    align-items: center;

    &:hover .arrow {
      transform: rotate(180deg);
    }
  }
}

.language-menu {
  .language-item {
    padding: 0;

    .language-option {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      width: 100%;

      .names {
        flex: 1;
        margin-left: 8px;

        .native-name {
          font-weight: 500;
          color: var(--n-text-color-base);
        }

        .english-name {
          font-size: 11px;
          color: var(--n-text-color-2);
          margin-top: 2px;
        }
      }

      .check-icon {
        color: var(--n-color-primary);
        font-size: 14px;
      }
    }

    &:hover .language-option {
      background: var(--n-color-primary-light-9);
    }
  }
}

// 选择器样式
.language-select {
  min-width: 140px;

  :deep(.n-select__wrapper) {
    .n-select__selected-item {
      display: flex;
      align-items: center;
    }
  }
}

// 按钮组样式
.language-buttons {
  display: flex;
  gap: 4px;

  .language-button {
    display: flex;
    align-items: center;
    min-width: auto;

    .label {
      margin-left: 4px;
    }
  }
}

// 弹窗样式
.language-modal {
  .language-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
  }

  .language-card {
    border: 1px solid var(--n-border-color);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--n-color-primary);
      background: var(--n-color-primary-light-9);
    }

    &.selected {
      border-color: var(--n-color-primary);
      background: var(--n-color-primary-light-8);
      box-shadow: 0 2px 8px var(--n-color-primary-light-7);
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      position: relative;

      .names {
        margin-top: 8px;

        .native-name {
          font-weight: 600;
          color: var(--n-text-color-base);
          font-size: 14px;
        }

        .english-name {
          font-size: 12px;
          color: var(--n-text-color-2);
          margin-top: 4px;
        }
      }

      .check-icon {
        position: absolute;
        top: -4px;
        right: -4px;
        color: var(--n-color-primary);
        background: var(--n-bg-color);
        border-radius: 50%;
        font-size: 16px;
      }
    }
  }
}

// 切换提示
.switching-tip {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 3000;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .language-buttons {
    flex-wrap: wrap;

    .language-button {
      .label {
        display: none;
      }
    }
  }

  .language-modal {
    .language-grid {
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 8px;
    }

    .language-card {
      padding: 12px;

      .card-content {
        .names {
          .native-name {
            font-size: 12px;
          }

          .english-name {
            font-size: 10px;
          }
        }
      }
    }
  }
}

// 紧凑模式
.language-switcher.compact {
  .language-trigger {
    .label {
      display: none;
    }
  }

  .language-buttons {
    .language-button {
      padding: 4px 8px;

      .label {
        font-size: 11px;
      }
    }
  }
}

// RTL支持
[dir='rtl'] {
  .language-switcher {
    .flag {
      margin-right: 0;
      margin-left: 6px;
    }

    .label {
      margin-left: 0;
      margin-right: 2px;
    }

    .arrow {
      margin-left: 0;
      margin-right: 4px;
    }
  }

  .language-menu {
    .language-option {
      .names {
        margin-left: 0;
        margin-right: 8px;
        text-align: right;
      }
    }
  }

  .language-buttons {
    .language-button {
      .label {
        margin-left: 0;
        margin-right: 4px;
      }
    }
  }
}

// 无障碍支持
.language-switcher {
  [role='button'] {
    &:focus {
      outline: 2px solid var(--n-color-primary);
      outline-offset: 2px;
    }
  }
}

// 打印样式
@media print {
  .language-switcher {
    display: none;
  }
}
</style>
