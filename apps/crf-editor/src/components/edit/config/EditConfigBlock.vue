<template>
  <div>
    <edit-config-render
      :list="list"
      :schema="schema as Record<string, unknown>"
    />
  </div>
</template>
<script lang="ts" setup>
import EditConfigRender from '@/components/edit/config/EditConfigRender.vue'
import { ref, watch } from 'vue'
import { useEditorStore } from '@/stores/editor-store'
import { type BlockSchema, blockSchema, type BlockSchemaKeys } from '@/schemas'
import type { BaseBlock } from '@/types/editor'

const editorStore = useEditorStore()

const list = ref<BaseBlock[]>([])
const schema = ref<BlockSchema[BlockSchemaKeys]>()

watch(
  () => editorStore.selectedComponent,
  (newSelect) => {
    console.log('选中组件变化:', newSelect)

    if (!newSelect?.code) {
      console.log('没有选中组件或组件code为空')
      list.value = []
      schema.value = undefined
      return
    }

    const code = newSelect.code as BlockSchemaKeys
    console.log('组件code:', code)

    if (!blockSchema[code]) {
      console.log('未找到组件schema:', code)
      list.value = []
      schema.value = undefined
      return
    }

    const componentSchema = blockSchema[code]
    const properties = componentSchema.properties
    if (!properties) {
      console.log('组件schema没有properties:', code)
      list.value = []
      schema.value = undefined
      return
    }

    schema.value = componentSchema
    console.log('设置schema:', schema.value)
    console.log('Schema properties:', properties)

    const { id } = newSelect as Record<string, unknown>
    console.log('组件配置:', newSelect)

    const listResult = Object.fromEntries(
      Object.entries(properties).map((itemChild) => {
        const [key, value] = itemChild as [string, Record<string, unknown>]

        // 特殊处理title字段，允许空值，不使用默认值回填
        let configValue
        if (key === 'title') {
          // 对于title字段，即使为空也不使用默认值
          configValue =
            (newSelect as Record<string, unknown>)[key] !== undefined
              ? (newSelect as Record<string, unknown>)[key]
              : ''
        } else {
          // 其他字段保持原有逻辑
          configValue =
            (newSelect as Record<string, unknown>)[key] || value.default
        }

        // 构建配置项，包含校验规则
        const configItem = {
          ...value,
          id,
          key,
          formData: { value: configValue || '' },
          // 添加校验规则
          validation: value.validation || {},
        }

        return [key, configItem]
      }),
    )

    list.value = [...Object.values(listResult)] as BaseBlock[]
    console.log('最终配置列表:', list.value)
  },
  { immediate: true, deep: true },
)
</script>
