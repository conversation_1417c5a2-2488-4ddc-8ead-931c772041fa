<template>
  <div class="config-render-container">
    <form ref="ruleFormRef" class="config-form">
      <!-- 基础设置分组 -->
      <div v-if="basicConfigItems.length > 0" class="config-group">
        <div class="config-group-header">
          <div class="config-group-title">
            <n-icon class="config-group-icon">
              <Settings />
            </n-icon>
            <span>基础设置</span>
          </div>
        </div>
        <div class="config-group-content">
          <div
            v-for="(item, index) in basicConfigItems"
            :key="`basic-${item.code || 'unknown'}-${index}`"
            class="config-item-wrapper"
          >
            <component
              :is="getComponent(item)"
              v-if="getComponent(item)"
              :data="item"
              :component-config="item"
              @update="update"
              @update:component-config="handleComponentConfigUpdate"
            />
          </div>
        </div>
      </div>

      <!-- 验证设置分组 -->
      <div v-if="validationConfigItems.length > 0" class="config-group">
        <div class="config-group-header">
          <div class="config-group-title">
            <n-icon class="config-group-icon">
              <CheckCircle />
            </n-icon>
            <span>验证设置</span>
          </div>
        </div>
        <div class="config-group-content">
          <div
            v-for="(item, index) in validationConfigItems"
            :key="`validation-${item.code || 'unknown'}-${index}`"
            class="config-item-wrapper"
          >
            <component
              :is="getComponent(item)"
              v-if="getComponent(item)"
              :data="item"
              :component-config="item"
              @update="update"
              @update:component-config="handleComponentConfigUpdate"
            />
          </div>
        </div>
      </div>

      <!-- 状态设置分组 -->
      <div v-if="stateConfigItems.length > 0" class="config-group">
        <div class="config-group-header">
          <div class="config-group-title">
            <n-icon class="config-group-icon">
              <Build />
            </n-icon>
            <span>状态设置</span>
          </div>
        </div>
        <div class="config-group-content">
          <div
            v-for="(item, index) in stateConfigItems"
            :key="`state-${item.code || 'unknown'}-${index}`"
            class="config-item-wrapper"
          >
            <component
              :is="getComponent(item)"
              v-if="getComponent(item)"
              :data="item"
              :component-config="item"
              @update="update"
              @update:component-config="handleComponentConfigUpdate"
            />
          </div>
        </div>
      </div>

      <!-- 医疗专业设置分组 -->
      <div v-if="medicalConfigItems.length > 0" class="config-group">
        <div class="config-group-header">
          <div class="config-group-title">
            <n-icon class="config-group-icon">
              <Engineering />
            </n-icon>
            <span>医疗专业设置</span>
          </div>
        </div>
        <div class="config-group-content">
          <div
            v-for="(item, index) in medicalConfigItems"
            :key="`medical-${item.code || 'unknown'}-${index}`"
            class="config-item-wrapper"
          >
            <component
              :is="getComponent(item)"
              v-if="getComponent(item)"
              :data="item"
              :component-config="item"
              @update="update"
              @update:component-config="handleComponentConfigUpdate"
            />
          </div>
        </div>
      </div>

      <!-- 未分组的其他项 -->
      <div v-if="otherConfigItems.length > 0" class="config-group">
        <div class="config-group-content">
          <div
            v-for="(item, index) in otherConfigItems"
            :key="`other-${item.code || 'unknown'}-${index}`"
            class="config-item-wrapper"
          >
            <component
              :is="getComponent(item)"
              v-if="getComponent(item)"
              :data="item"
              :component-config="item"
              @update="update"
              @update:component-config="handleComponentConfigUpdate"
            />
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  defineAsyncComponent,
  markRaw,
  nextTick,
  ref,
  watch,
} from 'vue'
import { NIcon } from 'naive-ui'
import {
  SettingsOutline as Settings,
  CheckmarkCircleOutline as CheckCircle,
  BuildOutline as Build,
  HardwareChipOutline as Engineering,
} from '@vicons/ionicons5'
import type { BaseBlock } from '@/types/editor'
import { useEditorStore } from '@/stores/editor-store'

const ruleFormRef = ref()
const form = ref<Record<string, unknown>>({})

const props = defineProps({
  list: {
    type: Array as () => BaseBlock[],
    default: () => [],
  },
  schema: {
    type: Object,
    default: () => {},
  },
})

const transfer = (
  b: Record<string, unknown>,
  key: string = 'default',
): Record<string, unknown> => {
  if (!b || !b.properties) {
    console.warn('[EditConfigRender] Schema properties is undefined:', b)
    return {}
  }

  return Object.fromEntries(
    Object.entries(b.properties).map((item: [string, unknown]) => {
      const [keyP, valueP] = item
      if (valueP.properties) return [keyP, transfer(valueP, key)]
      return [keyP, valueP[key]]
    }),
  )
}

// 获取 schema 中的 rules 定义
const rules = ref(transfer(props.schema, 'rules'))

// 组件缓存
const componentCache = ref(new Map<string, Record<string, unknown>>())

// 使用 Vite 的 import.meta.glob 自动导入所有配置组件
const configModules = import.meta.glob('@/components/config/Config*.vue')

// 从文件路径中提取组件名称
const getComponentNameFromPath = (path: string): string => {
  const match = path.match(/Config(.+)\.vue$/)
  return match ? `config-${match[1].toLowerCase()}` : ''
}

const getComponent = (item: BaseBlock) => {
  const componentCode = item.code

  // 如果code不存在，返回null
  if (!componentCode) {
    return null
  }

  // 如果缓存中已有，直接返回
  if (componentCache.value.has(componentCode)) {
    return componentCache.value.get(componentCode)
  }

  // 在所有配置模块中查找匹配的组件
  const matchingPath = Object.keys(configModules).find((path) => {
    const componentName = getComponentNameFromPath(path)
    return componentName === componentCode
  })

  if (matchingPath) {
    const component = markRaw(
      defineAsyncComponent(
        configModules[matchingPath] as () => Promise<Record<string, unknown>>,
      ),
    )
    componentCache.value.set(componentCode, component)
    return component
  }

  console.warn(`[EditConfigRender] 找不到配置组件: ${componentCode}`)
  return null
}

// 初始化表单数据
const initFormData = () => {
  const formData: Record<string, unknown> = {}

  props.list.forEach((item) => {
    if (item.key) {
      // 从组件的formData中获取值，如果没有则使用默认值
      formData[item.key] =
        (item as Record<string, unknown>).formData?.value ||
        (item as Record<string, unknown>).default ||
        ''
    }
  })

  form.value = formData
}

const editorStore = useEditorStore()

// 配置项分组逻辑
const basicConfigItems = computed(() => {
  return props.list.filter((item) => {
    const configGroup = (item as Record<string, unknown>).configGroup
    const key = (item as Record<string, unknown>).key
    return (
      configGroup === 'basic' ||
      ['title', 'label', 'placeholder', 'description'].includes(key)
    )
  })
})

const validationConfigItems = computed(() => {
  return props.list.filter((item) => {
    const configGroup = (item as Record<string, unknown>).configGroup
    const key = (item as Record<string, unknown>).key
    return (
      configGroup === 'validation' ||
      [
        'required',
        'format',
        'minLength',
        'maxLength',
        'enableCustomError',
        'customErrorMessage',
      ].includes(key)
    )
  })
})

const stateConfigItems = computed(() => {
  return props.list.filter((item) => {
    const configGroup = (item as Record<string, unknown>).configGroup
    const key = (item as Record<string, unknown>).key
    return (
      configGroup === 'state' ||
      ['disabled', 'readonly', 'visible'].includes(key)
    )
  })
})

const medicalConfigItems = computed(() => {
  return props.list.filter((item) => {
    const configGroup = (item as Record<string, unknown>).configGroup
    const key = (item as Record<string, unknown>).key
    return (
      configGroup === 'medical' ||
      ['medicalType', 'codingSystem', 'unit', 'helpText'].includes(key)
    )
  })
})

const otherConfigItems = computed(() => {
  const usedItems = [
    ...basicConfigItems.value,
    ...validationConfigItems.value,
    ...stateConfigItems.value,
    ...medicalConfigItems.value,
  ]
  return props.list.filter((item) => !usedItems.includes(item))
})

// 防止死循环的标志
const isUpdating = ref(false)

// 处理组件配置更新
const handleComponentConfigUpdate = (
  updatedConfig: Record<string, unknown>,
) => {
  console.log('📝 组件配置更新:', updatedConfig)

  // 将配置更新转换为props更新
  const updateData: Record<string, unknown> = {}
  Object.keys(updatedConfig).forEach((key) => {
    if (key !== 'id' && key !== 'key' && key !== 'code') {
      updateData[key] = updatedConfig[key]
    }
  })

  // 调用统一的更新方法
  update(updateData)
}

const update = (params: Record<string, unknown>) => {
  // 防止死循环
  if (isUpdating.value) {
    return
  }

  isUpdating.value = true

  try {
    const list = Object.entries(params || {})
    list.forEach(([key, value]) => {
      form.value[key] = value
    })

    // 更新当前选中组件的属性 - 使用统一状态管理
    if (editorStore.selectedComponent?.id) {
      const updateData = Object.fromEntries(list)

      // 处理props特殊逻辑
      const propsUpdate: Record<string, unknown> = {}
      const directUpdate: Record<string, unknown> = {}

      Object.entries(updateData).forEach(([key, value]) => {
        // title等字段需要同时更新到props和直接属性
        if (['title', 'placeholder', 'description'].includes(key)) {
          propsUpdate[key] = value
          directUpdate[key] = value
        } else {
          propsUpdate[key] = value
        }
      })

      // 组合最终的更新数据
      const finalUpdateData = {
        ...directUpdate,
        props: {
          ...(editorStore.selectedComponent?.props || {}),
          ...propsUpdate,
        },
      }

      // 使用统一状态管理更新组件配置
      editorStore.updateComponent(
        editorStore.selectedComponent.id,
        finalUpdateData,
      )
    }

    // 立即重置更新标志，允许下一次更新
    isUpdating.value = false

    // 触发表单验证
    nextTick(() => {
      submitForm()
    })
  } catch (error) {
    console.error('配置更新出错:', error)
    isUpdating.value = false
  }
}

// 验证整个表单
const submitForm = () => {
  setTimeout(() => {
    if (!ruleFormRef.value) return
    ruleFormRef.value.validate(
      (valid: boolean, field: Record<string, unknown>) => {
        if (valid) {
          console.log('✅ 表单验证通过')
          return
        }
        console.log('❌ 表单验证失败:', field)
        // 显示验证错误
        if (field) {
          const fieldNames = Object.keys(field)
          if (fieldNames.length > 0) {
            const fieldName = fieldNames[0]
            const errorMessage = fieldName
              ? field[fieldName]?.[0]?.message
              : undefined
            if (errorMessage) {
              console.error('验证错误:', fieldName, errorMessage)
            }
          }
        }
      },
    )
  }, 100)
}

// 监听list变化，重新初始化表单数据和校验规则
watch(
  () => props.list,
  (newList, oldList) => {
    // 防止死循环 - 只在list真正变化时初始化
    if (
      isUpdating.value ||
      JSON.stringify(newList) === JSON.stringify(oldList)
    ) {
      return
    }

    console.log('🔧 配置列表更新:', newList)
    console.log('📋 配置列表长度:', newList.length)

    if (newList.length === 0) {
      console.log('⚠️ 配置列表为空，可能是schema未找到或组件配置不正确')
    }

    initFormData()
    nextTick(() => {
      // 只在非更新状态下进行表单验证
      if (!isUpdating.value) {
        submitForm()
      }
    })
  },
  { immediate: true },
)

// 监听schema变化，更新校验规则
watch(
  () => props.schema,
  (newSchema) => {
    if (newSchema) {
      rules.value = transfer(newSchema, 'rules')
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables/_color.scss';

.config-render-container {
  .config-form {
    padding: 0;
  }

  .config-group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .config-group-header {
      border-bottom: none;
      padding: 10px;
      margin: 0;
    }

    .config-group-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #495057;

      .config-group-icon {
        color: #6c757d;
        font-size: 16px;
      }
    }

    .config-group-content {
      background: #ffffff;

      padding: 12px;

      // 如果没有header，单独显示
      .config-group:not(:has(.config-group-header)) & {
        margin-top: 8px;
      }
    }

    .config-item-wrapper {
      margin-bottom: 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 全局配置项样式统一
:deep(.form-item) {
  margin-bottom: 20px !important;

  .form-label {
    font-weight: 500 !important;
    color: #374151 !important;
    line-height: 1.5 !important;
    text-align: left !important;
    padding: 0 !important;
    font-size: 14px !important;
    width: 100% !important;
    margin-bottom: 8px !important;
    display: block !important;
  }

  .n-input {
    .n-input__input-el {
      border-radius: 6px !important;
      font-size: 14px !important;
      padding: 8px 12px !important;
    }
  }

  .n-input--textarea {
    .n-input__textarea-el {
      border-radius: 6px !important;
      font-size: 14px !important;
      padding: 8px 12px !important;
    }
  }

  .n-select {
    width: 100% !important;
  }

  .n-input-number {
    width: 100% !important;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;

  span {
    margin-top: 8px;
  }
}
</style>
