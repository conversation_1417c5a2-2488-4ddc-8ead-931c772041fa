<template>
  <div class="page-config">
    <div class="config-header">
      <div class="config-title">
        <crf-icon icon="material-symbols:settings" size="20" />
        <span>页面配置</span>
      </div>
    </div>

    <n-form :model="pageConfig" label-placement="top" class="config-form">
      <!-- 基础信息 -->
      <div class="config-section">
        <div class="section-title">
          <crf-icon icon="material-symbols:info" size="16" />
          <span>基础信息</span>
        </div>

        <n-form-item label="表单标题">
          <n-input
            v-model:value="pageConfig.title"
            placeholder="请输入表单标题"
            @update:value="handleConfigChange"
          />
        </n-form-item>

        <n-form-item label="表单描述">
          <n-input
            v-model:value="pageConfig.description"
            type="textarea"
            :rows="3"
            placeholder="请输入表单描述"
            @update:value="handleConfigChange"
          />
        </n-form-item>

        <n-form-item label="版本号">
          <n-input
            v-model:value="pageConfig.version"
            placeholder="如：1.0.0"
            @update:value="handleConfigChange"
          />
        </n-form-item>

        <n-form-item label="作者">
          <n-input
            v-model:value="pageConfig.author"
            placeholder="请输入作者姓名"
            @update:value="handleConfigChange"
          />
        </n-form-item>
      </div>

      <!-- 表单设置 -->
      <div class="config-section">
        <div class="section-title">
          <crf-icon icon="material-symbols:tune" size="16" />
          <span>表单设置</span>
        </div>

        <n-form-item label="表单布局">
          <n-select
            v-model:value="formSettings.layout"
            placeholder="选择布局方式"
            @update:value="handleConfigChange"
          >
            <n-option label="单列布局" value="single" />
            <n-option label="双列布局" value="double" />
            <n-option label="自适应布局" value="responsive" />
          </n-select>
        </n-form-item>

        <n-form-item label="标签位置">
          <n-select
            v-model:value="formSettings.labelPosition"
            placeholder="选择标签位置"
            @update:value="handleConfigChange"
          >
            <n-option label="顶部" value="top" />
            <n-option label="左侧" value="left" />
            <n-option label="右侧" value="right" />
          </n-select>
        </n-form-item>

        <n-form-item label="标签宽度">
          <n-input-number
            v-model:value="formSettings.labelWidth"
            :min="80"
            :max="200"
            :step="10"
            @update:value="handleConfigChange"
          />
          <span class="unit">px</span>
        </n-form-item>
      </div>

      <!-- 验证设置 -->
      <div class="config-section">
        <div class="section-title">
          <crf-icon icon="material-symbols:verified" size="16" />
          <span>验证设置</span>
        </div>

        <n-form-item>
          <n-checkbox
            v-model:checked="validationSettings.realTimeValidation"
            @update:checked="handleConfigChange"
          >
            实时验证
          </n-checkbox>
        </n-form-item>

        <n-form-item>
          <n-checkbox
            v-model:checked="validationSettings.showErrorSummary"
            @update:checked="handleConfigChange"
          >
            显示错误汇总
          </n-checkbox>
        </n-form-item>

        <n-form-item>
          <n-checkbox
            v-model:checked="validationSettings.highlightErrors"
            @update:checked="handleConfigChange"
          >
            高亮错误字段
          </n-checkbox>
        </n-form-item>
      </div>

      <!-- 提交设置 -->
      <div class="config-section">
        <div class="section-title">
          <crf-icon icon="material-symbols:send" size="16" />
          <span>提交设置</span>
        </div>

        <n-form-item label="提交方式">
          <n-select
            v-model:value="submitSettings.method"
            placeholder="选择提交方式"
            @update:value="handleConfigChange"
          >
            <n-option label="自动提交" value="auto" />
            <n-option label="手动提交" value="manual" />
            <n-option label="分步提交" value="step" />
          </n-select>
        </n-form-item>

        <n-form-item label="提交确认">
          <n-checkbox
            v-model:checked="submitSettings.confirmBeforeSubmit"
            @update:checked="handleConfigChange"
          >
            提交前确认
          </n-checkbox>
        </n-form-item>

        <n-form-item label="保存草稿">
          <n-checkbox
            v-model:checked="submitSettings.allowDraft"
            @update:checked="handleConfigChange"
          >
            允许保存草稿
          </n-checkbox>
        </n-form-item>
      </div>

      <!-- 时间信息 -->
      <div class="config-section">
        <div class="section-title">
          <crf-icon icon="material-symbols:schedule" size="16" />
          <span>时间信息</span>
        </div>

        <n-form-item label="创建时间">
          <n-input :value="formatTime(pageConfig.createTime)" readonly />
        </n-form-item>

        <n-form-item label="更新时间">
          <n-input :value="formatTime(pageConfig.updateTime)" readonly />
        </n-form-item>
      </div>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed } from 'vue'
import { useEditorStore } from '@/stores/editor-store'
import { CrfIcon } from '@crf/ui-components'

const editorStore = useEditorStore()

// 页面配置
const pageConfig = computed(() => editorStore.pageConfig)

// 表单设置
const formSettings = reactive({
  layout: 'single',
  labelPosition: 'top',
  labelWidth: 120,
})

// 验证设置
const validationSettings = reactive({
  realTimeValidation: true,
  showErrorSummary: true,
  highlightErrors: true,
})

// 提交设置
const submitSettings = reactive({
  method: 'manual',
  confirmBeforeSubmit: true,
  allowDraft: true,
})

// 处理配置变更
const handleConfigChange = () => {
  // 这里可以添加配置变更的处理逻辑
  console.log('页面配置已更新')
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.page-config {
  padding: 16px;
}

.config-header {
  margin-bottom: 20px;

  .config-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.config-form {
  .config-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;

    .section-title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e7eb;
    }
  }

  .unit {
    margin-left: 8px;
    color: #6b7280;
    font-size: 12px;
  }
}

:deep(.n-form-item) {
  margin-bottom: 16px;
}

:deep(.n-form-item__label) {
  font-weight: 500;
  color: #374151;
}
</style>
