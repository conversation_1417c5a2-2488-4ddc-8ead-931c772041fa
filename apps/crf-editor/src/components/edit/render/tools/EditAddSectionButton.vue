<template>
  <div class="add-section-container">
    <button class="add-section-btn" @click="handleAddSection">
      <crf-icon class="mr-2" icon="si:add-fill" size="12" />
      新增章节
    </button>
  </div>
</template>

<script lang="ts" setup>
import { CrfIcon } from '@crf/ui-components'

const emit = defineEmits<{
  'add-section': []
}>()

const handleAddSection = () => {
  emit('add-section')
}
</script>

<style lang="scss" scoped>
.add-section-container {
  position: absolute;
  bottom: -29px;
  left: 50%;
  transform: translateX(-50%);
}

.add-section-btn {
  height: 28px;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #e3f2fd;
  border: 1px solid #90caf9;
  border-top: none;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #1976d2;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: -2px;
  position: relative;
  z-index: 2;

  &:hover {
    background: #bbdefb;
    color: #1565c0;
    border-color: #64b5f6;
    transform: translateY(1px);
  }

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 2px;
    background: #e3f2fd;
    border-top: 1px solid #90caf9;
  }

  &:hover::before {
    border-color: #64b5f6;
  }
}
</style>
