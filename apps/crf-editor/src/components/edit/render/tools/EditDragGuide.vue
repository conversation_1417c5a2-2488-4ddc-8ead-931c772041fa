<template>
  <Teleport to="body">
    <Transition name="guide-fade">
      <div v-if="showGuide" class="drag-guide">
        <div class="guide-content">
          <crf-icon :icon="guideIcon" class="guide-icon" size="16" />
          <span class="guide-text">{{ guideText }}</span>
        </div>
        <div v-if="isDragging" class="ghost">
          <!-- Ghost 效果区域 -->
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { CrfIcon } from '@crf/ui-components'

const showGuide = ref(false)
const guideType = ref<'drag' | 'drop' | 'success'>('drag')
const isDragging = ref(false)

const guideIcon = computed(() => {
  switch (guideType.value) {
    case 'drag':
      return 'material-symbols:drag-pan'
    case 'drop':
      return 'material-symbols:touch-app'
    case 'success':
      return 'material-symbols:check-circle'
    default:
      return 'material-symbols:drag-pan'
  }
})

const guideText = computed(() => {
  switch (guideType.value) {
    case 'drag':
      return '拖拽组件到蓝色区域或双击快速添加'
    case 'drop':
      return '松手放置到高亮区域'
    case 'success':
      return '组件添加成功！'
    default:
      return '拖拽组件到章节区域'
  }
})

// 监听拖拽事件
const onDragStart = () => {
  guideType.value = 'drag'
  showGuide.value = true
}

const onDragOver = (event: DragEvent) => {
  const target = event.target as HTMLElement
  if (target?.closest('.section-drop-zone')) {
    guideType.value = 'drop'
  } else {
    guideType.value = 'drag'
  }
}

const onDragEnd = () => {
  showGuide.value = false
  // 延迟重置状态
  setTimeout(() => {
    guideType.value = 'drag'
  }, 300)
}

const onDrop = () => {
  guideType.value = 'success'
  setTimeout(() => {
    showGuide.value = false
    setTimeout(() => {
      guideType.value = 'drag'
    }, 300)
  }, 1500)
}

// 监听全局事件
onMounted(() => {
  document.addEventListener('dragstart', onDragStart)
  document.addEventListener('dragover', onDragOver)
  document.addEventListener('dragend', onDragEnd)
  document.addEventListener('drop', onDrop)
})

onUnmounted(() => {
  document.removeEventListener('dragstart', onDragStart)
  document.removeEventListener('dragover', onDragOver)
  document.removeEventListener('dragend', onDragEnd)
  document.removeEventListener('drop', onDrop)
})

// 监听拖动事件已在全局事件中处理
</script>

<style scoped>
.drag-guide {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.guide-content {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.guide-icon {
  color: #60a5fa;
  animation: guideIconBounce 1s ease-in-out infinite;
}

.guide-text {
  color: white;
}

.guide-fade-enter-active,
.guide-fade-leave-active {
  transition: all 0.3s ease;
}

.guide-fade-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.guide-fade-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

@keyframes guideIconBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.ghost {
  animation: ghostEffect 1.5s infinite;
  border: 1px dashed #94a3b8;
  background-color: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
}

@keyframes ghostEffect {
  0% {
    opacity: 0.5;
    transform: scale(0.9);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.5;
    transform: scale(0.9);
  }
}

@media (max-width: 768px) {
  .drag-guide {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    font-size: 12px;
  }
}
</style>
