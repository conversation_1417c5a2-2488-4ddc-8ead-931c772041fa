import type { BaseBlock } from '@/types/editor'

export interface FormSection {
  id: string
  name: string
  blocks: BaseBlock[]
  level?: number
  parentId?: string
  children?: FormSection[]
  order?: number
  collapsed?: boolean
}

export interface SectionActions {
  addSection: () => void
  addSubSection: (parentId: string) => void
  deleteSection: (sectionId: string) => void
  editSectionName: (sectionId: string) => void
  saveSectionName: (sectionId: string, newName: string) => void
  scrollToSection: (sectionId: string) => void
}
