<template>
  <div v-show="props.isSelected" class="edit-render-hover">
    <!-- 现代化工具栏 -->
    <div class="modern-toolbar">
      <div class="toolbar-item copy-btn" title="复制组件" @click="handleCopy">
        <crf-icon
          icon="material-symbols:content-copy-outline"
          size="14px"
          tip="复制组件"
        />
      </div>
      <div class="toolbar-divider"></div>
      <div
        class="toolbar-item delete-btn"
        title="删除组件"
        @click="handleDelete"
      >
        <crf-icon
          icon="material-symbols:delete-outline"
          size="14px"
          tip="删除组件"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.edit-render-hover {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  pointer-events: none;
  z-index: 15;

  // 简单的虚线边框
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed #1890ff;
    border-radius: 4px;
  }
}

.modern-toolbar {
  position: absolute;
  top: -16px;
  right: 8px;
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 4px;
  gap: 2px;
  pointer-events: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: slideInDown 0.2s ease-out;
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.toolbar-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  .crf-icon {
    transition: all 0.2s ease;
  }

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.drag-handle {
  color: #6b7280;

  &:hover {
    background: #f3f4f6;
    color: #374151;
  }
}

.copy-btn {
  color: #16a34a;

  &:hover {
    background: #f0fdf4;
    color: #15803d;
  }
}

.delete-btn {
  color: #dc2626;

  &:hover {
    background: #fef2f2;
    color: #b91c1c;
  }
}

.toolbar-divider {
  width: 1px;
  height: 12px;
  background: #e5e7eb;
  margin: 0 2px;
}
</style>

<script lang="ts" setup>
import { CrfIcon } from '@crf/ui-components'
import { useDialog } from 'naive-ui'

interface Props {
  isSelected: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['copy', 'delete'])
const dialog = useDialog()

const handleCopy = () => {
  emit('copy')
}

const handleDelete = async () => {
  try {
    await new Promise<void>((resolve, reject) => {
      dialog.warning({
        title: '提示',
        content: '确认要删除该组件吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(),
        onNegativeClick: () => reject(new Error('User cancelled')),
      })
    })
    emit('delete')
  } catch {
    // 用户取消删除操作
  }
}
</script>
