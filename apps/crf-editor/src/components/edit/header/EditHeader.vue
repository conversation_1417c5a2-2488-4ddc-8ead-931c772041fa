<template>
  <div class="header">
    <div class="header-left">
      <button class="back" @click="handleGoBack">
        <crf-icon icon="stash:arrow-left" size="1rem" />
        <span>返回</span>
      </button>

      <div class="title-section">
        <div
          class="title"
          @click="editorStore.mode !== 'fill' ? openTitleDialog : null"
          :class="{ 'title-disabled': editorStore.mode === 'fill' }"
        >
          <span class="font-800">{{ pageTitle }}</span>
          <crf-icon
            v-if="editorStore.mode !== 'fill'"
            icon="material-symbols:edit"
            size="0.8rem"
          />
        </div>

        <!-- 时间信息 -->
        <div class="time-info">
          <span class="edit-time">编辑于 {{ formatTime(editTime) }}</span>
          <span v-if="autoSaveStatus.lastSaveTime" class="save-time">
            保存于 {{ formatTime(autoSaveStatus.lastSaveTime) }}
          </span>
          <span v-if="autoSaveStatus.isSaving" class="saving-indicator">
            <crf-icon
              icon="material-symbols:hourglass-empty"
              size="0.8rem"
              class="rotating"
            />
            自动保存中...
          </span>
          <span
            v-else-if="autoSaveStatus.hasUnsavedChanges"
            class="unsaved-indicator"
          >
            <crf-icon icon="material-symbols:circle" size="0.6rem" />
            未保存
          </span>
          <span
            v-if="autoSaveStatus.config"
            class="auto-save-config"
            :title="getAutoSaveConfigTooltip()"
          >
            <crf-icon icon="material-symbols:settings" size="0.7rem" />
            {{ autoSaveStatus.config.interval_seconds }}s
          </span>
        </div>
      </div>

      <!-- 编辑标题对话框 -->
      <n-modal
        v-model:show="showTitleDialog"
        preset="dialog"
        title="编辑表单标题"
        style="width: 400px"
      >
        <n-form>
          <n-form-item label="表单标题">
            <n-input v-model:value="tempTitle" placeholder="请输入表单标题" />
          </n-form-item>
          <n-form-item label="表单描述">
            <n-input
              v-model:value="tempDescription"
              :rows="3"
              placeholder="请输入表单描述"
              type="textarea"
            />
          </n-form-item>
        </n-form>
        <template #action>
          <n-button @click="showTitleDialog = false">取消</n-button>
          <n-button type="primary" @click="saveTitleAndDescription"
            >保存</n-button
          >
        </template>
      </n-modal>
    </div>

    <div class="header-center">
      <!-- 填写模式 - 显示进度和指标 -->
      <div v-if="editorStore.mode === 'fill'" class="fill-mode-indicators">
        <div class="fill-metrics-row">
          <div class="progress-section">
            <span class="metric-label">完成度</span>
            <div class="progress-container">
              <n-progress
                :percentage="fillProgress"
                :stroke-width="6"
                :show-indicator="false"
                type="line"
              />
              <span class="progress-text">{{ fillProgress }}%</span>
            </div>
          </div>

          <div class="stat-item">
            <span class="metric-label">已填写</span>
            <span class="metric-value">{{ filledFieldsCount }}</span>
          </div>

          <div class="stat-item">
            <span class="metric-label">总字段</span>
            <span class="metric-value">{{ totalFieldsCount }}</span>
          </div>
        </div>

        <n-divider vertical />

        <!-- 只保留保存按钮 - 微信绿色 -->
        <n-button
          @click="manualSave"
          :loading="autoSaveStatus.isSaving"
          class="save-button wechat-green"
        >
          <crf-icon icon="material-symbols:save" size="16px" />
          保存实例
        </n-button>
      </div>

      <!-- 编辑/预览模式 - 原有的操作按钮 -->
      <div v-else class="action-buttons">
        <!-- 模式切换按钮 -->
        <div class="mode-toggle-buttons">
          <button
            @click="switchToEditMode"
            :class="['mode-btn', { active: editorStore.mode === 'edit' }]"
          >
            <crf-icon icon="material-symbols:edit" size="16px" />
            <span>编辑</span>
          </button>
          <button
            @click="switchToPreviewMode"
            :class="['mode-btn', { active: editorStore.mode === 'preview' }]"
          >
            <crf-icon icon="material-symbols:visibility" size="16px" />
            <span>预览</span>
          </button>
        </div>

        <!-- 分隔线 -->
        <n-divider vertical />

        <!-- 版本管理按钮 -->
        <n-button
          @click="openVersionSidebar"
          :disabled="isVersionButtonDisabled"
          :title="versionButtonTitle"
        >
          版本
          <n-badge
            v-if="versionCount > 0"
            :value="versionCount"
            :max="99"
            class="version-badge"
          />
        </n-button>

        <!-- 手动保存按钮 -->
        <n-button
          type="warning"
          @click="manualSave"
          :loading="autoSaveStatus.isSaving"
          class="save-button"
        >
          <crf-icon icon="material-symbols:save" size="16px" />
          保存
        </n-button>
      </div>
    </div>

    <div class="header-right">
      <!-- 发布按钮 - 只在编辑模式显示 -->
      <n-button
        v-if="editorStore.mode !== 'fill'"
        type="success"
        @click="handlePublish"
        :loading="publishLoading"
        :disabled="!hasUnpublishedChanges"
      >
        发布
      </n-button>
    </div>

    <!-- 版本管理模态框 -->
    <VersionManagementModal
      v-model="showVersionDialog"
      :template-id="templateId"
      @rollback="handleVersionRollback"
    />

    <!-- 版本管理侧边栏 -->
    <transition name="sidebar-overlay" appear>
      <div
        v-if="showVersionSidebar"
        class="sidebar-overlay"
        @click="showVersionSidebar = false"
      >
        <transition name="sidebar-slide" appear>
          <VersionSidebar
            v-if="showVersionSidebar"
            :template-id="templateId"
            @close="showVersionSidebar = false"
            @rollback="handleVersionRollback"
            @click.stop
          />
        </transition>
      </div>
    </transition>

    <!-- 发布对话框 -->
    <n-modal
      v-model:show="showPublishDialog"
      preset="dialog"
      title="发布表单"
      style="width: 400px"
      :mask-closable="false"
    >
      <div class="publish-dialog-content">
        <n-alert
          title="发布确认"
          type="info"
          :closable="false"
          :show-icon="true"
        >
          <p>确定要发布此表单吗？</p>
          <p>发布后用户即可通过链接访问和填写表单。</p>
        </n-alert>
      </div>

      <template #action>
        <n-button @click="showPublishDialog = false">取消</n-button>
        <n-button
          @click="confirmPublish"
          type="primary"
          :loading="publishLoading"
        >
          确认发布
        </n-button>
      </template>
    </n-modal>

    <!-- 编辑器蒙版 -->
    <EditorOverlay
      :show-overlay="showOverlay"
      :overlay-message="overlayMessage"
      :overlay-type="getOverlayType()"
      :can-exit="true"
      :can-continue="false"
      @exit="handleOverlayExit"
    />

    <!-- 发布侧边栏 -->
    <transition name="sidebar-overlay" appear>
      <div
        v-if="showPublishSidebar"
        class="sidebar-overlay"
        @click="showPublishSidebar = false"
      >
        <transition name="sidebar-slide" appear>
          <PublishSidebar
            v-if="showPublishSidebar"
            :template-id="templateId"
            :loading="publishLoading"
            @close="showPublishSidebar = false"
            @publish="handlePublishConfirm"
            @click.stop
          />
        </transition>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
// 导入依赖
import { ref, computed, onMounted, onUnmounted, reactive, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
// import { CrfIcon } from '@crf/ui-components'

// 定义CrfIcon组件类型
interface CrfIconComponent {
  icon: string
  size?: string | number
  color?: string
}

// 声明全局组件
declare global {
  interface GlobalComponents {
    'crf-icon': CrfIconComponent
  }
}
import { useEditorStore } from '@/stores/editor-store'
import { useAutoSave } from '@/composables/useAutoSave'
import { OverlayType } from '@/types/editor'
import { EditorMode } from '@crf/type-definitions/core'
import EditorOverlay from '@/components/EditorOverlay.vue'
import VersionManager from '@/components/version/VersionManager.vue'
import VersionManagementModal from '@/components/version/VersionManagementModal.vue'
import VersionSidebar from '@/components/version/VersionSidebar.vue'
import PublishSidebar from '@/components/publish/PublishSidebar.vue'
import { templateAPI } from '@/api'
import type { CRFVersion } from '@/types/api'

// Props
interface Props {
  templateId?: string
  projectId?: string
}

const props = withDefaults(defineProps<Props>(), {
  templateId: 'demo-template-id',
  projectId: 'demo-project-id',
})

// 解构props以便在模板中使用
const { templateId, projectId } = toRefs(props)

// Router
const router = useRouter()
const route = useRoute()

// Naive UI Message
const message = useMessage()

// Store
const editorStore = useEditorStore()

// 自动保存功能
const autoSave = useAutoSave({
  resourceType: 'crf_template',
  resourceId: props.templateId,
  interval: 30000, // 30秒间隔
  debounceTime: 2000, // 2秒防抖
  enabled: true,
})

// 状态
const showTitleDialog = ref(false)
const showVersionDialog = ref(false)
const showVersionSidebar = ref(false)
const showPublishDialog = ref(false)
const showPublishSidebar = ref(false)
const publishLoading = ref(false)
const hasUnpublishedChanges = ref(true) // 简化逻辑，实际项目中应该根据实际状态判断
const tempTitle = ref('')
const tempDescription = ref('')
const showOverlay = ref(false)
const overlayMessage = ref('')
const versionCount = ref(0)
const templateStatus = ref('draft')
const templatePublishedAt = ref<string | null>(null)

// 计算版本按钮状态
const isVersionButtonDisabled = computed(() => {
  return templateStatus.value === 'draft' && !templatePublishedAt.value
})

const versionButtonTitle = computed(() => {
  if (isVersionButtonDisabled.value) {
    return '表单尚未发布，暂无版本记录'
  }
  return `查看版本记录 (${versionCount.value})`
})

// 发布表单数据
const editTime = ref(new Date())

// 计算属性
const pageTitle = computed(() => editorStore.pageConfig.title)
const autoSaveStatus = computed(() => autoSave.status.value)

// 填写模式相关计算属性
const fillProgress = computed(() => {
  if (editorStore.mode !== 'fill') return 0
  return editorStore.getFieldStats().progress
})

const filledFieldsCount = computed(() => {
  if (editorStore.mode !== 'fill') return 0
  return editorStore.getFieldStats().filledFields
})

const totalFieldsCount = computed(() => {
  if (editorStore.mode !== 'fill') return 0
  return editorStore.getFieldStats().totalFields
})

// 格式化时间
const formatTime = (time: Date | null) => {
  if (!time) return ''
  return time.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 方法
const openTitleDialog = () => {
  tempTitle.value = editorStore.pageConfig.title
  tempDescription.value = editorStore.pageConfig.description
  showTitleDialog.value = true
}

const saveTitleAndDescription = async () => {
  if (tempTitle.value.trim()) {
    // 更新本地状态
    editorStore.updatePageConfig({
      title: tempTitle.value.trim(),
      description: tempDescription.value.trim(),
    })
    editTime.value = new Date()

    // 同步到后端
    if (props.templateId && props.templateId !== 'demo-template-id') {
      try {
        await templateAPI.updateTemplate(props.templateId, {
          name: tempTitle.value.trim(),
          title: tempTitle.value.trim(),
          description: tempDescription.value.trim(),
        })
        message.success('标题已保存并同步到服务器')
      } catch (error) {
        console.error('同步标题到服务器失败:', error)
        message.warning('标题已保存到本地，但同步到服务器失败')
      }
    } else {
      message.success('标题已保存')
    }
  }
  showTitleDialog.value = false
}

const switchToEditMode = () => {
  editorStore.setMode('edit' as 'edit')
  editTime.value = new Date()
}

const switchToPreviewMode = () => {
  editorStore.setMode('preview' as 'preview')
}

const openVersionManager = () => {
  showVersionDialog.value = true
}

// 初始化模板状态和版本信息
const initializeTemplateInfo = async () => {
  try {
    // 获取模板信息
    const templateResponse = await templateAPI.getTemplate(props.templateId)
    if (templateResponse.success && templateResponse.data?.template) {
      const template = templateResponse.data.template
      templateStatus.value = template.status || 'draft'
      templatePublishedAt.value = template.published_at || null
    }

    // 获取版本数量
    const versionResponse = await templateAPI.getVersions(props.templateId)
    if (versionResponse.success && versionResponse.data?.versions) {
      versionCount.value = versionResponse.data.versions.length
    }
  } catch (error) {
    console.error('初始化模板信息失败:', error)
  }
}

const openVersionSidebar = async () => {
  // 如果按钮被禁用，显示提示
  if (isVersionButtonDisabled.value) {
    message.info('表单尚未发布，暂无版本记录。请先发布表单以创建版本。')
    return
  }

  // 显示版本管理侧边栏
  showVersionSidebar.value = true
}

const manualSave = async () => {
  try {
    await autoSave.save()
    // 取消手动保存成功的提醒
  } catch (error) {
    message.error('手动保存失败')
  }
}

// 发布表单
const handlePublish = () => {
  // 先保存当前编辑的内容
  manualSave()

  // 打开发布侧边栏
  showPublishSidebar.value = true
}

// 发布确认（来自侧边栏）
const handlePublishConfirm = async (description: string) => {
  publishLoading.value = true

  try {
    // 发布模板 - 后端API不需要额外参数
    await templateAPI.publishTemplate(props.templateId)

    message.success('表单发布成功！')
    showPublishSidebar.value = false
    hasUnpublishedChanges.value = false

    // 更新模板状态和版本信息
    templateStatus.value = 'published'
    templatePublishedAt.value = new Date().toISOString()
    versionCount.value += 1

    // 可选：显示访问链接
    setTimeout(() => {
      message.info('你可以在版本管理中获取访问链接')
    }, 1000)
  } catch (error) {
    console.error('发布失败:', error)
    message.error('发布失败，请稍后重试')
  } finally {
    publishLoading.value = false
  }
}

// 发布确认
const confirmPublish = async () => {
  publishLoading.value = true

  try {
    // 发布模板 - 后端API不需要额外参数
    await templateAPI.publishTemplate(props.templateId)

    message.success('表单发布成功！')
    showPublishDialog.value = false
    hasUnpublishedChanges.value = false

    // 可选：显示访问链接
    setTimeout(() => {
      message.info('你可以在版本管理中获取访问链接')
    }, 1000)
  } catch (error) {
    console.error('发布失败:', error)
    message.error('发布失败，请稍后重试')
  } finally {
    publishLoading.value = false
  }
}

// 处理版本回滚
const handleVersionRollback = async (version: CRFVersion) => {
  try {
    showOverlay.value = true
    overlayMessage.value = `正在回滚到版本 ${version.version}...`

    // 调用回滚API
    await templateAPI.rollbackToVersion(props.templateId, version.id)

    message.success(`成功回滚到版本 ${version.version}`)

    // 重新加载编辑器数据
    try {
      const response = await templateAPI.getTemplate(props.templateId)
      if (response.data?.template) {
        // 更新页面配置
        editorStore.updatePageConfig({
          title: response.data.template.title || '',
          description: response.data.template.description || '',
        })
        // 如果有模板数据，更新编辑器状态
        if (response.data.template.template_data) {
          // 这里可以根据实际需要更新编辑器的其他状态
        }
      }
    } catch (error) {
      console.error('重新加载模板数据失败:', error)
    }

    // 标记为有未发布的更改
    hasUnpublishedChanges.value = true
  } catch (error) {
    console.error('版本回滚失败:', error)
    message.error('版本回滚失败，请稍后重试')
  } finally {
    showOverlay.value = false
  }
}

// 获取自动保存配置提示信息
const getAutoSaveConfigTooltip = () => {
  const config = autoSaveStatus.value.config
  if (!config) return '加载配置中...'

  return `自动保存配置:
• 间隔: ${config.interval_seconds}秒
• 防抖: ${config.debounce_ms}ms
• 存储: ${config.storage_type}
• 历史记录: ${config.max_history}条
• 清理: ${config.cleanup_days}天`
}

const handleGoBack = () => {
  // 获取来源标签页信息
  const fromTab = (route.query.fromTab as string) || 'unpublished'

  // 构建返回URL，包含标签页参数
  const returnUrl = `/forms?tab=${fromTab}`

  console.log('返回表单页面，标签页:', fromTab)
  router.push(returnUrl)
}

const handleOverlayExit = () => {
  // 蒙版退出处理
  showOverlay.value = false
  editorStore.setMode('edit' as 'edit')
}

// 状态相关方法
// const getStatusTagType = () => {
//   if (editorStore.mode === 'preview') return 'info'
//   if (editorStore.mode === 'publish') return 'success'
//   return 'success' // 简化状态管理
// }

// const getStatusText = () => {
//   if (editorStore.mode === 'preview') return '预览模式'
//   if (editorStore.mode === 'publish') return '发布模式'
//   return '编辑模式'
// }

// const getStatusIcon = () => {
//   if (editorStore.mode === 'preview') return 'material-symbols:visibility'
//   if (editorStore.mode === 'publish') return 'material-symbols:public'
//   return 'material-symbols:edit'
// }

const getOverlayType = () => {
  if (editorStore.mode === 'preview') return OverlayType.WARNING
  if (editorStore.mode === 'publish') return OverlayType.SUCCESS
  return OverlayType.LOADING
}

// 生命周期
onMounted(() => {
  // 初始化自动保存
  autoSave.init()

  // 尝试恢复上次保存的数据
  autoSave.restore()

  // 初始化模板信息
  initializeTemplateInfo()

  // 监听编辑器变化，更新编辑时间
  editorStore.$subscribe(() => {
    editTime.value = new Date()
  })
})

onUnmounted(() => {
  // 清理工作在 useAutoSave 中自动处理
})
</script>

<style lang="scss" scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--edit-header-height);
  background-color: white;
  border-bottom: 1px solid var(--edit-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  padding: 0 1rem;

  .header-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 0;
    flex-shrink: 0;

    .back {
      display: flex;
      align-items: center;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--gray-100);
      }

      span {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-bold);
        margin-left: 0.2rem;
        color: var(--edit-disabled-text-color);
      }
    }

    .title-section {
      display: flex;
      flex-direction: column;
      margin-left: 1rem;
      min-width: 0;

      .title {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0.5rem 0.8rem;
        border-radius: 4px;
        transition: background-color 0.2s;
        gap: 0.5rem;

        &:hover {
          background-color: var(--gray-100);
        }

        // 填写模式下禁用编辑
        &.title-disabled {
          cursor: default;

          &:hover {
            background-color: transparent;
          }
        }

        span {
          font-weight: var(--font-weight-bold);
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .time-info {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        padding-left: 0.8rem;

        .edit-time {
          color: var(--text-color-secondary);
        }

        .save-time {
          color: var(--text-color-placeholder);
        }

        .saving-indicator {
          display: flex;
          align-items: center;
          gap: 0.3rem;
          color: var(--primary-color);
        }

        .unsaved-indicator {
          display: flex;
          align-items: center;
          gap: 0.3rem;
          color: var(--warning-color);
        }

        .auto-save-config {
          display: flex;
          align-items: center;
          gap: 0.3rem;
          color: var(--text-color-placeholder);
          cursor: help;
          font-size: 0.7rem;

          &:hover {
            color: var(--text-color-secondary);
          }
        }
      }
    }
  }

  .header-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 0.8rem;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
    min-width: 200px; /* 为右侧预留固定空间 */
    justify-content: flex-end;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.8rem;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .header {
    .header-left {
      .title-section {
        .time-info {
          .save-time {
            display: none;
          }
        }
      }
    }

    .header-center {
      .action-buttons {
        gap: 0.5rem;

        :deep(.n-button) {
          padding: 8px 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 0.5rem;

    .header-left {
      .title-section {
        margin-left: 0.5rem;

        .title {
          span {
            max-width: 200px;
          }
        }

        .time-info {
          .saving-indicator,
          .unsaved-indicator {
            span {
              display: none;
            }
          }
        }
      }
    }

    .header-right {
      .action-buttons {
        gap: 0.25rem;

        :deep(.n-button) {
          padding: 6px 8px;
        }
      }
    }
  }

  .mode-status-badge {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 100px;
    gap: 8px;

    .mode-text {
      // 小屏幕上保持文字显示
    }
  }
}

:deep(.n-button-group) {
  .n-button {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;

    // 隐藏按钮文字，只显示图标
    span:not(.n-icon) {
      display: none;
    }
  }
}

// 中间操作按钮只显示图标
.action-buttons {
  .n-button {
    min-width: 40px;

    // 隐藏文字
    span:not(.n-icon) {
      display: none;
    }
  }
}

:deep(.n-divider--vertical) {
  height: 20px;
  margin: 0;
}

// 自定义模式切换按钮样式
.mode-toggle-buttons {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 3px;
  gap: 2px;

  .mode-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    font-weight: 500;
    color: #64748b;

    &:hover {
      background: rgba(255, 255, 255, 0.8);
      color: #334155;
      transform: translateY(-1px);
    }

    &.active {
      background: #ffffff;
      color: #3b82f6;
      box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(59, 130, 246, 0.1);

      .crf-icon {
        color: #3b82f6;
      }
    }

    .crf-icon {
      transition: color 0.2s ease;
    }

    span {
      font-weight: 600;
    }
  }
}

// 填写模式指标样式
.fill-mode-indicators {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 20px;
  background: linear-gradient(135deg, #f8faff, #f0f7ff);
  border: 1px solid #e1f0ff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);

  .fill-metrics-row {
    display: flex;
    align-items: center;
    gap: 20px;

    .progress-section {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 140px;

      .metric-label {
        font-size: 12px;
        font-weight: 500;
        color: #64748b;
        min-width: 40px;
      }

      .progress-container {
        display: flex;
        align-items: center;
        gap: 8px;

        :deep(.n-progress) {
          flex: 1;
          min-width: 80px;

          .n-progress-rail {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
          }

          .n-progress-fill {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
            transition: width 0.6s ease;
          }
        }

        .progress-text {
          font-size: 14px;
          font-weight: 600;
          color: #3b82f6;
          min-width: 35px;
          text-align: right;
        }
      }
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 6px;

      .metric-label {
        font-size: 12px;
        color: #64748b;
        font-weight: 500;
      }

      .metric-value {
        font-size: 16px;
        font-weight: 700;
        color: #1e293b;
        min-width: 25px;
        text-align: center;
      }
    }
  }

  .save-button {
    font-size: 14px;
    padding: 8px 16px;

    .crf-icon {
      margin-right: 4px;
    }

    // 微信绿色样式
    &.wechat-green {
      background: linear-gradient(135deg, #07c160, #06ad56);
      border-color: #07c160;
      color: #ffffff;
      box-shadow: 0 2px 4px rgba(7, 193, 96, 0.2);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #06ad56, #059748);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(7, 193, 96, 0.3);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(7, 193, 96, 0.2);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .crf-icon {
        color: #ffffff;
      }

      // 加载状态
      &.is-loading {
        .crf-icon {
          animation: rotate 1s linear infinite;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 时间信息中的旋转动画
.time-info .rotating {
  animation: rotate 1s linear infinite;
}

// 发布对话框样式
.publish-dialog-content {
  .n-alert {
    p {
      margin: 8px 0;
      font-size: 14px;
    }
  }
}

// 自定义保存按钮样式 - 黄色主题
.save-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #f59e0b;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #ffffff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .crf-icon {
    transition: color 0.2s ease;
    color: #ffffff;
  }

  // 加载状态
  &.is-loading {
    .crf-icon {
      animation: rotate 1s linear infinite;
    }
  }
}

// 侧边栏遮罩层
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
}

// 遮罩层动画
.sidebar-overlay-enter-active,
.sidebar-overlay-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay-enter-from,
.sidebar-overlay-leave-to {
  opacity: 0;
}

.sidebar-overlay-enter-to,
.sidebar-overlay-leave-from {
  opacity: 1;
}

// 侧边栏滑入滑出动画
.sidebar-slide-enter-active,
.sidebar-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-slide-enter-from {
  transform: translateX(100%);
}

.sidebar-slide-leave-to {
  transform: translateX(100%);
}

.sidebar-slide-enter-to,
.sidebar-slide-leave-from {
  transform: translateX(0);
}

// 版本按钮徽章样式
.version-badge {
  :deep(.n-badge-sup) {
    background-color: #409eff;
    border-color: #409eff;
  }
}

.n-button:disabled .version-badge {
  :deep(.n-badge-sup) {
    background-color: #c0c4cc;
    border-color: #c0c4cc;
  }
}
</style>
