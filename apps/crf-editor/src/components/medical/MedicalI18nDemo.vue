<template>
  <div class="medical-i18n-demo">
    <h2>医疗国际化系统演示</h2>

    <!-- 语言切换器 -->
    <div class="language-selector">
      <h3>语言切换</h3>
      <select v-model="selectedLocale" @change="changeLanguage">
        <option
          v-for="locale in availableLocales"
          :key="locale.code"
          :value="locale.code"
        >
          {{ locale.flag }} {{ locale.nativeName }}
        </option>
      </select>
      <p>当前语言: {{ currentLocale }}</p>
    </div>

    <!-- 医疗术语翻译演示 -->
    <div class="medical-terms">
      <h3>医疗术语翻译</h3>
      <div class="term-examples">
        <div class="term-item">
          <strong>体温:</strong> {{ mt('medical.vitalSigns.temperature') }}
        </div>
        <div class="term-item">
          <strong>心率:</strong> {{ mt('medical.vitalSigns.heartRate') }}
        </div>
        <div class="term-item">
          <strong>血压:</strong> {{ mt('medical.vitalSigns.bloodPressure') }}
        </div>
        <div class="term-item">
          <strong>体重:</strong> {{ mt('medical.measurements.weight') }}
        </div>
        <div class="term-item">
          <strong>身高:</strong> {{ mt('medical.measurements.height') }}
        </div>
      </div>
    </div>

    <!-- 医疗数据格式化演示 -->
    <div class="medical-formatting">
      <h3>医疗数据格式化</h3>
      <div class="format-examples">
        <div class="format-item">
          <strong>体温:</strong>
          {{ formatTemperature(37.2, currentLocale, 'celsius') }}
        </div>
        <div class="format-item">
          <strong>体重:</strong> {{ formatWeight(70.5, currentLocale, 'kg') }}
        </div>
        <div class="format-item">
          <strong>身高:</strong> {{ formatHeight(175, currentLocale, 'cm') }}
        </div>
        <div class="format-item">
          <strong>血压:</strong>
          {{ formatBloodPressure(120, 80, currentLocale) }}
        </div>
        <div class="format-item">
          <strong>心率:</strong> {{ formatHeartRate(72, currentLocale) }}
        </div>
      </div>
    </div>

    <!-- 医疗表单验证演示 -->
    <div class="medical-validation">
      <h3>医疗表单验证</h3>
      <div class="validation-form">
        <div class="form-field">
          <label>体温 (°C):</label>
          <input
            v-model.number="formData.temperature"
            type="number"
            step="0.1"
            @blur="validateTemperature"
          />
          <span v-if="validationErrors.temperature" class="error">
            {{ validationErrors.temperature }}
          </span>
        </div>

        <div class="form-field">
          <label>体重 (kg):</label>
          <input
            v-model.number="formData.weight"
            type="number"
            step="0.1"
            @blur="validateWeight"
          />
          <span v-if="validationErrors.weight" class="error">
            {{ validationErrors.weight }}
          </span>
        </div>

        <div class="form-field">
          <label>心率 (次/分):</label>
          <input
            v-model.number="formData.heartRate"
            type="number"
            @blur="validateHeartRate"
          />
          <span v-if="validationErrors.heartRate" class="error">
            {{ validationErrors.heartRate }}
          </span>
        </div>

        <button @click="validateForm" class="validate-btn">验证表单</button>
      </div>
    </div>

    <!-- 医疗术语搜索演示 -->
    <div class="medical-search">
      <h3>医疗术语搜索</h3>
      <div class="search-form">
        <input
          v-model="searchQuery"
          placeholder="搜索医疗术语..."
          @input="searchTerms"
        />
        <div v-if="searchResults.length > 0" class="search-results">
          <div
            v-for="result in searchResults"
            :key="result.id"
            class="search-result"
          >
            <strong>{{ result.primary }}</strong>
            <span v-if="result.secondary"> ({{ result.secondary }})</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useMedicalI18n } from '@crf/i18n-medical'

// 使用医疗国际化
const {
  mt,
  currentLocale,
  availableLocales,
  setLocale,
  formatTemperature,
  formatWeight,
  formatHeight,
  formatBloodPressure,
  formatHeartRate,
  validateField,
  searchTerms: searchMedicalTerms,
} = useMedicalI18n()

// 语言切换
const selectedLocale = ref(currentLocale.value)

async function changeLanguage() {
  try {
    await setLocale(selectedLocale.value)
  } catch (error) {
    console.error('语言切换失败:', error)
    // 回退到默认语言
    selectedLocale.value = currentLocale.value
  }
}

// 表单数据和验证
const formData = reactive({
  temperature: 37.2,
  weight: 70.5,
  heartRate: 72,
})

const validationErrors = reactive({
  temperature: '',
  weight: '',
  heartRate: '',
})

function validateTemperature() {
  const result = validateField(formData.temperature, [
    'required',
    'temperature',
  ])
  validationErrors.temperature = result.valid
    ? ''
    : result.message || '体温值无效'
}

function validateWeight() {
  const result = validateField(formData.weight, ['required', 'weight'])
  validationErrors.weight = result.valid ? '' : result.message || '体重值无效'
}

function validateHeartRate() {
  const result = validateField(formData.heartRate, ['required', 'heartRate'])
  validationErrors.heartRate = result.valid
    ? ''
    : result.message || '心率值无效'
}

function validateForm() {
  validateTemperature()
  validateWeight()
  validateHeartRate()

  const hasErrors = Object.values(validationErrors).some(
    (error) => error !== '',
  )
  if (!hasErrors) {
    alert('表单验证通过！')
  }
}

// 术语搜索
const searchQuery = ref('')
const searchResults = ref([])

async function searchTerms() {
  if (searchQuery.value.trim()) {
    try {
      const results = await searchMedicalTerms(searchQuery.value, {
        limit: 5,
      })
      searchResults.value = results
    } catch (error) {
      console.error('搜索失败:', error)
      searchResults.value = []
    }
  } else {
    searchResults.value = []
  }
}
</script>

<style scoped>
.medical-i18n-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.medical-i18n-demo h2 {
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.medical-i18n-demo h3 {
  color: #34495e;
  margin-top: 30px;
  margin-bottom: 15px;
}

.language-selector {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.language-selector select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
}

.term-examples,
.format-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.term-item,
.format-item {
  background: #fff;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.validation-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.form-field {
  margin-bottom: 15px;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-field input {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.error {
  color: #e74c3c;
  font-size: 14px;
  margin-left: 10px;
}

.validate-btn {
  background: #3498db;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.validate-btn:hover {
  background: #2980b9;
}

.search-form input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.search-results {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.search-result {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.search-result:last-child {
  border-bottom: none;
}

.search-result:hover {
  background: #f8f9fa;
}
</style>
