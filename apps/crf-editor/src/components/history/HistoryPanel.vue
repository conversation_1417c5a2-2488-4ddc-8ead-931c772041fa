<template>
  <div class="history-panel">
    <!-- 工具栏 -->
    <div class="history-toolbar">
      <div class="toolbar-left">
        <n-button :disabled="!canUndo" @click="handleUndo" size="small">
          <template #icon>
            <CrfIcon name="arrow-back" />
          </template>
          撤销
        </n-button>

        <n-button :disabled="!canRedo" @click="handleRedo" size="small">
          <template #icon>
            <CrfIcon name="arrow-forward" />
          </template>
          重做
        </n-button>

        <n-divider vertical />

        <n-tooltip trigger="hover">
          <template #trigger>
            <n-badge :value="historySize" class="history-badge">
              <n-button size="small">
                <template #icon>
                  <CrfIcon name="document" />
                </template>
                历史
              </n-button>
            </n-badge>
          </template>
          历史记录统计
        </n-tooltip>
      </div>

      <div class="toolbar-right">
        <n-dropdown @select="handleCommand">
          <n-button size="small">
            <template #icon>
              <CrfIcon name="ellipsis-horizontal" />
            </template>
            更多
            <template #icon-right>
              <CrfIcon name="chevron-down" />
            </template>
          </n-button>
          <template #default>
            <n-dropdown-option key="compress">压缩历史</n-dropdown-option>
            <n-dropdown-option key="export">导出历史</n-dropdown-option>
            <n-dropdown-option key="import">导入历史</n-dropdown-option>
            <n-dropdown-divider />
            <n-dropdown-option key="clear">清空历史</n-dropdown-option>
          </template>
        </n-dropdown>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="history-search" v-if="showSearch">
      <n-input
        v-model:value="searchQuery"
        placeholder="搜索历史记录..."
        size="small"
        clearable
      >
        <template #prefix>
          <CrfIcon name="search" />
        </template>
      </n-input>
    </div>

    <!-- 历史记录列表 -->
    <div class="history-list" ref="historyListRef">
      <div class="history-timeline">
        <div
          v-for="(entry, index) in displayedHistory"
          :key="entry.id"
          class="history-item"
          :class="{
            'is-current': entry.id === (current as Record<string, unknown>)?.id,
            'is-future': isFutureEntry(entry),
            'is-compressed': isCompressed(entry.id),
          }"
          @click="jumpToEntry(entry)"
        >
          <!-- 时间线指示器 -->
          <div class="timeline-indicator">
            <div class="timeline-dot"></div>
            <div
              v-if="index < displayedHistory.length - 1"
              class="timeline-line"
            ></div>
          </div>

          <!-- 历史内容 -->
          <div class="history-content">
            <div class="history-header">
              <span class="history-action">{{
                getActionIcon(entry.action)
              }}</span>
              <span class="history-description">{{ entry.description }}</span>
              <span class="history-time">{{
                formatTime(entry.timestamp)
              }}</span>
            </div>

            <div class="history-meta" v-if="entry.metadata && showMetadata">
              <n-tag size="small" type="info">
                {{ formatSize(entry.metadata.size) }}
              </n-tag>
              <n-tag v-if="entry.metadata.component" size="small">
                {{ entry.metadata.component }}
              </n-tag>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="displayedHistory.length === 0" class="history-empty">
          <n-empty description="暂无历史记录" size="large" />
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="history-stats" v-if="showStats">
      <n-descriptions :column="2" size="small" bordered>
        <n-descriptions-item label="总条数">
          {{ historyStats.totalEntries }}
        </n-descriptions-item>
        <n-descriptions-item label="内存使用">
          {{ formatSize(historyStats.memoryUsage) }}
        </n-descriptions-item>
        <n-descriptions-item label="最频繁操作">
          {{ historyStats.mostFrequentAction || '无' }}
        </n-descriptions-item>
        <n-descriptions-item label="压缩比">
          {{ Math.round(historyStats.compressionRatio * 100) }}%
        </n-descriptions-item>
      </n-descriptions>
    </div>

    <!-- 分支管理 (如果启用) -->
    <div class="history-branches" v-if="config.enableBranching">
      <n-divider>历史分支</n-divider>

      <div class="branch-controls">
        <n-select
          v-model:value="currentBranch"
          @update:value="handleBranchChange"
          size="small"
        >
          <n-option
            v-for="branchName in branchNames"
            :key="branchName"
            :label="branchName"
            :value="branchName"
          />
        </n-select>

        <n-button @click="showCreateBranch = true" size="small">
          <template #icon>
            <CrfIcon name="add" />
          </template>
          新建分支
        </n-button>
      </div>
    </div>

    <!-- 创建分支对话框 -->
    <n-modal v-model:show="showCreateBranch" preset="dialog" title="创建新分支">
      <n-form @submit.prevent="handleCreateBranch">
        <n-form-item label="分支名称">
          <n-input
            v-model:value="newBranchName"
            placeholder="请输入分支名称"
            maxlength="20"
            show-count
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-button @click="showCreateBranch = false">取消</n-button>
        <n-button type="primary" @click="handleCreateBranch">创建</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useHistoryManager } from '@/composables/history/useHistoryManager'
import { useDebouncedSearch } from '@/composables/usePerformance'
import { useMessage, useDialog, NDropdown } from 'naive-ui'
import { CrfIcon } from '@crf/ui-components'

// Props
interface Props {
  showSearch?: boolean
  showMetadata?: boolean
  showStats?: boolean
  maxDisplayItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true,
  showMetadata: true,
  showStats: false,
  maxDisplayItems: 50,
})

// Naive UI 实例
const message = useMessage()
const dialog = useDialog()

// 历史管理器
const historyManager = useHistoryManager({
  maxSize: 100,
  autoCompress: true,
  compressAfter: 20,
  enableBranching: true,
  trackMetadata: true,
})

const {
  // past,
  // future,
  // current,
  canUndo,
  canRedo,
  // historySize,
  // historyStats,
  // branches,
  // currentBranch,
  // config,
  undo,
  redo,
  // jumpTo,
  // compressHistory,
  // createBranch,
  // switchBranch,
  getHistory,
  // searchHistory,
  clear,
  // exportHistory,
  // importHistory
} = historyManager

// 本地状态
const searchQuery = ref('')
const showCreateBranch = ref(false)
const newBranchName = ref('')
const historyListRef = ref<HTMLElement>()
const currentBranch = ref('main')
const config = ref({ enableBranching: true })
const historyStats = ref({
  totalEntries: 0,
  compressedEntries: 0,
  compressionRatio: 0,
  memoryUsage: 0,
  mostFrequentAction: '',
})
const branches = ref(['main'])
const current = ref(null)
const historySize = ref(0)
const future = ref([])

// 搜索功能
const { searchResults } = useDebouncedSearch(
  computed(() => getHistory()),
  ['description', 'action'],
  300,
)

// 显示的历史记录
const displayedHistory = computed(() => {
  const history = searchQuery.value ? searchResults.value : getHistory()
  return history.slice(-props.maxDisplayItems).reverse()
})

// 分支名称列表
const branchNames = computed(() => {
  return Array.from(branches.value.keys()).sort()
})

// 方法
const handleUndo = () => {
  const entry = undo()
  if (entry) {
    message.success(`已撤销: ${entry.description}`)
  }
}

const handleRedo = () => {
  const entry = redo()
  if (entry) {
    message.success(`已重做: ${entry.description}`)
  }
}

const jumpToEntry = (_entry: Record<string, unknown>) => {
  // TODO: 实现跳转功能
  message.info('跳转功能开发中...')
}

const isFutureEntry = (entry: Record<string, unknown>) => {
  return future.value.some((f: Record<string, unknown>) => f.id === entry.id)
}

const isCompressed = (_entryId: string) => {
  // 这里需要从历史管理器获取压缩信息
  return false // 简化实现
}

const getActionIcon = (action: string) => {
  const iconMap: Record<string, string> = {
    add_section: '📄',
    delete_section: '🗑️',
    update_section: '✏️',
    add_block: '➕',
    delete_block: '❌',
    update_block: '🔧',
    update_form_data: '📝',
    drag_move: '🔄',
    init: '🚀',
  }
  return iconMap[action] || '📋'
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

const formatSize = (bytes: number | undefined) => {
  if (!bytes) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'compress':
      // TODO: 实现压缩功能
      message.info('压缩功能开发中...')
      break

    case 'export':
      try {
        // TODO: 实现导出功能
        const data = JSON.stringify(getHistory(), null, 2)
        const blob = new Blob([data], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `history-${Date.now()}.json`
        a.click()
        URL.revokeObjectURL(url)
        message.success('历史记录已导出')
      } catch (error) {
        message.error('导出失败')
      }
      break

    case 'import':
      // 这里应该打开文件选择器
      message.info('请选择历史记录文件')
      break

    case 'clear':
      try {
        await new Promise((resolve, reject) => {
          dialog.warning({
            title: '确认清空',
            content: '确定要清空所有历史记录吗？此操作不可恢复。',
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => resolve(true),
            onNegativeClick: () => reject(new Error('用户取消')),
          })
        })
        clear()
        message.success('历史记录已清空')
      } catch {
        // 用户取消
      }
      break
  }
}

const handleBranchChange = (_branchName: string) => {
  // TODO: 实现分支切换功能
  message.info('分支功能开发中...')
}

const handleCreateBranch = () => {
  if (!newBranchName.value.trim()) {
    message.warning('请输入分支名称')
    return
  }

  // TODO: 实现分支创建功能
  message.info('分支功能开发中...')
  showCreateBranch.value = false
  newBranchName.value = ''
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key.toLowerCase()) {
      case 'z':
        if (event.shiftKey) {
          handleRedo()
        } else {
          handleUndo()
        }
        event.preventDefault()
        break
      case 'y':
        handleRedo()
        event.preventDefault()
        break
    }
  }
}

// 监听键盘事件
watch(
  () => props,
  () => {
    document.addEventListener('keydown', handleKeydown)
  },
  { immediate: true },
)

// 暴露给父组件
defineExpose({
  historyManager,
})
</script>

<style scoped lang="scss">
.history-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--n-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.history-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--n-border-color-light);
  background: var(--n-bg-color-page);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .history-badge {
    .n-badge-sup {
      font-size: 10px;
    }
  }
}

.history-search {
  padding: 12px 16px;
  border-bottom: 1px solid var(--n-border-color-lighter);
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.history-timeline {
  position: relative;
}

.history-item {
  display: flex;
  margin-bottom: 16px;
  cursor: pointer;
  border-radius: 6px;
  padding: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--n-color-primary-light-9);
  }

  &.is-current {
    background: var(--n-color-primary-light-8);
    border-left: 3px solid var(--n-color-primary);
  }

  &.is-future {
    opacity: 0.6;
  }

  &.is-compressed {
    .timeline-dot {
      background: var(--n-color-warning);
    }
  }
}

.timeline-indicator {
  position: relative;
  margin-right: 12px;
  width: 20px;

  .timeline-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--n-color-primary);
    margin-top: 6px;
  }

  .timeline-line {
    position: absolute;
    left: 3px;
    top: 14px;
    width: 2px;
    height: calc(100% + 8px);
    background: var(--n-border-color-light);
  }
}

.history-content {
  flex: 1;
  min-width: 0;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;

  .history-action {
    font-size: 16px;
  }

  .history-description {
    flex: 1;
    font-weight: 500;
    color: var(--n-text-color-base);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .history-time {
    font-size: 12px;
    color: var(--n-text-color-2);
  }
}

.history-meta {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.history-empty {
  text-align: center;
  padding: 40px 20px;
}

.history-stats {
  padding: 16px;
  border-top: 1px solid var(--n-border-color-light);
  background: var(--n-bg-color-page);
}

.history-branches {
  padding: 16px;
  border-top: 1px solid var(--n-border-color-light);

  .branch-controls {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .history-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;

    .toolbar-left,
    .toolbar-right {
      justify-content: center;
    }
  }

  .history-item {
    .history-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
