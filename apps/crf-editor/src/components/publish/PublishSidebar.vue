<template>
  <div class="publish-sidebar">
    <div class="sidebar-header">
      <div class="title">发布任务</div>
      <button class="close-btn" @click="$emit('close')">
        <crf-icon icon="material-symbols:close" size="20px" />
      </button>
    </div>

    <div class="sidebar-content">
      <!-- 生成版本 -->
      <div class="section">
        <div class="section-title">生成版本</div>
        <div class="version-tag">{{ currentVersion || 'V1.0.0' }}</div>
      </div>

      <!-- 任务发布检验 -->
      <div class="section">
        <div class="section-title">任务发布检验</div>
        <div class="validation-list">
          <div
            v-for="item in validationItems"
            :key="item.id"
            class="validation-item"
            :class="{
              success: item.passed,
              pending: !item.passed,
              loading: item.loading,
            }"
          >
            <crf-icon
              v-if="item.loading"
              icon="material-symbols:sync"
              size="16px"
              class="loading-icon"
            />
            <crf-icon
              v-else
              :icon="
                item.passed
                  ? 'material-symbols:check-circle'
                  : 'material-symbols:radio-button-unchecked'
              "
              size="16px"
              :class="{
                'success-icon': item.passed,
                'pending-icon': !item.passed,
              }"
            />
            <span>{{ item.text }}</span>
          </div>

          <div
            v-if="!allValidationsPassed && !isValidating"
            class="validation-error"
          >
            <crf-icon icon="material-symbols:error-outline" size="16px" />
            <span>未存在调用任务</span>
          </div>
        </div>
      </div>

      <!-- 变更描述 -->
      <div class="section">
        <div class="section-title">变更描述</div>
        <div class="description-tips">
          <crf-icon icon="material-symbols:info-outline" size="14px" />
          <span>请简要描述本次发布的主要变更内容</span>
        </div>
        <n-input
          v-model:value="publishDescription"
          type="textarea"
          :rows="4"
          placeholder="请输入变更描述，例如：
• 新增用户信息字段
• 优化表单验证逻辑
• 修复已知问题"
          class="description-input"
          maxlength="500"
          show-count
        />
        <div class="description-examples">
          <div class="examples-title">常用模板：</div>
          <div class="example-tags">
            <span
              v-for="example in descriptionExamples"
              :key="example"
              class="example-tag"
              @click="useDescriptionExample(example)"
            >
              {{ example }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="sidebar-footer">
      <n-button
        type="primary"
        :disabled="!allValidationsPassed || !publishDescription.trim()"
        :loading="props.loading"
        @click="handlePublish"
        class="publish-btn"
      >
        发布内容
      </n-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { CrfIcon } from '@crf/ui-components'
import { templateAPI } from '@/api'

interface ValidationItem {
  id: string
  text: string
  passed: boolean
  loading?: boolean
}

interface Props {
  templateId: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  close: []
  publish: [description: string]
}>()

// 添加message实例
const message = useMessage()

// 响应式数据
const publishDescription = ref('')
const currentVersion = ref('')
const isValidating = ref(false)
const validationItems = ref<ValidationItem[]>([
  { id: '1', text: '当前任务配置完毕', passed: true, loading: false },
  { id: '2', text: '调用任务发布检验', passed: false, loading: true },
])

// 描述示例
const descriptionExamples = ref(['新增字段', '新增章节', '调整结构'])

// 使用描述示例
const useDescriptionExample = (example: string) => {
  if (publishDescription.value) {
    publishDescription.value += `\n• ${example}`
  } else {
    publishDescription.value = `• ${example}`
  }
}

// 计算属性
const allValidationsPassed = computed(() => {
  return validationItems.value.every((item) => item.passed)
})

// 模拟校验过程
const startValidation = () => {
  isValidating.value = true

  // 模拟第二个校验项的加载过程
  setTimeout(() => {
    const item = validationItems.value.find((item) => item.id === '2')
    if (item) {
      item.loading = false
      item.passed = true
    }
    isValidating.value = false
  }, 2000) // 2秒后完成校验
}

// 方法
const handlePublish = async () => {
  if (!publishDescription.value.trim()) {
    message.warning('请填写变更描述')
    return
  }

  if (!allValidationsPassed.value) {
    message.warning('请先完成所有发布检验')
    return
  }

  emit('publish', publishDescription.value.trim())
}

// 自动生成版本号（基于git tag）
const generateVersion = async () => {
  try {
    // 获取当前模板信息
    const response = await templateAPI.getTemplate(props.templateId)
    const templateInfo = response.data?.template

    // 获取git tag信息（如果有的话）
    let baseVersion = templateInfo?.version || '1.0.0'

    // 尝试从git tag获取最新版本
    try {
      // 这里可以调用git API获取最新tag
      // 暂时使用模板版本作为基础
      const versionsResponse = await templateAPI.getTemplateVersions(
        props.templateId,
      )
      const existingVersions = versionsResponse.data?.versions
      if (existingVersions && existingVersions.length > 0) {
        const latestPublished = existingVersions
          .filter((v) => v.status === 'published')
          .sort(
            (a, b) =>
              new Date(b.published_at || b.created_at).getTime() -
              new Date(a.published_at || a.created_at).getTime(),
          )[0]

        if (latestPublished) {
          baseVersion = latestPublished.version
        }
      }
    } catch (error) {
      console.warn('无法获取git tag信息，使用模板版本:', error)
    }

    // 自动递增patch版本
    const versionParts = baseVersion.split('.')
    const majorVersion = parseInt(versionParts[0]) || 1
    const minorVersion = parseInt(versionParts[1]) || 0
    const patchVersion = parseInt(versionParts[2]) || 0

    const newVersion = `${majorVersion}.${minorVersion}.${patchVersion + 1}`
    currentVersion.value = `V${newVersion}`
  } catch (error) {
    console.error('生成版本号失败:', error)
    currentVersion.value = 'V1.0.1'
  }
}

// 检查表单验证
const checkValidations = async () => {
  try {
    // 获取当前模板信息
    const response = await templateAPI.getTemplate(props.templateId)
    const templateInfo = response.data?.template

    const validations: ValidationItem[] = []

    // 检查1: 表单标题
    if (templateInfo?.title && templateInfo.title.trim()) {
      validations.push({ id: '1', text: '表单标题已设置', passed: true })
    } else {
      validations.push({ id: '1', text: '表单标题未设置', passed: false })
    }

    // 检查2: 表单结构
    if (
      templateInfo?.form_structure &&
      Object.keys(templateInfo.form_structure).length > 0
    ) {
      validations.push({ id: '2', text: '表单结构已配置', passed: true })
    } else {
      validations.push({ id: '2', text: '表单结构为空', passed: false })
    }

    // 检查3: 表单字段
    const formFields = templateInfo?.form_structure?.fields || []
    if (formFields.length > 0) {
      validations.push({ id: '3', text: '表单字段已添加', passed: true })
    } else {
      validations.push({ id: '3', text: '表单字段为空', passed: false })
    }

    // 检查4: 必填字段验证
    const requiredFields = formFields.filter((field) => field.required)
    if (requiredFields.length > 0) {
      validations.push({ id: '4', text: '必填字段已设置', passed: true })
    } else {
      validations.push({ id: '4', text: '建议设置必填字段', passed: false })
    }

    // 检查5: 表单描述
    if (templateInfo?.description && templateInfo.description.trim()) {
      validations.push({ id: '5', text: '表单描述已添加', passed: true })
    } else {
      validations.push({ id: '5', text: '表单描述未设置', passed: false })
    }

    validationItems.value = validations
  } catch (error) {
    console.error('检查表单验证失败:', error)
    // 使用默认检查项
    validationItems.value = [
      { id: '1', text: '当前任务配置检查', passed: false },
      { id: '2', text: '表单结构验证', passed: false },
    ]
  }
}

onMounted(() => {
  generateVersion()
  startValidation()
})
</script>

<style lang="scss" scoped>
.publish-sidebar {
  width: 400px;
  height: 100vh;
  background: #ffffff;
  border-left: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1001;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;

    &:hover {
      background: #e5e7eb;
      color: #374151;
    }
  }
}

.sidebar-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
  }
}

.version-tag {
  display: inline-block;
  background: #dbeafe;
  color: #1d4ed8;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #bfdbfe;
  min-height: 32px;
  line-height: 20px;
  min-width: 60px;
  text-align: center;
}

.validation-list {
  .validation-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    font-size: 14px;

    &.success {
      color: #059669;

      .success-icon {
        color: #10b981;
      }
    }

    &.pending {
      color: #6b7280;

      .pending-icon {
        color: #9ca3af;
      }
    }

    &.loading {
      color: #3b82f6;

      .loading-icon {
        color: #3b82f6;
        animation: spin 1s linear infinite;
      }
    }
  }

  .validation-error {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #fef3f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    margin-top: 8px;
    font-size: 14px;
    color: #dc2626;
  }
}

.description-input {
  :deep(.n-input__textarea-el) {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.description-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 6px;
  font-size: 12px;
  color: #0369a1;
}

.description-examples {
  margin-top: 12px;

  .examples-title {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
  }

  .example-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .example-tag {
      display: inline-block;
      padding: 4px 8px;
      background: #f3f4f6;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      font-size: 12px;
      color: #4b5563;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #e5e7eb;
        border-color: #d1d5db;
        color: #374151;
      }
    }
  }
}

.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;

  .publish-btn {
    width: 100%;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s;

    &:not(:disabled) {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-color: #3b82f6;

      &:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .publish-sidebar {
    width: 100vw;
    left: 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
