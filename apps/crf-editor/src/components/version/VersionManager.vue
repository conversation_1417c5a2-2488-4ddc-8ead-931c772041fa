<template>
  <div class="version-manager">
    <!-- 头部信息 -->
    <div class="version-header">
      <div class="version-info">
        <h3>版本管理</h3>
        <div class="current-version">
          <span class="label">当前版本:</span>
          <n-tag :type="versionTagType">{{
            versionManager.currentVersion
          }}</n-tag>
          <n-tag
            v-if="versionManager.hasUnpublishedChanges"
            type="warning"
            size="small"
          >
            未发布
          </n-tag>
        </div>
      </div>

      <div class="version-actions">
        <n-button
          v-if="versionManager.currentTemplate.value?.status === 'published'"
          @click="handleGetAccessLink"
          type="success"
        >
          <template #icon>
            <n-icon><LinkIcon /></n-icon>
          </template>
          获取访问链接
        </n-button>
        <n-button
          v-if="versionManager.currentTemplate.value?.status === 'published'"
          @click="handleViewData"
          type="info"
        >
          <template #icon>
            <n-icon><AnalyticsIcon /></n-icon>
          </template>
          查看数据
        </n-button>
        <n-button
          @click="handlePublish"
          type="primary"
          :loading="versionManager.isPublishing.value"
          :disabled="!versionManager.hasUnpublishedChanges.value"
        >
          <template #icon>
            <n-icon><CloudUploadIcon /></n-icon>
          </template>
          发布版本
        </n-button>
        <n-button @click="handleCreateVersion" :loading="createVersionLoading">
          <template #icon>
            <n-icon><AddIcon /></n-icon>
          </template>
          创建版本
        </n-button>
        <n-button @click="handleRefresh">
          <template #icon>
            <n-icon><RefreshIcon /></n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 版本统计 -->
    <div class="version-stats">
      <div class="stat-item">
        <div class="stat-number">{{ stats.total }}</div>
        <div class="stat-label">总版本数</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ stats.published }}</div>
        <div class="stat-label">已发布</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ stats.draft }}</div>
        <div class="stat-label">草稿</div>
      </div>
    </div>

    <!-- 版本列表 -->
    <div class="version-list">
      <n-tabs v-model:value="activeTab" type="card">
        <n-tab-pane label="所有版本" name="all">
          <version-list-panel
            :versions="versionManager.versions.value"
            :current-version="versionManager.currentVersion.value"
            @rollback="handleRollback"
            @compare="handleCompare"
            @view-details="handleViewDetails"
          />
        </n-tab-pane>
        <n-tab-pane label="已发布" name="published">
          <version-list-panel
            :versions="versionManager.publishedVersions.value"
            :current-version="versionManager.currentVersion.value"
            @rollback="handleRollback"
            @compare="handleCompare"
            @view-details="handleViewDetails"
          />
        </n-tab-pane>
        <n-tab-pane label="草稿版本" name="draft">
          <version-list-panel
            :versions="versionManager.draftVersions.value"
            :current-version="versionManager.currentVersion.value"
            @rollback="handleRollback"
            @compare="handleCompare"
            @view-details="handleViewDetails"
          />
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 发布对话框 -->
    <n-modal
      v-model:show="showPublishDialog"
      title="发布版本"
      style="width: 600px"
      :mask-closable="false"
    >
      <n-form
        ref="publishFormRef"
        :model="publishForm"
        :rules="publishRules"
        label-width="80"
      >
        <n-form-item label="版本号" path="version">
          <n-input-group>
            <n-input
              v-model:value="publishForm.version"
              placeholder="如: 1.0.1"
            />
            <n-button @click="autoIncrementVersion('patch')">补丁</n-button>
            <n-button @click="autoIncrementVersion('minor')">次版本</n-button>
            <n-button @click="autoIncrementVersion('major')">主版本</n-button>
          </n-input-group>
        </n-form-item>

        <n-form-item label="描述">
          <n-input
            v-model:value="publishForm.description"
            type="textarea"
            :rows="3"
            placeholder="描述本次发布的主要内容"
          />
        </n-form-item>

        <n-form-item label="变更日志">
          <n-input
            v-model:value="publishForm.changeLog"
            type="textarea"
            :rows="4"
            placeholder="详细的变更记录"
          />
          <div class="changelog-suggestion">
            <n-button @click="generateChangeLog" size="small" text>
              自动生成变更日志
            </n-button>
          </div>
        </n-form-item>
      </n-form>

      <template #action>
        <n-button @click="showPublishDialog = false">取消</n-button>
        <n-button
          @click="confirmPublish"
          type="primary"
          :loading="versionManager.isPublishing.value"
        >
          确认发布
        </n-button>
      </template>
    </n-modal>

    <!-- 创建版本对话框 -->
    <n-modal
      v-model:show="showCreateVersionDialog"
      title="创建新版本"
      style="width: 500px"
      :mask-closable="false"
    >
      <n-form
        ref="createVersionFormRef"
        :model="createVersionForm"
        :rules="createVersionRules"
        label-width="100"
      >
        <n-form-item label="版本号" path="version">
          <n-input
            v-model:value="createVersionForm.version"
            placeholder="如: 1.0.1"
          />
        </n-form-item>

        <n-form-item label="描述">
          <n-input
            v-model:value="createVersionForm.description"
            type="textarea"
            :rows="3"
            placeholder="版本描述"
          />
        </n-form-item>

        <n-form-item label="变更日志">
          <n-input
            v-model:value="createVersionForm.changeLog"
            type="textarea"
            :rows="3"
            placeholder="变更记录"
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-button @click="showCreateVersionDialog = false">取消</n-button>
        <n-button
          @click="confirmCreateVersion"
          type="primary"
          :loading="createVersionLoading"
          >创建</n-button
        >
      </template>
    </n-modal>

    <!-- 版本详情对话框 -->
    <n-modal
      v-model:show="showDetailsDialog"
      title="版本详情"
      style="width: 800px"
    >
      <version-details
        v-if="selectedVersion"
        :version="selectedVersion"
        :template="versionManager.currentTemplate.value"
      />
    </n-modal>

    <!-- 版本比较对话框 -->
    <n-modal
      v-model:show="showCompareDialog"
      title="版本比较"
      style="width: 900px"
      :fullscreen="true"
    >
      <version-compare
        v-if="
          compareVersions.length === 2 &&
          compareVersions[0] &&
          compareVersions[1]
        "
        :version1="compareVersions[0]"
        :version2="compareVersions[1]"
      />
    </n-modal>

    <!-- 访问链接对话框 -->
    <n-modal
      v-model:show="showAccessLinkDialog"
      title="表单访问链接"
      style="width: 600px"
      :mask-closable="false"
    >
      <div class="access-link-content">
        <n-alert
          title="表单已发布"
          type="success"
          :closable="false"
          show-icon
        />

        <div class="link-section">
          <n-form-item label="访问链接">
            <n-input-group>
              <n-input
                v-model:value="accessLinkInfo.access_link"
                readonly
                :placeholder="accessLinkInfo.access_link || '生成中...'"
              />
              <n-button @click="copyAccessLink">
                <template #icon>
                  <n-icon
                    ><crf-icon icon="material-symbols:content-copy" size="14px"
                  /></n-icon>
                </template>
                复制
              </n-button>
            </n-input-group>
          </n-form-item>

          <n-form-item label="完整URL">
            <n-input-group>
              <n-input
                v-model:value="accessLinkInfo.full_url"
                readonly
                :placeholder="accessLinkInfo.full_url || '生成中...'"
              />
              <n-button @click="copyFullUrl">
                <template #icon>
                  <n-icon
                    ><crf-icon icon="material-symbols:content-copy" size="14px"
                  /></n-icon>
                </template>
                复制
              </n-button>
            </n-input-group>
          </n-form-item>
        </div>

        <div class="usage-info">
          <n-alert title="使用说明" type="info" :closable="false">
            <p>用户可以通过此链接直接访问和填写表单，无需登录。</p>
            <p>管理员可以在数据管理中查看所有提交的数据。</p>
          </n-alert>
        </div>
      </div>

      <template #action>
        <n-button @click="showAccessLinkDialog = false">关闭</n-button>
        <n-button type="primary" @click="openFormInNewTab">
          在新窗口中打开
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'
import { useVersionManagement } from '@/composables/useVersionManagement'
import { templateAPI } from '@/api'
import { CrfIcon } from '@crf/ui-components'
import type { CRFVersion } from '@/types/api'
import VersionListPanel from './VersionListPanel.vue'
import VersionDetails from './VersionDetails.vue'
import VersionCompare from './VersionCompare.vue'

interface Props {
  templateId: string
  projectId?: string
}

const props = defineProps<Props>()

// 版本管理器
const versionManager = useVersionManagement({
  templateId: props.templateId,
  projectId: props.projectId,
})

// 添加message和dialog实例
const message = useMessage()
const dialog = useDialog()

// 状态
const activeTab = ref('all')
const showPublishDialog = ref(false)
const showCreateVersionDialog = ref(false)
const showDetailsDialog = ref(false)
const showCompareDialog = ref(false)
const showAccessLinkDialog = ref(false)
const createVersionLoading = ref(false)
const selectedVersion = ref<CRFVersion | null>(null)
const compareVersions = ref<CRFVersion[]>([])
const accessLinkInfo = reactive({
  access_link: '',
  full_url: '',
})

// 表单引用
const publishFormRef = ref<FormInst>()
const createVersionFormRef = ref<FormInst>()

// 发布表单
const publishForm = reactive({
  version: '',
  description: '',
  changeLog: '',
})

const publishRules: FormRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: '版本号格式应为 x.y.z',
      trigger: 'blur',
    },
  ],
}

// 创建版本表单
const createVersionForm = reactive({
  version: '',
  description: '',
  changeLog: '',
})

const createVersionRules: FormRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: '版本号格式应为 x.y.z',
      trigger: 'blur',
    },
  ],
}

// 计算属性
const stats = computed(() => versionManager.getVersionStats())
const versionTagType = computed(() => {
  if (versionManager.hasUnpublishedChanges.value) return 'warning'
  return versionManager.currentTemplate.value?.status === 'published'
    ? 'success'
    : 'info'
})

// 处理发布
const handlePublish = () => {
  publishForm.version = versionManager.getNextVersion('patch')
  publishForm.description = ''
  publishForm.changeLog = versionManager.generateChangeLog()
  showPublishDialog.value = true
}

// 自动递增版本号
const autoIncrementVersion = (type: 'major' | 'minor' | 'patch') => {
  publishForm.version = versionManager.getNextVersion(type)
}

// 生成变更日志
const generateChangeLog = () => {
  publishForm.changeLog = versionManager.generateChangeLog()
}

// 确认发布
const confirmPublish = async () => {
  if (!publishFormRef.value) return

  try {
    await publishFormRef.value.validate()

    await versionManager.publishTemplate({
      version: publishForm.version,
      description: publishForm.description,
      changeLog: publishForm.changeLog,
    })

    showPublishDialog.value = false
    message.success('版本发布成功')
  } catch (error) {
    console.error('发布失败:', error)
    message.error('发布失败')
  }
}

// 处理创建版本对话框
const handleCreateVersion = () => {
  // 重置表单
  createVersionForm.version = ''
  createVersionForm.description = ''
  createVersionForm.changeLog = ''

  // 显示对话框
  showCreateVersionDialog.value = true
}

// 确认创建版本
const confirmCreateVersion = async () => {
  if (!createVersionFormRef.value) return

  createVersionLoading.value = true
  try {
    await createVersionFormRef.value.validate()

    await versionManager.createVersion(
      versionManager.currentTemplate.value.id,
      {
        version: createVersionForm.version,
        description: createVersionForm.description,
        change_log: createVersionForm.changeLog,
      },
    )

    showCreateVersionDialog.value = false
    await handleRefresh()

    message.success('版本创建成功')
  } catch (error) {
    console.error('创建版本失败:', error)
    message.error(`创建版本失败: ${error}`)
  } finally {
    createVersionLoading.value = false
  }
}

// 处理回滚
const handleRollback = async (version: CRFVersion) => {
  try {
    await new Promise<void>((resolve, reject) => {
      dialog.warning({
        title: '确认回滚',
        content: `确定要回滚到版本 ${version.version} 吗？当前未保存的更改将丢失。`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          resolve()
        },
        onNegativeClick: () => {
          reject('cancel')
        },
      })
    })

    await versionManager.rollbackToVersion(version.id)
    message.success('回滚成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('回滚失败:', error)
      message.error('回滚失败')
    }
  }
}

// 处理比较
const handleCompare = (versions: CRFVersion[]) => {
  if (versions.length !== 2) {
    message.warning('请选择两个版本进行比较')
    return
  }

  compareVersions.value = versions
  showCompareDialog.value = true
}

// 查看详情
const handleViewDetails = (version: CRFVersion) => {
  selectedVersion.value = version
  showDetailsDialog.value = true
}

// 刷新
const handleRefresh = async () => {
  try {
    await versionManager.loadVersions()
    message.success('刷新成功')
  } catch (error) {
    message.error('刷新失败')
  }
}

// 获取访问链接
const handleGetAccessLink = async () => {
  try {
    const response = await templateAPI.getAccessLink(props.templateId)
    if (response.success && response.data) {
      accessLinkInfo.access_link = response.data.access_link
      accessLinkInfo.full_url = response.data.full_url
      showAccessLinkDialog.value = true
    }
  } catch (error) {
    console.error('获取访问链接失败:', error)
    message.error('获取访问链接失败')
  }
}

// 复制访问链接
const copyAccessLink = async () => {
  try {
    await navigator.clipboard.writeText(accessLinkInfo.access_link)
    message.success('访问链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

// 复制完整URL
const copyFullUrl = async () => {
  try {
    await navigator.clipboard.writeText(accessLinkInfo.full_url)
    message.success('完整URL已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

// 在新窗口中打开表单
const openFormInNewTab = () => {
  if (accessLinkInfo.full_url) {
    window.open(accessLinkInfo.full_url, '_blank')
  }
}

// 查看填写数据
const handleViewData = () => {
  // 跳转到实例管理页面
  window.open(`/instances?template_id=${props.templateId}`, '_blank')
}

// 初始化
onMounted(async () => {
  try {
    await versionManager.init()
  } catch (error) {
    console.error('版本管理器初始化失败:', error)
    message.error('加载版本信息失败')
  }
})
</script>

<style lang="scss" scoped>
.version-manager {
  padding: 20px;

  .version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .version-info {
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
      }

      .current-version {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          font-size: 14px;
          color: #666;
        }
      }
    }

    .version-actions {
      display: flex;
      gap: 8px;
    }
  }

  .version-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      padding: 12px 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #6c757d;
      }
    }
  }

  .version-list {
    :deep(.n-tabs__content) {
      padding: 0;
    }
  }

  .changelog-suggestion {
    margin-top: 8px;
    text-align: right;
  }

  .access-link-content {
    .link-section {
      margin: 20px 0;

      .n-form-item {
        margin-bottom: 16px;
      }
    }

    .usage-info {
      margin-top: 20px;

      p {
        margin: 8px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
