<template>
  <span class="version-icon-wrapper" :class="{ active }">
    <VersionIcon
      size="16"
      version="v"
      :background-color="active ? '#409eff' : '#666'"
      :border-color="active ? '#409eff' : '#666'"
      text-color="#ffffff"
    />
  </span>
</template>

<script setup lang="ts">
import { VersionIcon } from '@/assets/icons'

interface Props {
  active?: boolean
}

withDefaults(defineProps<Props>(), {
  active: false,
})
</script>

<style scoped>
.version-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: color 0.2s;
}

.version-icon-wrapper.active {
  color: #409eff;
}

.version-icon-wrapper:hover {
  color: #409eff;
}
</style>
