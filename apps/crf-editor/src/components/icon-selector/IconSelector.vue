<template>
  <div class="w-full" @click.stop>
    <n-popover
      v-model:show="state.popoverVisible"
      placement="bottom-start"
      :style="{ width: '360px' }"
      trigger="click"
      raw
      :content-style="{ padding: '0px' }"
      @click.stop
    >
      <template #trigger>
        <div
          class="flex items-center justify-between px-3 py-1 border border-[#dcdfe6] rounded cursor-pointer transition-all duration-200 bg-white h-[34px] hover:border-[#c0c4cc]"
          @click.stop="togglePopover"
        >
          <div class="flex items-center gap-2 flex-1">
            <div
              v-if="modelValue"
              class="flex items-center justify-center w-6 h-6 rounded"
              :style="{ backgroundColor: state.selectedColor }"
            >
              <n-icon :size="20" color="#fff">
                <component :is="iconComponents[modelValue]" />
              </n-icon>
            </div>
            <span v-else class="text-[#a8abb2] text-sm">选择图标</span>
          </div>
          <n-icon
            class="text-[#a8abb2] transition-transform duration-200"
            :class="{ 'rotate-180': popoverVisible }"
          >
            <ArrowDownOutlined />
          </n-icon>
        </div>
      </template>

      <div
        class="p-3 max-w-[360px] max-h-[320px] overflow-hidden flex flex-col bg-white rounded shadow-[0_2px_12px_0_rgba(0,0,0,0.1)]"
        @click.stop
      >
        <div class="mb-3 flex-shrink-0">
          <n-input
            v-model:value="searchQuery"
            placeholder="搜索图标..."
            size="small"
            clearable
            @click.stop
          >
            <template #prefix>
              <n-icon><SearchOutlined /></n-icon>
            </template>
          </n-input>
        </div>

        <div class="mb-3 pb-3 border-b border-[#f0f0f0] flex-shrink-0">
          <div class="text-sm text-[#606266] mb-2 font-medium">选择颜色：</div>
          <div class="flex flex-col gap-2">
            <div
              v-for="(row, rowIndex) in colorOptions"
              :key="rowIndex"
              class="flex gap-2 justify-start flex-nowrap"
            >
              <div
                v-for="color in row"
                :key="color.value"
                class="w-6 h-6 rounded-full cursor-pointer transition-all duration-200 flex items-center justify-center border-2 border-transparent flex-shrink-0 relative hover:scale-110"
                :class="{
                  'border-white shadow-[0_0_0_2px_#409eff]':
                    state.selectedColor === color.value,
                }"
                :style="{ backgroundColor: color.value }"
                @click.stop="selectColor(color.value)"
                :title="color.name"
              >
                <n-icon
                  v-if="state.selectedColor === color.value"
                  class="text-white text-xs font-bold"
                >
                  <CheckOutlined />
                </n-icon>
              </div>
            </div>
          </div>
        </div>

        <div
          class="grid grid-cols-6 gap-2 max-h-[150px] overflow-y-auto p-0.5 flex-1 scrollbar-thin scrollbar-track-[#f1f1f1] scrollbar-thumb-[#c1c1c1] hover:scrollbar-thumb-[#a8a8a8] scrollbar-track-rounded scrollbar-thumb-rounded"
        >
          <div
            v-for="iconName in filteredIcons"
            :key="iconName"
            class="flex items-center justify-center p-2 border border-[#e4e7ed] rounded cursor-pointer transition-all duration-200 bg-white hover:bg-[#f5f7fa] hover:border-[#409eff] hover:text-[#409eff]"
            :class="{
              'bg-[#ecf5ff] border-[#409eff] text-[#409eff]':
                modelValue === iconName,
            }"
            @click.stop="selectIcon(iconName)"
          >
            <n-icon :size="24" :color="state.selectedColor">
              <component :is="iconComponents[iconName]" />
            </n-icon>
          </div>
        </div>

        <div
          v-if="filteredIcons.length === 0"
          class="text-center p-5 text-[#909399] flex-1 flex items-center justify-center"
        >
          <n-empty description="未找到匹配的图标" />
        </div>
      </div>
    </n-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { NPopover, NInput, NIcon, NEmpty } from 'naive-ui'
import {
  ArrowDownOutlined,
  SearchOutlined,
  CheckOutlined,
  UserOutlined,
  EditOutlined,
  FileOutlined,
  FolderOutlined,
  SettingOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  StarOutlined,
  BellOutlined,
  MessageOutlined,
  HomeOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  PlusOutlined,
  MinusOutlined,
  CloseOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  LeftOutlined,
  RightOutlined,
  UpOutlined,
  DownOutlined,
  MenuOutlined,
  AppstoreOutlined,
  BarsOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  ToolOutlined,
  KeyOutlined,
  ShareAltOutlined,
  CopyOutlined,
  LinkOutlined,
  FullscreenOutlined,
  AimOutlined,
  CompassOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  DesktopOutlined,
  DatabaseOutlined,
  LineChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  DashboardOutlined,
  ControlOutlined,
  BuildOutlined,
  MonitorOutlined,
  MedicineBoxOutlined,
  SafetyOutlined,
  BookOutlined,
  ReadOutlined,
  FormOutlined,
  ContactsOutlined,
  IdcardOutlined,
  ProfileOutlined,
  FileAddOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FolderAddOutlined,
  FolderOpenOutlined,
  InboxOutlined,
  TagOutlined,
  TagsOutlined,
  CalendarOutlined,
  ScheduleOutlined,
  ClusterOutlined,
  NodeIndexOutlined,
  PartitionOutlined,
  GlobalOutlined,
  WifiOutlined,
  CloudOutlined,
  SaveOutlined,
  PrinterOutlined,
  ScanOutlined,
  CameraOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  CustomerServiceOutlined,
  PhoneOutlined,
  MailOutlined,
  WechatOutlined,
  QqOutlined,
  DingtalkOutlined,
  SlackOutlined,
  SkypeOutlined,
  WhatsAppOutlined,
  TwitterOutlined,
  FacebookOutlined,
  InstagramOutlined,
  LinkedinOutlined,
  YoutubeOutlined,
  GithubOutlined,
  GitlabOutlined,
  CodeOutlined,
  BugOutlined,
  RocketOutlined,
  ExperimentOutlined,
  BulbOutlined,
  HeartOutlined,
  LikeOutlined,
  DislikeOutlined,
  SmileOutlined,
  MehOutlined,
  FrownOutlined,
} from '@vicons/antd'

defineOptions({
  name: 'IconSelector',
})

interface Props {
  modelValue?: string
  color?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'update:color', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 'FormOutlined',
  color: '#3b82f6',
})

const emit = defineEmits<Emits>()

// Reactive state
const popoverVisible = ref(false)
const searchQuery = ref('')
const selectedColor = ref(props.color || '#3b82f6')

// Expose reactive values to template
const state = {
  popoverVisible,
  searchQuery,
  selectedColor,
}

const colorOptions = [
  [
    { name: '蓝色', value: '#3b82f6' },
    { name: '绿色', value: '#10b981' },
    { name: '红色', value: '#ef4444' },
    { name: '黄色', value: '#f59e0b' },
    { name: '紫色', value: '#8b5cf6' },
    { name: '粉色', value: '#ec4899' },
    { name: '青色', value: '#06b6d4' },
    { name: '橙色', value: '#f97316' },
  ],
  [
    { name: '深蓝', value: '#1e40af' },
    { name: '深绿', value: '#059669' },
    { name: '深红', value: '#dc2626' },
    { name: '深黄', value: '#d97706' },
    { name: '深紫', value: '#7c3aed' },
    { name: '深粉', value: '#db2777' },
    { name: '深青', value: '#0891b2' },
    { name: '深橙', value: '#ea580c' },
  ],
  [
    { name: '浅蓝', value: '#60a5fa' },
    { name: '浅绿', value: '#34d399' },
    { name: '浅红', value: '#f87171' },
    { name: '浅黄', value: '#fbbf24' },
    { name: '浅紫', value: '#a78bfa' },
    { name: '浅粉', value: '#f472b6' },
    { name: '灰色', value: '#6b7280' },
    { name: '黑色', value: '#374151' },
  ],
  [
    { name: '翠绿', value: '#22c55e' },
    { name: '天蓝', value: '#0ea5e9' },
    { name: '玫红', value: '#e11d48' },
    { name: '金黄', value: '#eab308' },
    { name: '靛蓝', value: '#6366f1' },
    { name: '石灰', value: '#84cc16' },
    { name: '琥珀', value: '#f59e0b' },
    { name: '紫罗兰', value: '#8b5cf6' },
  ],
]

const iconComponents: Record<string, any> = {
  // 用户和身份
  MedicineBoxOutlined,
  MonitorOutlined,
  UserOutlined,
  SafetyOutlined,
  ContactsOutlined,
  IdcardOutlined,
  ProfileOutlined,

  // 文件和文档
  FileOutlined,
  FileAddOutlined,
  FileTextOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FolderOutlined,
  FolderAddOutlined,
  FolderOpenOutlined,
  BookOutlined,
  ReadOutlined,
  FormOutlined,
  InboxOutlined,

  // 编辑和工具
  EditOutlined,
  BarsOutlined,
  AppstoreOutlined,
  MenuOutlined,
  SettingOutlined,
  ToolOutlined,
  KeyOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,

  // 状态和反馈
  CheckOutlined,
  CloseOutlined,
  PlusOutlined,
  MinusOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,

  // 操作
  DownloadOutlined,
  UploadOutlined,
  ShareAltOutlined,
  CopyOutlined,
  DeleteOutlined,
  ReloadOutlined,
  LeftOutlined,
  RightOutlined,
  UpOutlined,
  DownOutlined,
  SaveOutlined,
  PrinterOutlined,
  ScanOutlined,

  // 图表和数据
  LineChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  ControlOutlined,
  BuildOutlined,

  // 通用和导航
  StarOutlined,
  BellOutlined,
  MessageOutlined,
  HomeOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  DesktopOutlined,
  LinkOutlined,
  FullscreenOutlined,
  AimOutlined,
  CompassOutlined,
  GlobalOutlined,
  CloudOutlined,
  HeartOutlined,
  LikeOutlined,
  SmileOutlined,

  // 标签和分类
  TagOutlined,
  TagsOutlined,
  CalendarOutlined,
  ScheduleOutlined,

  // 技术和开发
  CodeOutlined,
  BugOutlined,
  RocketOutlined,
  ExperimentOutlined,
  BulbOutlined,
  GithubOutlined,
  GitlabOutlined,

  // 媒体和通信
  CameraOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  CustomerServiceOutlined,
  PhoneOutlined,
  MailOutlined,

  // 社交媒体
  WechatOutlined,
  QqOutlined,
  DingtalkOutlined,
  SlackOutlined,
  SkypeOutlined,
  WhatsAppOutlined,
  TwitterOutlined,
  FacebookOutlined,
  InstagramOutlined,
  LinkedinOutlined,
  YoutubeOutlined,

  // 网络和系统
  WifiOutlined,
  ClusterOutlined,
  NodeIndexOutlined,
  PartitionOutlined,

  // 表情和反应
  DislikeOutlined,
  MehOutlined,
  FrownOutlined,
}

const commonIcons = Object.keys(iconComponents)

const filteredIcons = computed(() => {
  if (!searchQuery.value) return commonIcons
  const query = searchQuery.value.toLowerCase()
  return commonIcons.filter((iconName) =>
    iconName.toLowerCase().includes(query),
  )
})

const togglePopover = () => {
  popoverVisible.value = !popoverVisible.value
}

const selectColor = (color: string) => {
  console.log('Selecting color:', color) // 调试日志
  selectedColor.value = color
  emit('update:color', color)
}

const selectIcon = (iconName: string) => {
  emit('update:modelValue', iconName)
  nextTick(() => {
    popoverVisible.value = false
  })
}

watch(
  () => props.color,
  (newColor) => {
    if (newColor) {
      selectedColor.value = newColor
    }
  },
  { immediate: true },
)

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && !iconComponents[newValue]) {
      console.warn(`Icon ${newValue} not found in iconComponents`)
    }
  },
  { immediate: true },
)
</script>
