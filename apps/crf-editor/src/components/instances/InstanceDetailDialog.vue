<template>
  <n-modal
    v-model:show="visible"
    :title="`查看实例数据 - ${instance?.id || ''}`"
    style="width: 90%"
    :mask-closable="false"
    @close="handleClose"
  >
    <div v-if="instance" class="instance-detail">
      <!-- 实例基本信息 -->
      <div class="instance-header">
        <h3>实例信息</h3>
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="实例ID">
            {{ instance.id }}
          </n-descriptions-item>
          <n-descriptions-item label="实例名称">
            {{ instance.instance_name || `实例_${instance.id}` }}
          </n-descriptions-item>
          <n-descriptions-item label="模板名称">
            {{ instance.template_name }}
          </n-descriptions-item>
          <n-descriptions-item label="模板版本">
            <n-tag size="small" type="info"
              >v{{ instance.template_version }}</n-tag
            >
          </n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="getStatusType(instance.status)" size="small">
              {{ getStatusText(instance.status) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="完成进度">
            <n-progress
              :percentage="instance.progress || 0"
              :stroke-width="6"
              :show-indicator="false"
            />
            <span class="progress-text">{{ instance.progress || 0 }}%</span>
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">
            {{ formatDateTime(instance.created_at) }}
          </n-descriptions-item>
          <n-descriptions-item label="更新时间">
            {{ formatDateTime(instance.updated_at) }}
          </n-descriptions-item>
          <n-descriptions-item label="提交时间">
            {{
              instance.submitted_at
                ? formatDateTime(instance.submitted_at)
                : '未提交'
            }}
          </n-descriptions-item>
          <n-descriptions-item label="受试者ID">
            {{ instance.subject_id || '-' }}
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <!-- 表单数据 -->
      <div class="form-data-section">
        <h3>表单数据</h3>
        <div v-if="loading" class="loading-container">
          <n-skeleton :repeat="5" text />
        </div>
        <div v-else-if="templateData" class="form-content">
          <form-renderer
            :template-data="templateData"
            :form-data="instance.form_data || {}"
            :mode="FormMode.VIEW"
            readonly
            @data-change="handleDataChange"
            @validation-change="handleValidationChange"
          />
        </div>
        <div v-else class="empty-data">
          <n-empty description="暂无表单数据" />
        </div>
      </div>

      <!-- 操作历史 -->
      <div class="history-section">
        <h3>操作历史</h3>
        <n-timeline>
          <n-timeline-item
            v-for="(item, index) in operationHistory"
            :key="index"
            :time="formatDateTime(item.timestamp)"
            :type="getHistoryType(item.type)"
          >
            <div class="history-item">
              <div class="history-title">{{ item.title }}</div>
              <div v-if="item.description" class="history-description">
                {{ item.description }}
              </div>
              <div v-if="item.user" class="history-user">
                操作人: {{ item.user }}
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
      </div>
    </div>

    <template #action>
      <div class="dialog-footer">
        <n-button @click="handleClose">关闭</n-button>
        <n-button type="primary" @click="handleExport">导出数据</n-button>
        <n-button
          v-if="instance?.status === 'submitted'"
          type="warning"
          @click="handleLock"
        >
          锁定实例
        </n-button>
        <n-button
          v-if="instance?.status === 'locked'"
          type="success"
          @click="handleUnlock"
        >
          解锁实例
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { templateAPI, instanceAPI } from '@/api'
import FormRenderer from '@/components/form/FormRenderer.vue'

// 临时类型定义
enum FormMode {
  EDIT = 'edit',
  FILL = 'fill',
  VIEW = 'view',
  PREVIEW = 'preview',
}

interface Props {
  modelValue: boolean
  instance: Record<string, unknown>
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'instance-updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Naive UI 消息
const message = useMessage()

// 状态管理
const loading = ref(false)
const templateData = ref<Record<string, unknown> | null>(null)
const operationHistory = ref<Record<string, unknown>[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'submitted':
      return 'primary'
    case 'locked':
      return 'warning'
    case 'draft':
      return 'info'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'submitted':
      return '已提交'
    case 'locked':
      return '已锁定'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

// 获取历史记录类型
const getHistoryType = (type: string) => {
  switch (type) {
    case 'create':
      return 'primary'
    case 'update':
      return 'info'
    case 'submit':
      return 'success'
    case 'lock':
      return 'warning'
    case 'unlock':
      return 'success'
    default:
      return 'info'
  }
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载模板数据
const loadTemplateData = async () => {
  if (!props.instance?.template_id) return

  loading.value = true
  try {
    const response = await templateAPI.getTemplate(props.instance.template_id)
    if (response.success && response.data) {
      templateData.value = response.data.template.template_data
    }
  } catch (error) {
    console.error('加载模板数据失败:', error)
    message.error('加载模板数据失败')
  } finally {
    loading.value = false
  }
}

// 生成模拟操作历史
const generateOperationHistory = () => {
  if (!props.instance) return

  const history = []

  // 创建记录
  history.push({
    type: 'create',
    title: '实例创建',
    description: '实例已创建',
    timestamp: props.instance.created_at,
    user: '系统',
  })

  // 更新记录
  if (props.instance.updated_at !== props.instance.created_at) {
    history.push({
      type: 'update',
      title: '数据更新',
      description: '实例数据已更新',
      timestamp: props.instance.updated_at,
      user: '用户',
    })
  }

  // 提交记录
  if (props.instance.submitted_at) {
    history.push({
      type: 'submit',
      title: '实例提交',
      description: '实例已提交审核',
      timestamp: props.instance.submitted_at,
      user: '用户',
    })
  }

  // 锁定记录
  if (props.instance.status === 'locked') {
    history.push({
      type: 'lock',
      title: '实例锁定',
      description: '实例已锁定，禁止修改',
      timestamp: props.instance.updated_at,
      user: '管理员',
    })
  }

  operationHistory.value = history.sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
  )
}

// 处理数据变更
const handleDataChange = (data: Record<string, unknown>) => {
  // 在查看模式下不处理数据变更
  console.log('数据变更:', data)
}

// 处理验证变更
const handleValidationChange = (errors: Record<string, unknown>) => {
  // 在查看模式下不处理验证变更
  console.log('验证变更:', errors)
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理导出
const handleExport = () => {
  message.info('导出功能开发中...')
}

// 处理锁定
const handleLock = async () => {
  if (!props.instance) return

  try {
    const response = await instanceAPI.lockInstance(props.instance.id)
    if (response.success) {
      message.success('实例已锁定')
      emit('instance-updated')
    }
  } catch (error) {
    console.error('锁定实例失败:', error)
    message.error('锁定实例失败')
  }
}

// 处理解锁
const handleUnlock = async () => {
  if (!props.instance) return

  try {
    const response = await instanceAPI.unlockInstance(props.instance.id)
    if (response.success) {
      message.success('实例已解锁')
      emit('instance-updated')
    }
  } catch (error) {
    console.error('解锁实例失败:', error)
    message.error('解锁实例失败')
  }
}

// 监听实例变化
watch(
  () => props.instance,
  (newInstance) => {
    if (newInstance && visible.value) {
      loadTemplateData()
      generateOperationHistory()
    }
  },
  { immediate: true },
)

// 监听对话框可见性
watch(visible, (isVisible) => {
  if (isVisible && props.instance) {
    loadTemplateData()
    generateOperationHistory()
  }
})
</script>

<style lang="scss" scoped>
.instance-detail {
  .instance-header {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .progress-text {
      margin-left: 8px;
      font-size: 12px;
      color: #6b7280;
    }
  }

  .form-data-section {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .loading-container {
      padding: 20px;
    }

    .form-content {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .empty-data {
      padding: 40px;
      text-align: center;
    }
  }

  .history-section {
    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .history-item {
      .history-title {
        font-weight: 500;
        color: #1f2937;
        margin-bottom: 4px;
      }

      .history-description {
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 4px;
      }

      .history-user {
        color: #9ca3af;
        font-size: 12px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>
