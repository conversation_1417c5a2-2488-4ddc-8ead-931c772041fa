<template>
  <div class="permission-error">
    <div class="permission-error__icon">
      <n-icon size="20" color="#ffffff">
        <svg viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
          />
        </svg>
      </n-icon>
    </div>
    <div class="permission-error__content">
      <div class="permission-error__title">{{ title }}</div>
      <div v-if="description" class="permission-error__description">
        {{ description }}
      </div>
    </div>
    <div
      v-if="showClose"
      class="permission-error__close"
      @click="$emit('close')"
    >
      <n-icon size="16" color="#ffffff">
        <svg viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
          />
        </svg>
      </n-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NIcon } from 'naive-ui'

interface Props {
  title?: string
  description?: string
  showClose?: boolean
}

withDefaults(defineProps<Props>(), {
  title: '您没有访问此页面的权限',
  description: '',
  showClose: false,
})

defineEmits<{
  close: []
}>()
</script>

<style lang="scss" scoped>
.permission-error {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: 1px solid #dc2626;
  border-radius: 8px;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    Arial, sans-serif;

  &__icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-top: 2px;
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    margin: 0;
  }

  &__description {
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4;
    margin-top: 4px;
    opacity: 0.9;
  }

  &__close {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .permission-error {
    padding: 14px 16px;

    &__title {
      font-size: 13px;
    }

    &__description {
      font-size: 12px;
    }
  }
}
</style>
