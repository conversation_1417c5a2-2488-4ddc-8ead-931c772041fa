<!-- 用户详情对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="700px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        用户详情: {{ user?.username }}
      </v-card-title>

      <v-card-text>
        <v-container>
          <!-- 基本信息 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">基本信息</h3>
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12" md="4" class="d-flex justify-center">
              <v-avatar size="120" class="elevation-4">
                <v-img
                  v-if="user?.avatar_url"
                  :src="user.avatar_url"
                  :alt="user.full_name"
                />
                <v-icon v-else size="60">mdi-account</v-icon>
              </v-avatar>
            </v-col>

            <v-col cols="12" md="8">
              <v-list density="compact">
                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis"
                    >用户名</v-list-item-title
                  >
                  <v-list-item-subtitle class="text-h6">{{
                    user?.username
                  }}</v-list-item-subtitle>
                </v-list-item>

                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis"
                    >全名</v-list-item-title
                  >
                  <v-list-item-subtitle class="text-body-1">{{
                    user?.full_name
                  }}</v-list-item-subtitle>
                </v-list-item>

                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis"
                    >邮箱</v-list-item-title
                  >
                  <v-list-item-subtitle class="text-body-1">{{
                    user?.email
                  }}</v-list-item-subtitle>
                </v-list-item>

                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis"
                    >状态</v-list-item-title
                  >
                  <v-list-item-subtitle>
                    <v-chip
                      :color="user?.is_active ? 'success' : 'error'"
                      size="small"
                    >
                      {{ user?.is_active ? '激活' : '停用' }}
                    </v-chip>
                  </v-list-item-subtitle>
                </v-list-item>

                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis"
                    >创建时间</v-list-item-title
                  >
                  <v-list-item-subtitle class="text-body-1">{{
                    formatDate(user?.created_at)
                  }}</v-list-item-subtitle>
                </v-list-item>

                <v-list-item>
                  <v-list-item-title class="text-caption text-medium-emphasis"
                    >最后更新</v-list-item-title
                  >
                  <v-list-item-subtitle class="text-body-1">{{
                    formatDate(user?.updated_at)
                  }}</v-list-item-subtitle>
                </v-list-item>
              </v-list>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- 用户角色 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">用户角色</h3>
              <div v-if="userRoles.length > 0" class="role-chips">
                <v-chip
                  v-for="roleInfo in userRoles"
                  :key="roleInfo.id"
                  :color="getRoleColor(roleInfo.name)"
                  class="ma-1"
                >
                  <v-icon start>mdi-shield-account</v-icon>
                  {{ roleInfo.display_name }}
                  <span v-if="roleInfo.project_name" class="ml-2 text-caption">
                    ({{ roleInfo.project_name }})
                  </span>
                </v-chip>
              </div>
              <v-alert v-else type="info" variant="tonal">
                该用户尚未分配任何角色
              </v-alert>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- 活动统计 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">活动统计</h3>

              <v-row>
                <v-col cols="6" md="3">
                  <v-card
                    class="text-center pa-3"
                    variant="tonal"
                    color="primary"
                  >
                    <v-icon size="24" class="mb-2">mdi-file-document</v-icon>
                    <div class="text-h5">{{ userStats.templatesCreated }}</div>
                    <div class="text-caption">创建表单</div>
                  </v-card>
                </v-col>

                <v-col cols="6" md="3">
                  <v-card
                    class="text-center pa-3"
                    variant="tonal"
                    color="success"
                  >
                    <v-icon size="24" class="mb-2">mdi-form-select</v-icon>
                    <div class="text-h5">
                      {{ userStats.instancesSubmitted }}
                    </div>
                    <div class="text-caption">提交实例</div>
                  </v-card>
                </v-col>

                <v-col cols="6" md="3">
                  <v-card
                    class="text-center pa-3"
                    variant="tonal"
                    color="warning"
                  >
                    <v-icon size="24" class="mb-2">mdi-eye-check</v-icon>
                    <div class="text-h5">{{ userStats.reviewsCompleted }}</div>
                    <div class="text-caption">完成审核</div>
                  </v-card>
                </v-col>

                <v-col cols="6" md="3">
                  <v-card class="text-center pa-3" variant="tonal" color="info">
                    <v-icon size="24" class="mb-2">mdi-login</v-icon>
                    <div class="text-h5">
                      {{ formatDate(userStats.lastLogin, true) }}
                    </div>
                    <div class="text-caption">最后登录</div>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- 最近活动 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">最近活动</h3>

              <v-timeline density="compact" v-if="recentActivities.length > 0">
                <v-timeline-item
                  v-for="activity in recentActivities"
                  :key="activity.id"
                  :dot-color="getActivityColor(activity.type)"
                  size="small"
                >
                  <template v-slot:icon>
                    <v-icon size="16">{{
                      getActivityIcon(activity.type)
                    }}</v-icon>
                  </template>

                  <div class="d-flex justify-space-between align-center">
                    <div>
                      <div class="text-body-2">{{ activity.description }}</div>
                      <div class="text-caption text-medium-emphasis">
                        {{ formatDate(activity.created_at) }}
                      </div>
                    </div>
                  </div>
                </v-timeline-item>
              </v-timeline>

              <v-alert v-else type="info" variant="tonal">
                暂无活动记录
              </v-alert>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn color="grey" variant="text" @click="closeDialog"> 关闭 </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="editUser"
          :disabled="!canManageUsers"
        >
          编辑用户
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { User, RoleInfo } from '@/types/rbac'
import { api } from '@/api/rbac'

interface Props {
  modelValue: boolean
  user: User | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit-user', user: User): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const userRoles = ref<RoleInfo[]>([])
const userStats = ref({
  templatesCreated: 0,
  instancesSubmitted: 0,
  reviewsCompleted: 0,
  lastLogin: null as string | null,
})
const recentActivities = ref<Record<string, unknown>[]>([])

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const canManageUsers = computed(() =>
  permissionStore.hasPermission('user', 'update'),
)

// 方法
const getRoleColor = (roleName: string) => {
  const colorMap: Record<string, string> = {
    super_admin: 'purple',
    admin: 'red',
    researcher: 'blue',
    data_entry: 'green',
    reviewer: 'orange',
    viewer: 'grey',
  }
  return colorMap[roleName] || 'primary'
}

const getActivityColor = (type: string) => {
  const colorMap: Record<string, string> = {
    login: 'success',
    create: 'primary',
    update: 'warning',
    delete: 'error',
    submit: 'info',
    approve: 'success',
  }
  return colorMap[type] || 'grey'
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    login: 'mdi-login',
    create: 'mdi-plus',
    update: 'mdi-pencil',
    delete: 'mdi-delete',
    submit: 'mdi-send',
    approve: 'mdi-check',
  }
  return iconMap[type] || 'mdi-information'
}

const formatDate = (dateString?: string, relative = false) => {
  if (!dateString) return '-'

  const date = new Date(dateString)

  if (relative) {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return '今天'
    if (diffDays === 2) return '昨天'
    if (diffDays <= 7) return `${diffDays}天前`
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`
    return `${Math.ceil(diffDays / 30)}月前`
  }

  return date.toLocaleString('zh-CN')
}

const fetchUserRoles = async () => {
  if (!props.user) return

  try {
    const response = await permissionStore.fetchUserRoles(props.user.id)
    // 将UserRole数据映射为RoleInfo格式
    userRoles.value = (response.roles || []).map(
      (userRole: Record<string, unknown>): RoleInfo => ({
        id: userRole.role?.id || userRole.role_id,
        name: userRole.role?.name || userRole.role?.code || 'unknown',
        display_name:
          userRole.role?.display_name || userRole.role?.name || 'Unknown Role',
        project_id: userRole.project_id,
        project_name: userRole.project?.name,
        assigned_at: userRole.assigned_at,
        expires_at: userRole.expires_at,
      }),
    )
  } catch (error) {
    console.error('获取用户角色失败:', error)
  }
}

const fetchUserStats = async () => {
  if (!props.user) return

  try {
    // 获取用户统计数据
    // const response = await api.get(`/users/${props.user.id}/stats`)
    // userStats.value = response.data

    // 模拟数据
    userStats.value = {
      templatesCreated: 5,
      instancesSubmitted: 23,
      reviewsCompleted: 12,
      lastLogin: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

const fetchRecentActivities = async () => {
  if (!props.user) return

  try {
    // 获取用户最近活动
    // const response = await api.get(`/users/${props.user.id}/activities?limit=10`)
    // recentActivities.value = response.data

    // 模拟数据
    recentActivities.value = [
      {
        id: '1',
        type: 'login',
        description: '用户登录系统',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: '2',
        type: 'create',
        description: '创建了表单模板：患者基本信息',
        created_at: new Date(
          Date.now() - 1 * 24 * 60 * 60 * 1000,
        ).toISOString(),
      },
      {
        id: '3',
        type: 'submit',
        description: '提交了CRF实例',
        created_at: new Date(
          Date.now() - 3 * 24 * 60 * 60 * 1000,
        ).toISOString(),
      },
    ]
  } catch (error) {
    console.error('获取最近活动失败:', error)
  }
}

const loadUserData = async () => {
  if (!props.user) return

  await Promise.all([
    fetchUserRoles(),
    fetchUserStats(),
    fetchRecentActivities(),
  ])
}

const editUser = () => {
  if (props.user) {
    emit('edit-user', props.user)
    closeDialog()
  }
}

const closeDialog = () => {
  isOpen.value = false
}

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.user) {
      loadUserData()
    }
  },
)

watch(
  () => props.user,
  () => {
    if (props.modelValue && props.user) {
      loadUserData()
    }
  },
)
</script>

<style scoped>
.role-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.v-card {
  overflow: visible;
}
</style>
