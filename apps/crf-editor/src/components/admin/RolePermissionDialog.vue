<!-- 角色权限管理对话框 -->
<template>
  <v-dialog v-model="isOpen" max-width="900px" persistent>
    <v-card>
      <v-card-title class="text-h5">
        管理角色权限: {{ role?.display_name }}
      </v-card-title>

      <v-card-text>
        <v-container>
          <!-- 当前权限 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">当前权限</h3>
              <div v-if="currentPermissions.length > 0">
                <div class="permission-groups">
                  <div
                    v-for="(group, resource) in groupedCurrentPermissions"
                    :key="resource"
                    class="permission-group mb-4"
                  >
                    <h4 class="text-subtitle-1 mb-2 d-flex align-center">
                      <v-icon
                        :color="getPermissionColor(resource)"
                        class="mr-2"
                      >
                        {{ getPermissionIcon(resource) }}
                      </v-icon>
                      {{ getResourceDisplayName(resource) }}
                    </h4>
                    <div class="permission-chips">
                      <v-chip
                        v-for="permission in group"
                        :key="permission.id"
                        :color="getPermissionColor(permission.resource)"
                        closable
                        @click:close="removePermission(permission.id)"
                        class="ma-1"
                        size="small"
                      >
                        {{ permission.action }}:{{ permission.scope }}
                      </v-chip>
                    </div>
                  </div>
                </div>
              </div>
              <v-alert v-else type="info" variant="tonal">
                该角色尚未分配任何权限
              </v-alert>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- 添加权限 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">添加权限</h3>

              <v-tabs v-model="activeTab" class="mb-4">
                <v-tab
                  v-for="resource in availableResources"
                  :key="resource"
                  :value="resource"
                >
                  <v-icon :color="getPermissionColor(resource)" class="mr-2">
                    {{ getPermissionIcon(resource) }}
                  </v-icon>
                  {{ getResourceDisplayName(resource) }}
                </v-tab>
              </v-tabs>

              <v-window v-model="activeTab">
                <v-window-item
                  v-for="resource in availableResources"
                  :key="resource"
                  :value="resource"
                >
                  <div class="resource-permissions">
                    <div class="d-flex flex-wrap gap-2">
                      <v-chip
                        v-for="permission in getAvailablePermissionsForResource(
                          resource,
                        )"
                        :key="permission.id"
                        :color="getPermissionColor(permission.resource)"
                        :variant="
                          isPermissionSelected(permission.id)
                            ? 'elevated'
                            : 'outlined'
                        "
                        clickable
                        @click="togglePermission(permission.id)"
                        class="ma-1"
                      >
                        <v-icon start>
                          {{
                            isPermissionSelected(permission.id)
                              ? 'mdi-check'
                              : 'mdi-plus'
                          }}
                        </v-icon>
                        {{ permission.action }}:{{ permission.scope }}
                      </v-chip>
                    </div>

                    <v-alert
                      v-if="
                        getAvailablePermissionsForResource(resource).length ===
                        0
                      "
                      type="info"
                      variant="tonal"
                      class="mt-4"
                    >
                      所有{{
                        getResourceDisplayName(resource)
                      }}权限已经分配给该角色
                    </v-alert>
                  </div>
                </v-window-item>
              </v-window>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- 批量操作 -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">批量操作</h3>
              <div class="d-flex gap-2 flex-wrap">
                <v-btn
                  color="success"
                  variant="outlined"
                  @click="addAllSelectedPermissions"
                  :disabled="selectedPermissions.length === 0"
                >
                  <v-icon start>mdi-check-all</v-icon>
                  添加选中的权限 ({{ selectedPermissions.length }})
                </v-btn>

                <v-btn
                  color="warning"
                  variant="outlined"
                  @click="clearAllPermissions"
                  :disabled="currentPermissions.length === 0"
                >
                  <v-icon start>mdi-delete-sweep</v-icon>
                  清除所有权限
                </v-btn>

                <v-btn
                  color="info"
                  variant="outlined"
                  @click="addCommonPermissions"
                >
                  <v-icon start>mdi-star</v-icon>
                  添加常用权限
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn color="grey" variant="text" @click="closeDialog"> 关闭 </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :loading="loading"
          @click="savePermissions"
        >
          保存更改
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePermissionStore } from '@/stores/permission-store'
import type { Role, Permission } from '@/types/rbac'

interface Props {
  modelValue: boolean
  role: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const permissionStore = usePermissionStore()

// 响应式数据
const loading = ref(false)
const activeTab = ref('user')
const allPermissions = ref<Permission[]>([])
const currentPermissions = ref<Permission[]>([])
const selectedPermissions = ref<string[]>([])

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const availableResources = computed(() => {
  const resources = new Set(allPermissions.value.map((p) => p.resource))
  return Array.from(resources).sort()
})

const groupedCurrentPermissions = computed(() => {
  return currentPermissions.value.reduce(
    (groups, permission) => {
      if (!groups[permission.resource]) {
        groups[permission.resource] = []
      }
      groups[permission.resource].push(permission)
      return groups
    },
    {} as Record<string, Permission[]>,
  )
})

// 方法
const getPermissionColor = (resource: string) => {
  const colorMap: Record<string, string> = {
    user: 'purple',
    role: 'red',
    project: 'blue',
    template: 'green',
    instance: 'orange',
    data: 'cyan',
    system: 'grey',
  }
  return colorMap[resource] || 'primary'
}

const getPermissionIcon = (resource: string) => {
  const iconMap: Record<string, string> = {
    user: 'mdi-account',
    role: 'mdi-shield-account',
    project: 'mdi-folder',
    template: 'mdi-file-document',
    instance: 'mdi-form-select',
    data: 'mdi-database',
    system: 'mdi-cog',
  }
  return iconMap[resource] || 'mdi-key'
}

const getResourceDisplayName = (resource: string) => {
  const nameMap: Record<string, string> = {
    user: '用户管理',
    role: '角色管理',
    project: '项目管理',
    template: '模板管理',
    instance: '实例管理',
    data: '数据管理',
    system: '系统管理',
  }
  return nameMap[resource] || resource
}

const getAvailablePermissionsForResource = (resource: string) => {
  return allPermissions.value.filter(
    (p) =>
      p.resource === resource &&
      !currentPermissions.value.some((cp) => cp.id === p.id),
  )
}

const isPermissionSelected = (permissionId: string) => {
  return selectedPermissions.value.includes(permissionId)
}

const togglePermission = (permissionId: string) => {
  const index = selectedPermissions.value.indexOf(permissionId)
  if (index > -1) {
    selectedPermissions.value.splice(index, 1)
  } else {
    selectedPermissions.value.push(permissionId)
  }
}

const fetchData = async () => {
  if (!props.role) return

  try {
    // 获取所有权限
    allPermissions.value = await permissionStore.fetchAllPermissions()

    // 获取角色当前权限
    const roleWithPermissions = await permissionStore.fetchAllRoles(true)
    const currentRole = roleWithPermissions.find((r) => r.id === props.role!.id)
    currentPermissions.value = currentRole?.permissions || []
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

const removePermission = async (permissionId: string) => {
  if (!props.role) return

  loading.value = true
  try {
    const remainingPermissions = currentPermissions.value
      .filter((p) => p.id !== permissionId)
      .map((p) => p.id)

    await permissionStore.assignPermissionsToRole(
      props.role.id,
      remainingPermissions,
    )
    await fetchData()
  } catch (error) {
    console.error('移除权限失败:', error)
  } finally {
    loading.value = false
  }
}

const addAllSelectedPermissions = async () => {
  if (!props.role || selectedPermissions.value.length === 0) return

  loading.value = true
  try {
    const allPermissionIds = [
      ...currentPermissions.value.map((p) => p.id),
      ...selectedPermissions.value,
    ]

    await permissionStore.assignPermissionsToRole(
      props.role.id,
      allPermissionIds,
    )
    selectedPermissions.value = []
    await fetchData()
  } catch (error) {
    console.error('添加权限失败:', error)
  } finally {
    loading.value = false
  }
}

const clearAllPermissions = async () => {
  if (!props.role || !confirm('确定要清除该角色的所有权限吗？')) return

  loading.value = true
  try {
    await permissionStore.assignPermissionsToRole(props.role.id, [])
    await fetchData()
  } catch (error) {
    console.error('清除权限失败:', error)
  } finally {
    loading.value = false
  }
}

const addCommonPermissions = () => {
  // 添加常用权限（读取权限）
  const commonPermissions = allPermissions.value
    .filter(
      (p) =>
        p.action === 'read' &&
        !currentPermissions.value.some((cp) => cp.id === p.id),
    )
    .map((p) => p.id)

  selectedPermissions.value = [
    ...new Set([...selectedPermissions.value, ...commonPermissions]),
  ]
}

const savePermissions = () => {
  emit('updated')
  closeDialog()
}

const closeDialog = () => {
  isOpen.value = false
  selectedPermissions.value = []
}

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.role) {
      fetchData()
    }
  },
)

watch(
  () => props.role,
  () => {
    if (props.modelValue && props.role) {
      fetchData()
    }
  },
)
</script>

<style scoped>
.permission-groups {
  max-height: 400px;
  overflow-y: auto;
}

.permission-group {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background-color: #fafafa;
}

.permission-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.resource-permissions {
  min-height: 200px;
}

.v-card {
  overflow: visible;
}
</style>
