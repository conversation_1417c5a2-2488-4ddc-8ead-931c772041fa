<!-- 预览渲染器组件 -->
<template>
  <div class="preview-renderer" :class="{ 'is-fullscreen': isFullscreen }">
    <div class="preview-renderer__actions">
      <button class="preview-renderer__action-btn" @click="handleCopy">
        <i class="i-carbon-copy"></i>
        复制
      </button>
      <button class="preview-renderer__action-btn" @click="handleDelete">
        <i class="i-carbon-trash-can"></i>
        删除
      </button>
    </div>
    <!-- 工具栏 -->
    <div class="preview-toolbar">
      <n-button-group>
        <n-button
          :type="mode === 'preview' ? 'primary' : 'default'"
          @click="switchMode('preview')"
        >
          <template #icon>
            <View />
          </template>
          预览
        </n-button>
        <n-button
          :type="mode === 'json' ? 'primary' : 'default'"
          @click="switchMode('json')"
        >
          <template #icon>
            <Document />
          </template>
          JSON
        </n-button>
        <n-button
          :type="mode === 'table' ? 'primary' : 'default'"
          @click="switchMode('table')"
        >
          <template #icon>
            <Grid />
          </template>
          表格
        </n-button>
      </n-button-group>

      <div class="toolbar-right">
        <n-button @click="exportData">
          <template #icon>
            <Download />
          </template>
          导出
        </n-button>
        <n-button @click="toggleFullscreen">
          <template #icon>
            <FullScreen v-if="!isFullscreen" />
            <Close v-else />
          </template>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </n-button>
      </div>
    </div>

    <!-- 预览内容 -->
    <div class="preview-content">
      <!-- 预览模式 -->
      <template v-if="mode === 'preview'">
        <div
          v-for="section in flattenedSections"
          :key="section.id"
          class="preview-section"
          :style="{ marginLeft: `${(section.level || 0) * 20}px` }"
        >
          <div class="section-header">
            <h3>{{ section.name }}</h3>
            <p v-if="section.description" class="section-description">
              {{ section.description }}
            </p>
          </div>

          <div class="section-content">
            <template v-for="block in section.blocks" :key="block.id">
              <component
                :is="getComponent(block.code)"
                v-bind="getComponentProps(block)"
                :disabled="true"
              />
            </template>
          </div>
        </div>
      </template>

      <!-- JSON模式 -->
      <div v-else-if="mode === 'json'" class="preview-json">
        <n-tabs v-model:value="jsonTab">
          <n-tab-pane name="schema" tab="Schema">
            <pre><code>{{ formatJson(schemaData) }}</code></pre>
          </n-tab-pane>
          <n-tab-pane name="formData" tab="表单数据">
            <pre><code>{{ formatJson(store.formData) }}</code></pre>
          </n-tab-pane>
          <n-tab-pane name="validation" tab="验证结果">
            <pre><code>{{ formatJson(store.validationResults) }}</code></pre>
          </n-tab-pane>
        </n-tabs>
      </div>

      <!-- 表格模式 -->
      <div v-else-if="mode === 'table'" class="preview-table">
        <n-data-table
          :data="tableData.rows"
          :columns="
            tableData.headers.map((header, index) => ({
              key: String(index),
              title: header,
            }))
          "
          bordered
          striped
        />
      </div>
    </div>

    <!-- 导出对话框 -->
    <n-modal
      v-model:show="exportDialogVisible"
      preset="dialog"
      title="导出数据"
    >
      <n-form :model="exportForm" label-width="100px">
        <n-form-item label="导出格式">
          <n-radio-group v-model:value="exportForm.format">
            <n-radio value="json">JSON</n-radio>
            <n-radio value="table">表格</n-radio>
          </n-radio-group>
        </n-form-item>

        <n-form-item label="包含元数据">
          <n-switch v-model:value="exportForm.includeMetadata" />
        </n-form-item>

        <n-form-item label="选择章节">
          <n-tree
            ref="sectionTree"
            :data="sectionTreeData"
            checkable
            key-field="id"
            label-field="name"
            @update:checked-keys="handleSectionCheck"
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-button @click="exportDialogVisible = false">取消</n-button>
        <n-button type="primary" @click="handleExport">确认导出</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  View,
  Document,
  Grid,
  Download,
  FullScreen,
  Close,
} from '@vicons/ionicons5'
import { useEditorStore } from '@/stores/editor-store'

import { DataConverter } from '@/utils/data-converter'

// 组件属性
interface Props {
  initialMode?: 'preview' | 'json' | 'table'
}

const props = withDefaults(defineProps<Props>(), {
  initialMode: 'preview',
})

// Store
const store = useEditorStore()
const message = useMessage()

// 状态
const mode = ref(props.initialMode)
const isFullscreen = ref(false)
const jsonTab = ref('schema')
const exportDialogVisible = ref(false)
const exportForm = ref({
  format: 'json',
  includeMetadata: true,
  sections: [] as string[],
})

// 计算属性
const flattenedSections = computed(() => {
  const result: Record<string, unknown>[] = []
  const flatten = (sectionList: Record<string, unknown>[]) => {
    sectionList.forEach((section) => {
      result.push(section)
      if (section.children?.length > 0) {
        flatten(section.children)
      }
    })
  }
  flatten(store.sections || [])
  return result
})

const schemaData = computed(() => store.schema)

const tableData = computed(() =>
  DataConverter.toTableData(
    store.schema!,
    store.formData,
    store.validationResults,
  ),
)

const sectionTreeData = computed(() => {
  const convertSection = (section: Record<string, unknown>) => ({
    id: section.id,
    name: section.name,
    children: section.children?.map(convertSection) || [],
  })
  return store.schema?.sections.map(convertSection) || []
})

// 方法
const switchMode = (newMode: 'preview' | 'json' | 'table') => {
  mode.value = newMode
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const getComponent = (_code: string) => {
  // TODO: Implement component resolution
  return null
}

const getComponentProps = (block: Record<string, unknown>) => ({
  ...block.props,
  modelValue: store.formData[block.id],
})

const formatJson = (data: unknown) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return ''
  }
}

const exportData = () => {
  exportDialogVisible.value = true
}

const handleSectionCheck = (
  _data: Record<string, unknown>,
  checked: boolean,
) => {
  exportForm.value.sections = checked.checkedKeys
}

const handleExport = async () => {
  try {
    await exportData()
    exportDialogVisible.value = false
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// 生命周期
onMounted(() => {
  // 监听全屏切换事件
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement
  }
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 清理
  onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
  })
})

const emit = defineEmits(['delete', 'copy'])

const handleCopy = () => {
  try {
    // 触发复制事件
    emit('copy')
    message.success('复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const handleDelete = () => {
  try {
    // 触发删除事件
    emit('delete')
    message.success('删除成功')
  } catch (error) {
    message.error('删除失败')
  }
}
</script>

<style lang="scss" scoped>
.preview-renderer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;

  &.is-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
  }

  &__actions {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
  }

  &__action-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: #f5f5f5;
    }

    i {
      font-size: 16px;
    }
  }
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #dcdfe6;
  background: #f5f7fa;

  .toolbar-right {
    display: flex;
    gap: 8px;
  }
}

.preview-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.preview-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .section-header {
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }

    .section-description {
      margin: 8px 0 0;
      font-size: 14px;
      color: #606266;
    }
  }

  .section-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

.preview-json {
  :deep(.n-tabs__content) {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  code {
    font-family: Monaco, Consolas, Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
  }
}

.preview-table {
  :deep(.n-data-table) {
    margin-bottom: 20px;
  }
}
</style>
