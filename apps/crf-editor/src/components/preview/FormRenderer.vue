<template>
  <div class="form-renderer">
    <div
      v-for="section in schema"
      :key="section.id"
      class="form-section"
      :class="{ 'has-error': hasSectionError(section) }"
    >
      <div v-if="section.name" class="section-header">
        <h3>{{ section.name }}</h3>
        <p v-if="section.description" class="section-description">
          {{ section.description }}
        </p>
      </div>

      <div class="section-content">
        <div
          v-for="block in section.blocks"
          :key="block.id"
          class="form-field"
          :class="{ 'has-error': hasFieldError(block.id) }"
        >
          <component
            :is="getFormComponent(block.code)"
            v-bind="getFormProps(block)"
            :readonly="readonly"
            :disabled="readonly"
            :model-value="formData[block.id]"
            @update:model-value="updateField(block.id, $event)"
            @blur="validateField(block.id)"
            @change="validateField(block.id)"
          />

          <div v-if="hasFieldError(block.id)" class="field-error">
            <div
              v-for="error in validationErrors?.[block.id]"
              :key="error"
              class="error-message"
            >
              {{ error }}
            </div>
          </div>
        </div>
      </div>

      <!-- 递归渲染子章节 -->
      <form-renderer
        v-if="section.children && section.children.length > 0"
        :schema="section.children"
        :form-data="formData"
        :validation-errors="validationErrors"
        :readonly="readonly"
        :show-validation="showValidation"
        class="subsection"
        @update-field="updateField"
        @validate-field="validateField"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  TextInput as TextComponent,
  TextareaInput as TextareaComponent,
  NumberInput as NumberComponent,
  SelectInput as SelectComponent,
  RadioInput as RadioComponent,
  CheckboxInput as CheckboxComponent,
  DateInput as DateComponent,
  TimeInput as TimeComponent,
  DateRangeInput as DateRangeComponent,
  TimeRangeInput as TimeRangeComponent,
} from '@crf/ui-components'

interface Props {
  schema: Record<string, unknown>[]
  formData: Record<string, unknown>
  validationErrors?: Record<string, string[]>
  readonly?: boolean
  showValidation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showValidation: true,
})

const emit = defineEmits<{
  'update-field': [fieldId: string, value: unknown]
  'validate-field': [fieldId: string]
}>()

// 获取表单组件
const getFormComponent = (componentCode: string) => {
  const componentMap: Record<string, unknown> = {
    text: TextComponent,
    textarea: TextareaComponent,
    number: NumberComponent,
    select: SelectComponent,
    radio: RadioComponent,
    checkbox: CheckboxComponent,
    date: DateComponent,
    time: TimeComponent,
    'date-range': DateRangeComponent,
    'time-range': TimeRangeComponent,
  }

  return componentMap[componentCode] || TextComponent
}

// 获取表单属性
const getFormProps = (block: Record<string, unknown>) => {
  const baseProps = {
    ...block.props,
    id: block.id,
    label: block.name || block.title,
    placeholder:
      block.props?.placeholder || `请输入${block.name || block.title}`,
    required: block.props?.required || false,
  }

  // 根据组件类型添加特定属性
  switch (block.code) {
    case 'select':
    case 'radio':
    case 'checkbox':
      return {
        ...baseProps,
        options: block.props?.options || [],
      }
    case 'number':
      return {
        ...baseProps,
        min: block.props?.min,
        max: block.props?.max,
        step: block.props?.step || 1,
      }
    case 'date':
      return {
        ...baseProps,
        format: block.props?.format || 'YYYY-MM-DD',
        valueFormat: block.props?.valueFormat || 'YYYY-MM-DD',
      }
    case 'time':
      return {
        ...baseProps,
        format: block.props?.format || 'HH:mm:ss',
      }
    default:
      return baseProps
  }
}

// 更新字段值
const updateField = (fieldId: string, value: unknown) => {
  emit('update-field', fieldId, value)
}

// 验证字段
const validateField = (fieldId: string) => {
  emit('validate-field', fieldId)
}

// 检查字段是否有错误
const hasFieldError = (fieldId: string) => {
  if (!props.showValidation || !props.validationErrors) return false
  const errors = props.validationErrors[fieldId]
  return errors && errors.length > 0
}

// 检查章节是否有错误
const hasSectionError = (section: Record<string, unknown>) => {
  if (!props.showValidation) return false

  // 检查当前章节的字段
  const hasBlockError = section.blocks?.some((block: Record<string, unknown>) =>
    hasFieldError(block.id),
  )

  // 检查子章节
  const hasChildError = section.children?.some(
    (child: Record<string, unknown>) => hasSectionError(child),
  )

  return hasBlockError || hasChildError
}
</script>

<style lang="scss" scoped>
.form-renderer {
  .form-section {
    margin-bottom: 24px;

    &.has-error {
      .section-header {
        border-left: 4px solid #f56c6c;
        padding-left: 12px;
        margin-left: -16px;
      }
    }

    .section-header {
      margin-bottom: 16px;

      h3 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .section-description {
        margin: 0;
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
      }
    }

    .section-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }

  .form-field {
    position: relative;

    &.has-error {
      :deep(.n-input__wrapper) {
        border-color: #f56c6c;
      }

      :deep(.n-input__inner) {
        border-color: #f56c6c;
      }

      :deep(.n-select .n-input__wrapper) {
        border-color: #f56c6c;
      }

      :deep(.n-radio-group) {
        border-color: #f56c6c;
      }

      :deep(.n-checkbox-group) {
        border-color: #f56c6c;
      }
    }

    .field-error {
      margin-top: 4px;

      .error-message {
        color: #f56c6c;
        font-size: 12px;
        line-height: 1.4;

        &:not(:last-child) {
          margin-bottom: 2px;
        }
      }
    }
  }

  .subsection {
    margin-left: 20px;
    padding-left: 16px;
    border-left: 2px solid #e4e7ed;

    .form-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
