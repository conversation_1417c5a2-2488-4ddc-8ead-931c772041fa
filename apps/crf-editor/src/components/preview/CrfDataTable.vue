<template>
  <div class="crf-data-table">
    <!-- 工具栏 -->
    <div v-if="exportOptions && exportOptions.length > 0" class="table-toolbar">
      <n-space>
        <n-button
          v-for="option in exportOptions"
          :key="option.type"
          @click="handleExport(option.type)"
        >
          <template #icon>
            <n-icon><Download /></n-icon>
          </template>
          {{ option.label }}
        </n-button>
      </n-space>
    </div>

    <!-- 表格 -->
    <n-data-table
      :data="data"
      :columns="naiveColumns"
      :striped="true"
      :bordered="true"
      :row-props="rowProps"
      @update:sorter="handleSorterChange"
    />

    <!-- 分页 -->
    <div v-if="pagination" class="table-pagination">
      <n-pagination
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :item-count="total || 0"
        :page-sizes="[10, 20, 50, 100]"
        show-size-picker
        show-quick-jumper
        @update:page="handleCurrentChange"
        @update:page-size="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import {
  NDataTable,
  NButton,
  NIcon,
  NInput,
  NPagination,
  NSpace,
} from 'naive-ui'
import { Download } from '@vicons/ionicons5'

// Props定义
interface Column {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  sortable?: boolean
  editable?: boolean
  formatter?: (
    row: Record<string, unknown>,
    column: Record<string, unknown>,
    cellValue: unknown,
    index: number,
  ) => string
}

interface ExportOption {
  type: string
  label: string
}

interface Props {
  data: Record<string, unknown>[]
  columns: Column[]
  showSummary?: boolean
  exportOptions?: ExportOption[]
  pagination?: boolean
  total?: number
}

withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => [],
  showSummary: false,
  exportOptions: () => [],
  pagination: false,
  total: 0,
})

// Emits定义
const emit = defineEmits<{
  'row-click': [
    row: Record<string, unknown>,
    column: Record<string, unknown>,
    event: Event,
  ]
  'cell-edit': [row: Record<string, unknown>, prop: string, value: unknown]
  export: [type: string]
  'page-change': [page: number, size: number]
}>()

// 响应式数据
const editingCell = ref<string | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)

// 将 Element Plus 列配置转换为 Naive UI 列配置
const naiveColumns = computed(() => {
  return props.columns.map((column) => {
    const naiveColumn: Record<string, unknown> = {
      key: column.prop,
      title: column.label,
    }

    if (column.width) {
      naiveColumn.width =
        typeof column.width === 'string' ? parseInt(column.width) : column.width
    }

    if (column.minWidth) {
      naiveColumn.minWidth =
        typeof column.minWidth === 'string'
          ? parseInt(column.minWidth)
          : column.minWidth
    }

    if (column.sortable) {
      naiveColumn.sortOrder = false
      naiveColumn.sorter = 'default'
    }

    if (column.editable) {
      naiveColumn.render = (row: Record<string, unknown>, rowIndex: number) => {
        const cellKey = `${rowIndex}-${column.prop}`
        if (editingCell.value === cellKey) {
          return h(NInput, {
            value: row[column.prop],
            onUpdateValue: (value: string) => {
              row[column.prop] = value
            },
            onBlur: () => handleCellEditComplete(row, column.prop, rowIndex),
            onKeyup: (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                handleCellEditComplete(row, column.prop, rowIndex)
              }
            },
          })
        } else {
          return h(
            'span',
            {
              onClick: () => startCellEdit(rowIndex, column.prop),
              style: { cursor: 'pointer' },
            },
            row[column.prop],
          )
        }
      }
    } else if (column.formatter) {
      naiveColumn.render = (row: Record<string, unknown>, rowIndex: number) => {
        return column.formatter!(row, column, row[column.prop], rowIndex)
      }
    }

    return naiveColumn
  })
})

// 行属性
const rowProps = (row: Record<string, unknown>) => {
  return {
    onClick: () => handleRowClick(row),
    style: 'cursor: pointer',
  }
}

// 处理行点击
const handleRowClick = (row: Record<string, unknown>) => {
  emit('row-click', row, null, null)
}

// 处理排序变化
const handleSorterChange = (sorter: Record<string, unknown>) => {
  // 这里可以添加排序逻辑
  console.log('排序变化:', sorter)
}

// 开始编辑单元格
const startCellEdit = (rowIndex: number, prop: string) => {
  editingCell.value = `${rowIndex}-${prop}`
}

// 完成单元格编辑
const handleCellEditComplete = (
  row: Record<string, unknown>,
  prop: string,
  _rowIndex: number,
) => {
  editingCell.value = null
  emit('cell-edit', row, prop, row[prop])
}

// 处理导出
const handleExport = (type: string) => {
  emit('export', type)
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('page-change', currentPage.value, size)
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page, pageSize.value)
}
</script>

<style scoped>
.crf-data-table {
  background: #fff;
  border-radius: 4px;
}

.table-toolbar {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.table-pagination {
  padding: 16px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
}

/* Naive UI 表格样式覆盖 */
:deep(.n-data-table) {
  border-radius: 0;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #f5f7fa;
}

:deep(.n-data-table .n-data-table-td) {
  cursor: pointer;
}

:deep(.n-data-table .n-data-table-td__cell) {
  padding: 8px 12px;
}
</style>
