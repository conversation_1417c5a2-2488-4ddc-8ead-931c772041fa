<template>
  <div class="crf-preview" :class="preview.getThemeClass()">
    <!-- 预览头部 -->
    <div class="preview-header">
      <div class="preview-title">
        <h2>{{ previewData?.pageConfig?.title || '表单预览' }}</h2>
        <div class="preview-meta">
          <span class="completion"
            >完成度: {{ preview.completionPercentage }}%</span
          >
          <span class="mode-badge" :class="preview.mode">{{ modeText }}</span>
        </div>
      </div>

      <div class="preview-actions">
        <n-button-group>
          <n-button @click="handleExport('json')" size="small">
            <template #icon>
              <CrfIcon name="Download" />
            </template>
            导出JSON
          </n-button>
          <n-button @click="handleExport('csv')" size="small">
            <template #icon>
              <CrfIcon name="Document" />
            </template>
            导出CSV
          </n-button>
          <n-button
            v-if="preview.mode === 'interactive'"
            @click="handleReset"
            size="small"
          >
            <template #icon>
              <CrfIcon name="Refresh" />
            </template>
            重置
          </n-button>
          <n-button
            v-if="preview.enableSubmit && preview.mode === 'interactive'"
            @click="handleSubmit"
            type="primary"
            size="small"
            :loading="preview.isSubmitting"
          >
            <template #icon>
              <CrfIcon name="Check" />
            </template>
            提交
          </n-button>
        </n-button-group>
      </div>
    </div>

    <!-- 预览内容 -->
    <div class="preview-content">
      <form-renderer
        v-if="previewData"
        :schema="previewData.sections"
        :form-data="preview.currentFormData"
        :validation-errors="preview.validationErrors.value"
        :readonly="preview.mode === 'readonly'"
        :show-validation="preview.showValidation"
        @update-field="handleUpdateField"
        @validate-field="handleValidateField"
      />

      <div v-else class="empty-state">
        <n-empty description="暂无预览内容" />
      </div>
    </div>

    <!-- 验证错误汇总 -->
    <div
      v-if="hasValidationErrors && preview.showValidation"
      class="validation-summary"
    >
      <n-alert title="表单验证失败" type="error" show-icon :closable="false">
        <ul class="error-list">
          <li
            v-for="(errors, fieldId) in preview.validationErrors"
            :key="fieldId"
          >
            <strong>{{ getFieldName(String(fieldId)) }}:</strong>
            <span v-for="(error, index) in errors" :key="index">{{
              error
            }}</span>
          </li>
        </ul>
      </n-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { useEditorStore } from '@/stores/editor-store'
import { usePreview } from '@/composables/usePreview'
import FormRenderer from './FormRenderer.vue'
import CrfIcon from '@/components/common/CrfIcon.vue'

interface Props {
  mode?: 'readonly' | 'interactive'
  showValidation?: boolean
  enableSubmit?: boolean
  theme?: 'default' | 'compact' | 'clinical'
  autoEnter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'readonly',
  showValidation: true,
  enableSubmit: false,
  theme: 'default',
  autoEnter: true,
})

const store = useEditorStore()
const message = useMessage()
const dialog = useDialog()

// 初始化预览功能
const preview = usePreview({
  mode: props.mode,
  showValidation: props.showValidation,
  enableSubmit: props.enableSubmit,
  theme: props.theme,
})

// 计算属性
const previewData = computed(() => preview.previewData.value)
const modeText = computed(() =>
  props.mode === 'readonly' ? '只读模式' : '交互模式',
)
const hasValidationErrors = computed(
  () => Object.keys(preview.validationErrors.value).length > 0,
)

// 获取字段名称
const getFieldName = (fieldId: string) => {
  // 这里可以根据字段ID查找字段名称
  const field = findFieldById(fieldId)
  return field?.title || field?.name || fieldId
}

// 在表单结构中查找字段
const findFieldById = (fieldId: string) => {
  if (!previewData.value) return null

  const searchInSections = (
    sections: Record<string, unknown>[],
  ): Record<string, unknown> | null => {
    for (const section of sections) {
      if (section.blocks) {
        const field = section.blocks.find(
          (block: Record<string, unknown>) => block.id === fieldId,
        )
        if (field) return field
      }
      if (section.children) {
        const found = searchInSections(section.children)
        if (found) return found
      }
    }
    return null
  }

  return searchInSections(previewData.value.sections)
}

// 处理字段更新
const handleUpdateField = (fieldId: string, value: unknown) => {
  preview.updateField(fieldId, value)
}

// 处理字段验证
const handleValidateField = (fieldId: string) => {
  preview.validateField(fieldId)
}

// 处理导出
const handleExport = (format: 'json' | 'csv' | 'pdf') => {
  try {
    const exportData = preview.exportFormData(format)
    if (exportData) {
      // 创建下载链接
      const dataString =
        typeof exportData === 'string' ? exportData : JSON.stringify(exportData)
      const blob = new Blob([dataString], {
        type: format === 'json' ? 'application/json' : 'text/csv',
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `crf-form-${Date.now()}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      message.success(`${format.toUpperCase()} 文件已导出`)
    }
  } catch (error) {
    message.error('导出失败')
    console.error('导出失败:', error)
  }
}

// 处理重置
const handleReset = async () => {
  try {
    await new Promise((resolve, reject) => {
      dialog.warning({
        title: '提示',
        content: '确定要重置表单吗？所有修改将丢失。',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => reject('cancel'),
      })
    })

    preview.resetForm()
    message.success('表单已重置')
  } catch {
    // 用户取消
  }
}

// 处理提交
const handleSubmit = async () => {
  try {
    const isValid = preview.validateForm()
    if (!isValid) {
      message.error('请先修正表单错误')
      return
    }

    const success = await preview.submitForm()
    if (success) {
      message.success('表单提交成功')
    } else {
      message.error('表单提交失败')
    }
  } catch (error) {
    message.error('提交失败')
    console.error('提交失败:', error)
  }
}

// 监听store变化自动更新预览
watch(
  [() => store.sections, () => store.pageConfig, () => store.formData],
  () => {
    if (preview.isPreviewMode.value) {
      preview.enterPreview() // 重新生成预览数据
    }
  },
  { deep: true },
)

// 组件挂载时自动进入预览模式
onMounted(() => {
  if (props.autoEnter) {
    preview.enterPreview()
  }
})

// 暴露给父组件的方法
defineExpose({
  enterPreview: preview.enterPreview,
  exitPreview: preview.exitPreview,
  exportData: preview.exportFormData,
  validateForm: preview.validateForm,
  submitForm: preview.submitForm,
  resetForm: preview.resetForm,
})
</script>

<style lang="scss" scoped>
.crf-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f9f9f9;

  .preview-header {
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .preview-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }

      .preview-meta {
        display: flex;
        gap: 12px;
        align-items: center;

        .completion {
          font-size: 12px;
          color: #6b7280;
          padding: 4px 8px;
          background: #f3f4f6;
          border-radius: 4px;
        }

        .mode-badge {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;

          &.readonly {
            background: #fef3c7;
            color: #92400e;
          }

          &.interactive {
            background: #dbeafe;
            color: #1d4ed8;
          }
        }
      }
    }

    .preview-actions {
      :deep(.n-button-group) {
        // 使用 Naive UI 原始圆角配置
      }
    }
  }

  .preview-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      min-height: 200px;
    }
  }

  .validation-summary {
    padding: 16px 20px;
    border-top: 1px solid #e8e8e8;
    background: white;
    flex-shrink: 0;

    .error-list {
      margin: 8px 0 0 0;
      padding: 0;
      list-style: none;

      li {
        padding: 4px 0;

        strong {
          color: #dc2626;
          margin-right: 8px;
        }

        span {
          color: #6b7280;

          &:not(:last-child)::after {
            content: '; ';
          }
        }
      }
    }
  }

  // 主题样式
  &.preview-theme-compact {
    .preview-header {
      padding: 12px 16px;

      .preview-title h2 {
        font-size: 16px;
      }
    }

    .preview-content {
      padding: 12px;
    }
  }

  &.preview-theme-clinical {
    background: #fbfbfb;

    .preview-header {
      background: #f8fafc;
      border-bottom: 2px solid #e2e8f0;

      .preview-title h2 {
        color: #1e293b;
        font-weight: 700;
      }
    }

    .preview-content {
      :deep(.form-section) {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin-bottom: 16px;
        padding: 16px;
      }
    }
  }
}
</style>
