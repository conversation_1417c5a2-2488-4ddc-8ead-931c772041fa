<!-- 头像编辑器对话框 -->
<template>
  <n-modal
    v-model:show="dialogVisible"
    preset="dialog"
    title="头像编辑"
    :style="{ width: '600px' }"
    :mask-closable="false"
  >
    <div class="avatar-editor">
      <!-- 上传区域 -->
      <div v-if="!imageUrl" class="upload-area">
        <n-upload
          ref="uploadRef"
          :default-upload="false"
          :show-file-list="false"
          accept="image/*"
          @change="handleFileSelect"
          directory-dnd
        >
          <n-upload-dragger>
            <div class="upload-content">
              <n-icon size="67" class="upload-icon"><UploadFilled /></n-icon>
              <div class="upload-text">
                <p>拖拽图片到此处，或<em>点击上传</em></p>
                <p class="upload-hint">
                  支持JPG、PNG、GIF、WebP格式，文件大小不超过2MB
                </p>
              </div>
            </div>
          </n-upload-dragger>
        </n-upload>
      </div>

      <!-- 图片编辑区域 -->
      <div v-if="imageUrl" class="edit-area">
        <div class="cropper-container">
          <cropper
            ref="cropperRef"
            class="cropper"
            :src="imageUrl"
            :stencil-props="{
              aspectRatio: 1,
              resizable: true,
              movable: true,
              lines: {
                north: true,
                south: true,
                east: true,
                west: true,
              },
              grid: true,
            }"
            :resize-image="{
              adjustStencil: false,
            }"
            :transitions="true"
            @change="updatePreview"
          />
        </div>

        <!-- 编辑工具栏 -->
        <div class="edit-toolbar">
          <div class="toolbar-group">
            <span class="toolbar-label">缩放:</span>
            <n-slider
              v-model:value="scale"
              :min="0.1"
              :max="3"
              :step="0.1"
              @update:value="handleScaleChange"
              style="width: 120px"
            />
          </div>

          <div class="toolbar-group">
            <n-button size="small" @click="rotateLeft">
              <template #icon
                ><n-icon><RefreshLeft /></n-icon
              ></template>
              左旋转
            </n-button>
            <n-button size="small" @click="rotateRight">
              <template #icon
                ><n-icon><RefreshRight /></n-icon
              ></template>
              右旋转
            </n-button>
          </div>

          <div class="toolbar-group">
            <n-button size="small" @click="flipHorizontal">
              <template #icon
                ><n-icon><Sort /></n-icon
              ></template>
              水平翻转
            </n-button>
            <n-button size="small" @click="flipVertical">
              <template #icon
                ><n-icon><DCaret /></n-icon
              ></template>
              垂直翻转
            </n-button>
          </div>

          <div class="toolbar-group">
            <n-button size="small" @click="reset">
              <template #icon
                ><n-icon><Refresh /></n-icon
              ></template>
              重置
            </n-button>
            <n-button size="small" type="primary" @click="selectNewImage">
              <template #icon
                ><n-icon><FolderOpened /></n-icon
              ></template>
              重新选择
            </n-button>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-area">
          <div class="preview-title">预览效果:</div>
          <div class="preview-avatars">
            <div class="preview-item">
              <canvas
                ref="previewCanvas80"
                width="80"
                height="80"
                class="preview-canvas"
              ></canvas>
              <span class="preview-label">80x80</span>
            </div>
            <div class="preview-item">
              <canvas
                ref="previewCanvas64"
                width="64"
                height="64"
                class="preview-canvas"
              ></canvas>
              <span class="preview-label">64x64</span>
            </div>
            <div class="preview-item">
              <canvas
                ref="previewCanvas40"
                width="40"
                height="40"
                class="preview-canvas"
              ></canvas>
              <span class="preview-label">40x40</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #action>
      <n-space>
        <n-button @click="handleClose">取消</n-button>
        <n-button
          v-if="imageUrl"
          type="primary"
          @click="handleUpload"
          :loading="uploading"
        >
          <template #icon
            ><n-icon><Upload /></n-icon
          ></template>
          确定上传
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import {
  CloudUploadOutline as UploadFilled,
  CloudUploadSharp as Upload,
  ArrowBackOutline as RefreshLeft,
  ArrowForwardOutline as RefreshRight,
  SwapHorizontalOutline as Sort,
  CaretDownOutline as DCaret,
  RefreshOutline as Refresh,
  FolderOpenOutline as FolderOpened,
} from '@vicons/ionicons5'
import { Cropper } from 'vue-advanced-cropper'
import 'vue-advanced-cropper/dist/style.css'
import { useUserStore } from '@/stores/user-store'
import { profileAPI } from '@/api/rbac'
import { storeToRefs } from 'pinia'

interface Props {
  visible: boolean
  currentAvatar?: string
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'uploaded', avatarUrl: string): void
}

const props = withDefaults(defineProps<Props>(), {
  currentAvatar: '',
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()
const { user } = storeToRefs(userStore)
const message = useMessage()

// 响应式数据
const uploadRef = ref()
const cropperRef = ref()
const previewCanvas80 = ref<HTMLCanvasElement>()
const previewCanvas64 = ref<HTMLCanvasElement>()
const previewCanvas40 = ref<HTMLCanvasElement>()

const imageUrl = ref('')
const scale = ref(1)
const uploading = ref(false)

// 文件上传相关
const fileList = ref([])
const beforeUpload = () => false // 阻止自动上传

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 方法
const handleClose = () => {
  imageUrl.value = ''
  scale.value = 1
  emit('update:visible', false)
}

const handleFileSelect = (file: { raw: File }) => {
  const rawFile = file.raw

  // 文件类型检查
  if (!rawFile.type.startsWith('image/')) {
    message.error('请选择图片文件')
    return
  }

  // 文件大小检查
  if (rawFile.size > 2 * 1024 * 1024) {
    message.error('图片大小不能超过2MB')
    return
  }

  // 读取文件并显示
  const reader = new FileReader()
  reader.onload = (e) => {
    imageUrl.value = e.target?.result as string
    nextTick(() => {
      scale.value = 1
    })
  }
  reader.readAsDataURL(rawFile)
}

const updatePreview = () => {
  if (!cropperRef.value) return

  const canvases = [
    { canvas: previewCanvas80.value, size: 80 },
    { canvas: previewCanvas64.value, size: 64 },
    { canvas: previewCanvas40.value, size: 40 },
  ]

  canvases.forEach(({ canvas, size }) => {
    if (canvas) {
      const ctx = canvas.getContext('2d')
      if (ctx && cropperRef.value) {
        const result = cropperRef.value.getResult()
        if (result && result.canvas) {
          // 清空画布
          ctx.clearRect(0, 0, size, size)

          // 绘制圆形裁剪区域
          ctx.save()
          ctx.beginPath()
          ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2)
          ctx.clip()

          // 绘制图片
          ctx.drawImage(result.canvas, 0, 0, size, size)
          ctx.restore()
        }
      }
    }
  })
}

const handleScaleChange = (value: number) => {
  if (cropperRef.value) {
    const currentScale = cropperRef.value.getImageTransforms().scale || 1
    const newScale = value / currentScale
    cropperRef.value.zoom(newScale)
    scale.value = value
  }
}

const rotateLeft = () => {
  if (cropperRef.value) {
    cropperRef.value.rotate(-90)
  }
}

const rotateRight = () => {
  if (cropperRef.value) {
    cropperRef.value.rotate(90)
  }
}

const flipHorizontal = () => {
  if (cropperRef.value) {
    cropperRef.value.flip(true, false)
  }
}

const flipVertical = () => {
  if (cropperRef.value) {
    cropperRef.value.flip(false, true)
  }
}

const reset = () => {
  if (cropperRef.value) {
    cropperRef.value.reset()
    scale.value = 1
  }
}

const selectNewImage = () => {
  uploadRef.value?.clearFiles()
  imageUrl.value = ''
  scale.value = 1
}

const handleUpload = async () => {
  if (!cropperRef.value || !user.value) return

  uploading.value = true

  try {
    // 获取裁剪结果
    const result = cropperRef.value.getResult()
    if (!result || !result.canvas) {
      message.error('获取裁剪结果失败')
      return
    }

    // 转换为blob
    const blob = await new Promise<Blob>((resolve, reject) => {
      result.canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('转换图片失败'))
          }
        },
        'image/jpeg',
        0.9,
      )
    })

    // 创建File对象
    const file = new File([blob], 'avatar.jpg', { type: 'image/jpeg' })

    // 上传到服务器
    const response = await profileAPI.uploadAvatar(user.value.id, file)

    console.log('头像上传响应:', response)

    // 处理多种可能的响应格式
    let isSuccess = false
    let avatarUrl = null

    if (response && typeof response === 'object') {
      if (response.success && response.data?.avatar_url) {
        // 标准成功响应格式
        isSuccess = true
        avatarUrl = response.data.avatar_url
      } else if (response.data?.avatar_url) {
        // 有data字段但没有success字段
        isSuccess = true
        avatarUrl = response.data.avatar_url
      } else if (response.avatar_url) {
        // 直接avatar_url字段
        isSuccess = true
        avatarUrl = response.avatar_url
      } else if (response.message && response.message.includes('成功')) {
        // 消息表明成功，但没有avatar_url
        isSuccess = true
        avatarUrl = response.url || response.avatar_url
      }
    }

    if (isSuccess && avatarUrl) {
      message.success('头像上传成功')

      // 更新用户store中的头像URL
      const updatedUser = { ...user.value, avatar_url: avatarUrl }
      await userStore.setUser(updatedUser)

      emit('uploaded', avatarUrl)
      handleClose()
    } else {
      console.error('头像上传失败 - 响应格式不正确:', response)
      message.error(response?.message || '头像上传失败')
    }
  } catch (error) {
    console.error('上传头像失败:', error)
    message.error('上传头像失败')
  } finally {
    uploading.value = false
  }
}

// 监听对话框显示状态
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    handleClose()
  }
})
</script>

<style scoped>
.avatar-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-area {
  padding: 20px;
}

.upload-content {
  text-align: center;
  padding: 60px 20px;
}

.upload-icon {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.edit-area {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cropper-container {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.cropper {
  height: 300px;
  background: #fff;
  border-radius: 4px;
}

.edit-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  align-items: center;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.preview-area {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.preview-avatars {
  display: flex;
  gap: 16px;
  align-items: center;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.preview-canvas {
  border: 2px solid #e4e7ed;
  border-radius: 50%;
  background: #fff;
}

.preview-label {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-group {
    justify-content: center;
  }

  .preview-avatars {
    justify-content: center;
  }
}
</style>
