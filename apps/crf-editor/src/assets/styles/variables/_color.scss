// 颜色变量定义 - 与 Naive UI 主题保持一致
:root {
  // 主色调 - 与登录页和 Naive UI 主题一致
  --primary-color: #667eea;
  --primary-hover: #5a67d8;
  --primary-pressed: #4c51bf;
  
  // 辅助色调 - 与 Naive UI 主题一致
  --secondary-color: #0f766e;
  --accent-color: #dc2626;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #4facfe;
  
  // 中性色调 - 与登录页和 Naive UI 主题一致
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-tertiary: #718096;
  --text-disabled: #a0aec0;
  
  // 背景色调 - 与登录页和 Naive UI 主题一致
  --body-color: #ffffff;
  --page-background: #f8fafc;
  --card-background: #ffffff;
  
  // 边框色调 - 与登录页和 Naive UI 主题一致
  --border-color: #e2e8f0;
  --divider-color: #e2e8f0;
  
  // 编辑器专用颜色
  --edit-background-color: #f9fafb;
  --edit-border-color: #e2e8f0;
  --edit-disabled-text-color: #a0aec0;
  --color-block-hover: #667eea;
  
  // 圆角 - 与登录页和 Naive UI 主题一致
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 20px;
  
  // 阴影 - 与登录页和 Naive UI 主题一致
  --box-shadow-1: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --box-shadow-2: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-3: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}