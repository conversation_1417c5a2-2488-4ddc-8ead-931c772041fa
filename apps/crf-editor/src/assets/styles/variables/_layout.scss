:root {
  // 基础字体大小设置
  font-size: 14px;
  --edit-header-height: 4rem;
  --edit-block-width: 25rem;
  --edit-block-toolbox-width: 5rem;
  --edit-config-width: 23rem;
  --border-radius: 0.357rem;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px !important;
}

::-webkit-scrollbar-track {
  background: var(--gray-100) !important;
  border-radius: 3px !important;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300) !important;
  border-radius: 3px !important;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400) !important;
}