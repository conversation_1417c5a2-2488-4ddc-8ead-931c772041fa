<template>
  <svg
    :width="size"
    :height="size"
    viewBox="0 0 18 18"
    fill="none"
    :class="iconClass"
  >
    <path
      d="M14.25 7.5H13.5V5.25C13.5 2.3505 11.1495 0 8.25 0C5.3505 0 3 2.3505 3 5.25V7.5H2.25C1.425 7.5 0.75 8.175 0.75 9V15.75C0.75 16.575 1.425 17.25 2.25 17.25H14.25C15.075 17.25 15.75 16.575 15.75 15.75V9C15.75 8.175 15.075 7.5 14.25 7.5ZM8.25 13.5C7.425 13.5 6.75 12.825 6.75 12C6.75 11.175 7.425 10.5 8.25 10.5C9.075 10.5 9.75 11.175 9.75 12C9.75 12.825 9.075 13.5 8.25 13.5ZM11.25 7.5H5.25V5.25C5.25 3.6075 6.6075 2.25 8.25 2.25C9.8925 2.25 11.25 3.6075 11.25 5.25V7.5Z"
      :fill="color"
      :opacity="opacity"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  opacity?: number | string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 18,
  color: 'currentColor',
  opacity: 1,
  class: '',
})

const iconClass = computed(() => `lock-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.lock-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
