<template>
  <svg
    :width="size"
    :height="size"
    viewBox="0 0 18 18"
    fill="none"
    :class="iconClass"
  >
    <path
      d="M15 3H3C2.175 3 1.5075 3.675 1.5075 4.5L1.5 13.5C1.5 14.325 2.175 15 3 15H15C15.825 15 16.5 14.325 16.5 13.5V4.5C16.5 3.675 15.825 3 15 3ZM15 6L9 9.75L3 6V4.5L9 8.25L15 4.5V6Z"
      :fill="color"
      :opacity="opacity"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  opacity?: number | string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 18,
  color: 'currentColor',
  opacity: 1,
  class: '',
})

const iconClass = computed(() => `mail-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.mail-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
