<template>
  <svg
    :width="size"
    :height="size"
    viewBox="0 0 120 120"
    fill="none"
    :class="iconClass"
  >
    <circle
      cx="60"
      cy="60"
      r="50"
      :fill="backgroundColor"
      :stroke="borderColor"
      stroke-width="2"
    />
    <rect
      x="30"
      y="35"
      width="60"
      height="50"
      rx="4"
      fill="#ffffff"
      :stroke="formBorderColor"
      stroke-width="2"
    />
    <rect x="35" y="42" width="40" height="3" rx="1.5" :fill="line1Color" />
    <rect x="35" y="50" width="32" height="3" rx="1.5" :fill="line2Color" />
    <rect x="35" y="58" width="36" height="3" rx="1.5" :fill="line3Color" />
    <rect x="35" y="66" width="28" height="3" rx="1.5" :fill="line4Color" />
    <rect x="35" y="74" width="44" height="3" rx="1.5" :fill="line5Color" />
    <circle
      cx="85"
      cy="35"
      r="12"
      :fill="plusCircleColor"
      stroke="#ffffff"
      stroke-width="2"
    />
    <path
      d="M81 35h8M85 31v8"
      stroke="#ffffff"
      stroke-width="2"
      stroke-linecap="round"
    />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  backgroundColor?: string
  borderColor?: string
  formBorderColor?: string
  line1Color?: string
  line2Color?: string
  line3Color?: string
  line4Color?: string
  line5Color?: string
  plusCircleColor?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 120,
  backgroundColor: '#F0F9FF',
  borderColor: '#E1F5FE',
  formBorderColor: '#B3E5FC',
  line1Color: '#81C784',
  line2Color: '#FFB74D',
  line3Color: '#90CAF9',
  line4Color: '#A1C181',
  line5Color: '#FFAB91',
  plusCircleColor: '#4FC3F7',
  class: '',
})

const iconClass = computed(() => `empty-form-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.empty-form-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
