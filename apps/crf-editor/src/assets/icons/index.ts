import { defineAsyncComponent } from 'vue'

// 异步加载图标组件以优化性能
export const UserIcon = defineAsyncComponent(() => import('./UserIcon.vue'))
export const LockIcon = defineAsyncComponent(() => import('./LockIcon.vue'))
export const PhoneIcon = defineAsyncComponent(() => import('./PhoneIcon.vue'))
export const MailIcon = defineAsyncComponent(() => import('./MailIcon.vue'))
export const BrandLogo = defineAsyncComponent(() => import('./BrandLogo.vue'))
export const StarIcon = defineAsyncComponent(() => import('./StarIcon.vue'))
export const ArrowIcon = defineAsyncComponent(() => import('./ArrowIcon.vue'))
export const TabIcon = defineAsyncComponent(() => import('./TabIcon.vue'))
export const VersionIcon = defineAsyncComponent(
  () => import('./VersionIcon.vue'),
)
export const EmptyFormIcon = defineAsyncComponent(
  () => import('./EmptyFormIcon.vue'),
)

// 图标注册表
export const iconComponents = {
  UserIcon,
  LockIcon,
  PhoneIcon,
  MailIcon,
  BrandLogo,
  StarIcon,
  ArrowIcon,
  TabIcon,
  VersionIcon,
  EmptyFormIcon,
}

// 图标名称枚举
export enum IconNames {
  USER = 'UserIcon',
  LOCK = 'LockIcon',
  PHONE = 'PhoneIcon',
  MAIL = 'MailIcon',
  BRAND_LOGO = 'BrandLogo',
  STAR = 'StarIcon',
  ARROW = 'ArrowIcon',
  TAB = 'TabIcon',
  VERSION = 'VersionIcon',
  EMPTY_FORM = 'EmptyFormIcon',
}

// 图标类型
export type IconComponent = keyof typeof iconComponents
