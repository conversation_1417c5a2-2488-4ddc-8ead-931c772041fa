<template>
  <svg
    :width="size"
    :height="size"
    viewBox="0 0 20 20"
    fill="none"
    :class="iconClass"
  >
    <path :d="starPath" :fill="color" />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
  variant?: 'star1' | 'star2' | 'star3'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 20,
  color: 'currentColor',
  variant: 'star1',
  class: '',
})

const starPaths = {
  star1: 'M10 2L12.5 7H18L14 10.5L15.5 16L10 13L4.5 16L6 10.5L2 7H7.5L10 2Z',
  star2: 'M10 1L13 7L19 7L14.5 11L16 17L10 14L4 17L5.5 11L1 7L7 7L10 1Z',
  star3: 'M10 2L12.5 7H18L14 10.5L15.5 16L10 13L4.5 16L6 10.5L2 7H7.5L10 2Z',
}

const starPath = computed(() => starPaths[props.variant])
const iconClass = computed(() => `star-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.star-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
