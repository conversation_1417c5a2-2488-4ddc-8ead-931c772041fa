<template>
  <svg
    :width="size"
    :height="size"
    viewBox="0 0 16 16"
    fill="none"
    :class="iconClass"
  >
    <circle
      cx="8"
      cy="8"
      r="6"
      :fill="backgroundColor"
      :stroke="borderColor"
      stroke-width="1"
    />
    <text
      x="8"
      y="12"
      text-anchor="middle"
      :fill="textColor"
      font-size="10"
      font-family="Arial, sans-serif"
      font-weight="bold"
    >
      {{ versionText }}
    </text>
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  size?: number | string
  version?: string | number
  backgroundColor?: string
  borderColor?: string
  textColor?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  version: 'v1',
  backgroundColor: '#667eea',
  borderColor: '#5a67d8',
  textColor: '#ffffff',
  class: '',
})

const versionText = computed(() => {
  const version = props.version.toString()
  if (version.startsWith('v')) {
    return version.substring(0, 2)
  }
  return `v${version}`.substring(0, 2)
})

const iconClass = computed(() => `version-icon ${props.class}`)
</script>

<style lang="scss" scoped>
.version-icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
