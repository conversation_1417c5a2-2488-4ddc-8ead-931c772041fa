import _pageSchema, { type PageSchema } from '@/schemas/page-schema.ts'
import { type crfComponentSchemas, schema } from '@crf/ui-components'

export type BlockSchema = typeof schema

export type BlockSchemaKeys = keyof BlockSchema

export const pageSchema = _pageSchema

export const blockSchema: BlockSchema = schema

// 导出所有支持的组件类型
export const supportedComponents = schema

// 导出组件类型
export type ComponentType = keyof typeof supportedComponents

// 导出块组件配置类型
export type BlockSchemaFormData = typeof crfComponentSchemas

export type PageSchemaFormData = PageSchema
