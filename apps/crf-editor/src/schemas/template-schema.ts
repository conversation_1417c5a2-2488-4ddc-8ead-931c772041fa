/**
 * CRF模板数据的JSON Schema定义
 * 用于验证template_data字段的结构和完整性
 */

export interface PageConfig {
  theme?: string
  layout?: 'vertical' | 'horizontal' | 'grid'
  language?: string
  title?: string
  description?: string
  showProgress?: boolean
  allowSave?: boolean
  allowPreview?: boolean
  [key: string]: unknown
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: unknown
  message?: string
  validator?: string
}

export interface ComponentConfig {
  id: string
  type:
    | 'input'
    | 'textarea'
    | 'select'
    | 'radio'
    | 'checkbox'
    | 'date'
    | 'number'
    | 'file'
    | 'signature'
  label: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  hidden?: boolean
  defaultValue?: unknown
  options?: Array<{ label: string; value: unknown }>
  validation?: ValidationRule[]
  dependencies?: Array<{
    field: string
    condition: 'equals' | 'not_equals' | 'greater' | 'less'
    value: unknown
    action: 'show' | 'hide' | 'require' | 'disable'
  }>
  style?: {
    width?: string
    height?: string
    className?: string
  }
  [key: string]: unknown
}

export interface FormSection {
  id: string
  title: string
  description?: string
  collapsible?: boolean
  collapsed?: boolean
  components: ComponentConfig[]
}

export interface FormStructure {
  sections: FormSection[]
  layout?: 'single-column' | 'two-column' | 'three-column'
  [key: string]: unknown
}

export interface StyleConfig {
  theme?: string
  customStyles?: Record<string, unknown>
  fontFamily?: string
  borderRadius?: string
  spacing?: string
  customCSS?: string
  [key: string]: unknown
}

export interface CRFTemplateData {
  pageConfig?: PageConfig
  formStructure?: FormStructure
  componentConfigs?: Record<string, ComponentConfig>
  validationRules?: Record<string, ValidationRule[]>
  styleConfig?: StyleConfig
  version?: string
  lastModified?: string
  metadata?: Record<string, unknown>
}

// JSON Schema定义
export const CRFTemplateSchema = {
  type: 'object',
  properties: {
    pageConfig: {
      type: 'object',
      properties: {
        theme: { type: 'string' },
        layout: {
          type: 'string',
          enum: ['vertical', 'horizontal', 'grid'],
        },
        language: { type: 'string' },
        title: { type: 'string' },
        description: { type: 'string' },
        showProgress: { type: 'boolean' },
        allowSave: { type: 'boolean' },
        allowPreview: { type: 'boolean' },
      },
      additionalProperties: false,
    },
    formStructure: {
      type: 'object',
      properties: {
        sections: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
              description: { type: 'string' },
              collapsible: { type: 'boolean' },
              collapsed: { type: 'boolean' },
              components: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    type: {
                      type: 'string',
                      enum: [
                        'input',
                        'textarea',
                        'select',
                        'radio',
                        'checkbox',
                        'date',
                        'number',
                        'file',
                        'signature',
                      ],
                    },
                    label: { type: 'string' },
                    placeholder: { type: 'string' },
                    required: { type: 'boolean' },
                    readonly: { type: 'boolean' },
                    hidden: { type: 'boolean' },
                    defaultValue: {},
                    options: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          label: { type: 'string' },
                          value: {},
                        },
                        required: ['label', 'value'],
                      },
                    },
                    validation: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          type: {
                            type: 'string',
                            enum: [
                              'required',
                              'min',
                              'max',
                              'pattern',
                              'custom',
                            ],
                          },
                          value: {},
                          message: { type: 'string' },
                          validator: { type: 'string' },
                        },
                        required: ['type'],
                      },
                    },
                    dependencies: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          field: { type: 'string' },
                          condition: {
                            type: 'string',
                            enum: ['equals', 'not_equals', 'greater', 'less'],
                          },
                          value: {},
                          action: {
                            type: 'string',
                            enum: ['show', 'hide', 'require', 'disable'],
                          },
                        },
                        required: ['field', 'condition', 'value', 'action'],
                      },
                    },
                    style: {
                      type: 'object',
                      properties: {
                        width: { type: 'string' },
                        height: { type: 'string' },
                        className: { type: 'string' },
                      },
                      additionalProperties: false,
                    },
                  },
                  required: ['id', 'type', 'label'],
                },
              },
            },
            required: ['id', 'title', 'components'],
          },
        },
        layout: {
          type: 'string',
          enum: ['single-column', 'two-column', 'three-column'],
        },
      },
      required: ['sections'],
      additionalProperties: false,
    },
    componentConfigs: {
      type: 'object',
      additionalProperties: {
        type: 'object',
      },
    },
    validationRules: {
      type: 'object',
      additionalProperties: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['required', 'min', 'max', 'pattern', 'custom'],
            },
            value: {},
            message: { type: 'string' },
            validator: { type: 'string' },
          },
          required: ['type'],
        },
      },
    },
    styleConfig: {
      type: 'object',
      properties: {
        primaryColor: { type: 'string' },
        secondaryColor: { type: 'string' },
        backgroundColor: { type: 'string' },
        fontSize: { type: 'string' },
        fontFamily: { type: 'string' },
        borderRadius: { type: 'string' },
        spacing: { type: 'string' },
        customCSS: { type: 'string' },
      },
      additionalProperties: false,
    },
    version: { type: 'string' },
    lastModified: { type: 'string' },
    metadata: {
      type: 'object',
      additionalProperties: true,
    },
  },
  additionalProperties: false,
}

/**
 * 验证CRF模板数据
 * @param data 要验证的模板数据
 * @returns 验证结果和错误信息
 */
export function validateTemplateData(data: unknown): {
  valid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    // 基本结构验证
    if (!data || typeof data !== 'object') {
      errors.push('模板数据必须是一个对象')
      return { valid: false, errors, warnings }
    }

    // 验证formStructure
    const dataObj = data as Record<string, any>
    if (dataObj.formStructure) {
      if (
        !dataObj.formStructure.sections ||
        !Array.isArray(dataObj.formStructure.sections)
      ) {
        errors.push('formStructure.sections 必须是一个数组')
      } else {
        // 验证每个section
        dataObj.formStructure.sections.forEach(
          (section: Record<string, unknown>, index: number) => {
            if (!section.id || typeof section.id !== 'string') {
              errors.push(`第${index + 1}个section缺少有效的id`)
            }
            if (!section.title || typeof section.title !== 'string') {
              errors.push(`第${index + 1}个section缺少有效的title`)
            }
            if (!section.components || !Array.isArray(section.components)) {
              errors.push(`第${index + 1}个section的components必须是数组`)
            } else {
              // 验证每个component
              section.components.forEach(
                (component: Record<string, unknown>, compIndex: number) => {
                  if (!component.id || typeof component.id !== 'string') {
                    errors.push(
                      `第${index + 1}个section的第${compIndex + 1}个组件缺少有效的id`,
                    )
                  }
                  if (!component.type || typeof component.type !== 'string') {
                    errors.push(
                      `第${index + 1}个section的第${compIndex + 1}个组件缺少有效的type`,
                    )
                  }
                  if (!component.label || typeof component.label !== 'string') {
                    errors.push(
                      `第${index + 1}个section的第${compIndex + 1}个组件缺少有效的label`,
                    )
                  }

                  // 检查组件类型是否有效
                  const validTypes = [
                    'input',
                    'textarea',
                    'select',
                    'radio',
                    'checkbox',
                    'date',
                    'number',
                    'file',
                    'signature',
                  ]
                  if (
                    component.type &&
                    !validTypes.includes(component.type as string)
                  ) {
                    errors.push(
                      `第${index + 1}个section的第${compIndex + 1}个组件的类型"${component.type}"无效`,
                    )
                  }
                },
              )
            }
          },
        )

        // 检查重复的组件ID
        const componentIds = new Set()
        dataObj.formStructure.sections.forEach(
          (section: Record<string, unknown>) => {
            ;(section.components as Record<string, unknown>[])?.forEach(
              (component: Record<string, unknown>) => {
                if (component.id) {
                  if (componentIds.has(component.id)) {
                    errors.push(`组件ID "${component.id}" 重复`)
                  } else {
                    componentIds.add(component.id)
                  }
                }
              },
            )
          },
        )
      }
    }

    // 验证pageConfig
    if (dataObj.pageConfig) {
      if (
        dataObj.pageConfig.layout &&
        !['vertical', 'horizontal', 'grid'].includes(dataObj.pageConfig.layout)
      ) {
        errors.push('pageConfig.layout 值无效')
      }
    }

    // 验证styleConfig
    if (dataObj.styleConfig && typeof dataObj.styleConfig === 'object') {
      // 检查颜色格式
      const colorFields = ['primaryColor', 'secondaryColor', 'backgroundColor']
      colorFields.forEach((field) => {
        if (
          dataObj.styleConfig[field] &&
          typeof dataObj.styleConfig[field] === 'string'
        ) {
          const colorRegex =
            /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/
          if (!colorRegex.test(dataObj.styleConfig[field])) {
            warnings.push(`${field} 的颜色格式可能无效`)
          }
        }
      })
    }

    // 验证版本号格式
    if (dataObj.version && typeof dataObj.version === 'string') {
      const versionRegex = /^\d+\.\d+\.\d+$/
      if (!versionRegex.test(dataObj.version)) {
        warnings.push('版本号格式建议使用语义化版本 (如: 1.0.0)')
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    }
  } catch (error) {
    errors.push(`验证过程中发生错误: ${error}`)
    return { valid: false, errors, warnings }
  }
}

/**
 * 创建默认的CRF模板数据
 */
export function createDefaultTemplateData(): CRFTemplateData {
  return {
    pageConfig: {
      theme: 'medical',
      layout: 'vertical',
      language: 'zh-CN',
      title: '新建CRF表单',
      description: '',
      showProgress: true,
      allowSave: true,
      allowPreview: true,
    },
    formStructure: {
      sections: [
        {
          id: 'section_1',
          title: '基本信息',
          description: '请填写基本信息',
          collapsible: false,
          collapsed: false,
          components: [
            {
              id: 'subject_id',
              type: 'input',
              label: '受试者编号',
              placeholder: '请输入受试者编号',
              required: true,
              readonly: false,
              hidden: false,
              validation: [
                {
                  type: 'required',
                  message: '受试者编号不能为空',
                },
              ],
            },
          ],
        },
      ],
      layout: 'single-column',
    },
    componentConfigs: {},
    validationRules: {},
    styleConfig: {
      primaryColor: '#1976d2',
      secondaryColor: '#42a5f5',
      backgroundColor: '#ffffff',
      fontSize: '14px',
      fontFamily: 'Arial, sans-serif',
      borderRadius: '4px',
      spacing: '16px',
    },
    version: '1.0.0',
    lastModified: new Date().toISOString(),
    metadata: {},
  }
}
