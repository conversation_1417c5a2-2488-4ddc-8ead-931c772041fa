import { type Static, Type } from '@sinclair/typebox'

const title = Type.String({
  code: 'config-input',
  title: '标题',
  placeholder: '请输入页面标题',
})

const keyword = Type.String({
  code: 'config-input',
  title: '关键字',
  placeholder: '请输入表单关键词',
})

const description = Type.String({
  code: 'config-input',
  title: '描述',
  placeholder: '请输入页面描述',
})

const T = Type.Object({
  title: Type.Optional(title),
  keyword: Type.Optional(keyword),
  description: Type.Optional(description),
})

export type PageSchema = Static<typeof T>

export default T
