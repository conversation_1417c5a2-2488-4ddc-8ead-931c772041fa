import { ref, computed, watch, type Ref } from 'vue'
import type {
  UnifiedFormConfig,
  UnifiedFieldConfig,
  ValidationRule,
} from '@/types/form-renderer'

/**
 * 表单验证器 Composable
 */
export function useFormValidator(
  formConfig: Ref<UnifiedFormConfig | null>,
  formData: Record<string, unknown>,
  validationErrors: Record<string, string>,
) {
  const validationRules = ref<Record<string, ValidationRule[]>>({})

  // 初始化验证规则
  const initializeValidationRules = () => {
    if (!formConfig.value) return

    const rules: Record<string, ValidationRule[]> = {}

    for (const section of formConfig.value.sections) {
      for (const field of section.fields) {
        if (field.validation && field.validation.length > 0) {
          rules[field.id] = field.validation
        }
      }
    }

    validationRules.value = rules
  }

  // 验证单个字段
  const validateField = (
    field: UnifiedFieldConfig,
    value: unknown,
  ): string | null => {
    const rules = field.validation || []

    for (const rule of rules) {
      const error = executeValidationRule(rule, value, field)
      if (error) {
        return error
      }
    }

    return null
  }

  // 执行验证规则
  const executeValidationRule = (
    rule: ValidationRule,
    value: unknown,
    field: UnifiedFieldConfig,
  ): string | null => {
    switch (rule.type) {
      case 'required':
        if (!value || value === '') {
          return rule.message || `${field.label}是必填项`
        }
        break

      case 'email':
        if (value && !isValidEmail(value)) {
          return rule.message || '请输入有效的邮箱地址'
        }
        break

      case 'url':
        if (value && !isValidUrl(value)) {
          return rule.message || '请输入有效的URL'
        }
        break

      case 'number':
        if (value && isNaN(Number(value))) {
          return rule.message || '请输入有效的数字'
        }
        break

      case 'min':
        if (value && Number(value) < rule.value) {
          return rule.message || `值不能小于${rule.value}`
        }
        break

      case 'max':
        if (value && Number(value) > rule.value) {
          return rule.message || `值不能大于${rule.value}`
        }
        break

      case 'minLength':
        if (value && value.length < rule.value) {
          return rule.message || `最少需要${rule.value}个字符`
        }
        break

      case 'maxLength':
        if (value && value.length > rule.value) {
          return rule.message || `最多允许${rule.value}个字符`
        }
        break

      case 'pattern':
        if (value && !new RegExp(rule.value).test(value)) {
          return rule.message || '格式不正确'
        }
        break

      case 'custom':
        if (rule.validator && typeof rule.validator === 'function') {
          const result = rule.validator(value, field)
          if (result !== true) {
            return result || rule.message || '验证失败'
          }
        }
        break

      default:
        console.warn(`未知的验证规则类型: ${rule.type}`)
    }

    return null
  }

  // 验证所有字段
  const validateAllFields = (): Record<string, string> => {
    if (!formConfig.value) return {}

    const errors: Record<string, string> = {}

    for (const section of formConfig.value.sections) {
      for (const field of section.fields) {
        const value = formData[field.id]
        const error = validateField(field, value)
        if (error) {
          errors[field.id] = error
        }
      }
    }

    return errors
  }

  // 获取字段验证状态
  const getFieldValidationStatus = (fieldId: string) => {
    return {
      hasError: !!validationErrors[fieldId],
      errorMessage: validationErrors[fieldId] || null,
      isValid: !validationErrors[fieldId],
    }
  }

  // 清除字段错误
  const clearFieldError = (fieldId: string) => {
    if (validationErrors[fieldId]) {
      delete validationErrors[fieldId]
    }
  }

  // 清除所有错误
  const clearAllErrors = () => {
    Object.keys(validationErrors).forEach((key) => {
      delete validationErrors[key]
    })
  }

  // 工具函数
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  // 监听配置变化
  watch(formConfig, initializeValidationRules, { immediate: true })

  return {
    validationRules,
    validateField,
    validateAllFields,
    getFieldValidationStatus,
    clearFieldError,
    clearAllErrors,
    initializeValidationRules,
  }
}
