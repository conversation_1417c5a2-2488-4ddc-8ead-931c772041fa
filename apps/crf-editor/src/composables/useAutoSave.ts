import { ref, watch, onUnmounted } from 'vue'
import { useEditorStore } from '@/stores/editor-store'
import { templateAPI } from '@/api'
import { debounce } from 'lodash-es'

interface AutoSaveOptions {
  interval?: number // 自动保存间隔（毫秒）
  debounceTime?: number // 防抖时间（毫秒）
  resourceType: string // 资源类型
  resourceId: string // 资源ID
  enabled?: boolean // 是否启用自动保存
}

interface AutoSaveConfig {
  enabled: boolean
  interval_seconds: number
  debounce_ms: number
  max_history: number
  cleanup_days: number
  storage_type: string
}

export interface AutoSaveStatus {
  isSaving: boolean
  lastSaveTime: Date | null
  error: string | null
  hasUnsavedChanges: boolean
  config: AutoSaveConfig | null
}

export function useAutoSave(options: AutoSaveOptions) {
  const editorStore = useEditorStore()

  const { resourceType, resourceId, enabled = true } = options

  // 自动保存状态
  const status = ref<AutoSaveStatus>({
    isSaving: false,
    lastSaveTime: null,
    error: null,
    hasUnsavedChanges: false,
    config: null,
  })

  // 动态配置
  let currentInterval = options.interval || 30000
  let currentDebounceTime = options.debounceTime || 2000
  let debouncedSave: ReturnType<typeof debounce> | null = null
  let autoSaveTimer: number | null = null

  // 确保使用有效的UUID格式的resourceId
  const validResourceId = resourceId.match(
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  )
    ? resourceId
    : `demo-template-${resourceId}`

  // 加载自动保存配置
  const loadAutoSaveConfig = async () => {
    try {
      const response = await templateAPI.getAutoSaveConfig()
      if (response.success && response.data?.auto_save_config) {
        const config = response.data.auto_save_config
        status.value.config = config

        // 更新配置
        currentInterval = config.interval_seconds * 1000
        currentDebounceTime = config.debounce_ms

        console.log('📋 自动保存配置已加载:', {
          interval: currentInterval,
          debounce: currentDebounceTime,
          enabled: config.enabled,
          storage: config.storage_type,
        })

        // 重新初始化防抖函数
        initDebouncedSave()

        // 重启定时器（如果已启动）
        if (autoSaveTimer) {
          stopAutoSave()
          startAutoSave()
        }

        return config
      }
    } catch (error) {
      console.error('❌ 加载自动保存配置失败:', error)
      // 使用默认配置
      status.value.config = {
        enabled: true,
        interval_seconds: 30,
        debounce_ms: 2000,
        max_history: 10,
        cleanup_days: 7,
        storage_type: 'backend',
      }
    }
  }

  // 保存数据到后端
  const saveToBackend = async (data: Record<string, unknown>) => {
    if (!enabled) return

    try {
      status.value.isSaving = true
      status.value.error = null

      console.log('🔄 开始保存到后端...', {
        resourceType,
        resourceId: validResourceId,
        dataSize: JSON.stringify(data).length,
      })

      // 调用后端API保存草稿
      const response = await templateAPI.saveDraft(validResourceId, data)

      if (response.success) {
        status.value.lastSaveTime = new Date()
        status.value.hasUnsavedChanges = false

        console.log('✅ 保存到后端成功', {
          savedAt: status.value.lastSaveTime,
        })
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (error) {
      status.value.error = error instanceof Error ? error.message : '保存失败'
      console.error('❌ 保存到后端失败:', {
        error,
        resourceType,
        resourceId: validResourceId,
      })

      // 后端保存失败，尝试本地存储作为备份
      await saveToLocalStorage(data)
    } finally {
      status.value.isSaving = false
    }
  }

  // 保存数据到本地存储（作为备份）
  const saveToLocalStorage = async (data: Record<string, unknown>) => {
    try {
      const saveKey = `autosave_${resourceType}_${validResourceId}`
      const saveData = {
        resource_type: resourceType,
        resource_id: validResourceId,
        data,
        saved_at: new Date().toISOString(),
      }

      localStorage.setItem(saveKey, JSON.stringify(saveData))
      console.log('📁 本地存储备份保存成功')
    } catch (error) {
      console.error('❌ 本地存储备份失败:', error)
    }
  }

  // 获取当前编辑器数据
  const getCurrentData = () => {
    return {
      sections: editorStore.sections,
      pageConfig: editorStore.pageConfig,
      formData: editorStore.formData,
      timestamp: Date.now(),
    }
  }

  // 初始化防抖函数
  const initDebouncedSave = () => {
    debouncedSave = debounce(() => {
      const data = getCurrentData()
      saveToBackend(data)
    }, currentDebounceTime)
  }

  // 防抖的保存函数（废弃，用initDebouncedSave代替）
  // const debouncedSave = debounce(() => {
  //   const data = getCurrentData()
  //   saveToBackend(data)
  // }, debounceTime)

  // 手动保存
  const save = () => {
    const data = getCurrentData()
    return saveToBackend(data)
  }

  // 标记有未保存的更改
  const markUnsaved = () => {
    status.value.hasUnsavedChanges = true
    if (enabled && debouncedSave) {
      debouncedSave()
    }
  }

  // 从本地存储恢复数据
  const restore = async () => {
    try {
      console.log('🔄 尝试恢复自动保存数据...', {
        resourceType,
        resourceId: validResourceId,
      })

      const saveKey = `autosave_${resourceType}_${validResourceId}`
      const savedDataStr = localStorage.getItem(saveKey)

      if (savedDataStr) {
        const savedDataWrapper = JSON.parse(savedDataStr)
        const savedData = savedDataWrapper.data

        // 检查数据是否有效
        if (!savedData || typeof savedData !== 'object') {
          console.warn('⚠️ 自动保存的数据格式无效', { savedData })
          return false
        }

        console.log('📥 找到自动保存数据，开始恢复...', {
          hasSection: !!savedData.sections,
          hasPageConfig: !!savedData.pageConfig,
          hasFormData: !!savedData.formData,
          timestamp: savedData.timestamp,
        })

        // 恢复编辑器状态
        if (savedData.sections && Array.isArray(savedData.sections)) {
          editorStore.sections.splice(
            0,
            editorStore.sections.length,
            ...savedData.sections,
          )
          console.log(
            '✅ 恢复sections数据:',
            savedData.sections.length,
            '个章节',
          )
        }
        if (savedData.pageConfig && typeof savedData.pageConfig === 'object') {
          Object.assign(editorStore.pageConfig, savedData.pageConfig)
          console.log('✅ 恢复pageConfig数据')
        }
        if (savedData.formData && typeof savedData.formData === 'object') {
          Object.assign(editorStore.formData, savedData.formData)
          console.log(
            '✅ 恢复formData数据:',
            Object.keys(savedData.formData).length,
            '个字段',
          )
        }

        status.value.lastSaveTime = new Date(savedDataWrapper.saved_at)
        status.value.hasUnsavedChanges = false

        console.log('🎉 自动保存数据恢复完成', {
          savedAt: status.value.lastSaveTime,
        })
        return true
      }
    } catch (error) {
      console.log('ℹ️ 没有找到自动保存数据或恢复失败:', {
        error: error instanceof Error ? error.message : error,
        resourceType,
        resourceId: validResourceId,
      })
    }
    return false
  }

  // 删除自动保存的数据
  const clearSaved = async () => {
    try {
      const saveKey = `autosave_${resourceType}_${validResourceId}`
      localStorage.removeItem(saveKey)
      status.value.lastSaveTime = null
      status.value.hasUnsavedChanges = false
      console.log('🗑️ 自动保存数据已清除')
    } catch (error) {
      console.error('❌ 清除自动保存数据失败:', error)
    }
  }

  // 启动自动保存定时器
  const startAutoSave = () => {
    if (autoSaveTimer || !status.value.config?.enabled) return

    autoSaveTimer = window.setInterval(() => {
      if (status.value.hasUnsavedChanges && !status.value.isSaving) {
        const data = getCurrentData()
        saveToBackend(data)
      }
    }, currentInterval)

    console.log(`⏰ 自动保存已启动，间隔: ${currentInterval}ms`)
  }

  // 停止自动保存定时器
  const stopAutoSave = () => {
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
      console.log('⏹️ 自动保存已停止')
    }
  }

  // 监听编辑器状态变化
  const startWatching = () => {
    // 监听sections变化
    const stopWatchSections = watch(
      () => editorStore.sections,
      () => markUnsaved(),
      { deep: true },
    )

    // 监听pageConfig变化
    const stopWatchPageConfig = watch(
      () => editorStore.pageConfig,
      () => markUnsaved(),
      { deep: true },
    )

    // 监听formData变化
    const stopWatchFormData = watch(
      () => editorStore.formData,
      () => markUnsaved(),
      { deep: true },
    )

    return () => {
      stopWatchSections()
      stopWatchPageConfig()
      stopWatchFormData()
    }
  }

  // 页面离开前保存
  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    if (status.value.hasUnsavedChanges) {
      event.preventDefault()
      event.returnValue = '您有未保存的更改，确定要离开吗？'

      // 尝试立即保存
      const data = getCurrentData()
      saveToBackend(data)
    }
  }

  // 清理函数
  let stopWatching: (() => void) | null = null

  const cleanup = () => {
    stopWatching?.()
    stopAutoSave()
    window.removeEventListener('beforeunload', handleBeforeUnload)
  }

  // 在组件销毁时清理
  onUnmounted(() => {
    cleanup()
  })

  // 初始化
  const init = async () => {
    if (!enabled) return

    // 首先初始化防抖函数（使用默认配置）
    initDebouncedSave()

    // 在组件销毁时清理
    stopWatching = startWatching()
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 然后加载配置并重新初始化
    await loadAutoSaveConfig()

    // 重新初始化防抖函数（使用服务器配置）
    initDebouncedSave()

    // 启动自动保存（只有在配置允许时）
    if (status.value.config?.enabled) {
      startAutoSave()
    }

    console.log('✅ 自动保存功能已初始化（使用后端API + 动态配置）')
  }

  return {
    status,
    save,
    restore,
    clearSaved,
    startAutoSave,
    stopAutoSave,
    init,
    cleanup,
    markUnsaved,
    loadAutoSaveConfig,
  }
}
