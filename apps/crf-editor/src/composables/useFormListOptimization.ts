import { ref, computed, nextTick } from 'vue'
import { debouncedRef } from '@vueuse/core'

export interface FormItem {
  id: string
  name?: string
  title?: string
  description?: string
  status?: string
  created_at?: string
  updated_at?: string
  [key: string]: unknown
}

export interface FormListState {
  forms: FormItem[]
  loading: boolean
  searchQuery: string
  pagination: {
    current: number
    size: number
    total: number
  }
}

/**
 * 表单列表优化 Composable
 * 提供防抖搜索、智能更新、过渡动画等功能
 */
export function useFormListOptimization() {
  // 状态管理
  const state = ref<FormListState>({
    forms: [],
    loading: false,
    searchQuery: '',
    pagination: {
      current: 1,
      size: 12,
      total: 0,
    },
  })
  // 新添加的表单ID集合，用于入场动画
  const newlyAddedFormIds = ref(new Set<string>())

  // 防抖搜索 - 使用computed来创建响应式的防抖查询
  const searchQueryRef = computed(() => state.value.searchQuery)
  const debouncedSearchQuery = debouncedRef(searchQueryRef, 300)

  // 过滤后的表单列表
  const filteredForms = computed(() => {
    if (!debouncedSearchQuery.value) return state.value.forms

    const query = debouncedSearchQuery.value.toLowerCase()
    return state.value.forms.filter(
      (form) =>
        (form.name || form.title || form.id).toLowerCase().includes(query) ||
        (form.description || '').toLowerCase().includes(query),
    )
  })

  // 分页后的表单列表
  const paginatedForms = computed(() => {
    const start =
      (state.value.pagination.current - 1) * state.value.pagination.size
    const end = start + state.value.pagination.size
    return filteredForms.value.slice(start, end)
  })

  /**
   * 智能添加表单到列表
   * 避免重新获取整个列表
   */
  const addFormToList = async (newForm: FormItem) => {
    try {
      // 检查是否已存在相同ID的表单，避免重复添加
      const existingIndex = state.value.forms.findIndex(
        (f) => f.id === newForm.id,
      )
      if (existingIndex > -1) {
        // 如果已存在，更新现有的表单
        state.value.forms[existingIndex] = newForm
        console.warn('表单已存在，已更新:', newForm.id)
      } else {
        // 添加到列表开头
        state.value.forms.unshift(newForm)
        state.value.pagination.total += 1

        // 标记为新添加的表单，用于入场动画
        newlyAddedFormIds.value.add(newForm.id)

        // 3秒后移除入场动画标记
        setTimeout(() => {
          newlyAddedFormIds.value.delete(newForm.id)
        }, 3000)
      }

      // 等待DOM更新，确保动画效果
      await nextTick()
    } catch (error) {
      console.error('添加表单到列表失败:', error)
      throw error // 重新抛出错误，让调用方处理
    }
  }

  /**
   * 智能从列表移除表单
   * 避免重新获取整个列表
   */
  const removeFormFromList = async (formId: string) => {
    const index = state.value.forms.findIndex((f) => f.id === formId)
    if (index > -1) {
      state.value.forms.splice(index, 1)
      state.value.pagination.total -= 1

      // 等待DOM更新，确保动画效果
      await nextTick()
    }
  }

  /**
   * 更新表单状态
   * 本地更新，避免重新获取
   */
  const updateFormInList = async (
    formId: string,
    updates: Partial<FormItem>,
  ) => {
    const index = state.value.forms.findIndex((f) => f.id === formId)
    if (index > -1) {
      state.value.forms[index] = {
        ...state.value.forms[index],
        ...updates,
        updated_at: new Date().toISOString(),
      }

      // 等待DOM更新
      await nextTick()
    }
  }

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    state.value.loading = loading
  }

  /**
   * 设置表单列表
   */
  const setForms = (forms: FormItem[]) => {
    state.value.forms = forms
  }

  /**
   * 设置搜索查询
   */
  const setSearchQuery = (query: string) => {
    state.value.searchQuery = query
    // 防抖查询会自动通过computed响应式更新
  }

  /**
   * 设置分页信息
   */
  const setPagination = (pagination: Partial<FormListState['pagination']>) => {
    state.value.pagination = {
      ...state.value.pagination,
      ...pagination,
    }
  }

  /**
   * 重置状态
   */
  const reset = () => {
    state.value = {
      forms: [],
      loading: false,
      searchQuery: '',
      pagination: {
        current: 1,
        size: 12,
        total: 0,
      },
    }
    // 防抖查询会自动通过computed响应式重置
  }

  return {
    // 状态
    state: computed(() => state.value),
    filteredForms,
    paginatedForms,
    debouncedSearchQuery,
    newlyAddedFormIds: computed(() => newlyAddedFormIds.value),

    // 方法
    addFormToList,
    removeFormFromList,
    updateFormInList,
    setLoading,
    setForms,
    setSearchQuery,
    setPagination,
    reset,
  }
}
