import {
  showNoPermissionError,
  showPermissionError,
  showPermissionDenied,
  clearPermissionError,
} from '@/utils/permission-error'
import { useMessage, createDiscreteApi } from 'naive-ui'

// 创建一个单例消息实例，用于组件上下文外的错误处理
let globalMessageInstance: any = null

/**
 * 获取全局消息实例
 */
function getGlobalMessage() {
  if (!globalMessageInstance) {
    const { message } = createDiscreteApi(['message'])
    globalMessageInstance = message
  }
  return globalMessageInstance
}

/**
 * 错误处理Composable
 * 统一管理各种错误提示，包括权限错误、API错误等
 */
export function useErrorHandler() {
  // 尝试使用组件上下文中的 message API
  let message
  try {
    message = useMessage()
  } catch (error) {
    // 如果在组件上下文外使用，则使用全局单例实例
    message = getGlobalMessage()
  }

  /**
   * 处理权限错误
   */
  const handlePermissionError = (error: {
    code?: string
    message?: string
    type?: 'no_permission' | 'access_denied' | 'forbidden'
    resource?: string
    action?: string
  }) => {
    console.log('权限错误:', error)

    switch (error.type || error.code) {
      case 'no_permission':
      case 'access_denied':
      case 'forbidden':
      case '403':
        showNoPermissionError(error.message)
        break

      default:
        if (
          error.message?.includes('权限') ||
          error.message?.includes('permission')
        ) {
          showNoPermissionError(error.message)
        } else {
          message.error(error.message || '权限验证失败')
        }
    }
  }

  /**
   * 处理路由权限错误
   */
  const handleRoutePermissionError = (routeName?: string) => {
    let description = '请联系管理员获取相应权限。'

    if (routeName) {
      const routeMessages: Record<string, string> = {
        AdminUsers: '您需要用户管理权限才能访问此页面。',
        AdminRoles: '您需要角色管理权限才能访问此页面。',
        Admin: '您需要系统管理权限才能访问此页面。',
        Templates: '您需要模板管理权限才能访问此页面。',
        Projects: '您需要项目管理权限才能访问此页面。',
        DataAnalysis: '您需要数据分析权限才能访问此页面。',
      }

      description = routeMessages[routeName] || description
    }

    showNoPermissionError(description)
  }

  /**
   * 处理API错误
   */
  const handleAPIError = (error: any) => {
    console.log('API错误:', error)

    if (error.status === 403 || error.code === 403) {
      handlePermissionError({
        type: 'forbidden',
        message: error.message || '您没有访问此资源的权限',
      })
      return
    }

    if (error.status === 401 || error.code === 401) {
      message.error('登录已过期，请重新登录')
      // 可以在这里触发登出逻辑
      return
    }

    // 其他错误使用常规提示
    message.error(error.message || '操作失败')
  }

  /**
   * 处理操作权限错误
   */
  const handleOperationError = (operation: string, resource?: string) => {
    const resourceText = resource ? `${resource}` : '此'
    showPermissionDenied(
      '操作权限不足',
      `您没有${operation}${resourceText}的权限，请联系管理员。`,
    )
  }

  /**
   * 清除所有错误提示
   */
  const clearAllErrors = () => {
    clearPermissionError()
  }

  return {
    // 权限错误处理
    handlePermissionError,
    handleRoutePermissionError,
    handleOperationError,

    // API错误处理
    handleAPIError,

    // 通用方法
    clearAllErrors,

    // 直接导出的错误提示方法
    showNoPermissionError,
    showPermissionError,
    showPermissionDenied,
    clearPermissionError,
  }
}

export default useErrorHandler
