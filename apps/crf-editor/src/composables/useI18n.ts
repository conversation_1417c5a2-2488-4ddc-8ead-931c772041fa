/**
 * 国际化管理器
 * 支持多语言切换、动态加载、命名空间、插值、复数形式等
 */

import { ref, shallowRef, computed, watch } from 'vue'
import { useCacheManager } from '@/composables/useCacheManager'
import { useErrorHandler, ErrorCategory } from '@/utils/error-handler'
// import { useTimerManager } from '@/composables/useTimerManager'

// 支持的语言
export type SupportedLocale =
  | 'zh-CN'
  | 'zh-TW'
  | 'en-US'
  | 'ja-JP'
  | 'ko-KR'
  | 'fr-FR'
  | 'de-DE'
  | 'es-ES'

// 消息格式
export type MessageValue = string | number | boolean | MessageObject
export interface MessageObject {
  [key: string]: MessageValue
}

// 插值参数
export interface InterpolateParams {
  [key: string]: string | number | boolean
}

// 复数规则
export type PluralRule = 'zero' | 'one' | 'two' | 'few' | 'many' | 'other'

export interface PluralRules {
  [key: string]: PluralRule
}

// 语言配置
export interface LocaleConfig {
  code: SupportedLocale
  name: string
  nativeName: string
  flag: string
  dir: 'ltr' | 'rtl'
  fallback?: SupportedLocale
  pluralRules?: PluralRules
  dateTimeFormats?: Record<string, Intl.DateTimeFormatOptions>
  numberFormats?: Record<string, Intl.NumberFormatOptions>
}

// 翻译消息
export interface Messages {
  [locale: string]: {
    [namespace: string]: MessageObject
  }
}

// 国际化配置
export interface I18nConfig {
  locale: SupportedLocale
  fallbackLocale: SupportedLocale
  messages: Messages
  lazy: boolean
  silentTranslationWarn: boolean
  missingHandler?: (locale: string, key: string, instance?: unknown) => string
}

/**
 * 国际化组合式API
 */
export function useI18n(config: Partial<I18nConfig> = {}) {
  const cache = useCacheManager<Record<string, unknown>>({
    strategy: 'LRU',
    maxSize: 50,
    defaultTTL: 3600000, // 1小时
    storageKey: 'i18n-cache',
  })
  const { handleError } = useErrorHandler()
  // const { safeSetTimeout } = useTimerManager()

  // 默认配置
  const defaultConfig: I18nConfig = {
    locale: 'zh-CN',
    fallbackLocale: 'zh-CN',
    messages: {},
    lazy: true,
    silentTranslationWarn: false,
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 当前语言和消息
  const currentLocale = ref<SupportedLocale>(finalConfig.locale)
  const messages = shallowRef<Messages>(finalConfig.messages)
  const loadingLocales = shallowRef<Set<string>>(new Set())
  const loadedLocales = shallowRef<Set<string>>(new Set())

  // 语言配置映射
  const localeConfigs: Record<SupportedLocale, LocaleConfig> = {
    'zh-CN': {
      code: 'zh-CN',
      name: 'Chinese (Simplified)',
      nativeName: '简体中文',
      flag: '🇨🇳',
      dir: 'ltr',
      dateTimeFormats: {
        short: { year: 'numeric', month: '2-digit', day: '2-digit' },
        long: {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long',
        },
      },
      numberFormats: {
        currency: { style: 'currency', currency: 'CNY' },
        decimal: { minimumFractionDigits: 2, maximumFractionDigits: 2 },
      },
    },
    'zh-TW': {
      code: 'zh-TW',
      name: 'Chinese (Traditional)',
      nativeName: '繁體中文',
      flag: '🇹🇼',
      dir: 'ltr',
      fallback: 'zh-CN',
    },
    'en-US': {
      code: 'en-US',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      dir: 'ltr',
      dateTimeFormats: {
        short: { year: 'numeric', month: '2-digit', day: '2-digit' },
        long: {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long',
        },
      },
      numberFormats: {
        currency: { style: 'currency', currency: 'USD' },
        decimal: { minimumFractionDigits: 2, maximumFractionDigits: 2 },
      },
    },
    'ja-JP': {
      code: 'ja-JP',
      name: 'Japanese',
      nativeName: '日本語',
      flag: '🇯🇵',
      dir: 'ltr',
      fallback: 'en-US',
    },
    'ko-KR': {
      code: 'ko-KR',
      name: 'Korean',
      nativeName: '한국어',
      flag: '🇰🇷',
      dir: 'ltr',
      fallback: 'en-US',
    },
    'fr-FR': {
      code: 'fr-FR',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷',
      dir: 'ltr',
      fallback: 'en-US',
    },
    'de-DE': {
      code: 'de-DE',
      name: 'German',
      nativeName: 'Deutsch',
      flag: '🇩🇪',
      dir: 'ltr',
      fallback: 'en-US',
    },
    'es-ES': {
      code: 'es-ES',
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸',
      dir: 'ltr',
      fallback: 'en-US',
    },
  }

  // 计算属性
  const availableLocales = computed(
    () => Object.keys(localeConfigs) as SupportedLocale[],
  )
  const currentLocaleConfig = computed(() => localeConfigs[currentLocale.value])
  const isRTL = computed(() => currentLocaleConfig.value.dir === 'rtl')

  /**
   * 获取嵌套对象的值
   */
  function getNestedValue(obj: unknown, path: string): unknown {
    return path.split('.').reduce((current: Record<string, unknown>, key) => {
      return current?.[key]
    }, obj)
  }

  /**
   * 插值处理
   */
  function interpolate(
    template: string,
    params: InterpolateParams = {},
  ): string {
    return template.replace(/\{([^}]+)\}/g, (match, key) => {
      const value = params[key]
      return value !== undefined ? String(value) : match
    })
  }

  /**
   * 复数形式处理
   */
  function handlePlural(key: string, count: number, locale: string): string {
    const pluralKey = getPluralKey(count, locale)
    const pluralMessage = getNestedValue(
      messages.value[locale],
      `${key}.${pluralKey}`,
    )

    if (pluralMessage) {
      return pluralMessage
    }

    // 回退到 other 形式
    const otherMessage = getNestedValue(messages.value[locale], `${key}.other`)
    return otherMessage || key
  }

  /**
   * 获取复数键
   */
  function getPluralKey(count: number, locale: string): PluralRule {
    // 简化的复数规则，实际项目中应使用 Intl.PluralRules
    if (locale.startsWith('zh')) {
      return 'other' // 中文没有复数形式
    }

    if (locale.startsWith('en')) {
      return count === 1 ? 'one' : 'other'
    }

    // 其他语言的复数规则...
    return count === 1 ? 'one' : 'other'
  }

  /**
   * 翻译函数
   */
  function t(key: string, params?: InterpolateParams | number): string {
    // 如果第二个参数是数字，处理复数
    if (typeof params === 'number') {
      return handlePluralTranslation(key, params)
    }

    const locale = currentLocale.value
    let message = getTranslation(key, locale)

    // 如果找不到，尝试回退语言
    if (!message && currentLocaleConfig.value.fallback) {
      message = getTranslation(key, currentLocaleConfig.value.fallback)
    }

    // 最后回退到默认语言
    if (!message && locale !== finalConfig.fallbackLocale) {
      message = getTranslation(key, finalConfig.fallbackLocale)
    }

    // 如果仍然找不到
    if (!message) {
      if (!finalConfig.silentTranslationWarn) {
        console.warn(
          `Translation key "${key}" not found for locale "${locale}"`,
        )
      }

      if (finalConfig.missingHandler) {
        return finalConfig.missingHandler(locale, key)
      }

      return key
    }

    // 插值处理
    if (params && typeof params === 'object') {
      return interpolate(message, params)
    }

    return message
  }

  /**
   * 处理复数翻译
   */
  function handlePluralTranslation(key: string, count: number): string {
    const locale = currentLocale.value
    let message = handlePlural(key, count, locale)

    // 回退处理
    if (message === key && currentLocaleConfig.value.fallback) {
      message = handlePlural(key, count, currentLocaleConfig.value.fallback)
    }

    if (message === key && locale !== finalConfig.fallbackLocale) {
      message = handlePlural(key, count, finalConfig.fallbackLocale)
    }

    // 插值处理（添加count参数）
    return interpolate(message, { count })
  }

  /**
   * 获取翻译文本
   */
  function getTranslation(key: string, locale: string): string | null {
    // 支持命名空间，如 'common.buttons.save'
    const parts = key.split('.')
    const namespace = parts.length > 1 ? parts[0] : 'common'
    const messageKey = parts.length > 1 ? parts.slice(1).join('.') : key

    const namespaceMessages = namespace
      ? messages.value[locale]?.[namespace]
      : undefined
    return getNestedValue(namespaceMessages, messageKey) || null
  }

  /**
   * 切换语言
   */
  async function setLocale(locale: SupportedLocale): Promise<void> {
    if (locale === currentLocale.value) return

    try {
      // 如果启用懒加载且未加载过该语言
      if (finalConfig.lazy && !loadedLocales.value.has(locale)) {
        await loadLocaleMessages(locale)
      }

      const oldLocale = currentLocale.value
      currentLocale.value = locale

      // 更新文档语言属性
      document.documentElement.lang = locale
      document.documentElement.dir = localeConfigs[locale].dir

      // 持久化语言设置
      localStorage.setItem('preferred-locale', locale)

      console.log(`语言已切换: ${oldLocale} -> ${locale}`)
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM)
      throw new Error(`语言切换失败: ${locale}`)
    }
  }

  /**
   * 动态加载语言包
   */
  async function loadLocaleMessages(locale: string): Promise<void> {
    if (loadingLocales.value.has(locale) || loadedLocales.value.has(locale)) {
      return
    }

    loadingLocales.value.add(locale)

    try {
      // 检查缓存
      const cacheKey = `locale:${locale}`
      let localeMessages = cache.get(cacheKey)

      if (!localeMessages) {
        // 动态导入语言包
        const modules = await Promise.allSettled([
          import(`../locales/${locale}/common.json`),
          import(`../locales/${locale}/components.json`),
          import(`../locales/${locale}/pages.json`),
          import(`../locales/${locale}/errors.json`),
        ])

        localeMessages = {}
        const namespaces = ['common', 'components', 'pages', 'errors']

        modules.forEach((module, index) => {
          if (module.status === 'fulfilled') {
            const namespace = namespaces[index]
            if (namespace) {
              localeMessages[namespace] = module.value.default || module.value
            }
          } else {
            const namespace = namespaces[index]
            if (namespace) {
              console.warn(
                `Failed to load ${namespace} for ${locale}:`,
                module.reason,
              )
            }
          }
        })

        // 缓存加载的消息
        cache.set(cacheKey, localeMessages)
      }

      // 合并消息
      if (!messages.value[locale]) {
        messages.value[locale] = {}
      }

      Object.assign(messages.value[locale], localeMessages)
      loadedLocales.value.add(locale)

      console.log(`语言包加载成功: ${locale}`)
    } catch (error) {
      console.error(`语言包加载失败: ${locale}`, error)
      throw error
    } finally {
      loadingLocales.value.delete(locale)
    }
  }

  /**
   * 预加载语言包
   */
  async function preloadLocales(locales: SupportedLocale[]): Promise<void> {
    const promises = locales.map((locale) =>
      loadLocaleMessages(locale).catch((error) => {
        console.warn(`预加载语言包失败: ${locale}`, error)
      }),
    )

    await Promise.allSettled(promises)
  }

  /**
   * 添加翻译消息
   */
  function addMessages(
    locale: string,
    namespace: string,
    newMessages: MessageObject,
  ): void {
    if (!messages.value[locale]) {
      messages.value[locale] = {}
    }

    if (!messages.value[locale][namespace]) {
      messages.value[locale][namespace] = {}
    }

    Object.assign(messages.value[locale][namespace], newMessages)
  }

  /**
   * 移除翻译消息
   */
  function removeMessages(locale: string, namespace?: string): void {
    if (namespace) {
      delete messages.value[locale]?.[namespace]
    } else {
      delete messages.value[locale]
    }
  }

  /**
   * 获取可用语言列表
   */
  function getAvailableLocales(): LocaleConfig[] {
    return availableLocales.value.map((locale) => localeConfigs[locale])
  }

  /**
   * 检测浏览器语言
   */
  function detectBrowserLocale(): SupportedLocale {
    const browserLang =
      navigator.language || navigator.languages?.[0] || 'en-US'

    // 直接匹配
    if (availableLocales.value.includes(browserLang as SupportedLocale)) {
      return browserLang as SupportedLocale
    }

    // 匹配语言代码（忽略国家/地区）
    const langCode = browserLang.split('-')[0]
    const match = langCode
      ? availableLocales.value.find((locale) => locale.startsWith(langCode))
      : undefined

    return match || finalConfig.fallbackLocale
  }

  /**
   * 格式化日期
   */
  function formatDate(date: Date, format = 'short'): string {
    const config = currentLocaleConfig.value.dateTimeFormats?.[format]
    return new Intl.DateTimeFormat(currentLocale.value, config).format(date)
  }

  /**
   * 格式化数字
   */
  function formatNumber(value: number, format = 'decimal'): string {
    const config = currentLocaleConfig.value.numberFormats?.[format]
    return new Intl.NumberFormat(currentLocale.value, config).format(value)
  }

  /**
   * 格式化货币
   */
  function formatCurrency(value: number, currency?: string): string {
    const config = {
      ...currentLocaleConfig.value.numberFormats?.currency,
      currency:
        currency || currentLocaleConfig.value.numberFormats?.currency?.currency,
    }
    return new Intl.NumberFormat(currentLocale.value, config).format(value)
  }

  /**
   * 获取相对时间
   */
  function formatRelativeTime(date: Date): string {
    const now = new Date()
    const diff = date.getTime() - now.getTime()
    const absDiff = Math.abs(diff)

    const rtf = new Intl.RelativeTimeFormat(currentLocale.value, {
      numeric: 'auto',
    })

    if (absDiff < 60 * 1000) {
      // 1分钟
      return rtf.format(Math.round(diff / 1000), 'second')
    } else if (absDiff < 60 * 60 * 1000) {
      // 1小时
      return rtf.format(Math.round(diff / (60 * 1000)), 'minute')
    } else if (absDiff < 24 * 60 * 60 * 1000) {
      // 1天
      return rtf.format(Math.round(diff / (60 * 60 * 1000)), 'hour')
    } else if (absDiff < 30 * 24 * 60 * 60 * 1000) {
      // 30天
      return rtf.format(Math.round(diff / (24 * 60 * 60 * 1000)), 'day')
    } else if (absDiff < 12 * 30 * 24 * 60 * 60 * 1000) {
      // 12个月
      return rtf.format(Math.round(diff / (30 * 24 * 60 * 60 * 1000)), 'month')
    } else {
      return rtf.format(
        Math.round(diff / (12 * 30 * 24 * 60 * 60 * 1000)),
        'year',
      )
    }
  }

  /**
   * 初始化
   */
  async function init(): Promise<void> {
    // 从本地存储恢复语言设置
    const savedLocale = localStorage.getItem(
      'preferred-locale',
    ) as SupportedLocale
    if (savedLocale && availableLocales.value.includes(savedLocale)) {
      await setLocale(savedLocale)
    } else {
      // 检测浏览器语言
      const detectedLocale = detectBrowserLocale()
      if (detectedLocale !== currentLocale.value) {
        await setLocale(detectedLocale)
      }
    }

    // 预加载常用语言包
    const commonLocales: SupportedLocale[] = ['zh-CN', 'en-US']
    await preloadLocales(
      commonLocales.filter((locale) => locale !== currentLocale.value),
    )
  }

  // 监听语言变化
  watch(currentLocale, (newLocale) => {
    // 更新页面标题方向
    document.documentElement.dir = localeConfigs[newLocale].dir
  })

  return {
    // 状态
    currentLocale,
    messages,
    availableLocales,
    currentLocaleConfig,
    isRTL,
    loadingLocales,
    loadedLocales,

    // 翻译函数
    t,

    // 语言管理
    setLocale,
    getAvailableLocales,
    detectBrowserLocale,

    // 消息管理
    addMessages,
    removeMessages,
    loadLocaleMessages,
    preloadLocales,

    // 格式化
    formatDate,
    formatNumber,
    formatCurrency,
    formatRelativeTime,

    // 工具方法
    interpolate,

    // 配置
    config: finalConfig,

    // 初始化
    init,

    // 清理
    cleanup: () => {
      cache.cleanup()
    },
  }
}

/**
 * 创建简单的翻译函数
 */
export function createI18n(config: Partial<I18nConfig> = {}) {
  const i18n = useI18n(config)

  // 全局翻译函数
  const $t = i18n.t

  return {
    ...i18n,
    $t,
    install(app: unknown) {
      app.config.globalProperties.$t = $t
      app.provide('i18n', i18n)
    },
  }
}
