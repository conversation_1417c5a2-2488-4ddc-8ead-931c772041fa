import { ref, computed } from 'vue'

interface FormItem {
  id: string
  name?: string
  title?: string
  description?: string
  status?: string
  [key: string]: any
}

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(null, args), delay)
  }
}

export function useFormFilters() {
  // 过滤状态
  const activeTab = ref('published')
  const searchKeyword = ref('')
  const viewMode = ref<'grid' | 'list'>('grid')
  const tabSwitching = ref(false)

  // 根据标签页和搜索关键词过滤表单
  const getFilteredForms = (forms: FormItem[], deletedForms: FormItem[]) => {
    let filteredForms: FormItem[] = []

    // 先根据标签页过滤
    switch (activeTab.value) {
      case 'published':
        filteredForms = forms.filter((form) => form.status === 'published')
        break
      case 'unpublished':
        filteredForms = forms.filter((form) => form.status === 'draft')
        break
      case 'deleted':
        filteredForms = deletedForms
        break
      case 'all':
      default:
        filteredForms = [...forms, ...deletedForms]
    }

    // 再根据搜索关键词过滤
    if (searchKeyword.value.trim()) {
      const keyword = searchKeyword.value.trim().toLowerCase()
      filteredForms = filteredForms.filter((form) => {
        const name = (form.name || form.title || '').toLowerCase()
        const description = (form.description || '').toLowerCase()
        return name.includes(keyword) || description.includes(keyword)
      })
    }

    return filteredForms
  }

  // 标签页切换处理
  const handleTabChange = async (tabName: string, router: any, route: any) => {
    if (tabName === activeTab.value) return

    tabSwitching.value = true
    activeTab.value = tabName

    // 更新URL参数
    await router.replace({
      query: { ...route.query, tab: tabName },
    })

    setTimeout(() => {
      tabSwitching.value = false
    }, 300)
  }

  // 搜索处理方法
  const handleSearch = debounce((clearSelectionCallback?: () => void) => {
    // 搜索时清除选择状态
    if (clearSelectionCallback) {
      clearSelectionCallback()
    }
  }, 300)

  // 设置搜索关键词
  const setSearchKeyword = (keyword: string) => {
    searchKeyword.value = keyword
  }

  // 设置活动标签页
  const setActiveTab = (tab: string) => {
    activeTab.value = tab
  }

  // 设置视图模式，并调整分页大小
  const setViewMode = (mode: 'grid' | 'list', formList?: any) => {
    viewMode.value = mode

    // 根据视图模式调整分页大小
    if (formList) {
      const newPageSize = mode === 'grid' ? 12 : 20 // 网格视图12个，列表视图20个
      formList.setPagination({ current: 1, size: newPageSize })
      // 重新获取数据
      formList.fetchForms()
    }
  }

  // 设置标签页切换状态
  const setTabSwitching = (switching: boolean) => {
    tabSwitching.value = switching
  }

  return {
    // 状态
    activeTab,
    searchKeyword,
    viewMode,
    tabSwitching,

    // 方法
    getFilteredForms,
    handleTabChange,
    handleSearch,
    setSearchKeyword,
    setActiveTab,
    setViewMode,
    setTabSwitching,
  }
}
