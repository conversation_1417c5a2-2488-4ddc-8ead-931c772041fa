import { ref, computed, watch, onMounted, onActivated } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { templateAPI } from '@/api'

// 导入其他组合式函数
import { useFormList } from './useFormList'
import { useFormOperations } from './useFormOperations'
import { useFormSelection } from './useFormSelection'
import { useFormFilters } from './useFormFilters'

// 导入常量
import { createTableColumns } from '@/constants/tableColumns'
import { CREATE_FORM_DEFAULT, SUCCESS_MESSAGES } from '@/constants/formConfig'

export function useFormsPage() {
  const router = useRouter()
  const route = useRoute()
  const message = useMessage()

  // 使用其他组合式API
  const formList = useFormList()
  const formOperations = useFormOperations()
  const formSelection = useFormSelection()
  const formFilters = useFormFilters()

  // 本地状态
  const createDialogVisible = ref(false)
  const creating = ref(false)

  // 初始化标签页状态
  formFilters.setActiveTab((route.query.tab as string) || 'published')

  // 计算属性
  const tabFilteredForms = computed(() => {
    return formFilters.getFilteredForms(
      formList.forms.value,
      formList.deletedForms.value,
    )
  })

  // 分页处理（表格视图）
  const handlePageChange = async (page: number) => {
    formList.setPagination({ current: page })
    await formList.fetchForms()
  }

  const handlePageSizeChange = async (pageSize: number) => {
    formList.setPagination({ current: 1, size: pageSize })
    await formList.fetchForms()
  }

  // 无限滚动处理（网格视图）
  const handleLoadMore = async () => {
    const currentPage = formList.pagination.value.current
    const nextPage = currentPage + 1

    formList.setPagination({ current: nextPage })

    // 获取下一页数据并合并到现有数据
    const response = await templateAPI.getTemplates({
      limit: formList.pagination.value.size,
      offset: (nextPage - 1) * formList.pagination.value.size,
    })

    if (response.success && response.data?.templates) {
      const newForms = response.data.templates.map((template: any) => ({
        ...template,
        id:
          template.id ||
          template._id ||
          Math.random().toString(36).substr(2, 9),
      }))

      // 追加新数据到现有数据
      formList.setForms([...formList.forms.value, ...newForms])
    }
  }

  // 计算是否还有更多数据
  const hasMoreData = computed(() => {
    const pagination = formList.pagination.value
    const totalPages = Math.ceil(pagination.total / pagination.size)
    return pagination.current < totalPages
  })

  // 包装setViewMode，支持分页大小调整
  const handleViewModeChange = (mode: 'grid' | 'list') => {
    formFilters.setViewMode(mode, formList)
  }

  // 表格列定义
  const tableColumns = computed(() => {
    return createTableColumns({
      handleEdit: (row: any) =>
        formOperations.handleEdit(row, formFilters.activeTab.value),
      handleViewData: formOperations.handleViewData,
      handleRestoreForm: (row: any) => {
        console.log('恢复表单:', row)
        // 这里可以添加恢复表单的逻辑
      },
      getDropdownOptions: (form: any) => {
        if (form.status === 'deleted') {
          return [
            {
              key: 'restore',
              label: '恢复',
              disabled:
                formList.loading.value ||
                creating.value ||
                formOperations.duplicatingFormIds.value.has(String(form.id)),
            },
          ]
        }

        return [
          {
            key: 'duplicate',
            label: '复制',
            disabled:
              formList.loading.value ||
              creating.value ||
              formOperations.duplicatingFormIds.value.has(String(form.id)),
          },
          { type: 'divider' },
          {
            key: 'delete',
            label: '删除',
            disabled:
              formList.loading.value ||
              creating.value ||
              formOperations.deletingFormIds.value.has(String(form.id)),
          },
        ]
      },
      handleCommand,
    })
  })

  // 事件处理函数
  const handleTabChange = async (tabName: string) => {
    await formFilters.handleTabChange(tabName, router, route)
  }

  const handleSelectAll = (checked: boolean) => {
    formSelection.handleSelectAll(checked, tabFilteredForms.value)
  }

  const handleSearch = (keyword: string) => {
    formFilters.setSearchKeyword(keyword)
    formFilters.handleSearch(formSelection.clearSelection)
  }

  const handleBatchDelete = async () => {
    formSelection.setBatchDeleting(true)
    try {
      await formOperations.handleBatchDelete(
        formSelection.selectedFormIds.value,
        formList.forms.value,
        {
          onSuccess: (formIds: string[]) => {
            formIds.forEach(formList.removeFormFromList)
            formSelection.clearFormSelection(formIds)
            message.success(SUCCESS_MESSAGES.BATCH_DELETE)
          },
          onPartialSuccess: (results: any) => {
            results.success.forEach(formList.removeFormFromList)
            formSelection.clearFormSelection(results.success)
          },
        },
      )
    } finally {
      formSelection.setBatchDeleting(false)
    }
  }

  const handleCommand = async (command: string, form: any) => {
    switch (command) {
      case 'fill':
        await formOperations.handleFillForm(form, formFilters.activeTab.value)
        break
      case 'duplicate':
        await formOperations.handleDuplicateForm(
          form,
          formList.forms.value,
          formFilters.activeTab.value,
          {
            onSuccess: (newForm: any) => {
              formList.addFormToList(newForm)
            },
            onActiveTabChange: formFilters.setActiveTab,
          },
        )
        break
      case 'delete':
        await formOperations.handleDeleteForm(form, {
          onSuccess: formList.removeFormFromList,
        })
        break
      case 'restore':
        console.log('恢复表单:', form)
        break
    }
  }

  const handleCreateForm = async (formData: any) => {
    try {
      creating.value = true

      const templateData = {
        ...CREATE_FORM_DEFAULT,
        name: formData.name,
        title: formData.name,
        description: formData.description,
        icon: formData.icon,
        iconColor: formData.iconColor,
      }

      const response = await templateAPI.createTemplate(templateData)
      if (response.success) {
        message.success(SUCCESS_MESSAGES.FORM_CREATED)
        createDialogVisible.value = false

        // 刷新表单列表
        await formList.fetchForms()

        // 跳转到未发布标签页
        formFilters.setActiveTab('unpublished')
        await router.replace({
          query: { ...route.query, tab: 'unpublished' },
        })

        // 高亮新创建的表单
        setTimeout(() => {
          const templateId = (response.data as any)?.template?.id
          if (templateId) {
            const newFormElement = document.querySelector(
              `[data-form-id="${templateId}"]`,
            )
            if (newFormElement) {
              newFormElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
              })
              newFormElement.classList.add('highlight-new-form')
              setTimeout(() => {
                newFormElement.classList.remove('highlight-new-form')
              }, 2000)
            }
          }
        }, 300)
      } else {
        message.error(response.message || '创建表单失败')
      }
    } catch (error) {
      console.error('创建表单失败:', error)
      message.error('创建表单失败，请稍后重试')
    } finally {
      creating.value = false
    }
  }

  // 监听路由参数变化
  watch(
    () => route.query.tab,
    (newTab) => {
      if (newTab && newTab !== formFilters.activeTab.value) {
        formFilters.setActiveTab(newTab as string)
      }
    },
    { immediate: true },
  )

  // 生命周期
  onMounted(() => {
    formList.refreshAllLists()
  })

  onActivated(() => {
    formList.refreshAllLists()
  })

  return {
    // 状态
    createDialogVisible,
    creating,

    // 计算属性
    tabFilteredForms,
    tableColumns,
    hasMoreData,

    // 从其他组合式函数导出的状态和方法
    ...formList,
    ...formOperations,
    ...formSelection,
    // 排除setViewMode，使用我们自定义的handleViewModeChange
    activeTab: formFilters.activeTab,
    searchKeyword: formFilters.searchKeyword,
    viewMode: formFilters.viewMode,
    tabSwitching: formFilters.tabSwitching,
    getFilteredForms: formFilters.getFilteredForms,
    setSearchKeyword: formFilters.setSearchKeyword,
    setActiveTab: formFilters.setActiveTab,
    setTabSwitching: formFilters.setTabSwitching,

    // 事件处理函数 (使用本地定义的函数，覆盖从formFilters来的同名函数)
    handleTabChange,
    handleSelectAll,
    handleSearch,
    handleBatchDelete,
    handleCommand,
    handleCreateForm,
    handlePageChange,
    handlePageSizeChange,
    handleLoadMore,
    handleViewModeChange,
  }
}
