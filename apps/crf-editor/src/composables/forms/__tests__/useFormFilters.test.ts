import { describe, test, expect, vi, beforeEach } from 'vitest'
import { useFormFilters } from '@/composables/forms/useFormFilters'

describe('useFormFilters', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('应该初始化默认状态', () => {
    const filters = useFormFilters()
    
    expect(filters.activeTab.value).toBe('published')
    expect(filters.searchKeyword.value).toBe('')
    expect(filters.viewMode.value).toBe('grid')
    expect(filters.tabSwitching.value).toBe(false)
  })

  test('应该正确设置活动标签页', () => {
    const filters = useFormFilters()
    
    filters.setActiveTab('unpublished')
    expect(filters.activeTab.value).toBe('unpublished')
    
    filters.setActiveTab('deleted')
    expect(filters.activeTab.value).toBe('deleted')
  })

  test('应该正确设置搜索关键词', () => {
    const filters = useFormFilters()
    
    filters.setSearchKeyword('测试表单')
    expect(filters.searchKeyword.value).toBe('测试表单')
    
    // 测试空字符串
    filters.setSearchKeyword('')
    expect(filters.searchKeyword.value).toBe('')
  })

  test('应该正确切换视图模式', () => {
    const filters = useFormFilters()
    const mockFormList = {
      setPagination: vi.fn()
    }
    
    // 切换到列表视图
    filters.setViewMode('list', mockFormList)
    expect(filters.viewMode.value).toBe('list')
    expect(mockFormList.setPagination).toHaveBeenCalledWith({ current: 1 })
    
    // 切换到网格视图
    filters.setViewMode('grid', mockFormList)
    expect(filters.viewMode.value).toBe('grid')
    expect(mockFormList.setPagination).toHaveBeenCalledWith({ current: 1 })
  })

  test('应该正确过滤表单数据', () => {
    const filters = useFormFilters()
    
    const mockForms = [
      { id: '1', name: '用户注册表单', status: 'published' },
      { id: '2', name: '患者信息表单', status: 'draft' },
      { id: '3', name: '测试表单', status: 'published' }
    ]
    
    const mockDeletedForms = [
      { id: '4', name: '已删除表单', status: 'deleted' }
    ]
    
    // 测试发布状态过滤
    filters.setActiveTab('published')
    const publishedForms = filters.getFilteredForms(mockForms, mockDeletedForms)
    expect(publishedForms).toHaveLength(2)
    expect(publishedForms.every(form => form.status === 'published')).toBe(true)
    
    // 测试未发布状态过滤
    filters.setActiveTab('unpublished')
    const unpublishedForms = filters.getFilteredForms(mockForms, mockDeletedForms)
    expect(unpublishedForms).toHaveLength(1)
    expect(unpublishedForms[0].status).toBe('draft')
    
    // 测试已删除状态过滤
    filters.setActiveTab('deleted')
    const deletedForms = filters.getFilteredForms(mockForms, mockDeletedForms)
    expect(deletedForms).toHaveLength(1)
    expect(deletedForms[0].status).toBe('deleted')
    
    // 测试全部状态过滤
    filters.setActiveTab('all')
    const allForms = filters.getFilteredForms(mockForms, mockDeletedForms)
    expect(allForms).toHaveLength(4)
  })

  test('应该正确处理搜索过滤', () => {
    const filters = useFormFilters()
    
    const mockForms = [
      { id: '1', name: '用户注册表单', title: '用户注册', status: 'published' },
      { id: '2', name: '患者信息表单', title: '患者基本信息', status: 'published' },
      { id: '3', name: '测试表单', title: '系统测试', status: 'published' }
    ]
    
    // 设置搜索关键词
    filters.setSearchKeyword('用户')
    filters.setActiveTab('published')
    
    const filteredForms = filters.getFilteredForms(mockForms, [])
    expect(filteredForms).toHaveLength(1)
    expect(filteredForms[0].name).toContain('用户')
    
    // 测试title搜索
    filters.setSearchKeyword('患者')
    const titleFilteredForms = filters.getFilteredForms(mockForms, [])
    expect(titleFilteredForms).toHaveLength(1)
    expect(titleFilteredForms[0].title).toContain('患者')
    
    // 测试无匹配结果
    filters.setSearchKeyword('不存在的关键词')
    const noMatchForms = filters.getFilteredForms(mockForms, [])
    expect(noMatchForms).toHaveLength(0)
  })

  test('应该正确处理标签页切换状态', () => {
    const filters = useFormFilters()
    
    expect(filters.tabSwitching.value).toBe(false)
    
    filters.setTabSwitching(true)
    expect(filters.tabSwitching.value).toBe(true)
    
    filters.setTabSwitching(false)
    expect(filters.tabSwitching.value).toBe(false)
  })

  test('应该正确处理搜索操作', () => {
    const filters = useFormFilters()
    const mockClearSelection = vi.fn()
    
    // 模拟搜索操作
    filters.setSearchKeyword('测试')
    filters.handleSearch(mockClearSelection)
    
    // 验证清空选择被调用
    expect(mockClearSelection).toHaveBeenCalled()
  })
})