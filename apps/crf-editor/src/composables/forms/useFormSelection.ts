import { ref, computed } from 'vue'

interface FormItem {
  id: string
  [key: string]: any
}

export function useFormSelection() {
  // 选择状态
  const selectedFormIds = ref(new Set<string>())
  const batchDeleting = ref(false)

  // 计算属性
  const selectedCount = computed(() => selectedFormIds.value.size)

  // 检查是否全选
  const isAllSelected = (currentForms: FormItem[]) => {
    return (
      currentForms.length > 0 &&
      currentForms.every((form) => selectedFormIds.value.has(form.id))
    )
  }

  // 检查是否部分选择
  const isIndeterminate = (currentForms: FormItem[]) => {
    const selectedInCurrentPage = currentForms.filter((form) =>
      selectedFormIds.value.has(form.id),
    ).length
    return (
      selectedInCurrentPage > 0 && selectedInCurrentPage < currentForms.length
    )
  }

  // 处理单个表单选择
  const handleFormSelect = (formId: string, checked: boolean) => {
    if (checked) {
      selectedFormIds.value.add(formId)
    } else {
      selectedFormIds.value.delete(formId)
    }
  }

  // 处理全选
  const handleSelectAll = (checked: boolean, currentForms: FormItem[]) => {
    if (checked) {
      // 全选当前页面的所有表单
      currentForms.forEach((form) => {
        selectedFormIds.value.add(form.id)
      })
    } else {
      // 取消全选当前页面的所有表单
      currentForms.forEach((form) => {
        selectedFormIds.value.delete(form.id)
      })
    }
  }

  // 表格选择处理
  const handleTableSelection = (checkedKeys: string[]) => {
    selectedFormIds.value = new Set(checkedKeys)
  }

  // 清除选择
  const clearSelection = () => {
    selectedFormIds.value.clear()
  }

  // 清除特定表单的选择
  const clearFormSelection = (formIds: string[]) => {
    formIds.forEach((formId) => {
      selectedFormIds.value.delete(formId)
    })
  }

  // 设置批量删除状态
  const setBatchDeleting = (deleting: boolean) => {
    batchDeleting.value = deleting
  }

  return {
    // 状态
    selectedFormIds,
    batchDeleting,

    // 计算属性
    selectedCount,

    // 方法
    isAllSelected,
    isIndeterminate,
    handleFormSelect,
    handleSelectAll,
    handleTableSelection,
    clearSelection,
    clearFormSelection,
    setBatchDeleting,
  }
}
