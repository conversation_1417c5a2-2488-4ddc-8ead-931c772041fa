import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { templateAPI } from '@/api'
import { useNaive } from '@/composables/useNaive'
import { generateUniqueName } from '@/utils/form'

interface FormItem {
  id: string
  name?: string
  title?: string
  description?: string
  icon?: string
  iconColor?: string
  version?: string
  status?: string
  created_at?: string
  updated_at?: string
  project_id?: string
  keyword?: string
  template_data?: any
  permissions?: any
  [key: string]: any
}

export function useFormOperations() {
  const router = useRouter()
  const { message, dialog } = useNaive()

  // 操作状态
  const duplicatingFormIds = ref(new Set<string>())
  const deletingFormIds = ref(new Set<string>())
  const operationLoading = ref(false)

  // 处理编辑表单
  const handleEdit = (form: FormItem, activeTab: string) => {
    const editorUrl = `/editor?id=${String(form.id)}&name=${encodeURIComponent(String(form.name || form.title || ''))}&fromTab=${activeTab}&mode=edit`
    router.push(editorUrl)
  }

  // 处理查看数据
  const handleViewData = (form: FormItem) => {
    router.push(`/forms/${String(form.id)}/data`)
  }

  // 处理填写表单
  const handleFillForm = async (form: FormItem, activeTab: string) => {
    if (form.status !== 'published') {
      message.warning('⚠️ 表单需要先发布后才能进行填写', {
        duration: 3000,
        closable: true,
      })
      return
    }

    try {
      const editorUrl = `/editor?mode=fill&templateId=${String(form.id)}&name=${encodeURIComponent(String(form.name || form.title || ''))}&fromTab=${activeTab}`
      router.push(editorUrl)
    } catch (error) {
      console.error('打开表单填写失败:', error)
      message.error('打开表单填写失败')
    }
  }

  // 复制表单
  const handleDuplicateForm = async (
    form: FormItem,
    allForms: FormItem[],
    activeTab: string,
    callbacks: {
      onSuccess: (newForm: FormItem) => void
      onActiveTabChange: (tab: string) => void
    },
  ) => {
    const formId = String(form.id)

    // 防止重复调用
    if (duplicatingFormIds.value.has(formId)) {
      return
    }

    // 保存当前标签页状态
    const currentTab = activeTab

    try {
      // 添加到正在复制的集合中
      duplicatingFormIds.value.add(formId)

      // 生成唯一的名称，避免重复
      const existingNames = allForms.map(
        (f) => (f.name || f.title || '') as string,
      )
      const uniqueName = generateUniqueName(
        (form.name || form.title || '') as string,
        existingNames,
      )

      const templateData = {
        project_id: (form.project_id || 'default') as string,
        name: uniqueName,
        title: uniqueName,
        description: (form.description || '') as string,
        icon: (form.icon || 'Document') as string,
        iconColor: (form.iconColor || '#3b82f6') as string,
        keyword: (form.keyword || '') as string,
        version: '1.0.0',
        template_type: 'custom_form' as const,
        source_type: 'user_created' as const,
        template_data: (form.template_data as any) || {
          page_config: {},
          form_structure: [],
          component_configs: {},
          validation_rules: {},
          style_config: {},
        },
        permissions: (form.permissions as any) || {},
      }

      const response = await templateAPI.createTemplate(templateData)
      if (response.success) {
        // 开始操作加载状态
        operationLoading.value = true

        // 延迟显示成功消息
        setTimeout(() => {
          message.success('表单复制成功，已跳转到未发布列表', {
            duration: 3000,
            closable: true,
          })
        }, 100)

        // 准备新表单数据
        const newForm = {
          ...response.data.template,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          status: 'draft',
        }

        // 调用成功回调
        callbacks.onSuccess(newForm)

        // 复制成功后，切换到未发布页签
        setTimeout(async () => {
          operationLoading.value = false
          callbacks.onActiveTabChange('unpublished')

          // 等待DOM更新后高亮新表单
          await new Promise((resolve) => setTimeout(resolve, 100))

          const newFormElement = document.querySelector(
            `[data-form-id="${response.data.template.id}"]`,
          )
          if (newFormElement) {
            newFormElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            })
            newFormElement.classList.add('highlight-new-form')
            setTimeout(() => {
              newFormElement.classList.remove('highlight-new-form')
            }, 2000)
          }
        }, 800)
      } else {
        message.error(response.message || '复制失败')
      }
    } catch (error) {
      console.error('复制表单失败:', error)
      message.error('复制表单失败')

      // 发生错误时设置操作加载状态
      operationLoading.value = true
      callbacks.onActiveTabChange(currentTab)
      setTimeout(() => {
        operationLoading.value = false
      }, 300)
    } finally {
      duplicatingFormIds.value.delete(formId)
    }
  }

  // 删除表单
  const handleDeleteForm = async (
    form: FormItem,
    callbacks: {
      onSuccess: (formId: string) => void
    },
  ) => {
    const formId = String(form.id)

    // 防止重复调用
    if (deletingFormIds.value.has(formId)) {
      return
    }

    try {
      const confirmed = await new Promise<boolean>((resolve) => {
        dialog.warning({
          title: '确认删除',
          content: `确定要删除表单 "${form.name || form.title || form.id}" 吗？此操作不可恢复。`,
          positiveText: '确定删除',
          negativeText: '取消',
          onPositiveClick: () => resolve(true),
          onNegativeClick: () => resolve(false),
          onClose: () => resolve(false),
        })
      })

      if (!confirmed) {
        return
      }

      // 添加到正在删除的集合中
      deletingFormIds.value.add(formId)

      const response = await templateAPI.deleteTemplate(formId)
      if (response.success) {
        // 开始操作加载状态
        operationLoading.value = true

        // 延迟显示成功消息
        setTimeout(() => {
          message.success('删除成功', {
            duration: 2000,
            closable: true,
          })
        }, 100)

        // 调用成功回调
        callbacks.onSuccess(formId)

        // 结束操作加载状态
        setTimeout(() => {
          operationLoading.value = false
        }, 600)
      } else {
        // 处理后端返回的错误信息
        handleDeleteError(response.message, form)
      }
    } catch (error: unknown) {
      console.error('删除表单失败:', error)
      handleDeleteError(getErrorMessage(error), form)
    } finally {
      deletingFormIds.value.delete(formId)
    }
  }

  // 批量删除表单
  const handleBatchDelete = async (
    selectedFormIds: Set<string>,
    allForms: FormItem[],
    callbacks: {
      onSuccess: (formIds: string[]) => void
      onPartialSuccess: (results: {
        success: string[]
        failed: Array<{ id: string; name: string; reason: string }>
      }) => void
    },
  ) => {
    if (selectedFormIds.size === 0) return

    try {
      const confirmed = await new Promise<boolean>((resolve) => {
        dialog.warning({
          title: '批量删除确认',
          content: `确定要删除选中的 ${selectedFormIds.size} 个表单吗？此操作不可恢复。`,
          positiveText: '确定删除',
          negativeText: '取消',
          onPositiveClick: () => resolve(true),
          onNegativeClick: () => resolve(false),
          onClose: () => resolve(false),
        })
      })

      if (!confirmed) return

      // 逐个删除，收集成功和失败的结果
      const results = {
        success: [] as string[],
        failed: [] as { id: string; name: string; reason: string }[],
      }

      for (const formId of Array.from(selectedFormIds)) {
        try {
          const form = allForms.find((f) => f.id === formId)
          const formName = form?.name || form?.title || formId

          const response = await templateAPI.deleteTemplate(formId)
          if (response.success) {
            results.success.push(formId)
          } else {
            let reason = response.message || '删除失败'
            if (
              reason.includes('existing instances') ||
              reason.includes('关联实例') ||
              reason.includes('实例数据')
            ) {
              reason = '该表单包含填写数据，无法删除'
            }
            results.failed.push({ id: formId, name: formName, reason })
          }
        } catch (error: any) {
          const form = allForms.find((f) => f.id === formId)
          const formName = form?.name || form?.title || formId
          let reason = getBatchDeleteErrorReason(error)
          results.failed.push({ id: formId, name: formName, reason })
        }
      }

      // 显示批量删除结果
      if (results.success.length > 0 && results.failed.length === 0) {
        message.success(`成功删除 ${results.success.length} 个表单`)
        callbacks.onSuccess(results.success)
      } else {
        callbacks.onPartialSuccess(results)
        showBatchDeleteResults(results)
      }
    } catch (error) {
      console.error('批量删除过程中发生错误:', error)
      message.error('批量删除过程中发生错误，请稍后重试')
    }
  }

  // 处理删除错误
  const handleDeleteError = (errorMessage: string, form: FormItem) => {
    let displayMessage = errorMessage || '删除失败'

    if (
      errorMessage.includes('TEMPLATE_HAS_INSTANCES') ||
      errorMessage.includes('该表单包含填写数据')
    ) {
      displayMessage =
        '该表单包含填写数据，无法删除。请先清理相关数据后再尝试删除。'
    } else if (
      errorMessage.includes('existing instances') ||
      errorMessage.includes('关联实例') ||
      errorMessage.includes('实例数据')
    ) {
      displayMessage =
        '该表单包含填写数据，无法删除。请先清理相关数据后再尝试删除。'
    }

    dialog.error({
      title: '删除失败',
      content: `表单 "${form.name || form.title || form.id}" 删除失败：\n${displayMessage}`,
      positiveText: '确定',
      style: { width: '500px' },
    })
  }

  // 获取错误消息
  const getErrorMessage = (error: unknown): string => {
    if (error && typeof error === 'object') {
      const apiError = error as any
      const backendMessage =
        apiError?.response?.data?.error ||
        apiError?.response?.data?.message ||
        apiError?.message ||
        ''

      if (apiError?.response?.data?.code === 'TEMPLATE_HAS_INSTANCES') {
        return (
          apiError?.response?.data?.error ||
          '该表单包含填写数据，无法删除。请先清理相关数据后再尝试删除。'
        )
      } else if (
        backendMessage.includes('existing instances') ||
        backendMessage.includes('关联实例') ||
        backendMessage.includes('实例数据')
      ) {
        return '该表单包含填写数据，无法删除。请先清理相关数据后再尝试删除。'
      } else if (backendMessage.includes('cannot delete')) {
        return '无法删除该表单，请检查表单状态'
      } else if (backendMessage) {
        return backendMessage
      }
    }
    return '删除失败，请稍后重试'
  }

  // 获取批量删除错误原因
  const getBatchDeleteErrorReason = (error: any): string => {
    const errorMessage =
      error?.response?.data?.error ||
      error?.response?.data?.message ||
      error?.message ||
      ''

    if (error?.response?.data?.code === 'TEMPLATE_HAS_INSTANCES') {
      return error?.response?.data?.error || '该表单包含填写数据，无法删除'
    } else if (
      errorMessage.includes('existing instances') ||
      errorMessage.includes('关联实例') ||
      errorMessage.includes('实例数据')
    ) {
      return '该表单包含填写数据，无法删除'
    } else if (errorMessage.includes('cannot delete')) {
      return '无法删除该表单'
    } else if (errorMessage) {
      return errorMessage
    }
    return '删除失败'
  }

  // 显示批量删除结果
  const showBatchDeleteResults = (results: {
    success: string[]
    failed: Array<{ id: string; name: string; reason: string }>
  }) => {
    if (results.success.length > 0 && results.failed.length > 0) {
      const failedList = results.failed
        .map((f) => `• ${f.name}: ${f.reason}`)
        .join('\n')
      dialog.warning({
        title: '批量删除完成',
        content: `成功删除 ${results.success.length} 个表单\n\n以下 ${results.failed.length} 个表单删除失败：\n${failedList}`,
        positiveText: '确定',
        style: { width: '500px' },
      })
    } else if (results.failed.length > 0) {
      const failedList = results.failed
        .map((f) => `• ${f.name}: ${f.reason}`)
        .join('\n')
      dialog.error({
        title: '批量删除失败',
        content: `以下 ${results.failed.length} 个表单删除失败：\n${failedList}`,
        positiveText: '确定',
        style: { width: '500px' },
      })
    }
  }

  return {
    // 状态
    duplicatingFormIds,
    deletingFormIds,
    operationLoading,

    // 方法
    handleEdit,
    handleViewData,
    handleFillForm,
    handleDuplicateForm,
    handleDeleteForm,
    handleBatchDelete,
  }
}
