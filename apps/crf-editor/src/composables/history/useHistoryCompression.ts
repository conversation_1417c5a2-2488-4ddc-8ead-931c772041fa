/**
 * 历史记录压缩功能
 * 提供自动压缩和手动压缩功能
 */

import { ref, computed } from 'vue'
import type { HistoryEntry } from './useBasicHistory'
import {
  useError<PERSON><PERSON><PERSON>,
  ErrorCategory,
  ErrorLevel,
} from '@/utils/error-handler'

// 压缩配置
export interface CompressionConfig {
  autoCompress: boolean
  compressAfter: number
  compressionRatio: number
}

// 压缩统计
export interface CompressionStats {
  originalCount: number
  compressedCount: number
  spaceSaved: number
  compressionRatio: number
}

/**
 * 历史记录压缩管理器
 */
export function useHistoryCompression<T = unknown>(
  config: Partial<CompressionConfig> = {},
) {
  const { handleError } = useErrorHandler()

  // 默认配置
  const defaultConfig: CompressionConfig = {
    autoCompress: true,
    compressAfter: 10,
    compressionRatio: 0.5,
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 状态
  const compressionStats = ref<CompressionStats>({
    originalCount: 0,
    compressedCount: 0,
    spaceSaved: 0,
    compressionRatio: 0,
  })

  // 计算相似度
  const calculateSimilarity = (
    entry1: HistoryEntry<T>,
    entry2: HistoryEntry<T>,
  ): number => {
    try {
      if (entry1.action !== entry2.action) return 0

      const data1 = JSON.stringify(entry1.data)
      const data2 = JSON.stringify(entry2.data)

      if (data1 === data2) return 1

      // 简单的字符串相似度计算
      const maxLength = Math.max(data1.length, data2.length)
      const minLength = Math.min(data1.length, data2.length)

      let similarity = 0
      for (let i = 0; i < minLength; i++) {
        if (data1[i] === data2[i]) {
          similarity++
        }
      }

      return similarity / maxLength
    } catch {
      return 0
    }
  }

  // 压缩相似的历史记录
  const compressSimilarEntries = (
    entries: HistoryEntry<T>[],
    threshold = 0.8,
  ): HistoryEntry<T>[] => {
    if (entries.length <= 1) return entries

    try {
      const compressed: HistoryEntry<T>[] = []
      const processed = new Set<number>()

      for (let i = 0; i < entries.length; i++) {
        if (processed.has(i)) continue

        const currentEntry = entries[i]
        if (!currentEntry) continue

        const similarEntries = [currentEntry]
        processed.add(i)

        // 查找相似的条目
        for (let j = i + 1; j < entries.length; j++) {
          if (processed.has(j)) continue

          const targetEntry = entries[j]
          if (!targetEntry) continue

          const similarity = calculateSimilarity(currentEntry, targetEntry)
          if (similarity >= threshold) {
            similarEntries.push(targetEntry)
            processed.add(j)
          }
        }

        if (similarEntries.length > 1) {
          // 创建压缩条目
          const lastEntry = similarEntries[similarEntries.length - 1]
          if (!lastEntry) continue

          const compressedEntry: HistoryEntry<T> = {
            id: `compressed_${currentEntry.id}`,
            timestamp: currentEntry.timestamp,
            action: currentEntry.action,
            description: `${currentEntry.description} (压缩了${similarEntries.length}个相似操作)`,
            data: lastEntry.data, // 使用最后一个状态
            metadata: {
              ...currentEntry.metadata,
              size: JSON.stringify(similarEntries).length,
            },
          }

          compressed.push(compressedEntry)

          // 更新统计
          compressionStats.value.originalCount += similarEntries.length
          compressionStats.value.compressedCount += 1
        } else {
          compressed.push(currentEntry)
        }
      }

      // 更新统计
      const originalSize = entries.reduce(
        (sum, e) => sum + JSON.stringify(e).length,
        0,
      )
      const compressedSize = compressed.reduce(
        (sum, e) => sum + JSON.stringify(e).length,
        0,
      )
      compressionStats.value.spaceSaved += originalSize - compressedSize
      compressionStats.value.compressionRatio = compressedSize / originalSize

      return compressed
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, ErrorCategory.BUSINESS)
      return entries
    }
  }

  // 时间窗口压缩
  const compressTimeWindow = (
    entries: HistoryEntry<T>[],
    windowMs = 1000,
  ): HistoryEntry<T>[] => {
    if (entries.length <= 1) return entries

    try {
      const compressed: HistoryEntry<T>[] = []
      const windows: HistoryEntry<T>[][] = []
      let currentWindow: HistoryEntry<T>[] = []

      // 按时间窗口分组
      for (const entry of entries) {
        if (currentWindow.length === 0) {
          currentWindow.push(entry)
        } else {
          const firstEntry = currentWindow[0]
          if (!firstEntry) continue

          const timeDiff = entry.timestamp - firstEntry.timestamp
          if (timeDiff <= windowMs && entry.action === firstEntry.action) {
            currentWindow.push(entry)
          } else {
            windows.push(currentWindow)
            currentWindow = [entry]
          }
        }
      }

      if (currentWindow.length > 0) {
        windows.push(currentWindow)
      }

      // 压缩每个窗口
      for (const window of windows) {
        if (window.length > 1) {
          const firstEntry = window[0]
          const lastEntry = window[window.length - 1]
          if (!firstEntry || !lastEntry) continue

          const compressedEntry: HistoryEntry<T> = {
            id: `time_compressed_${firstEntry.id}`,
            timestamp: lastEntry.timestamp,
            action: firstEntry.action,
            description: `${firstEntry.description} (时间窗口压缩${window.length}个操作)`,
            data: lastEntry.data,
            metadata: {
              ...firstEntry.metadata,
              size: JSON.stringify(window).length,
            },
          }
          compressed.push(compressedEntry)
        } else if (window[0]) {
          compressed.push(window[0])
        }
      }

      return compressed
    } catch (error) {
      handleError(error as Error, ErrorLevel.ERROR, ErrorCategory.BUSINESS)
      return entries
    }
  }

  // 自动压缩检查
  const shouldAutoCompress = (entries: HistoryEntry<T>[]): boolean => {
    return (
      finalConfig.autoCompress && entries.length >= finalConfig.compressAfter
    )
  }

  // 执行压缩
  const compress = (
    entries: HistoryEntry<T>[],
    options?: {
      useSimilarityCompression?: boolean
      useTimeWindowCompression?: boolean
      similarityThreshold?: number
      timeWindowMs?: number
    },
  ): HistoryEntry<T>[] => {
    const {
      useSimilarityCompression = true,
      useTimeWindowCompression = true,
      similarityThreshold = 0.8,
      timeWindowMs = 1000,
    } = options || {}

    let result = entries

    if (useTimeWindowCompression) {
      result = compressTimeWindow(result, timeWindowMs)
    }

    if (useSimilarityCompression) {
      result = compressSimilarEntries(result, similarityThreshold)
    }

    return result
  }

  // 重置统计
  const resetStats = (): void => {
    compressionStats.value = {
      originalCount: 0,
      compressedCount: 0,
      spaceSaved: 0,
      compressionRatio: 0,
    }
  }

  return {
    // 状态
    compressionStats: computed(() => compressionStats.value),

    // 方法
    compress,
    compressSimilarEntries,
    compressTimeWindow,
    shouldAutoCompress,
    calculateSimilarity,
    resetStats,
  }
}
