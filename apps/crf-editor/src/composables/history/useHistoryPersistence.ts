/**
 * 历史记录持久化功能
 * 提供导入导出和本地存储功能
 */

import { ref } from 'vue'
import type { HistoryEntry } from './useBasicHistory'
import { useError<PERSON>and<PERSON>, ErrorCategory } from '@/utils/error-handler'

// 导出格式
export interface HistoryExport<T = unknown> {
  version: string
  timestamp: number
  entries: HistoryEntry<T>[]
  metadata: {
    totalEntries: number
    exportedBy?: string
    description?: string
  }
}

// 持久化配置
export interface PersistenceConfig {
  storageKey: string
  autoSave: boolean
  saveInterval: number
  maxStorageSize: number
}

/**
 * 历史记录持久化管理器
 */
export function useHistoryPersistence<T = unknown>(
  config: Partial<PersistenceConfig> = {},
) {
  const { handleError } = useErrorHandler()

  // 默认配置
  const defaultConfig: PersistenceConfig = {
    storageKey: 'crf_history',
    autoSave: true,
    saveInterval: 30000, // 30秒
    maxStorageSize: 5 * 1024 * 1024, // 5MB
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 状态
  const isLoading = ref(false)
  const isSaving = ref(false)
  const lastSaveTime = ref<Date | null>(null)

  // 检查存储大小
  const checkStorageSize = (data: string): boolean => {
    return data.length <= finalConfig.maxStorageSize
  }

  // 保存到本地存储
  const saveToStorage = (entries: HistoryEntry<T>[]): boolean => {
    try {
      isSaving.value = true

      const data: HistoryExport<T> = {
        version: '1.0.0',
        timestamp: Date.now(),
        entries,
        metadata: {
          totalEntries: entries.length,
          description: 'CRF编辑器历史记录',
        },
      }

      const jsonData = JSON.stringify(data)

      if (!checkStorageSize(jsonData)) {
        throw new Error('历史记录数据过大，无法保存到本地存储')
      }

      localStorage.setItem(finalConfig.storageKey, jsonData)
      lastSaveTime.value = new Date()

      return true
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, true)
      return false
    } finally {
      isSaving.value = false
    }
  }

  // 从本地存储加载
  const loadFromStorage = (): HistoryEntry<T>[] => {
    try {
      isLoading.value = true

      const data = localStorage.getItem(finalConfig.storageKey)
      if (!data) return []

      const parsed: HistoryExport<T> = JSON.parse(data)

      // 验证数据格式
      if (!parsed.entries || !Array.isArray(parsed.entries)) {
        throw new Error('无效的历史记录数据格式')
      }

      return parsed.entries
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, true)
      return []
    } finally {
      isLoading.value = false
    }
  }

  // 导出历史记录
  const exportHistory = (
    entries: HistoryEntry<T>[],
    description?: string,
  ): HistoryExport<T> => {
    return {
      version: '1.0.0',
      timestamp: Date.now(),
      entries,
      metadata: {
        totalEntries: entries.length,
        exportedBy: 'CRF编辑器',
        description: description || '历史记录导出',
      },
    }
  }

  // 导出为JSON文件
  const exportToFile = (
    entries: HistoryEntry<T>[],
    filename?: string,
  ): void => {
    try {
      const exportData = exportHistory(entries, '历史记录导出')
      const jsonData = JSON.stringify(exportData, null, 2)

      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename || `crf_history_${Date.now()}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.BUSINESS, true)
    }
  }

  // 从文件导入
  const importFromFile = (file: File): Promise<HistoryEntry<T>[]> => {
    return new Promise((resolve, reject) => {
      try {
        isLoading.value = true

        const reader = new FileReader()

        reader.onload = (event) => {
          try {
            const result = event.target?.result as string
            const parsed: HistoryExport<T> = JSON.parse(result)

            // 验证数据格式
            if (!parsed.entries || !Array.isArray(parsed.entries)) {
              throw new Error('无效的历史记录文件格式')
            }

            // 验证每个条目的格式
            for (const entry of parsed.entries) {
              if (
                !entry.id ||
                !entry.timestamp ||
                !entry.action ||
                !entry.data
              ) {
                throw new Error('历史记录条目格式无效')
              }
            }

            resolve(parsed.entries)
          } catch (error) {
            reject(
              new Error(`解析历史记录文件失败: ${(error as Error).message}`),
            )
          } finally {
            isLoading.value = false
          }
        }

        reader.onerror = () => {
          isLoading.value = false
          reject(new Error('读取文件失败'))
        }

        reader.readAsText(file)
      } catch (error) {
        isLoading.value = false
        reject(error)
      }
    })
  }

  // 清除本地存储
  const clearStorage = (): boolean => {
    try {
      localStorage.removeItem(finalConfig.storageKey)
      lastSaveTime.value = null
      return true
    } catch (error) {
      handleError(error as Error, undefined, ErrorCategory.SYSTEM, true)
      return false
    }
  }

  // 获取存储信息
  const getStorageInfo = () => {
    try {
      const data = localStorage.getItem(finalConfig.storageKey)
      if (!data) {
        return {
          exists: false,
          size: 0,
          entryCount: 0,
          lastModified: null,
        }
      }

      const parsed: HistoryExport<T> = JSON.parse(data)

      return {
        exists: true,
        size: data.length,
        entryCount: parsed.entries.length,
        lastModified: new Date(parsed.timestamp),
      }
    } catch {
      return {
        exists: false,
        size: 0,
        entryCount: 0,
        lastModified: null,
      }
    }
  }

  // 验证历史记录数据
  const validateHistoryData = (data: unknown): data is HistoryExport<T> => {
    return (
      data &&
      typeof data === 'object' &&
      data.version &&
      data.timestamp &&
      Array.isArray(data.entries) &&
      data.metadata &&
      typeof data.metadata.totalEntries === 'number'
    )
  }

  return {
    // 状态
    isLoading,
    isSaving,
    lastSaveTime,

    // 方法
    saveToStorage,
    loadFromStorage,
    exportHistory,
    exportToFile,
    importFromFile,
    clearStorage,
    getStorageInfo,
    validateHistoryData,
    checkStorageSize,
  }
}
