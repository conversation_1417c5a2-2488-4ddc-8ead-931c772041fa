/**
 * 统一历史记录管理器
 * 整合基础历史记录、压缩和持久化功能
 */

import { computed, watch } from 'vue'
import {
  useBasicHistory,
  type HistoryEntry,
  type BasicHistoryConfig,
} from './useBasicHistory'
import {
  useHistoryCompression,
  type CompressionConfig,
} from './useHistoryCompression'
import {
  useHistoryPersistence,
  type PersistenceConfig,
} from './useHistoryPersistence'

// 完整配置
export interface HistoryManagerConfig
  extends BasicHistoryConfig,
    CompressionConfig,
    PersistenceConfig {
  enableBranching?: boolean
}

// 历史记录统计
export interface HistoryStats {
  totalEntries: number
  memoryUsage: number
  oldestEntry?: Date
  newestEntry?: Date
  mostFrequentAction: string
  compressionRatio: number
}

/**
 * 完整的历史记录管理器
 */
export function useHistoryManager<T = unknown>(
  config: Partial<HistoryManagerConfig> = {},
) {
  // 基础历史记录管理
  const basicHistory = useBasicHistory<T>({
    maxSize: config.maxSize,
    trackMetadata: config.trackMetadata,
  })

  // 压缩功能
  const compression = useHistoryCompression<T>({
    autoCompress: config.autoCompress,
    compressAfter: config.compressAfter,
    compressionRatio: config.compressionRatio,
  })

  // 持久化功能
  const persistence = useHistoryPersistence<T>({
    storageKey: config.storageKey,
    autoSave: config.autoSave,
    saveInterval: config.saveInterval,
    maxStorageSize: config.maxStorageSize,
  })

  // 自动保存定时器
  let autoSaveTimer: number | null = null

  // 启动自动保存
  const startAutoSave = () => {
    if (config.autoSave && config.saveInterval && !autoSaveTimer) {
      autoSaveTimer = window.setInterval(() => {
        persistence.saveToStorage(
          basicHistory.getHistory() as HistoryEntry<T>[],
        )
      }, config.saveInterval)
    }
  }

  // 停止自动保存
  const stopAutoSave = () => {
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
    }
  }

  // 监听历史记录变化，执行自动压缩
  watch(
    () => basicHistory.history.value,
    (newHistory) => {
      if (compression.shouldAutoCompress(newHistory as HistoryEntry<T>[])) {
        const compressed = compression.compress(newHistory as HistoryEntry<T>[])
        if (compressed.length < newHistory.length) {
          // 替换历史记录为压缩后的版本
          basicHistory.clear()
          compressed.forEach((entry) => {
            basicHistory.addEntry(entry.action, entry.data, entry.description)
          })
        }
      }
    },
    { deep: true },
  )

  // 计算统计信息
  const stats = computed((): HistoryStats => {
    const entries = basicHistory.history.value
    if (entries.length === 0) {
      return {
        totalEntries: 0,
        memoryUsage: 0,
        mostFrequentAction: '',
        compressionRatio: 0,
      }
    }

    // 统计最频繁的操作
    const actionCounts: Record<string, number> = {}
    entries.forEach((entry) => {
      actionCounts[entry.action] = (actionCounts[entry.action] || 0) + 1
    })

    const mostFrequentAction =
      Object.entries(actionCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || ''

    return {
      totalEntries: entries.length,
      memoryUsage: basicHistory.memoryUsage.value,
      oldestEntry:
        entries.length > 0 ? new Date(entries[0]?.timestamp || 0) : undefined,
      newestEntry:
        entries.length > 0
          ? new Date(entries[entries.length - 1]?.timestamp || 0)
          : undefined,
      mostFrequentAction,
      compressionRatio: compression.compressionStats.value.compressionRatio,
    }
  })

  // 扩展的添加条目方法
  const addEntry = (action: string, data: T, description?: string): string => {
    const id = basicHistory.addEntry(action, data, description)

    // 自动保存
    if (config.autoSave && !config.saveInterval) {
      persistence.saveToStorage(basicHistory.getHistory() as HistoryEntry<T>[])
    }

    return id
  }

  // 批量操作
  const addBatch = (
    entries: Array<{ action: string; data: T; description?: string }>,
  ): string[] => {
    basicHistory.setRecording(false)
    const ids: string[] = []

    entries.forEach(({ action, data, description }) => {
      const id = basicHistory.addEntry(action, data, description)
      ids.push(id)
    })

    basicHistory.setRecording(true)
    return ids
  }

  // 手动压缩
  const compressHistory = (
    options?: Parameters<typeof compression.compress>[1],
  ) => {
    const currentHistory = basicHistory.getHistory()
    const compressed = compression.compress(
      currentHistory as HistoryEntry<T>[],
      options,
    )

    if (compressed.length < currentHistory.length) {
      basicHistory.clear()
      compressed.forEach((entry) => {
        basicHistory.addEntry(entry.action, entry.data, entry.description)
      })
      return true
    }

    return false
  }

  // 初始化加载
  const initialize = async () => {
    try {
      const savedHistory = persistence.loadFromStorage()
      if (savedHistory.length > 0) {
        basicHistory.clear()
        savedHistory.forEach((entry) => {
          basicHistory.addEntry(
            entry.action,
            entry.data as T,
            entry.description,
          )
        })
      }

      startAutoSave()
    } catch (error) {
      console.error('初始化历史记录管理器失败:', error)
    }
  }

  // 清理
  const destroy = () => {
    stopAutoSave()
  }

  return {
    // 基础功能
    ...basicHistory,

    // 压缩功能
    compress: compressHistory,
    compressionStats: compression.compressionStats,

    // 持久化功能
    saveToStorage: persistence.saveToStorage,
    loadFromStorage: persistence.loadFromStorage,
    exportToFile: persistence.exportToFile,
    importFromFile: persistence.importFromFile,
    clearStorage: persistence.clearStorage,
    getStorageInfo: persistence.getStorageInfo,

    // 扩展方法
    addEntry,
    addBatch,
    stats,

    // 生命周期
    initialize,
    destroy,
    startAutoSave,
    stopAutoSave,
  }
}

// 重新导出类型
export type { HistoryEntry }
