import { ref, computed } from 'vue'
import type { ComponentRegistry } from '@/types/form-renderer'

// 默认组件映射
const DEFAULT_COMPONENTS = {
  // 基础输入组件
  text: () => import('../../../../packages/components/text/TextInput.vue'),
  textarea: () =>
    import('../../../../packages/components/textarea/TextareaInput.vue'),
  number: () =>
    import('../../../../packages/components/number/NumberInput.vue'),
  email: () => import('../../../../packages/components/text/TextInput.vue'), // 使用text组件
  password: () => import('../../../../packages/components/text/TextInput.vue'), // 使用text组件
  url: () => import('../../../../packages/components/text/TextInput.vue'), // 使用text组件

  // 选择组件
  select: () =>
    import('../../../../packages/components/select/SelectInput.vue'),
  radio: () => import('../../../../packages/components/radio/RadioInput.vue'),
  checkbox: () =>
    import('../../../../packages/components/checkbox/CheckboxInput.vue'),
  switch: () =>
    import('../../../../packages/components/checkbox/CheckboxInput.vue'), // 使用checkbox组件

  // 日期时间组件
  date: () => import('../../../../packages/components/date/DateInput.vue'),
  time: () => import('../../../../packages/components/time/TimeInput.vue'),
  datetime: () => import('../../../../packages/components/date/DateInput.vue'), // 使用date组件
  'date-range': () =>
    import('../../../../packages/components/date-range/DateRangeInput.vue'),
  'time-range': () =>
    import('../../../../packages/components/time-range/TimeRangeInput.vue'),

  // 其他组件暂时使用基础组件
  file: () => import('../../../../packages/components/text/TextInput.vue'),
  image: () => import('../../../../packages/components/text/TextInput.vue'),
  rating: () =>
    import('../../../../packages/components/number/NumberInput.vue'),
  slider: () =>
    import('../../../../packages/components/number/NumberInput.vue'),
  color: () => import('../../../../packages/components/text/TextInput.vue'),
  cascader: () =>
    import('../../../../packages/components/select/SelectInput.vue'),

  // 医疗专用组件暂时使用基础组件
  'medical-measurement': () =>
    import('../../../../packages/components/text/TextInput.vue'),
  'medical-drug': () =>
    import('../../../../packages/components/text/TextInput.vue'),
  'medical-symptom': () =>
    import('../../../../packages/components/textarea/TextareaInput.vue'),
  'medical-diagnosis': () =>
    import('../../../../packages/components/textarea/TextareaInput.vue'),

  // 复合组件暂时使用基础组件
  repeater: () => import('../../../../packages/components/text/TextInput.vue'),
  group: () => import('../../../../packages/components/text/TextInput.vue'),
  table: () => import('../../../../packages/components/text/TextInput.vue'),

  // 显示组件暂时使用基础组件
  divider: () => import('../../../../packages/components/text/TextInput.vue'),
  html: () =>
    import('../../../../packages/components/textarea/TextareaInput.vue'),
  markdown: () =>
    import('../../../../packages/components/textarea/TextareaInput.vue'),
}

// 后备组件映射 (使用packages中的组件)
const FALLBACK_COMPONENTS = {
  text: () => import('../../../../packages/components/text/TextInput.vue'),
  textarea: () =>
    import('../../../../packages/components/textarea/TextareaInput.vue'),
  number: () =>
    import('../../../../packages/components/number/NumberInput.vue'),
  select: () =>
    import('../../../../packages/components/select/SelectInput.vue'),
  radio: () => import('../../../../packages/components/radio/RadioInput.vue'),
  checkbox: () =>
    import('../../../../packages/components/checkbox/CheckboxInput.vue'),
  date: () => import('../../../../packages/components/date/DateInput.vue'),
  time: () => import('../../../../packages/components/time/TimeInput.vue'),
  'date-range': () =>
    import('../../../../packages/components/date-range/DateRangeInput.vue'),
  'time-range': () =>
    import('../../../../packages/components/time-range/TimeRangeInput.vue'),
}

class ComponentRegistryImpl implements ComponentRegistry {
  public components = new Map<string, Record<string, unknown>>()
  public loadedComponents = new Map<string, Record<string, unknown>>()
  public loadingComponents = new Set<string>()

  constructor() {
    // 注册默认组件
    this.registerDefaults()
  }

  // 注册默认组件
  private registerDefaults() {
    Object.entries(DEFAULT_COMPONENTS).forEach(([type, loader]) => {
      this.components.set(type, loader)
    })
  }

  // 注册组件
  register(type: string, component: Record<string, unknown>): void {
    if (typeof component === 'function') {
      // 如果是异步组件加载器
      this.components.set(type, component)
    } else {
      // 如果是直接的组件对象
      this.loadedComponents.set(type, component)
    }
  }

  // 获取组件
  get(type: string): Record<string, unknown> | undefined {
    // 首先检查已加载的组件
    if (this.loadedComponents.has(type)) {
      return this.loadedComponents.get(type)
    }

    // 检查是否有组件加载器
    if (this.components.has(type)) {
      return this.components.get(type)
    }

    // 尝试后备组件
    if (FALLBACK_COMPONENTS[type as keyof typeof FALLBACK_COMPONENTS]) {
      return FALLBACK_COMPONENTS[type as keyof typeof FALLBACK_COMPONENTS]
    }

    // 返回默认文本组件
    console.warn(`组件类型 "${type}" 未注册，使用默认文本组件`)
    return this.components.get('text') || FALLBACK_COMPONENTS.text
  }

  // 检查组件是否存在
  has(type: string): boolean {
    return (
      this.loadedComponents.has(type) ||
      this.components.has(type) ||
      type in FALLBACK_COMPONENTS
    )
  }

  // 列出所有注册的组件类型
  list(): string[] {
    const types = new Set<string>()

    // 添加已加载的组件
    this.loadedComponents.forEach((_, type) => types.add(type))

    // 添加注册的组件
    this.components.forEach((_, type) => types.add(type))

    // 添加后备组件
    Object.keys(FALLBACK_COMPONENTS).forEach((type) => types.add(type))

    return Array.from(types).sort()
  }

  // 预加载组件
  async preload(type: string): Promise<Record<string, unknown> | undefined> {
    if (this.loadedComponents.has(type)) {
      return this.loadedComponents.get(type)
    }

    if (this.loadingComponents.has(type)) {
      // 等待正在加载的组件
      while (this.loadingComponents.has(type)) {
        await new Promise((resolve) => setTimeout(resolve, 10))
      }
      return this.loadedComponents.get(type)
    }

    const loader =
      this.components.get(type) ||
      FALLBACK_COMPONENTS[type as keyof typeof FALLBACK_COMPONENTS]
    if (!loader) {
      throw new Error(`组件类型 "${type}" 未注册`)
    }

    try {
      this.loadingComponents.add(type)
      const component = await loader()
      const resolvedComponent = component.default || component
      this.loadedComponents.set(type, resolvedComponent)
      return resolvedComponent
    } catch (error) {
      console.error(`加载组件 "${type}" 失败:`, error)
      throw error
    } finally {
      this.loadingComponents.delete(type)
    }
  }

  // 批量预加载组件
  async preloadMultiple(types: string[]): Promise<void> {
    await Promise.all(
      types.map((type) => this.preload(type).catch(console.error)),
    )
  }

  // 清除已加载的组件（用于热重载）
  clear(): void {
    this.loadedComponents.clear()
    this.loadingComponents.clear()
  }

  // 获取组件分组
  getGroups(): Record<string, string[]> {
    const groups: Record<string, string[]> = {
      基础输入: ['text', 'textarea', 'number', 'email', 'password', 'url'],
      选择组件: ['select', 'radio', 'checkbox', 'switch'],
      日期时间: ['date', 'time', 'datetime', 'date-range', 'time-range'],
      上传组件: ['file', 'image'],
      特殊组件: ['rating', 'slider', 'color', 'cascader'],
      医疗专用: [
        'medical-measurement',
        'medical-drug',
        'medical-symptom',
        'medical-diagnosis',
      ],
      复合组件: ['repeater', 'group', 'table'],
      显示组件: ['divider', 'html', 'markdown'],
    }

    return groups
  }
}

// 全局组件注册表实例
const globalRegistry = new ComponentRegistryImpl()

/**
 * 组件注册表 Composable
 */
export function useComponentRegistry() {
  const registry = ref(globalRegistry)

  // 获取组件
  const getComponent = (type: string) => {
    return registry.value.get(type)
  }

  // 注册组件
  const registerComponent = (
    type: string,
    component: Record<string, unknown>,
  ) => {
    registry.value.register(type, component)
  }

  // 检查组件是否存在
  const hasComponent = (type: string) => {
    return registry.value.has(type)
  }

  // 获取所有组件类型
  const getAllTypes = () => {
    return registry.value.list()
  }

  // 获取分组的组件
  const getGroupedComponents = () => {
    return registry.value.getGroups()
  }

  // 预加载组件
  const preloadComponent = async (type: string) => {
    return registry.value.preload(type)
  }

  // 批量预加载组件
  const preloadComponents = async (types: string[]) => {
    return registry.value.preloadMultiple(types)
  }

  // 获取组件信息
  const getComponentInfo = (type: string) => {
    const registryImpl = registry.value as ComponentRegistryImpl
    return {
      type,
      exists: hasComponent(type),
      loaded: registryImpl.loadedComponents.has(type),
      loading: registryImpl.loadingComponents.has(type),
    }
  }

  // 获取组件使用统计
  const getComponentStats = () => {
    const registryImpl = registry.value as ComponentRegistryImpl
    return {
      registered: registryImpl.components.size,
      loaded: registryImpl.loadedComponents.size,
      loading: registryImpl.loadingComponents.size,
      total: registryImpl.list().length,
    }
  }

  return {
    registry,
    getComponent,
    registerComponent,
    hasComponent,
    getAllTypes,
    getGroupedComponents,
    preloadComponent,
    preloadComponents,
    getComponentInfo,
    getComponentStats,
  }
}

// 导出全局注册表
export { globalRegistry }
