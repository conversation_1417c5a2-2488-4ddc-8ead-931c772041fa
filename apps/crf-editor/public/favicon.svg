<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 " width="24" height="24">
  <!-- 定义渐变和滤镜 -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#4aca95" />
      <stop offset="100%" stop-color="#39b883" />
    </linearGradient>
  </defs>

  <!-- 底层阴影 -->
  <rect x="4" y="4" width="18" height="18" rx="2" fill="#2a8c63" opacity="0.5" />

  <!-- 简洁背景 -->
  <rect x="3" y="3" width="18" height="18" rx="2" fill="url(#bg-gradient)" />

  <!-- 装饰线条 - 表单样式 -->
  <line x1="6" y1="7" x2="18" y2="7" stroke="white" stroke-width="0.7" opacity="0.9" />
  <line x1="6" y1="10" x2="18" y2="10" stroke="white" stroke-width="0.7" opacity="0.9" />
  <line x1="6" y1="13" x2="15" y2="13" stroke="white" stroke-width="0.7" opacity="0.9" />

  <!-- 装饰元素 - 右上角折角 -->
  <path d="M17,3 L21,3 L21,7 Z" fill="#2a8c63" opacity="0.7" />
  <path d="M17,3 L21,7 L17,7 Z" fill="#4aca95" opacity="0.9" />

  <!-- CRF 字母居中显示 -->
  <text x="12" y="17" font-family="Arial, sans-serif" font-weight="bold" font-size="6" fill="#ffffff" text-anchor="middle" dominant-baseline="central">CRF</text>

  <!-- 底部装饰线 -->
  <line x1="7" y1="20" x2="17" y2="20" stroke="white" stroke-width="0.7" opacity="0.6" />
</svg>
