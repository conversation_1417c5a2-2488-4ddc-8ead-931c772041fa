{"extends": "@crf/tsconfig/vite.json", "compilerOptions": {"baseUrl": ".", "strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noUncheckedIndexedAccess": false, "skipLibCheck": true, "jsxImportSource": "vue", "types": ["node", "vite/client"], "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "references": [{"path": "../../packages/type-definitions"}]}