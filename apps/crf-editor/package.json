{"name": "crf-editor", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "dev:test": "vite --mode test", "build": "vue-tsc --build && vite build", "build:dev": "vue-tsc --build && vite build --mode development", "build:prod": "vue-tsc --build && vite build --mode production", "build:test": "vue-tsc --build && vite build --mode test", "preview": "vite preview", "preview:prod": "vite preview --mode production", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@crf/ui-components": "workspace:*", "@crf/vue-composables": "workspace:*", "@crf/app-constants": "workspace:*", "@crf/core-editor": "workspace:*", "@crf/network-client": "workspace:*", "@crf/design-system": "workspace:*", "@crf/i18n-medical": "workspace:*", "@crf/type-definitions": "workspace:*", "@crf/shared-utils": "workspace:*", "naive-ui": "^2.42.0", "pinia": "^3.0.1", "vue": "^3.5.17", "vue-advanced-cropper": "^2.8.9", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.15.19", "@vicons/antd": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "esbuild-register": "^3.6.0", "unocss": "^66.1.2", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}