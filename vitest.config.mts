import { defineConfig } from 'vitest/config'
import Vue from '@vitejs/plugin-vue'
import VueJsx from '@vitejs/plugin-vue-jsx'
import VueMacros from 'unplugin-vue-macros/vite'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    VueMacros({
      setupComponent: false,
      setupSFC: false,
      plugins: {
        vue: Vue(),
        vueJsx: VueJsx(),
      },
    }),
  ],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, './apps/crf-editor/src'),
      '@crf': resolve(__dirname, './packages'),
    },
  },
  
  optimizeDeps: {
    disabled: true,
  },
  
  test: {
    clearMocks: true,
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
    reporters: ['default', 'json', 'html'],
    
    // 测试文件匹配模式
    include: [
      'apps/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'packages/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      '**/__tests__/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // 排除的文件
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/build/**'
    ],
    
    testTransformMode: {
      web: ['*.{ts,tsx}'],
    },
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json-summary', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'play/**',
        'packages/*/dist/**',
        'apps/*/dist/**',
        'packages/locale/lang/**',
        'packages/components/*/style/**',
        '**/node_modules/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/types/**',
        '**/__tests__/**',
        '**/*.test.*',
        '**/*.spec.*'
      ],
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 60,
          functions: 60,
          lines: 60,
          statements: 60
        }
      }
    },
    
    // 全局变量
    globals: true,
    
    // 测试超时时间
    testTimeout: 10000,
    
    // 并发测试
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    }
  },
})
