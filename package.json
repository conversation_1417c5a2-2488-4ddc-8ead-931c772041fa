{"name": "crf-frontend", "version": "1.0.0", "private": true, "packageManager": "pnpm@9.15.4", "description": "基于Vue3、TypeScript、Unocss 的现代化医疗专业领域 CRF 表单制作工具", "scripts": {"dev": "turbo dev", "build": "turbo build", "build:packages": "turbo build --filter='!./apps/**'", "build:apps": "turbo build --filter='./apps/**'", "build:fast": "turbo build --filter='!./apps/**' && turbo build --filter='./apps/**'", "build:safe": "turbo build --concurrency=1", "build:packages:safe": "turbo build --filter='!./apps/**' --concurrency=1", "build:apps:fast": "turbo build --filter='./apps/**'", "dev:packages": "turbo dev --filter='!./apps/**'", "type-check": "turbo type-check", "type-check:packages": "turbo type-check --filter='!./apps/**'", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "test": "turbo test", "test:ci": "turbo test --coverage", "clean": "turbo clean && rm -rf node_modules/.cache", "clean:dist": "pnpm -r exec rm -rf dist", "clean:all": "pnpm run clean:dist && pnpm run clean && rm -rf **/node_modules", "postinstall": "turbo build --filter='!./apps/**' --concurrency=1", "changeset": "changeset", "changeset:version": "changeset version", "changeset:publish": "changeset publish", "changeset:status": "changeset status", "api:mock": "node mock-api-server.js", "dev:full": "concurrently \"pnpm run api:mock\" \"pnpm run dev\"", "migrate:analyze": "node scripts/migrate-to-unocss.js", "migrate:analyze:editor": "node scripts/migrate-to-unocss.js apps/crf-editor/src", "migrate:analyze:components": "node scripts/migrate-to-unocss.js packages/components", "unocss:dev": "unocss --watch", "unocss:build": "unocss", "unocss:inspect": "unocss --inspect", "style:check": "npm run unocss:build && echo 'UnoCSS build completed successfully'", "docs:migration": "echo 'Opening migration documentation...' && start docs/development/UNOCSS_MIGRATION_GUIDE.md", "quality:check": "node scripts/check-code-quality.js", "quality:report": "npm run quality:check && echo '代码质量报告已生成'", "quality:fix": "echo '自动修复功能开发中...' && npm run quality:check", "format": "turbo format", "format:check": "prettier --check \"**/*.{vue,ts,js,json,md}\"", "format:write": "prettier --write \"**/*.{vue,ts,js,json,md}\"", "lint:check": "turbo lint", "lint:fix-all": "turbo lint:fix"}, "devDependencies": {"@changesets/cli": "^2.29.5", "@crf/eslint-config": "workspace:*", "@crf/tsconfig": "workspace:*", "turbo": "^2.3.3", "@sinclair/typebox": "^0.34.33", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/test-utils": "^2.4.6", "concurrently": "^8.2.2", "cors": "^2.8.5", "express": "^4.21.2", "sass": "^1.89.0", "typescript": "^5.8.3", "unbuild": "^3.5.0", "unocss": "^66.1.2", "vite": "^6.2.4", "vitest": "^3.1.3", "vue-tsc": "^2.2.8"}, "engines": {"node": ">=18.0.0", "pnpm": ">=9.0.0"}}