package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"gopkg.in/yaml.v3"
)

type ServerConfig struct {
	Port           int           `yaml:"port" env:"SERVER_PORT"`
	Host           string        `yaml:"host" env:"SERVER_HOST"`
	Timeout        TimeoutConfig `yaml:"timeout"`
	MaxHeaderBytes int           `yaml:"max_header_bytes" env:"MAX_HEADER_BYTES"`
}

type TimeoutConfig struct {
	Read  time.Duration `yaml:"read"`
	Write time.Duration `yaml:"write"`
}

type DatabaseConfig struct {
	Host     string `yaml:"host" env:"DATABASE_HOST"`
	Port     int    `yaml:"port" env:"DATABASE_PORT"`
	User     string `yaml:"user" env:"DATABASE_USER"`
	Password string `yaml:"password" env:"DATABASE_PASSWORD"`
	Name     string `yaml:"name" env:"DATABASE_NAME"`
	URL      string `yaml:"url" env:"DATABASE_URL"`
	SSLMode  string `yaml:"ssl_mode" env:"DATABASE_SSL_MODE"`
}

type RedisConfig struct {
	Host     string        `yaml:"host" env:"REDIS_HOST"`
	Port     int           `yaml:"port" env:"REDIS_PORT"`
	Password string        `yaml:"password" env:"REDIS_PASSWORD"`
	DB       int           `yaml:"db" env:"REDIS_DB"`
	PoolSize int           `yaml:"pool_size" env:"REDIS_POOL_SIZE"`
	Timeout  time.Duration `yaml:"timeout" env:"REDIS_TIMEOUT"`
}

type SecurityConfig struct {
	JWT     JWTConfig     `yaml:"jwt"`
	Session SessionConfig `yaml:"session"`
}

type JWTConfig struct {
	Secret              string `yaml:"secret" env:"JWT_SECRET"`
	AccessTokenTTL      string `yaml:"access_token_ttl" env:"JWT_ACCESS_TOKEN_TTL"`
	RefreshTokenTTL     string `yaml:"refresh_token_ttl" env:"JWT_REFRESH_TOKEN_TTL"`
	RefreshThreshold    string `yaml:"refresh_threshold" env:"JWT_REFRESH_THRESHOLD"`
	LongTermTokenTTL    string `yaml:"long_term_token_ttl" env:"JWT_LONG_TERM_TOKEN_TTL"`
	RememberMeThreshold string `yaml:"remember_me_threshold" env:"JWT_REMEMBER_ME_THRESHOLD"`
}

type SessionConfig struct {
	Secret string `yaml:"secret" env:"SESSION_SECRET"`
}

type UploadsConfig struct {
	Directory   string `yaml:"directory" env:"UPLOAD_DIR"`
	MaxFileSize int64  `yaml:"max_file_size" env:"MAX_FILE_SIZE"`
}

type MinIOConfig struct {
	Endpoint    string `yaml:"endpoint" env:"MINIO_ENDPOINT"`
	AccessKey   string `yaml:"access_key" env:"MINIO_ACCESS_KEY"`
	SecretKey   string `yaml:"secret_key" env:"MINIO_SECRET_KEY"`
	BucketName  string `yaml:"bucket_name" env:"MINIO_BUCKET_NAME"`
	UseSSL      bool   `yaml:"use_ssl" env:"MINIO_USE_SSL"`
	Region      string `yaml:"region" env:"MINIO_REGION"`
}

type EnvironmentConfig struct {
	Name  string `yaml:"name" env:"ENV"`
	Debug bool   `yaml:"debug" env:"DEBUG"`
}

type AutoSaveConfig struct {
	Interval        int `yaml:"interval" env:"AUTO_SAVE_INTERVAL"`
	CleanupInterval int `yaml:"cleanup_interval" env:"AUTO_SAVE_CLEANUP_INTERVAL"`
}

type LoggingConfig struct {
	Level  string `yaml:"level" env:"LOG_LEVEL"`
	Format string `yaml:"format" env:"LOG_FORMAT"`
	File   string `yaml:"file" env:"LOG_FILE"`
}

type Config struct {
	Server      ServerConfig      `yaml:"server"`
	Database    DatabaseConfig    `yaml:"database"`
	Redis       RedisConfig       `yaml:"redis"`
	Security    SecurityConfig    `yaml:"security"`
	Uploads     UploadsConfig     `yaml:"uploads"`
	MinIO       MinIOConfig       `yaml:"minio"`
	Environment EnvironmentConfig `yaml:"environment"`
	AutoSave    AutoSaveConfig    `yaml:"auto_save"`
	Logging     LoggingConfig     `yaml:"logging"`
}

func Load() (*Config, error) {
	_ = godotenv.Load()

	cfg := &Config{}

	if err := loadFromYAML(cfg); err != nil {
		return nil, fmt.Errorf("failed to load YAML config: %w", err)
	}

	if err := overrideWithEnv(cfg); err != nil {
		return nil, fmt.Errorf("failed to override with env vars: %w", err)
	}

	if err := setDefaults(cfg); err != nil {
		return nil, fmt.Errorf("failed to set defaults: %w", err)
	}

	if err := constructDatabaseURL(cfg); err != nil {
		return nil, fmt.Errorf("failed to construct database URL: %w", err)
	}

	return cfg, nil
}

func loadFromYAML(cfg *Config) error {
	configPath := getConfigPath()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil // YAML file is optional
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	if err := yaml.Unmarshal(data, cfg); err != nil {
		return fmt.Errorf("failed to unmarshal YAML: %w", err)
	}

	return nil
}

func getConfigPath() string {
	if path := os.Getenv("CONFIG_PATH"); path != "" {
		return path
	}

	possiblePaths := []string{
		"config.yaml",
		"config.yml",
		"configs/config.yaml",
		"configs/config.yml",
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return "config.yaml"
}

func overrideWithEnv(cfg *Config) error {
	setEnvString(&cfg.Server.Host, "SERVER_HOST")
	setEnvInt(&cfg.Server.Port, "SERVER_PORT")
	setEnvInt(&cfg.Server.MaxHeaderBytes, "MAX_HEADER_BYTES")

	setEnvString(&cfg.Database.Host, "DATABASE_HOST")
	setEnvInt(&cfg.Database.Port, "DATABASE_PORT")
	setEnvString(&cfg.Database.User, "DATABASE_USER")
	setEnvString(&cfg.Database.Password, "DATABASE_PASSWORD")
	setEnvString(&cfg.Database.Name, "DATABASE_NAME")
	setEnvString(&cfg.Database.URL, "DATABASE_URL")
	setEnvString(&cfg.Database.SSLMode, "DATABASE_SSL_MODE")

	setEnvString(&cfg.Redis.Host, "REDIS_HOST")
	setEnvInt(&cfg.Redis.Port, "REDIS_PORT")
	setEnvString(&cfg.Redis.Password, "REDIS_PASSWORD")
	setEnvInt(&cfg.Redis.DB, "REDIS_DB")
	setEnvInt(&cfg.Redis.PoolSize, "REDIS_POOL_SIZE")

	setEnvString(&cfg.Security.JWT.Secret, "JWT_SECRET")
	setEnvString(&cfg.Security.JWT.AccessTokenTTL, "JWT_ACCESS_TOKEN_TTL")
	setEnvString(&cfg.Security.JWT.RefreshTokenTTL, "JWT_REFRESH_TOKEN_TTL")
	setEnvString(&cfg.Security.JWT.RefreshThreshold, "JWT_REFRESH_THRESHOLD")
	setEnvString(&cfg.Security.JWT.LongTermTokenTTL, "JWT_LONG_TERM_TOKEN_TTL")
	setEnvString(&cfg.Security.JWT.RememberMeThreshold, "JWT_REMEMBER_ME_THRESHOLD")
	setEnvString(&cfg.Security.Session.Secret, "SESSION_SECRET")

	setEnvString(&cfg.Uploads.Directory, "UPLOAD_DIR")
	setEnvInt64(&cfg.Uploads.MaxFileSize, "MAX_FILE_SIZE")

	setEnvString(&cfg.MinIO.Endpoint, "MINIO_ENDPOINT")
	setEnvString(&cfg.MinIO.AccessKey, "MINIO_ACCESS_KEY")
	setEnvString(&cfg.MinIO.SecretKey, "MINIO_SECRET_KEY")
	setEnvString(&cfg.MinIO.BucketName, "MINIO_BUCKET_NAME")
	setEnvBool(&cfg.MinIO.UseSSL, "MINIO_USE_SSL")
	setEnvString(&cfg.MinIO.Region, "MINIO_REGION")

	setEnvString(&cfg.Environment.Name, "ENV")
	setEnvBool(&cfg.Environment.Debug, "DEBUG")

	setEnvInt(&cfg.AutoSave.Interval, "AUTO_SAVE_INTERVAL")
	setEnvInt(&cfg.AutoSave.CleanupInterval, "AUTO_SAVE_CLEANUP_INTERVAL")

	setEnvString(&cfg.Logging.Level, "LOG_LEVEL")
	setEnvString(&cfg.Logging.Format, "LOG_FORMAT")
	setEnvString(&cfg.Logging.File, "LOG_FILE")

	return nil
}

func setDefaults(cfg *Config) error {
	if cfg.Server.Host == "" {
		cfg.Server.Host = "localhost"
	}
	if cfg.Server.Port == 0 {
		cfg.Server.Port = 3000
	}
	if cfg.Server.MaxHeaderBytes == 0 {
		cfg.Server.MaxHeaderBytes = 1 << 20
	}
	if cfg.Server.Timeout.Read == 0 {
		cfg.Server.Timeout.Read = 10 * time.Second
	}
	if cfg.Server.Timeout.Write == 0 {
		cfg.Server.Timeout.Write = 10 * time.Second
	}

	if cfg.Database.Host == "" {
		cfg.Database.Host = "localhost"
	}
	if cfg.Database.Port == 0 {
		cfg.Database.Port = 5432
	}
	if cfg.Database.User == "" {
		cfg.Database.User = "postgres"
	}
	if cfg.Database.Name == "" {
		cfg.Database.Name = "crf_db"
	}
	if cfg.Database.SSLMode == "" {
		cfg.Database.SSLMode = "disable"
	}

	if cfg.Redis.Host == "" {
		cfg.Redis.Host = "localhost"
	}
	if cfg.Redis.Port == 0 {
		cfg.Redis.Port = 6379
	}
	if cfg.Redis.DB == 0 {
		cfg.Redis.DB = 0
	}
	if cfg.Redis.PoolSize == 0 {
		cfg.Redis.PoolSize = 10
	}
	if cfg.Redis.Timeout == 0 {
		cfg.Redis.Timeout = 5 * time.Second
	}

	if cfg.Security.JWT.Secret == "" {
		cfg.Security.JWT.Secret = "your-super-secret-jwt-key"
	}
	if cfg.Security.JWT.AccessTokenTTL == "" {
		cfg.Security.JWT.AccessTokenTTL = "15m"
	}
	if cfg.Security.JWT.RefreshTokenTTL == "" {
		cfg.Security.JWT.RefreshTokenTTL = "168h" // 7 days
	}
	if cfg.Security.JWT.RefreshThreshold == "" {
		cfg.Security.JWT.RefreshThreshold = "5m"
	}
	if cfg.Security.JWT.LongTermTokenTTL == "" {
		cfg.Security.JWT.LongTermTokenTTL = "720h" // 30 days for remember me
	}
	if cfg.Security.JWT.RememberMeThreshold == "" {
		cfg.Security.JWT.RememberMeThreshold = "24h" // 24 hours before expiry
	}
	if cfg.Security.Session.Secret == "" {
		cfg.Security.Session.Secret = "your-super-secret-session-key"
	}

	if cfg.Uploads.Directory == "" {
		cfg.Uploads.Directory = "./uploads"
	}
	if cfg.Uploads.MaxFileSize == 0 {
		cfg.Uploads.MaxFileSize = 10485760
	}

	if cfg.MinIO.BucketName == "" {
		cfg.MinIO.BucketName = "avatars"
	}
	if cfg.MinIO.Region == "" {
		cfg.MinIO.Region = "us-east-1"
	}

	if cfg.Environment.Name == "" {
		cfg.Environment.Name = "development"
	}

	if cfg.AutoSave.Interval == 0 {
		cfg.AutoSave.Interval = 30
	}
	if cfg.AutoSave.CleanupInterval == 0 {
		cfg.AutoSave.CleanupInterval = 3600
	}

	if cfg.Logging.Level == "" {
		cfg.Logging.Level = "info"
	}
	if cfg.Logging.Format == "" {
		cfg.Logging.Format = "text"
	}

	return nil
}

func constructDatabaseURL(cfg *Config) error {
	if cfg.Database.URL == "" {
		cfg.Database.URL = fmt.Sprintf(
			"postgres://%s:%s@%s:%d/%s?sslmode=%s",
			cfg.Database.User,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.Name,
			cfg.Database.SSLMode,
		)
	}
	return nil
}

func setEnvString(target *string, envKey string) {
	if value := os.Getenv(envKey); value != "" {
		*target = value
	}
}

func setEnvInt(target *int, envKey string) {
	if value := os.Getenv(envKey); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			*target = intValue
		}
	}
}

func setEnvInt64(target *int64, envKey string) {
	if value := os.Getenv(envKey); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			*target = intValue
		}
	}
}

func setEnvBool(target *bool, envKey string) {
	if value := os.Getenv(envKey); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			*target = boolValue
		}
	}
}
