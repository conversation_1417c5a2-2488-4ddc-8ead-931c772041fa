package handlers

import (
	"context"
	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type UserHandler struct {
	userService      *services.UserService
	jwtService       *services.JWTService
	rateLimitService *services.RateLimitService
	smsService       *services.SMSService
	logger           *logrus.Logger
}

func NewUserHandler(
	userService *services.UserService,
	jwtService *services.JWTService,
	rateLimitService *services.RateLimitService,
	smsService *services.SMSService,
	logger *logrus.Logger,
) *UserHandler {
	return &UserHandler{
		userService:      userService,
		jwtService:       jwtService,
		rateLimitService: rateLimitService,
		smsService:       smsService,
		logger:           logger,
	}
}

type LoginRequest struct {
	// 传统用户名密码登录
	Username string `json:"username"`
	Password string `json:"password"`

	// 手机号登录
	Phone     string `json:"phone"`
	PhoneCode string `json:"phone_code"`

	// 微信登录
	WechatCode string `json:"wechat_code"`

	// 登录类型
	LoginType string `json:"login_type" binding:"required,oneof=password sms wechat"`

	// 记住我选项
	RememberMe bool `json:"remember_me"`
}

type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	FullName string `json:"full_name"`
}

type SendSMSRequest struct {
	Phone string `json:"phone" binding:"required"`
}

func (h *UserHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind login request")
		response.ValidationError(c, err)
		return
	}

	ctx := c.Request.Context()

	// 确定用于速率限制的标识符
	var identifier string
	switch req.LoginType {
	case "password":
		identifier = req.Username
	case "sms":
		identifier = req.Phone
	case "wechat":
		identifier = req.WechatCode
	default:
		response.BadRequest(c, "不支持的登录类型")
		return
	}

	// 检查登录频率限制
	allowed, lockDuration, err := h.rateLimitService.CheckLoginRateLimit(ctx, identifier)
	if err != nil {
		h.logger.WithError(err).Error("Failed to check rate limit")
		// 继续处理，不因为Redis错误阻止登录
	}

	if !allowed {
		h.logger.WithFields(logrus.Fields{
			"identifier":    identifier,
			"login_type":    req.LoginType,
			"lock_duration": lockDuration,
		}).Warn("Login rate limit exceeded")

		response.TooManyRequests(c, fmt.Sprintf("登录尝试过于频繁，请 %.0f 分钟后再试", lockDuration.Minutes()))
		return
	}

	h.logger.WithFields(logrus.Fields{
		"login_type": req.LoginType,
		"identifier": identifier,
	}).Info("收到登录请求")

	var (
		user    *models.User
		authErr error
	)

	// 根据登录类型进行不同的认证
	switch req.LoginType {
	case "password":
		if req.Username == "" || req.Password == "" {
			response.BadRequest(c, "用户名和密码不能为空")
			return
		}
		user, authErr = h.authenticateWithPassword(ctx, req.Username, req.Password)
	case "sms":
		if req.Phone == "" || req.PhoneCode == "" {
			response.BadRequest(c, "手机号和验证码不能为空")
			return
		}
		user, authErr = h.authenticateWithSMS(ctx, req.Phone, req.PhoneCode)
	case "wechat":
		if req.WechatCode == "" {
			response.BadRequest(c, "微信授权码不能为空")
			return
		}
		user, authErr = h.authenticateWithWechat(req.WechatCode)
	default:
		response.BadRequest(c, "不支持的登录类型")
		return
	}

	if authErr != nil {
		h.logger.WithError(authErr).Warn("Authentication failed")

		// 记录失败的登录尝试
		if err := h.rateLimitService.RecordLoginAttempt(ctx, identifier); err != nil {
			h.logger.WithError(err).Error("Failed to record login attempt")
		}

		response.Unauthorized(c, "认证失败")
		return
	}

	// 检查用户是否激活
	if !user.IsActive {
		response.Unauthorized(c, "账号已被禁用")
		return
	}

	// 生成JWT Token对
	accessToken, refreshToken, err := h.jwtService.GenerateTokenPairWithRememberMe(user, req.RememberMe)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate JWT tokens")
		response.InternalError(c, "Token生成失败")
		return
	}

	// 创建用户会话记录
	session := &models.UserSession{
		UserID:       user.ID,
		SessionToken: accessToken,
		IPAddress:    c.ClientIP(),
		UserAgent:    c.GetHeader("User-Agent"),
	}
	if err := h.userService.CreateSession(session); err != nil {
		h.logger.WithError(err).Warn("Failed to create user session")
		// 不影响登录流程，只记录警告
	}

	// 清除登录尝试记录
	if err := h.rateLimitService.ClearLoginAttempts(ctx, identifier); err != nil {
		h.logger.WithError(err).Error("Failed to clear login attempts")
	}

	// 构建响应数据
	responseData := gin.H{
		"access_token":  accessToken,
		"refresh_token": refreshToken,
		"remember_me":   req.RememberMe,
		"user": gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"role":       user.Role,
			"login_type": user.LoginType,
		},
	}

	// 如果是记住我登录，添加额外信息
	if req.RememberMe {
		responseData["token_expires_in"] = "30 days"
		responseData["message"] = "登录成功，已记住登录状态"
	} else {
		responseData["token_expires_in"] = "7 days"
		responseData["message"] = "登录成功"
	}

	response.SuccessWithMessage(c, responseData["message"].(string), responseData)
}

// SendSMSCode 发送短信验证码
func (h *UserHandler) SendSMSCode(c *gin.Context) {
	var req SendSMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind SMS request")
		response.ValidationError(c, err)
		return
	}

	ctx := c.Request.Context()

	// 检查短信发送频率限制
	allowed, lockDuration, err := h.rateLimitService.CheckSMSRateLimit(ctx, req.Phone)
	if err != nil {
		h.logger.WithError(err).Error("Failed to check SMS rate limit")
		// 继续处理，不因为Redis错误阻止发送
	}

	if !allowed {
		h.logger.WithFields(logrus.Fields{
			"phone":         req.Phone,
			"lock_duration": lockDuration,
		}).Warn("SMS rate limit exceeded")

		response.TooManyRequests(c, fmt.Sprintf("短信发送过于频繁，请 %.0f 分钟后再试", lockDuration.Minutes()))
		return
	}

	// 发送短信验证码
	err = h.smsService.SendSMSCode(ctx, req.Phone)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send SMS code")

		// 记录失败的短信尝试
		if err := h.rateLimitService.RecordSMSAttempt(ctx, req.Phone); err != nil {
			h.logger.WithError(err).Error("Failed to record SMS attempt")
		}

		response.InternalError(c, "短信发送失败")
		return
	}

	h.logger.WithField("phone", req.Phone).Info("SMS code sent successfully")
	response.SuccessWithMessage(c, "短信验证码已发送", gin.H{
		"phone": req.Phone,
	})
}

// 用户名密码认证
func (h *UserHandler) authenticateWithPassword(ctx context.Context, username, password string) (*models.User, error) {
	user, err := h.userService.GetUserByUsername(username)
	if err != nil {
		return nil, err
	}

	if !h.userService.ValidatePassword(user, password) {
		return nil, fmt.Errorf("密码错误")
	}

	return user, nil
}

// 手机号短信验证码认证
func (h *UserHandler) authenticateWithSMS(ctx context.Context, phone, code string) (*models.User, error) {
	// 验证短信验证码
	valid, err := h.smsService.ValidateSMSCode(ctx, phone, code)
	if err != nil {
		return nil, fmt.Errorf("验证码验证失败: %w", err)
	}

	if !valid {
		return nil, fmt.Errorf("验证码错误或已过期")
	}

	user, err := h.userService.GetUserByPhone(phone)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// 微信认证
func (h *UserHandler) authenticateWithWechat(code string) (*models.User, error) {
	// 通过微信授权码获取用户信息
	wechatInfo, err := h.userService.GetWechatUserInfo(code)
	if err != nil {
		return nil, err
	}

	user, err := h.userService.GetUserByWechatOpenID(wechatInfo.OpenID)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func (h *UserHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, err)
		return
	}

	// Check if username already exists
	if _, err := h.userService.GetUserByUsername(req.Username); err == nil {
		response.Conflict(c, "用户名已存在")
		return
	}

	// Check if email already exists
	if _, err := h.userService.GetUserByEmail(req.Email); err == nil {
		response.Conflict(c, "邮箱已存在")
		return
	}

	// Create user
	user := &models.User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: req.Password, // Will be hashed in service
		FullName:     req.FullName,
		Role:         "user",
		IsActive:     true,
	}

	if err := h.userService.CreateUser(user); err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		response.InternalError(c, "用户创建失败")
		return
	}

	response.Created(c, "用户创建成功", gin.H{
		"user": gin.H{
			"id":        user.ID,
			"username":  user.Username,
			"email":     user.Email,
			"full_name": user.FullName,
			"role":      user.Role,
		},
	})
}

func (h *UserHandler) Logout(c *gin.Context) {
	// 从Header中获取Authorization
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) == 2 && tokenParts[0] == "Bearer" {
			// 将Token加入黑名单
			if err := h.jwtService.BlacklistToken(c.Request.Context(), tokenParts[1]); err != nil {
				h.logger.WithError(err).Warn("Failed to blacklist token on logout")
			}
		}
	}

	response.SuccessWithMessage(c, "退出登录成功", nil)
}

func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	// 从JWT中间件获取用户ID
	userIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	// 创建响应对象，包含用户的角色信息
	userResponse := map[string]interface{}{
		"id":              user.ID,
		"username":        user.Username,
		"email":           user.Email,
		"full_name":       user.FullName,
		"role":            user.Role,
		"avatar_url":      user.AvatarURL,
		"is_active":       user.IsActive,
		"phone":           user.Phone,
		"wechat_open_id":  user.WechatOpenID,
		"wechat_union_id": user.WechatUnionID,
		"login_type":      user.LoginType,
		"deleted_at":      user.DeletedAt,
		"is_deleted":      user.IsDeleted,
		"created_at":      user.CreatedAt,
		"updated_at":      user.UpdatedAt,
	}

	// 添加roles字段 - 根据role字段推断
	var roles []string
	switch user.Role {
	case "admin":
		roles = []string{"super_admin"}
	case "super_admin":
		roles = []string{"super_admin"}
	case "editor":
		roles = []string{"researcher"}
	case "viewer":
		roles = []string{"viewer"}
	case "user":
		roles = []string{"data_entry"}
	default:
		roles = []string{"viewer"}
	}
	userResponse["roles"] = roles

	response.Success(c, gin.H{"user": userResponse})
}

func (h *UserHandler) GetUsers(c *gin.Context) {
	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	users, total, err := h.userService.GetUsers(limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get users")
		response.InternalError(c, "获取用户列表失败")
		return
	}

	response.Success(c, gin.H{
		"users": users,
		"pagination": gin.H{
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	user, err := h.userService.GetUserByID(id)
	if err != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	// 创建响应对象，包含用户的角色信息
	userResponse := map[string]interface{}{
		"id":              user.ID,
		"username":        user.Username,
		"email":           user.Email,
		"full_name":       user.FullName,
		"role":            user.Role,
		"avatar_url":      user.AvatarURL,
		"is_active":       user.IsActive,
		"phone":           user.Phone,
		"wechat_open_id":  user.WechatOpenID,
		"wechat_union_id": user.WechatUnionID,
		"login_type":      user.LoginType,
		"deleted_at":      user.DeletedAt,
		"is_deleted":      user.IsDeleted,
		"created_at":      user.CreatedAt,
		"updated_at":      user.UpdatedAt,
	}

	// 添加roles字段 - 根据role字段推断
	var roles []string
	switch user.Role {
	case "admin":
		roles = []string{"super_admin"}
	case "super_admin":
		roles = []string{"super_admin"}
	case "editor":
		roles = []string{"researcher"}
	case "viewer":
		roles = []string{"viewer"}
	case "user":
		roles = []string{"data_entry"}
	default:
		roles = []string{"viewer"}
	}
	userResponse["roles"] = roles

	response.Success(c, gin.H{"user": userResponse})
}

func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		response.ValidationError(c, err)
		return
	}

	if err := h.userService.UpdateUser(id, updates); err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		response.InternalError(c, "用户更新失败")
		return
	}

	response.SuccessWithMessage(c, "用户更新成功", nil)
}

func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	if err := h.userService.DeleteUser(id); err != nil {
		h.logger.WithError(err).Error("Failed to delete user")
		response.InternalError(c, "用户删除失败")
		return
	}

	response.SuccessWithMessage(c, "用户删除成功", nil)
}

// RefreshToken 刷新JWT Token
func (h *UserHandler) RefreshToken(c *gin.Context) {
	// 从Authorization header获取当前refresh token
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		response.BadRequest(c, "缺少Authorization header")
		return
	}

	tokenParts := strings.SplitN(authHeader, " ", 2)
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		response.BadRequest(c, "无效的Token格式")
		return
	}

	refreshToken := tokenParts[1]
	newAccessToken, newRefreshToken, err := h.jwtService.RefreshToken(c.Request.Context(), refreshToken)
	if err != nil {
		h.logger.WithError(err).Warn("Failed to refresh JWT token")
		response.Unauthorized(c, "Token刷新失败")
		return
	}

	response.SuccessWithMessage(c, "Token刷新成功", gin.H{
		"access_token":  newAccessToken,
		"refresh_token": newRefreshToken,
	})
}
