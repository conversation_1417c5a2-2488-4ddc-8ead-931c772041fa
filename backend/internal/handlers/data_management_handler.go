package handlers

import (
	"crf-backend/internal/response"
	"crf-backend/internal/services"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// DataManagementHandler 数据管理处理器
type DataManagementHandler struct {
	dataAnalysisService     *services.DataAnalysisService
	dataExportService       *services.DataExportService
	versionAwareDataService *services.VersionAwareDataService
	logger                  *logrus.Logger
}

func NewDataManagementHandler(
	dataAnalysisService *services.DataAnalysisService,
	dataExportService *services.DataExportService,
	versionAwareDataService *services.VersionAwareDataService,
	logger *logrus.Logger,
) *DataManagementHandler {
	return &DataManagementHandler{
		dataAnalysisService:     dataAnalysisService,
		dataExportService:       dataExportService,
		versionAwareDataService: versionAwareDataService,
		logger:                  logger,
	}
}

// GetDataSummary 获取数据摘要
func (h *DataManagementHandler) GetDataSummary(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 解析过滤器
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if subjectID := c.Query("subject_id"); subjectID != "" {
		filters["subject_id"] = subjectID
	}
	if visitID := c.Query("visit_id"); visitID != "" {
		filters["visit_id"] = visitID
	}
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		filters["date_from"] = dateFrom
	}
	if dateTo := c.Query("date_to"); dateTo != "" {
		filters["date_to"] = dateTo
	}

	summary, err := h.dataAnalysisService.GetInstanceDataSummary(templateID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get data summary")
		response.InternalError(c, "获取数据摘要失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"count":       len(summary),
	}).Info("Data summary retrieved successfully")

	response.Success(c, gin.H{
		"summary": summary,
		"count":   len(summary),
	})
}

// GetDataStatistics 获取数据统计信息
func (h *DataManagementHandler) GetDataStatistics(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	statistics, err := h.dataAnalysisService.GetDataStatistics(templateID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get data statistics")
		response.InternalError(c, "获取数据统计失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":     templateID,
		"total_instances": statistics.TotalInstances,
	}).Info("Data statistics retrieved successfully")

	response.Success(c, gin.H{
		"statistics": statistics,
	})
}

// GetUnifiedData 获取统一数据（支持版本演化）
func (h *DataManagementHandler) GetUnifiedData(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 解析过滤器
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if subjectID := c.Query("subject_id"); subjectID != "" {
		filters["subject_id"] = subjectID
	}
	if visitID := c.Query("visit_id"); visitID != "" {
		filters["visit_id"] = visitID
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 1000 {
		limit = 50
	}

	unifiedData, err := h.versionAwareDataService.GetUnifiedData(templateID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get unified data")
		response.InternalError(c, "获取统一数据失败")
		return
	}

	// 分页处理
	total := len(unifiedData)
	offset := (page - 1) * limit
	end := offset + limit
	if end > total {
		end = total
	}

	var pagedData []services.UnifiedDataRow
	if offset < total {
		pagedData = unifiedData[offset:end]
	} else {
		pagedData = []services.UnifiedDataRow{}
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"total":       total,
		"page":        page,
		"limit":       limit,
	}).Info("Unified data retrieved successfully")

	response.Success(c, gin.H{
		"data": pagedData,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetVersionedFieldSchema 获取版本化字段结构
func (h *DataManagementHandler) GetVersionedFieldSchema(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	schema, err := h.versionAwareDataService.GetVersionedFieldSchema(templateID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get versioned field schema")
		response.InternalError(c, "获取版本化字段结构失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"field_count": len(schema),
	}).Info("Versioned field schema retrieved successfully")

	response.Success(c, gin.H{
		"schema": schema,
		"count":  len(schema),
	})
}

// GetVersionCompatibilityReport 获取版本兼容性报告
func (h *DataManagementHandler) GetVersionCompatibilityReport(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	report, err := h.versionAwareDataService.GenerateVersionCompatibilityReport(templateID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate version compatibility report")
		response.InternalError(c, "生成版本兼容性报告失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":   templateID,
		"version_range": report.VersionRange,
		"total_fields":  report.TotalFields,
		"common_fields": report.CommonFields,
	}).Info("Version compatibility report generated successfully")

	response.Success(c, gin.H{
		"report": report,
	})
}

// AnalyzeFields 分析字段数据
func (h *DataManagementHandler) AnalyzeFields(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 解析要分析的字段
	fieldsQuery := c.Query("fields")
	if fieldsQuery == "" {
		response.BadRequest(c, "请指定要分析的字段")
		return
	}

	fieldPaths := strings.Split(fieldsQuery, ",")
	for i, field := range fieldPaths {
		fieldPaths[i] = strings.TrimSpace(field)
	}

	analysis, err := h.dataAnalysisService.AnalyzeFields(templateID, fieldPaths)
	if err != nil {
		h.logger.WithError(err).Error("Failed to analyze fields")
		response.InternalError(c, "字段分析失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":    templateID,
		"field_count":    len(fieldPaths),
		"analysis_count": len(analysis),
	}).Info("Field analysis completed successfully")

	response.Success(c, gin.H{
		"analysis": analysis,
	})
}

// ExportData 导出数据
func (h *DataManagementHandler) ExportData(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 解析导出选项
	var options services.ExportOptions
	if err := c.ShouldBindJSON(&options); err != nil {
		response.BadRequest(c, "导出选项格式错误")
		return
	}

	// 设置默认格式
	if options.Format == "" {
		options.Format = services.ExportFormatCSV
	}

	result, err := h.dataExportService.ExportData(templateID, options)
	if err != nil {
		h.logger.WithError(err).Error("Failed to export data")
		response.InternalError(c, "数据导出失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":  templateID,
		"format":       options.Format,
		"record_count": result.RecordCount,
		"file_size":    result.Size,
	}).Info("Data exported successfully")

	// 设置响应头
	c.Header("Content-Type", result.ContentType)
	c.Header("Content-Disposition", "attachment; filename=\""+result.Filename+"\"")
	c.Header("Content-Length", strconv.FormatInt(result.Size, 10))

	c.Data(200, result.ContentType, result.Data)
}

// GetExportPreview 获取导出预览
func (h *DataManagementHandler) GetExportPreview(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 解析导出选项
	var options services.ExportOptions
	if err := c.ShouldBindJSON(&options); err != nil {
		response.BadRequest(c, "导出选项格式错误")
		return
	}

	preview, err := h.dataExportService.GetExportPreview(templateID, options)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get export preview")
		response.InternalError(c, "获取导出预览失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":   templateID,
		"field_count":   preview["field_count"],
		"total_records": preview["total_records"],
	}).Info("Export preview generated successfully")

	response.Success(c, gin.H{
		"preview": preview,
	})
}
