package handlers

import (
	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type HistoryHandler struct {
	historyService *services.HistoryService
	logger         *logrus.Logger
}

func NewHistoryHandler(historyService *services.HistoryService, logger *logrus.Logger) *HistoryHandler {
	return &HistoryHandler{
		historyService: historyService,
		logger:         logger,
	}
}

// CreateHistoryEntry 创建历史记录条目
func (h *HistoryHandler) CreateHistoryEntry(c *gin.Context) {
	var req struct {
		ResourceType string                 `json:"resource_type" binding:"required"`
		ResourceID   string                 `json:"resource_id" binding:"required"`
		Action       string                 `json:"action" binding:"required"`
		Description  string                 `json:"description"`
		Data         map[string]interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind create history entry request")
		response.ValidationError(c, err)
		return
	}

	// 解析资源ID
	resourceID, err := uuid.Parse(req.ResourceID)
	if err != nil {
		response.BadRequest(c, "无效的资源ID")
		return
	}

	// 获取用户ID（可选）
	userID := h.getUserFromContextOptional(c)

	// 如果没有用户ID，使用统一的匿名用户ID
	var finalUserID uuid.UUID
	if userID != nil {
		finalUserID = *userID
	} else {
		// 使用统一的匿名用户ID（与匿名用户服务保持一致）
		finalUserID = uuid.MustParse("00000000-0000-0000-0000-000000000001")
	}

	// 创建历史记录条目
	entry := &models.OperationHistory{
		ResourceType: req.ResourceType,
		ResourceID:   resourceID,
		Action:       req.Action,
		Description:  req.Description,
		UserID:       finalUserID,
	}

	if err := h.historyService.CreateHistoryEntry(entry); err != nil {
		h.logger.WithError(err).Error("Failed to create history entry")
		response.InternalError(c, "创建历史记录失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"resource_type": req.ResourceType,
		"resource_id":   req.ResourceID,
		"action":        req.Action,
		"user_id":       userID,
	}).Info("History entry created successfully")

	response.Created(c, "历史记录创建成功", gin.H{
		"entry": entry,
	})
}

// GetHistory 获取资源的历史记录
func (h *HistoryHandler) GetHistory(c *gin.Context) {
	resourceType := c.Param("resource_type")
	resourceIDStr := c.Param("resource_id")

	// 解析资源ID
	resourceID, err := uuid.Parse(resourceIDStr)
	if err != nil {
		response.BadRequest(c, "无效的资源ID")
		return
	}

	// 解析分页参数
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// 获取历史记录
	history, total, err := h.historyService.GetHistory(resourceType, resourceID, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get history")
		response.InternalError(c, "获取历史记录失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"resource_type": resourceType,
		"resource_id":   resourceID,
		"count":         len(history),
		"total":         total,
	}).Info("History retrieved successfully")

	response.Success(c, gin.H{
		"history": history,
		"pagination": gin.H{
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

// GetUserHistory 获取用户的历史记录
func (h *HistoryHandler) GetUserHistory(c *gin.Context) {
	// 获取用户ID
	userID := h.getUserFromContextOptional(c)
	if userID == nil {
		response.Unauthorized(c, "需要登录才能查看历史记录")
		return
	}

	// 解析分页参数
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// 获取用户历史记录
	history, total, err := h.historyService.GetUserHistory(*userID, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user history")
		response.InternalError(c, "获取用户历史记录失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id": *userID,
		"count":   len(history),
		"total":   total,
	}).Info("User history retrieved successfully")

	response.Success(c, gin.H{
		"history": history,
		"pagination": gin.H{
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

// 辅助函数：安全地从gin.Context获取用户ID（可选）
func (h *HistoryHandler) getUserFromContextOptional(c *gin.Context) *uuid.UUID {
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		return nil
	}

	userIDStr, ok := userIDInterface.(string)
	if !ok {
		return nil
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil
	}

	return &userID
}
