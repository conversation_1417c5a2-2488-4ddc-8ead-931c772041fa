package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"
)

// MedicalHandler 医疗数据API处理器
type MedicalHandler struct {
	medicalService *services.MedicalService
	logger         *logrus.Logger
}

// NewMedicalHandler 创建医疗处理器实例
func NewMedicalHandler(medicalService *services.MedicalService, logger *logrus.Logger) *MedicalHandler {
	return &MedicalHandler{
		medicalService: medicalService,
		logger:         logger,
	}
}

// GetMedicalCodes 获取医疗编码列表
// @Summary 获取医疗编码列表
// @Description 支持ICD-10/11, SNOMED CT, LOINC等编码系统
// @Tags Medical
// @Accept json
// @Produce json
// @Param code_system query string false "编码系统"
// @Param search query string false "搜索关键词"
// @Param language query string false "语言" default(zh-CN)
// @Param parent_code query string false "父级编码"
// @Param level query int false "层级"
// @Param limit query int false "每页数量" default(50)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} response.Response{data=object{codes=[]models.MedicalCode,pagination=object}}
// @Router /api/medical/codes [get]
func (h *MedicalHandler) GetMedicalCodes(c *gin.Context) {
	var req models.MedicalCodeSearchRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind medical codes query parameters")
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Limit <= 0 {
		req.Limit = 50
	}
	if req.Language == "" {
		req.Language = "zh-CN"
	}

	codes, total, err := h.medicalService.GetMedicalCodes(req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get medical codes")
		response.InternalError(c, "获取医疗编码失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"code_system": req.CodeSystem,
		"search":      req.Search,
		"total":       total,
		"limit":       req.Limit,
		"offset":      req.Offset,
	}).Info("Medical codes retrieved successfully")

	response.Success(c, gin.H{
		"codes": codes,
		"pagination": gin.H{
			"total":  total,
			"limit":  req.Limit,
			"offset": req.Offset,
		},
	})
}

// GetMedicalCodeByCode 根据编码获取医疗编码详情
// @Summary 获取医疗编码详情
// @Description 根据编码系统和编码获取详细信息
// @Tags Medical
// @Accept json
// @Produce json
// @Param code_system path string true "编码系统"
// @Param code path string true "编码"
// @Param language query string false "语言" default(zh-CN)
// @Success 200 {object} response.Response{data=models.MedicalCode}
// @Router /api/medical/codes/{code_system}/{code} [get]
func (h *MedicalHandler) GetMedicalCodeByCode(c *gin.Context) {
	codeSystem := c.Param("code_system")
	code := c.Param("code")
	language := c.DefaultQuery("language", "zh-CN")

	if codeSystem == "" || code == "" {
		response.BadRequest(c, "编码系统和编码不能为空")
		return
	}

	medicalCode, err := h.medicalService.GetMedicalCodeByCode(codeSystem, code, language)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get medical code by code")
		response.NotFound(c, "医疗编码不存在")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"code_system": codeSystem,
		"code":        code,
		"language":    language,
	}).Info("Medical code retrieved successfully")

	response.Success(c, gin.H{
		"code": medicalCode,
	})
}

// GetMedicalUnits 获取医疗单位列表
// @Summary 获取医疗单位列表
// @Description 获取支持的医疗单位，支持单位转换
// @Tags Medical
// @Accept json
// @Produce json
// @Param category query string false "单位分类"
// @Param search query string false "搜索关键词"
// @Param limit query int false "每页数量" default(100)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} response.Response{data=object{units=[]models.MedicalUnit,pagination=object}}
// @Router /api/medical/units [get]
func (h *MedicalHandler) GetMedicalUnits(c *gin.Context) {
	var req models.MedicalUnitSearchRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind medical units query parameters")
		response.BadRequest(c, "请求参数错误")
		return
	}

	if req.Limit <= 0 {
		req.Limit = 100
	}

	units, total, err := h.medicalService.GetMedicalUnits(req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get medical units")
		response.InternalError(c, "获取医疗单位失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"category": req.Category,
		"search":   req.Search,
		"total":    total,
	}).Info("Medical units retrieved successfully")

	response.Success(c, gin.H{
		"units": units,
		"pagination": gin.H{
			"total":  total,
			"limit":  req.Limit,
			"offset": req.Offset,
		},
	})
}

// ConvertUnit 单位转换
// @Summary 医疗单位转换
// @Description 在不同医疗单位之间进行数值转换
// @Tags Medical
// @Accept json
// @Produce json
// @Param request body models.UnitConversionRequest true "转换请求"
// @Success 200 {object} response.Response{data=models.UnitConversionResponse}
// @Router /api/medical/convert-unit [post]
func (h *MedicalHandler) ConvertUnit(c *gin.Context) {
	var req models.UnitConversionRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind unit conversion request")
		response.BadRequest(c, "请求参数错误")
		return
	}

	result, err := h.medicalService.ConvertUnit(req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to convert unit")
		response.BadRequest(c, err.Error())
		return
	}

	h.logger.WithFields(logrus.Fields{
		"from_unit":       req.FromUnit,
		"to_unit":         req.ToUnit,
		"original_value":  req.Value,
		"converted_value": result.ConvertedValue,
	}).Info("Unit conversion completed successfully")

	response.Success(c, gin.H{
		"conversion": result,
	})
}

// GetReferenceRange 获取参考值范围
// @Summary 获取医疗参考值范围
// @Description 根据检查项目、年龄、性别获取正常值范围
// @Tags Medical
// @Accept json
// @Produce json
// @Param test_code path string true "检查项目编码"
// @Param age query int false "年龄（月）"
// @Param gender query string false "性别" Enums(male,female,all)
// @Param population query string false "人群" Enums(adult,pediatric,elderly,pregnant)
// @Success 200 {object} response.Response{data=object{reference_range=models.MedicalReferenceRange}}
// @Router /api/medical/reference-ranges/{test_code} [get]
func (h *MedicalHandler) GetReferenceRange(c *gin.Context) {
	testCode := c.Param("test_code")
	if testCode == "" {
		response.BadRequest(c, "检查项目编码不能为空")
		return
	}

	var req models.MedicalReferenceRangeSearchRequest
	req.TestCode = testCode

	if ageStr := c.Query("age"); ageStr != "" {
		if age, err := strconv.Atoi(ageStr); err == nil {
			req.Age = &age
		}
	}

	req.Gender = c.Query("gender")
	req.Population = c.Query("population")

	refRange, err := h.medicalService.GetReferenceRange(req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reference range")
		response.NotFound(c, "未找到参考值范围")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"test_code":  testCode,
		"age":        req.Age,
		"gender":     req.Gender,
		"population": req.Population,
	}).Info("Reference range retrieved successfully")

	response.Success(c, gin.H{
		"reference_range": refRange,
	})
}

// AssessValue 评估数值
// @Summary 评估医疗数值
// @Description 根据参考值范围评估数值是否异常
// @Tags Medical
// @Accept json
// @Produce json
// @Param test_code path string true "检查项目编码"
// @Param value query number true "数值"
// @Param unit query string true "单位"
// @Param age query int false "年龄（月）"
// @Param gender query string false "性别"
// @Success 200 {object} response.Response{data=object{assessment=models.ValueAssessmentResult}}
// @Router /api/medical/assess-value/{test_code} [get]
func (h *MedicalHandler) AssessValue(c *gin.Context) {
	testCode := c.Param("test_code")
	if testCode == "" {
		response.BadRequest(c, "检查项目编码不能为空")
		return
	}

	valueStr := c.Query("value")
	if valueStr == "" {
		response.BadRequest(c, "数值不能为空")
		return
	}

	value, err := strconv.ParseFloat(valueStr, 64)
	if err != nil {
		response.BadRequest(c, "数值格式错误")
		return
	}

	unit := c.Query("unit")
	if unit == "" {
		response.BadRequest(c, "单位不能为空")
		return
	}

	var age *int
	if ageStr := c.Query("age"); ageStr != "" {
		if ageVal, err := strconv.Atoi(ageStr); err == nil {
			age = &ageVal
		}
	}

	gender := c.Query("gender")

	assessment, err := h.medicalService.AssessValue(testCode, value, unit, age, gender)
	if err != nil {
		h.logger.WithError(err).Error("Failed to assess value")
		response.InternalError(c, "数值评估失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"test_code": testCode,
		"value":     value,
		"unit":      unit,
		"level":     assessment.Level,
	}).Info("Value assessment completed successfully")

	response.Success(c, gin.H{
		"assessment": assessment,
	})
}

// GetMedicalDrugs 获取药物列表
// @Summary 获取药物列表
// @Description 获取药物数据库中的药物信息
// @Tags Medical
// @Accept json
// @Produce json
// @Param search query string false "搜索关键词"
// @Param drug_class query string false "药物分类"
// @Param atc_code query string false "ATC编码"
// @Param is_prescription query bool false "是否处方药"
// @Param limit query int false "每页数量" default(50)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} response.Response{data=object{drugs=[]models.MedicalDrug,pagination=object}}
// @Router /api/medical/drugs [get]
func (h *MedicalHandler) GetMedicalDrugs(c *gin.Context) {
	var req models.MedicalDrugSearchRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind medical drugs query parameters")
		response.BadRequest(c, "请求参数错误")
		return
	}

	if req.Limit <= 0 {
		req.Limit = 50
	}

	drugs, total, err := h.medicalService.GetMedicalDrugs(req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get medical drugs")
		response.InternalError(c, "获取药物信息失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"search":          req.Search,
		"drug_class":      req.DrugClass,
		"atc_code":        req.ATCCode,
		"is_prescription": req.IsPrescription,
		"total":           total,
	}).Info("Medical drugs retrieved successfully")

	response.Success(c, gin.H{
		"drugs": drugs,
		"pagination": gin.H{
			"total":  total,
			"limit":  req.Limit,
			"offset": req.Offset,
		},
	})
}

// GetMedicalDrugByCode 根据药物编码获取药物详情
// @Summary 获取药物详情
// @Description 根据药物编码获取详细信息
// @Tags Medical
// @Accept json
// @Produce json
// @Param drug_code path string true "药物编码"
// @Success 200 {object} response.Response{data=object{drug=models.MedicalDrug}}
// @Router /api/medical/drugs/{drug_code} [get]
func (h *MedicalHandler) GetMedicalDrugByCode(c *gin.Context) {
	drugCode := c.Param("drug_code")
	if drugCode == "" {
		response.BadRequest(c, "药物编码不能为空")
		return
	}

	drug, err := h.medicalService.GetMedicalDrugByCode(drugCode)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get medical drug by code")
		response.NotFound(c, "药物信息不存在")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"drug_code": drugCode,
	}).Info("Medical drug retrieved successfully")

	response.Success(c, gin.H{
		"drug": drug,
	})
}

// SearchMedicalDrugsByName 根据药物名称搜索
// @Summary 根据药物名称搜索
// @Description 根据通用名或商品名搜索药物
// @Tags Medical
// @Accept json
// @Produce json
// @Param name query string true "药物名称"
// @Param limit query int false "返回数量" default(20)
// @Success 200 {object} response.Response{data=object{drugs=[]models.MedicalDrug}}
// @Router /api/medical/drugs/search [get]
func (h *MedicalHandler) SearchMedicalDrugsByName(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		response.BadRequest(c, "药物名称不能为空")
		return
	}

	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	drugs, err := h.medicalService.SearchMedicalDrugsByName(name, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search medical drugs by name")
		response.InternalError(c, "搜索药物失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"name":  name,
		"limit": limit,
		"count": len(drugs),
	}).Info("Medical drugs searched successfully")

	response.Success(c, gin.H{
		"drugs": drugs,
	})
}

// ValidateMedicalData 验证医疗数据
// @Summary 验证医疗数据
// @Description 根据医疗数据验证规则验证输入数据
// @Tags Medical
// @Accept json
// @Produce json
// @Param request body object{field_type=string,value=interface{},rules=models.MedicalValidationRule} true "验证请求"
// @Success 200 {object} response.Response{data=object{valid=bool,errors=[]string}}
// @Router /api/medical/validate [post]
func (h *MedicalHandler) ValidateMedicalData(c *gin.Context) {
	var req struct {
		FieldType string                       `json:"field_type" binding:"required"`
		Value     interface{}                  `json:"value"`
		Rules     models.MedicalValidationRule `json:"rules" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind medical data validation request")
		response.BadRequest(c, "请求参数错误")
		return
	}

	err := h.medicalService.ValidateMedicalData(req.FieldType, req.Value, req.Rules)

	var errors []string
	valid := true

	if err != nil {
		valid = false
		errors = append(errors, err.Error())
	}

	h.logger.WithFields(logrus.Fields{
		"field_type": req.FieldType,
		"valid":      valid,
	}).Info("Medical data validation completed")

	response.Success(c, gin.H{
		"valid":  valid,
		"errors": errors,
	})
}
