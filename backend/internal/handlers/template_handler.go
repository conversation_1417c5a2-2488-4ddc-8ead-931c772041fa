package handlers

import (
	"crf-backend/internal/config"
	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/datatypes"
)

type TemplateHandler struct {
	templateService *services.TemplateService
	projectService  *services.ProjectService
	logger          *logrus.Logger
	config          *config.Config
}

func NewTemplateHandler(templateService *services.TemplateService, projectService *services.ProjectService, logger *logrus.Logger, config *config.Config) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
		projectService:  projectService,
		logger:          logger,
		config:          config,
	}
}

// 辅助函数：安全地从gin.Context获取用户ID
func (h *TemplateHandler) getUserFromContext(c *gin.Context) (uuid.UUID, error) {
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return uuid.Nil, http.ErrNoCookie
	}

	userIDStr, ok := userIDInterface.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user context"})
		return uuid.Nil, http.ErrNoCookie
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return uuid.Nil, err
	}

	return userID, nil
}

type CreateTemplateRequest struct {
	ProjectID    string          `json:"project_id"`
	Name         string          `json:"name" binding:"required"`
	Title        string          `json:"title" binding:"required"`
	Description  string          `json:"description"`
	Keyword      string          `json:"keyword"`
	Version      string          `json:"version"`
	TemplateType string          `json:"template_type"`                    // 新增：模板类型
	SourceType   string          `json:"source_type"`                      // 新增：模板来源
	IsPublic     bool            `json:"is_public"`                        // 新增：是否公共模板
	Tags         string          `json:"tags"`                             // 新增：标签
	Icon         string          `json:"icon"`                             // 新增：图标
	IconColor    string          `json:"iconColor"`                        // 新增：图标颜色
	TemplateData json.RawMessage `json:"template_data" binding:"required"` // 统一的模板数据
	Permissions  json.RawMessage `json:"permissions"`
}

func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind create template request")
		response.ValidationError(c, err)
		return
	}

	// Get current user
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":       userID,
		"template_name": req.Name,
		"project_id":    req.ProjectID,
	}).Info("Creating new template")

	// 处理项目ID - 支持默认项目
	var projectID uuid.UUID
	if req.ProjectID == "" || req.ProjectID == "default" {
		// 获取或创建默认项目
		project, err := h.projectService.GetOrCreateDefaultProject(userID)
		if err != nil {
			h.logger.WithError(err).Error("Failed to get or create default project")
			response.InternalError(c, "获取默认项目失败")
			return
		}
		projectID = project.ID
	} else {
		// 解析项目ID
		parsedID, err := uuid.Parse(req.ProjectID)
		if err != nil {
			h.logger.WithError(err).WithField("project_id", req.ProjectID).Error("Invalid project ID format")
			response.BadRequest(c, "无效的项目ID格式")
			return
		}
		projectID = parsedID
	}

	// 设置默认值
	templateType := req.TemplateType
	if templateType == "" {
		templateType = "custom_form"
	}

	sourceType := req.SourceType
	if sourceType == "" {
		sourceType = "user_created"
	}

	version := req.Version
	if version == "" {
		version = "1.0.0"
	}

	// 设置图标默认值
	icon := req.Icon
	if icon == "" {
		icon = "Document"
	}

	iconColor := req.IconColor
	if iconColor == "" {
		iconColor = "#3b82f6"
	}

	// 验证模板数据
	if len(req.TemplateData) == 0 {
		req.TemplateData = json.RawMessage("{}")
	}

	// 验证权限数据
	if len(req.Permissions) == 0 {
		req.Permissions = json.RawMessage("{}")
	}

	// 创建模板对象
	template := &models.CRFTemplate{
		ProjectID:    projectID,
		Name:         req.Name,
		Title:        req.Title,
		Description:  req.Description,
		Keyword:      req.Keyword,
		Version:      version,
		TemplateType: templateType,
		SourceType:   sourceType,
		IsPublic:     req.IsPublic,
		Tags:         req.Tags,
		Icon:         icon,
		IconColor:    iconColor,
		TemplateData: datatypes.JSON(req.TemplateData),
		Permissions:  datatypes.JSON(req.Permissions),
		CreatedBy:    userID,
		Status:       "draft",
		UsageCount:   0,
	}

	// 保存模板到数据库
	if err := h.templateService.CreateTemplate(template); err != nil {
		h.logger.WithError(err).WithField("template_name", req.Name).Error("Failed to create template")
		response.InternalError(c, "创建模板失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":   template.ID,
		"template_name": template.Name,
		"user_id":       userID,
	}).Info("Template created successfully")

	// 返回成功响应
	response.Created(c, "模板创建成功", gin.H{
		"template": gin.H{
			"id":            template.ID,
			"name":          template.Name,
			"title":         template.Title,
			"description":   template.Description,
			"version":       template.Version,
			"template_type": template.TemplateType,
			"source_type":   template.SourceType,
			"icon":          template.Icon,
			"iconColor":     template.IconColor,
			"status":        template.Status,
			"created_at":    template.CreatedAt,
			"updated_at":    template.UpdatedAt,
		},
	})
}

func (h *TemplateHandler) GetTemplates(c *gin.Context) {
	// Parse query parameters
	projectIDStr := c.Query("project_id")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	var projectID uuid.UUID
	if projectIDStr != "" {
		var err error
		projectID, err = uuid.Parse(projectIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
			return
		}
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	templates, total, err := h.templateService.GetTemplates(projectID, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get templates"})
		return
	}

	// 处理模板数据，确保iconColor字段有默认值
	processedTemplates := make([]gin.H, len(templates))
	for i, template := range templates {
		iconColor := template.IconColor
		if iconColor == "" {
			iconColor = "#3b82f6" // 默认颜色
		}
		
		processedTemplates[i] = gin.H{
			"id":            template.ID,
			"project_id":    template.ProjectID,
			"name":          template.Name,
			"title":         template.Title,
			"description":   template.Description,
			"keyword":       template.Keyword,
			"version":       template.Version,
			"status":        template.Status,
			"icon":          template.Icon,
			"iconColor":     iconColor,
			"template_type": template.TemplateType,
			"source_type":   template.SourceType,
			"is_public":     template.IsPublic,
			"template_data": template.TemplateData,
			"permissions":   template.Permissions,
			"tags":          template.Tags,
			"usage_count":   template.UsageCount,
			"published_at":  template.PublishedAt,
			"published_by":  template.PublishedBy,
			"created_by":    template.CreatedBy,
			"updated_by":    template.UpdatedBy,
			"created_at":    template.CreatedAt,
			"updated_at":    template.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Templates retrieved successfully",
		"data": gin.H{
			"templates": processedTemplates,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	template, err := h.templateService.GetTemplateByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Template retrieved successfully",
		"data": gin.H{
			"template": template,
		},
	})
}

func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	// Get current user
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Add updated_by field
	updates["updated_by"] = userID

	if err := h.templateService.UpdateTemplate(id, updates); err != nil {
		h.logger.WithError(err).Error("Failed to update template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update template"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Template updated successfully",
	})
}

func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	if err := h.templateService.DeleteTemplate(id); err != nil {
		h.logger.WithError(err).Error("Failed to delete template")
		
		// 检查是否是因为存在实例数据而无法删除
		if err.Error() == "cannot delete template with existing instances" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "该表单包含填写数据，无法删除。请先清理相关数据后再尝试删除。",
				"code": "TEMPLATE_HAS_INSTANCES",
			})
			return
		}
		
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete template"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

func (h *TemplateHandler) PublishTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	// Get current user
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	version, err := h.templateService.PublishTemplate(id, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to publish template"})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": id,
		"version_id":  version.ID,
		"version":     version.Version,
		"user_id":     userID,
	}).Info("Template published successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Template published successfully",
		"version": gin.H{
			"id":           version.ID,
			"template_id":  version.TemplateID,
			"version":      version.Version,
			"title":        version.Title,
			"description":  version.Description,
			"change_log":   version.ChangeLog,
			"status":       version.Status,
			"published_at": version.PublishedAt,
			"published_by": version.PublishedBy,
			"created_by":   version.CreatedBy,
			"created_at":   version.CreatedAt,
		},
	})
}

func (h *TemplateHandler) GetTemplateVersions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	versions, err := h.templateService.GetTemplateVersions(id)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template versions")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get template versions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"versions": versions})
}

type CreateVersionRequest struct {
	Version     string `json:"version" binding:"required"`
	Description string `json:"description"`
	ChangeLog   string `json:"change_log"`
}

func (h *TemplateHandler) CreateTemplateVersion(c *gin.Context) {
	idStr := c.Param("id")
	templateID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req CreateVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	// Get current template to create snapshot
	template, err := h.templateService.GetTemplateByID(templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	version := &models.CRFVersion{
		TemplateID:   templateID,
		Version:      req.Version,
		Title:        template.Title,
		Description:  req.Description,
		SnapshotData: template.TemplateData,
		ChangeLog:    req.ChangeLog,
		Status:       "draft",
		CreatedBy:    userID,
	}

	if err := h.templateService.CreateTemplateVersion(version); err != nil {
		h.logger.WithError(err).Error("Failed to create template version")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template version"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Template version created successfully",
		"version": version,
	})
}

type SaveTemplateDraftRequest struct {
	TemplateData json.RawMessage `json:"template_data" binding:"required"`
}

func (h *TemplateHandler) SaveTemplateDraft(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// Get current user
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	var req SaveTemplateDraftRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind save template draft request")
		response.ValidationError(c, err)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": id,
		"user_id":     userID,
		"data_size":   len(req.TemplateData),
	}).Info("Saving template draft")

	// 解析JSON数据以验证格式
	var templateDataMap map[string]interface{}
	if err := json.Unmarshal(req.TemplateData, &templateDataMap); err != nil {
		h.logger.WithError(err).Error("Invalid template data JSON format")
		response.BadRequest(c, "模板数据格式无效")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":     id,
		"parsed_sections": templateDataMap["sections"] != nil,
		"parsed_config":   templateDataMap["pageConfig"] != nil,
	}).Debug("Template data parsed successfully")

	// 保存模板草稿
	if err := h.templateService.SaveTemplateDraft(id, templateDataMap, userID); err != nil {
		h.logger.WithError(err).Error("Failed to save template draft")
		if err.Error() == "template not found" {
			response.NotFound(c, "模板不存在")
		} else if err.Error() == "only template creator can save draft" {
			response.Forbidden(c, "只有模板创建者可以保存草稿")
		} else {
			response.InternalError(c, "保存草稿失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": id,
		"user_id":     userID,
	}).Info("Template draft saved successfully")

	response.SuccessWithMessage(c, "草稿保存成功", gin.H{
		"template_id": id,
		"saved_at":    time.Now(),
	})
}

// GetAutoSaveConfig 获取自动保存配置
func (h *TemplateHandler) GetAutoSaveConfig(c *gin.Context) {
	// 从配置文件获取自动保存配置
	autoSaveConfig := h.config.AutoSave

	config := gin.H{
		"enabled":          true,
		"interval_seconds": autoSaveConfig.Interval, // 从配置文件读取
		"debounce_ms":      2000,                    // 防抖时间，前端输入停止后等待时间
		"max_history":      10,                      // 最大历史记录数
		"cleanup_days":     7,                       // 自动清理天数
		"storage_type":     "backend",               // backend 或 localStorage
	}

	h.logger.WithFields(logrus.Fields{
		"interval":    config["interval_seconds"],
		"debounce":    config["debounce_ms"],
		"max_history": config["max_history"],
		"source":      "config_file",
	}).Info("返回自动保存配置")

	response.Success(c, gin.H{
		"auto_save_config": config,
	})
}

// GetPublicAccessLink 获取已发布模板的公共访问链接
func (h *TemplateHandler) GetPublicAccessLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 获取当前用户
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	// 获取模板信息验证权限
	template, err := h.templateService.GetTemplateByID(id)
	if err != nil {
		if err.Error() == "template not found" {
			response.NotFound(c, "模板不存在")
		} else {
			response.InternalError(c, "获取模板信息失败")
		}
		return
	}

	// 检查权限 - 只有创建者或管理员可以获取访问链接
	if template.CreatedBy != userID {
		response.Forbidden(c, "只有模板创建者可以获取访问链接")
		return
	}

	// 获取访问链接
	accessLink, err := h.templateService.GetPublicAccessLink(id)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get access link")
		if err.Error() == "template is not published" {
			response.BadRequest(c, "模板未发布，无法生成访问链接")
		} else {
			response.InternalError(c, "获取访问链接失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": id,
		"user_id":     userID,
		"access_link": accessLink,
	}).Info("Access link generated successfully")

	response.Success(c, gin.H{
		"access_link": accessLink,
		"full_url":    c.Request.Host + accessLink, // 完整URL
	})
}

// GetTemplateStats 获取模板统计信息
func (h *TemplateHandler) GetTemplateStats(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 获取当前用户
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	// 获取模板信息验证权限
	template, err := h.templateService.GetTemplateByID(id)
	if err != nil {
		if err.Error() == "template not found" {
			response.NotFound(c, "模板不存在")
		} else {
			response.InternalError(c, "获取模板信息失败")
		}
		return
	}

	// 检查权限 - 只有创建者或管理员可以查看统计信息
	if template.CreatedBy != userID {
		response.Forbidden(c, "只有模板创建者可以查看统计信息")
		return
	}

	// 获取统计信息
	stats, err := h.templateService.GetTemplateStats(id)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template stats")
		response.InternalError(c, "获取统计信息失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": id,
		"user_id":     userID,
	}).Info("Template stats retrieved successfully")

	response.Success(c, gin.H{
		"stats": stats,
	})
}

// RollbackToVersion 回滚模板到指定版本
func (h *TemplateHandler) RollbackToVersion(c *gin.Context) {
	templateIDStr := c.Param("id")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	versionIDStr := c.Param("version_id")
	versionID, err := uuid.Parse(versionIDStr)
	if err != nil {
		response.BadRequest(c, "无效的版本ID")
		return
	}

	// 获取当前用户
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	// 验证权限
	template, err := h.templateService.GetTemplateByID(templateID)
	if err != nil {
		if err.Error() == "template not found" {
			response.NotFound(c, "模板不存在")
		} else {
			response.InternalError(c, "获取模板信息失败")
		}
		return
	}

	// 检查权限 - 只有创建者可以回滚版本
	if template.CreatedBy != userID {
		response.Forbidden(c, "只有模板创建者可以回滚版本")
		return
	}

	// 执行回滚
	if err := h.templateService.RollbackToVersion(templateID, versionID, userID); err != nil {
		h.logger.WithError(err).Error("Failed to rollback template version")
		if err.Error() == "version not found" {
			response.NotFound(c, "版本不存在")
		} else if err.Error() == "version does not belong to template" {
			response.BadRequest(c, "版本不属于该模板")
		} else {
			response.InternalError(c, "回滚失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"version_id":  versionID,
		"user_id":     userID,
	}).Info("Template rolled back successfully")

	response.SuccessWithMessage(c, "模板回滚成功", nil)
}

// GetDeletedTemplates 获取已删除的模板列表
func (h *TemplateHandler) GetDeletedTemplates(c *gin.Context) {
	// Parse query parameters
	projectIDStr := c.Query("project_id")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	var projectID uuid.UUID
	if projectIDStr != "" {
		var err error
		projectID, err = uuid.Parse(projectIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
			return
		}
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	templates, total, err := h.templateService.GetDeletedTemplates(projectID, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get deleted templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deleted templates"})
		return
	}

	// 处理模板数据，确保iconColor字段有默认值
	processedTemplates := make([]gin.H, len(templates))
	for i, template := range templates {
		iconColor := template.IconColor
		if iconColor == "" {
			iconColor = "#3b82f6" // 默认颜色
		}
		
		processedTemplates[i] = gin.H{
			"id":            template.ID,
			"project_id":    template.ProjectID,
			"name":          template.Name,
			"title":         template.Title,
			"description":   template.Description,
			"keyword":       template.Keyword,
			"version":       template.Version,
			"status":        template.Status,
			"icon":          template.Icon,
			"iconColor":     iconColor,
			"template_type": template.TemplateType,
			"source_type":   template.SourceType,
			"is_public":     template.IsPublic,
			"template_data": template.TemplateData,
			"permissions":   template.Permissions,
			"tags":          template.Tags,
			"usage_count":   template.UsageCount,
			"published_at":  template.PublishedAt,
			"published_by":  template.PublishedBy,
			"created_by":    template.CreatedBy,
			"updated_by":    template.UpdatedBy,
			"created_at":    template.CreatedAt,
			"updated_at":    template.UpdatedAt,
			"deleted_at":    template.DeletedAt,
			"is_deleted":    template.IsDeleted,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Deleted templates retrieved successfully",
		"data": gin.H{
			"templates": processedTemplates,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// RestoreTemplate 恢复已删除的模板
func (h *TemplateHandler) RestoreTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	// 获取当前用户
	userID, err := h.getUserFromContext(c)
	if err != nil {
		return // 错误响应已在getUserFromContext中处理
	}

	// 恢复模板
	if err := h.templateService.RestoreTemplate(id); err != nil {
		h.logger.WithError(err).Error("Failed to restore template")
		
		if err.Error() == "deleted template not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "已删除的模板不存在",
				"code":  "DELETED_TEMPLATE_NOT_FOUND",
			})
			return
		}
		
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to restore template"})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": id,
		"user_id":     userID,
	}).Info("Template restored successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Template restored successfully",
	})
}
