package handlers

import (
	"strconv"
	"time"

	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type UserManagementHandler struct {
	userService *services.UserService
	rbacService *services.RBACService
	logger      *logrus.Logger
}

func NewUserManagementHandler(userService *services.UserService, rbacService *services.RBACService, logger *logrus.Logger) *UserManagementHandler {
	return &UserManagementHandler{
		userService: userService,
		rbacService: rbacService,
		logger:      logger,
	}
}

// GetUsersWithRoles 获取用户列表（包含角色信息）
func (h *UserManagementHandler) GetUsersWithRoles(c *gin.Context) {
	pageStr := c.<PERSON>faultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")
	search := c.Query("search")
	roleFilter := c.Query("role")

	page, _ := strconv.Atoi(pageStr)
	limit, _ := strconv.Atoi(limitStr)

	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 调用用户服务获取用户列表
	users, total, err := h.userService.GetUsersWithRoles(c.Request.Context(), limit, offset, search, roleFilter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get users with roles")
		response.InternalServerError(c, "获取用户列表失败")
		return
	}

	// 转换为API响应格式
	userList := make([]map[string]interface{}, len(users))
	for i, user := range users {
		userMap := map[string]interface{}{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"is_active":  user.IsActive,
			"created_at": user.CreatedAt,
			"updated_at": user.UpdatedAt,
		}

		// 添加角色信息
		roles := make([]map[string]interface{}, len(user.UserRoles))
		for j, userRole := range user.UserRoles {
			roles[j] = map[string]interface{}{
				"id":          userRole.Role.ID,
				"code":        userRole.Role.Code,
				"name":        userRole.Role.Name,
				"description": userRole.Role.Description,
				"project_id":  userRole.ProjectID,
			}
		}
		userMap["roles"] = roles

		userList[i] = userMap
	}

	response.Success(c, gin.H{
		"users": userList,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// GetUserDetail 获取用户详情（包含角色和权限）
func (h *UserManagementHandler) GetUserDetail(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 获取用户角色信息
	userRoles, err := h.rbacService.GetUserRoles(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user roles")
		response.InternalServerError(c, "获取用户角色失败")
		return
	}

	response.Success(c, userRoles)
}

// CreateUserWithRole 创建用户并分配角色
func (h *UserManagementHandler) CreateUserWithRole(c *gin.Context) {
	var req struct {
		Username    string   `json:"username" binding:"required"`
		Email       string   `json:"email" binding:"required,email"`
		Password    string   `json:"password" binding:"required,min=6"`
		FullName    string   `json:"full_name"`
		RoleIDs     []string `json:"role_ids"`
		ProjectID   *string  `json:"project_id"`
		IsActive    *bool    `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 获取当前用户ID
	currentUserIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	currentUserID, err := uuid.Parse(currentUserIDStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	// 检查用户名和邮箱是否已存在
	existingUser, _ := h.userService.GetUserByUsername(req.Username)
	if existingUser != nil {
		response.BadRequest(c, "用户名已存在")
		return
	}

	existingUser, _ = h.userService.GetUserByEmail(req.Email)
	if existingUser != nil {
		response.BadRequest(c, "邮箱已存在")
		return
	}

	// 创建用户对象
	user := &models.User{
		ID:           uuid.New(),
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: req.Password, // 在UserService中会被加密
		FullName:     req.FullName,
		IsActive:     req.IsActive != nil && *req.IsActive,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 转换角色ID
	roleIDs := make([]uuid.UUID, len(req.RoleIDs))
	for i, idStr := range req.RoleIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			response.BadRequest(c, "无效的角色ID")
			return
		}
		roleIDs[i] = id
	}

	// 获取项目ID
	var projectID *uuid.UUID
	if req.ProjectID != nil {
		pid, err := uuid.Parse(*req.ProjectID)
		if err != nil {
			response.BadRequest(c, "无效的项目ID")
			return
		}
		projectID = &pid
	}

	// 创建用户并分配角色
	createdUser, err := h.userService.CreateUserWithRoles(c.Request.Context(), user, roleIDs, projectID, currentUserID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"username":   req.Username,
			"email":      req.Email,
			"role_ids":   roleIDs,
			"created_by": currentUserID,
		}).Error("Failed to create user with roles")
		response.InternalServerError(c, "创建用户失败")
		return
	}

	// 返回创建的用户信息（不包含密码）
	response.Success(c, gin.H{
		"user": map[string]interface{}{
			"id":         createdUser.ID,
			"username":   createdUser.Username,
			"email":      createdUser.Email,
			"full_name":  createdUser.FullName,
			"is_active":  createdUser.IsActive,
			"created_at": createdUser.CreatedAt,
			"updated_at": createdUser.UpdatedAt,
		},
		"message": "用户创建成功",
	})
}

// UpdateUserRoles 更新用户角色
func (h *UserManagementHandler) UpdateUserRoles(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req struct {
		RoleIDs   []string `json:"role_ids" binding:"required"`
		ProjectID *string  `json:"project_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 获取当前用户ID
	currentUserIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	currentUserID, err := uuid.Parse(currentUserIDStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	// 转换角色ID
	roleIDs := make([]uuid.UUID, len(req.RoleIDs))
	for i, idStr := range req.RoleIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			response.BadRequest(c, "无效的角色ID")
			return
		}
		roleIDs[i] = id
	}

	// 获取项目ID
	var projectID *uuid.UUID
	if req.ProjectID != nil {
		pid, err := uuid.Parse(*req.ProjectID)
		if err != nil {
			response.BadRequest(c, "无效的项目ID")
			return
		}
		projectID = &pid
	}

	// 批量分配角色
	err = h.rbacService.BatchAssignRoles(c.Request.Context(), userID, roleIDs, projectID, currentUserID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":     userID,
			"role_ids":    roleIDs,
			"project_id":  projectID,
			"assigned_by": currentUserID,
		}).Error("Failed to batch assign roles")
		response.InternalServerError(c, "批量分配角色失败")
		return
	}

	response.Success(c, gin.H{"message": "用户角色更新成功"})
}

// ActivateUser 激活用户
func (h *UserManagementHandler) ActivateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 获取当前用户ID
	currentUserIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	currentUserID, err := uuid.Parse(currentUserIDStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	// 调用用户服务激活用户
	err = h.userService.ActivateUser(c.Request.Context(), userID, currentUserID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    userID,
			"updated_by": currentUserID,
		}).Error("Failed to activate user")
		response.InternalServerError(c, "激活用户失败")
		return
	}

	response.Success(c, gin.H{
		"message": "用户激活成功",
	})
}

// DeactivateUser 停用用户
func (h *UserManagementHandler) DeactivateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 获取当前用户ID
	currentUserIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	currentUserID, err := uuid.Parse(currentUserIDStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	// 调用用户服务停用用户
	err = h.userService.DeactivateUser(c.Request.Context(), userID, currentUserID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    userID,
			"updated_by": currentUserID,
		}).Error("Failed to deactivate user")
		response.InternalServerError(c, "停用用户失败")
		return
	}

	response.Success(c, gin.H{
		"message": "用户停用成功",
	})
}

// GetUserPermissions 获取用户权限列表
func (h *UserManagementHandler) GetUserPermissions(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	projectIDStr := c.Query("project_id")
	var projectID *uuid.UUID
	if projectIDStr != "" {
		pid, err := uuid.Parse(projectIDStr)
		if err != nil {
			response.BadRequest(c, "无效的项目ID")
			return
		}
		projectID = &pid
	}
	_ = projectID // 暂时不使用，待实现项目范围权限查询

	// 获取用户角色信息
	userRoles, err := h.rbacService.GetUserRoles(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user roles")
		response.InternalServerError(c, "获取用户角色失败")
		return
	}

	// 这里可以进一步获取用户的具体权限列表
	// 目前返回角色信息
	response.Success(c, userRoles)
}

// ResetUserPassword 重置用户密码
func (h *UserManagementHandler) ResetUserPassword(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req struct {
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 获取当前用户ID
	currentUserIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	currentUserID, err := uuid.Parse(currentUserIDStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	// 调用用户服务重置密码
	err = h.userService.ResetPassword(c.Request.Context(), userID, req.NewPassword, currentUserID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    userID,
			"updated_by": currentUserID,
		}).Error("Failed to reset password")
		response.InternalServerError(c, "重置密码失败")
		return
	}

	response.Success(c, gin.H{
		"message": "密码重置成功",
	})
}

// GetUserStats 获取用户统计信息
func (h *UserManagementHandler) GetUserStats(c *gin.Context) {
	// 调用用户服务获取统计信息
	stats, err := h.userService.GetUserStats(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user stats")
		response.InternalServerError(c, "获取用户统计信息失败")
		return
	}

	response.Success(c, stats)
}

// BulkUpdateUsers 批量更新用户
func (h *UserManagementHandler) BulkUpdateUsers(c *gin.Context) {
	var req struct {
		UserIDs []string `json:"user_ids" binding:"required"`
		Action  string   `json:"action" binding:"required"` // activate, deactivate, delete
		RoleIDs []string `json:"role_ids,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 获取当前用户ID
	currentUserIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	currentUserID, err := uuid.Parse(currentUserIDStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	// 转换用户ID
	userIDs := make([]uuid.UUID, len(req.UserIDs))
	for i, idStr := range req.UserIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			response.BadRequest(c, "无效的用户ID")
			return
		}
		userIDs[i] = id
	}

	// 根据动作执行批量操作
	switch req.Action {
	case "activate", "deactivate":
		// 批量激活/停用用户
		err = h.userService.BatchUpdateUsers(c.Request.Context(), userIDs, req.Action, currentUserID)
		if err != nil {
			h.logger.WithError(err).WithFields(logrus.Fields{
				"user_ids":   userIDs,
				"action":     req.Action,
				"updated_by": currentUserID,
			}).Error("Failed to batch update users")
			response.InternalServerError(c, "批量更新用户失败")
			return
		}
		response.Success(c, gin.H{"message": "批量更新用户成功"})
	case "assign_roles":
		// 批量分配角色
		if len(req.RoleIDs) == 0 {
			response.BadRequest(c, "请提供角色ID")
			return
		}
		
		// 转换角色ID
		roleIDs := make([]uuid.UUID, len(req.RoleIDs))
		for i, idStr := range req.RoleIDs {
			id, err := uuid.Parse(idStr)
			if err != nil {
				response.BadRequest(c, "无效的角色ID")
				return
			}
			roleIDs[i] = id
		}

		// 为每个用户分配角色
		for _, userID := range userIDs {
			err = h.rbacService.BatchAssignRoles(c.Request.Context(), userID, roleIDs, nil, currentUserID)
			if err != nil {
				h.logger.WithError(err).WithFields(logrus.Fields{
					"user_id":     userID,
					"role_ids":    roleIDs,
					"assigned_by": currentUserID,
				}).Error("Failed to batch assign roles")
				response.InternalServerError(c, "批量分配角色失败")
				return
			}
		}
		response.Success(c, gin.H{"message": "批量分配角色成功"})
	default:
		response.BadRequest(c, "无效的操作类型")
		return
	}
}

// ExportUsers 导出用户数据
func (h *UserManagementHandler) ExportUsers(c *gin.Context) {
	format := c.DefaultQuery("format", "csv")
	includeRoles := c.DefaultQuery("include_roles", "true")

	if format != "csv" && format != "xlsx" {
		response.BadRequest(c, "不支持的导出格式")
		return
	}

	// 导出用户数据逻辑待实现
	response.Success(c, gin.H{
		"message": "用户导出功能待实现",
		"format":  format,
		"include_roles": includeRoles,
	})
}

// ImportUsers 导入用户数据
func (h *UserManagementHandler) ImportUsers(c *gin.Context) {
	// 处理文件上传和用户导入逻辑
	response.Success(c, gin.H{
		"message": "用户导入功能待实现",
	})
}

// GetUserActivity 获取用户活动日志
func (h *UserManagementHandler) GetUserActivity(c *gin.Context) {
	userIDStr := c.Param("id")
	_, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")

	page, _ := strconv.Atoi(pageStr)
	limit, _ := strconv.Atoi(limitStr)

	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 获取用户活动日志逻辑待实现
	response.Success(c, gin.H{
		"activities": []interface{}{},
		"total":      0,
		"page":       page,
		"limit":      limit,
	})
}