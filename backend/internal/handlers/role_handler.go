package handlers

import (
	"strconv"

	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type RoleHandler struct {
	rbacService *services.RBACService
	logger      *logrus.Logger
}

func NewRoleHandler(rbacService *services.RBACService, logger *logrus.Logger) *RoleHandler {
	return &RoleHandler{
		rbacService: rbacService,
		logger:      logger,
	}
}

// GetRoles 获取角色列表
func (h *RoleHandler) GetRoles(c *gin.Context) {
	includePermissions := c.<PERSON>ult<PERSON>("include_permissions", "false")
	includePerm, _ := strconv.ParseBool(includePermissions)

	roles, err := h.rbacService.GetRoles(c.Request.Context(), includePerm)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get roles")
		response.InternalServerError(c, "获取角色列表失败")
		return
	}

	response.Success(c, roles)
}

// GetRole 获取角色详情
func (h *RoleHandler) GetRole(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	role, err := h.rbacService.GetRoleByID(c.Request.Context(), roleID)
	if err != nil {
		h.logger.WithError(err).WithField("role_id", roleID).Error("Failed to get role")
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		response.InternalServerError(c, "获取角色详情失败")
		return
	}

	response.Success(c, role)
}

// CreateRole 创建角色
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		DisplayName string `json:"display_name" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	role := &models.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsSystem:    false,
		IsActive:    true,
	}

	err := h.rbacService.CreateRole(c.Request.Context(), role)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create role")
		if err.Error() == "role name already exists" {
			response.BadRequest(c, "角色名称已存在")
			return
		}
		response.InternalServerError(c, "创建角色失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"role_id":   role.ID,
		"role_name": role.Name,
	}).Info("Role created successfully")

	response.Success(c, role)
}

// UpdateRole 更新角色
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	var req struct {
		DisplayName string `json:"display_name"`
		Description string `json:"description"`
		IsActive    *bool  `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	updates := make(map[string]interface{})
	if req.DisplayName != "" {
		updates["display_name"] = req.DisplayName
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	err = h.rbacService.UpdateRole(c.Request.Context(), roleID, updates)
	if err != nil {
		h.logger.WithError(err).WithField("role_id", roleID).Error("Failed to update role")
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		if err.Error() == "cannot modify system role name" || err.Error() == "cannot modify system role flag" {
			response.BadRequest(c, "无法修改系统角色")
			return
		}
		response.InternalServerError(c, "更新角色失败")
		return
	}

	response.Success(c, gin.H{"message": "角色更新成功"})
}

// DeleteRole 删除角色
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	err = h.rbacService.DeleteRole(c.Request.Context(), roleID)
	if err != nil {
		h.logger.WithError(err).WithField("role_id", roleID).Error("Failed to delete role")
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		if err.Error() == "cannot delete system role" {
			response.BadRequest(c, "无法删除系统角色")
			return
		}
		if err.Error() == "cannot delete role that is assigned to users" {
			response.BadRequest(c, "无法删除已分配给用户的角色")
			return
		}
		response.InternalServerError(c, "删除角色失败")
		return
	}

	response.Success(c, gin.H{"message": "角色删除成功"})
}

// GetPermissions 获取权限列表
func (h *RoleHandler) GetPermissions(c *gin.Context) {
	resource := c.Query("resource")

	var permissions []models.Permission
	var err error

	if resource != "" {
		permissions, err = h.rbacService.GetPermissionsByResource(c.Request.Context(), resource)
	} else {
		permissions, err = h.rbacService.GetPermissions(c.Request.Context())
	}

	if err != nil {
		h.logger.WithError(err).Error("Failed to get permissions")
		response.InternalServerError(c, "获取权限列表失败")
		return
	}

	response.Success(c, permissions)
}

// AssignPermissions 为角色分配权限
func (h *RoleHandler) AssignPermissions(c *gin.Context) {
	roleIDStr := c.Param("id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	var req struct {
		PermissionIDs []string `json:"permission_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 转换权限ID
	permissionIDs := make([]uuid.UUID, len(req.PermissionIDs))
	for i, idStr := range req.PermissionIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			response.BadRequest(c, "无效的权限ID")
			return
		}
		permissionIDs[i] = id
	}

	err = h.rbacService.AssignPermissionsToRole(c.Request.Context(), roleID, permissionIDs)
	if err != nil {
		h.logger.WithError(err).WithField("role_id", roleID).Error("Failed to assign permissions to role")
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		response.InternalServerError(c, "分配权限失败")
		return
	}

	response.Success(c, gin.H{"message": "权限分配成功"})
}

// GetUserRoles 获取用户角色
func (h *RoleHandler) GetUserRoles(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	userRoles, err := h.rbacService.GetUserRoles(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user roles")
		response.InternalServerError(c, "获取用户角色失败")
		return
	}

	response.Success(c, userRoles)
}

// AssignRole 为用户分配角色
func (h *RoleHandler) AssignRole(c *gin.Context) {
	var req models.RoleAssignmentRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 获取当前用户ID作为分配者
	assignedByStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未认证")
		return
	}

	assignedBy, err := uuid.Parse(assignedByStr.(string))
	if err != nil {
		response.Unauthorized(c, "无效的用户ID")
		return
	}

	err = h.rbacService.AssignRoleToUser(c.Request.Context(), &req, assignedBy)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":     req.UserID,
			"role_id":     req.RoleID,
			"assigned_by": assignedBy,
		}).Error("Failed to assign role to user")

		if err.Error() == "user not found" {
			response.NotFound(c, "用户不存在")
			return
		}
		if err.Error() == "role not found" {
			response.NotFound(c, "角色不存在")
			return
		}
		if err.Error() == "user already has this role" {
			response.BadRequest(c, "用户已拥有此角色")
			return
		}
		response.InternalServerError(c, "分配角色失败")
		return
	}

	response.Success(c, gin.H{"message": "角色分配成功"})
}

// RemoveRole 移除用户角色
func (h *RoleHandler) RemoveRole(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	roleIDStr := c.Param("role_id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	var projectID *uuid.UUID
	if projectIDStr := c.Query("project_id"); projectIDStr != "" {
		if pid, err := uuid.Parse(projectIDStr); err == nil {
			projectID = &pid
		}
	}

	err = h.rbacService.RemoveRoleFromUser(c.Request.Context(), userID, roleID, projectID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    userID,
			"role_id":    roleID,
			"project_id": projectID,
		}).Error("Failed to remove role from user")
		response.InternalServerError(c, "移除角色失败")
		return
	}

	response.Success(c, gin.H{"message": "角色移除成功"})
}

// CheckPermission 检查权限
func (h *RoleHandler) CheckPermission(c *gin.Context) {
	var req models.PermissionCheck

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	result, err := h.rbacService.CheckPermission(c.Request.Context(), req.UserID, req.Resource, req.Action, req.ProjectID)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":  req.UserID,
			"resource": req.Resource,
			"action":   req.Action,
		}).Error("Failed to check permission")
		response.InternalServerError(c, "权限检查失败")
		return
	}

	response.Success(c, result)
}

// GetRoleStats 获取角色统计
func (h *RoleHandler) GetRoleStats(c *gin.Context) {
	// 这里可以添加角色统计逻辑
	// 例如：每个角色的用户数量、权限数量等
	response.Success(c, gin.H{
		"message": "角色统计功能待实现",
	})
}