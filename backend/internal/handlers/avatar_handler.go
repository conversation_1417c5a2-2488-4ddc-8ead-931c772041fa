package handlers

import (
	"strings"

	"crf-backend/internal/response"
	"crf-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type AvatarHandler struct {
	userService  *services.UserService
	minioService *services.MinIOService
	logger       *logrus.Logger
}

func NewAvatarHandler(userService *services.UserService, minioService *services.MinIOService, logger *logrus.Logger) *AvatarHandler {
	return &AvatarHandler{
		userService:  userService,
		minioService: minioService,
		logger:       logger,
	}
}

// UploadAvatar 上传用户头像
func (h *AvatarHandler) UploadAvatar(c *gin.Context) {
	// 获取用户ID
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查用户是否存在
	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		h.logger.WithError(err).Error("获取用户信息失败")
		response.NotFound(c, "用户不存在")
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("avatar")
	if err != nil {
		response.BadRequest(c, "获取文件失败")
		return
	}
	defer file.Close()

	// 验证文件类型
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		// 从文件名推断
		fileName := header.Filename
		if strings.HasSuffix(strings.ToLower(fileName), ".jpg") || strings.HasSuffix(strings.ToLower(fileName), ".jpeg") {
			contentType = "image/jpeg"
		} else if strings.HasSuffix(strings.ToLower(fileName), ".png") {
			contentType = "image/png"
		} else if strings.HasSuffix(strings.ToLower(fileName), ".gif") {
			contentType = "image/gif"
		} else if strings.HasSuffix(strings.ToLower(fileName), ".webp") {
			contentType = "image/webp"
		} else {
			response.BadRequest(c, "不支持的文件类型")
			return
		}
	}

	if !h.minioService.ValidateImageType(contentType) {
		response.BadRequest(c, "不支持的图片格式，仅支持 JPG、PNG、GIF、WebP")
		return
	}

	// 验证文件大小
	if !h.minioService.ValidateFileSize(header.Size) {
		response.BadRequest(c, "文件大小超过限制（最大2MB）")
		return
	}

	// 如果用户已有头像，删除旧头像
	if user.AvatarURL != "" {
		if err := h.minioService.DeleteAvatar(user.AvatarURL); err != nil {
			h.logger.WithError(err).Warn("删除旧头像失败")
		}
	}

	// 上传新头像到MinIO
	avatarURL, err := h.minioService.UploadAvatar(userID.String(), header.Filename, file, contentType, header.Size)
	if err != nil {
		h.logger.WithError(err).Error("上传头像到MinIO失败")
		response.InternalError(c, "上传头像失败")
		return
	}

	// 更新用户头像URL
	updateData := map[string]interface{}{
		"avatar_url": avatarURL,
	}
	if err := h.userService.UpdateUser(user.ID, updateData); err != nil {
		h.logger.WithError(err).Error("更新用户头像URL失败")
		
		// 删除已上传的文件
		if deleteErr := h.minioService.DeleteAvatar(avatarURL); deleteErr != nil {
			h.logger.WithError(deleteErr).Error("删除已上传的头像文件失败")
		}
		
		response.InternalError(c, "更新用户信息失败")
		return
	}

	// 清理用户的旧头像文件（保留最新的1个）
	go func() {
		if err := h.minioService.CleanupOldAvatars(userID.String(), 1); err != nil {
			h.logger.WithError(err).Warn("清理旧头像失败")
		}
	}()

	h.logger.Infof("用户 %s 头像上传成功: %s", user.Username, avatarURL)

	response.SuccessWithMessage(c, "头像上传成功", map[string]interface{}{
		"avatar_url": avatarURL,
		"user": map[string]interface{}{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"avatar_url": avatarURL,
		},
	})
}

// DeleteAvatar 删除用户头像
func (h *AvatarHandler) DeleteAvatar(c *gin.Context) {
	// 获取用户ID
	userIDStr := c.Param("id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	// 检查用户是否存在
	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		h.logger.WithError(err).Error("获取用户信息失败")
		response.NotFound(c, "用户不存在")
		return
	}

	// 如果用户有头像，删除它
	if user.AvatarURL != "" {
		if err := h.minioService.DeleteAvatar(user.AvatarURL); err != nil {
			h.logger.WithError(err).Error("删除头像文件失败")
			response.InternalError(c, "删除头像失败")
			return
		}

		// 清空用户头像URL
		updateData := map[string]interface{}{
			"avatar_url": "",
		}
		if err := h.userService.UpdateUser(user.ID, updateData); err != nil {
			h.logger.WithError(err).Error("更新用户头像URL失败")
			response.InternalError(c, "更新用户信息失败")
			return
		}
	}

	h.logger.Infof("用户 %s 头像删除成功", user.Username)

	response.SuccessWithMessage(c, "头像删除成功", map[string]interface{}{
		"user": map[string]interface{}{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"full_name":  user.FullName,
			"avatar_url": "",
		},
	})
}