package handlers

import (
	"crf-backend/internal/response"
	"crf-backend/internal/services"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type InstanceHandler struct {
	instanceService *services.InstanceService
	logger          *logrus.Logger
}

func NewInstanceHandler(instanceService *services.InstanceService, logger *logrus.Logger) *InstanceHandler {
	return &InstanceHandler{
		instanceService: instanceService,
		logger:          logger,
	}
}

// 辅助函数：从gin.Context获取用户ID（必须登录）
func (h *InstanceHandler) getUserFromContext(c *gin.Context) (uuid.UUID, bool) {
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, false
	}

	userIDStr, ok := userIDInterface.(string)
	if !ok {
		return uuid.Nil, false
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return uuid.Nil, false
	}

	return userID, true
}

// GetTemplateForFill 获取模板信息用于填写（需要登录）
func (h *InstanceHandler) GetTemplateForFill(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能访问模板")
		return
	}

	template, err := h.instanceService.GetTemplateForFill(id, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template for fill")
		if err.Error() == "template not found or not published" {
			response.NotFound(c, "模板不存在或未发布")
		} else {
			response.InternalError(c, "获取模板信息失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id":   id,
		"template_name": template.Name,
		"user_id":       userID,
	}).Info("Template retrieved for filling")

	response.Success(c, gin.H{
		"template": gin.H{
			"id":            template.ID,
			"name":          template.Name,
			"title":         template.Title,
			"description":   template.Description,
			"version":       template.Version,
			"template_data": template.TemplateData,
		},
	})
}

// CreateInstance 创建表单实例（需要登录）
func (h *InstanceHandler) CreateInstance(c *gin.Context) {
	var req struct {
		TemplateID string `json:"template_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind create instance request")
		response.ValidationError(c, err)
		return
	}

	// 解析模板ID为UUID
	templateID, err := uuid.Parse(req.TemplateID)
	if err != nil {
		h.logger.WithError(err).Error("Invalid template ID format")
		response.BadRequest(c, "无效的模板ID格式")
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能创建实例")
		return
	}

	instance, err := h.instanceService.CreateInstance(templateID, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create instance")
		if err.Error() == "template not found or not published" {
			response.NotFound(c, "模板不存在或未发布")
		} else {
			response.InternalError(c, "创建实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": instance.ID,
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Instance created successfully")

	response.Created(c, "实例创建成功", gin.H{
		"instance": gin.H{
			"id":                    instance.ID,
			"template_id":           instance.TemplateID,
			"template_version":      instance.TemplateVersion,
			"status":                instance.Status,
			"completion_percentage": instance.CompletionPercentage,
			"created_at":            instance.CreatedAt,
		},
	})
}

// GetInstance 获取实例详情
func (h *InstanceHandler) GetInstance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的实例ID")
		return
	}

	instance, err := h.instanceService.GetInstanceByID(id)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get instance")
		if err.Error() == "instance not found" {
			response.NotFound(c, "实例不存在")
		} else {
			response.InternalError(c, "获取实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
	}).Info("Instance retrieved successfully")

	response.Success(c, gin.H{
		"instance": instance,
	})
}

// GetInstances 获取实例列表
func (h *InstanceHandler) GetInstances(c *gin.Context) {
	templateIDStr := c.Query("template_id")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	// 解析模板ID
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		response.BadRequest(c, "无效的模板ID")
		return
	}

	// 解析分页参数
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	instances, total, err := h.instanceService.GetInstancesByTemplate(templateID, limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get instances")
		response.InternalError(c, "获取实例列表失败")
		return
	}

	h.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"count":       len(instances),
		"total":       total,
	}).Info("Instances retrieved successfully")

	response.Success(c, gin.H{
		"instances": instances,
		"pagination": gin.H{
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

// UpdateInstance 更新实例数据（需要登录）
func (h *InstanceHandler) UpdateInstance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的实例ID")
		return
	}

	var req struct {
		FormData               interface{} `json:"form_data" binding:"required"`
		CompletionPercentage   *float64    `json:"completion_percentage,omitempty"`
		Status                 *string     `json:"status,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind update instance request")
		response.ValidationError(c, err)
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能更新实例")
		return
	}

	// 记录接收到的数据，便于调试
	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"form_data_type": fmt.Sprintf("%T", req.FormData),
		"has_completion_percentage": req.CompletionPercentage != nil,
		"has_status": req.Status != nil,
	}).Info("Received instance update request")

	if err := h.instanceService.UpdateInstanceData(id, req.FormData, userID); err != nil {
		h.logger.WithError(err).Error("Failed to update instance")
		if err.Error() == "instance not found" {
			response.NotFound(c, "实例不存在")
		} else if err.Error() == "cannot update submitted instance" {
			response.BadRequest(c, "不能修改已提交的实例")
		} else if err.Error() == "instance is locked by another user" {
			response.Forbidden(c, "实例已被其他用户锁定")
		} else {
			response.InternalError(c, "更新实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance updated successfully")

	// 获取更新后的实例数据并返回
	updatedInstance, err := h.instanceService.GetInstanceByID(id)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get updated instance")
		response.InternalError(c, "获取更新后的实例失败")
		return
	}

	response.Success(c, gin.H{
		"message": "实例更新成功",
		"instance": updatedInstance,
	})
}

// SubmitInstance 提交实例（需要登录）
func (h *InstanceHandler) SubmitInstance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的实例ID")
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能提交实例")
		return
	}

	if err := h.instanceService.SubmitInstance(id, userID); err != nil {
		h.logger.WithError(err).Error("Failed to submit instance")
		if err.Error() == "instance not found" {
			response.NotFound(c, "实例不存在")
		} else if err.Error() == "instance already submitted" {
			response.BadRequest(c, "实例已提交")
		} else {
			response.InternalError(c, "提交实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance submitted successfully")

	response.Success(c, gin.H{
		"message": "实例提交成功",
	})
}

// LockInstance 锁定实例（需要登录）
func (h *InstanceHandler) LockInstance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的实例ID")
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能锁定实例")
		return
	}

	if err := h.instanceService.LockInstance(id, userID); err != nil {
		h.logger.WithError(err).Error("Failed to lock instance")
		if err.Error() == "instance not found" {
			response.NotFound(c, "实例不存在")
		} else if err.Error() == "instance is locked by another user" {
			response.Forbidden(c, "实例已被其他用户锁定")
		} else {
			response.InternalError(c, "锁定实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance locked successfully")

	response.Success(c, gin.H{
		"message": "实例锁定成功",
	})
}

// UnlockInstance 解锁实例（需要登录）
func (h *InstanceHandler) UnlockInstance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的实例ID")
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能解锁实例")
		return
	}

	if err := h.instanceService.UnlockInstance(id, userID); err != nil {
		h.logger.WithError(err).Error("Failed to unlock instance")
		if err.Error() == "instance not found or not locked by current user" {
			response.NotFound(c, "实例不存在或未被当前用户锁定")
		} else {
			response.InternalError(c, "解锁实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance unlocked successfully")

	response.Success(c, gin.H{
		"message": "实例解锁成功",
	})
}

// DeleteInstance 删除实例（需要登录）
func (h *InstanceHandler) DeleteInstance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "无效的实例ID")
		return
	}

	// 获取用户ID（需要登录）
	userID, ok := h.getUserFromContext(c)
	if !ok {
		response.Unauthorized(c, "需要登录才能删除实例")
		return
	}

	if err := h.instanceService.DeleteInstance(id, userID); err != nil {
		h.logger.WithError(err).Error("Failed to delete instance")
		if err.Error() == "instance not found" {
			response.NotFound(c, "实例不存在")
		} else if err.Error() == "only instance creator can delete instance" {
			response.Forbidden(c, "只有创建者可以删除实例")
		} else if err.Error() == "cannot delete submitted instance" {
			response.BadRequest(c, "不能删除已提交的实例")
		} else {
			response.InternalError(c, "删除实例失败")
		}
		return
	}

	h.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance deleted successfully")

	response.Success(c, gin.H{
		"message": "实例删除成功",
	})
}
