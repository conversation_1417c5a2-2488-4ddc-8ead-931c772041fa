package middleware

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Logger middleware
func Logger(logger *logrus.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 根据状态码选择日志级别
		var logLevel logrus.Level
		if param.StatusCode >= 500 {
			logLevel = logrus.ErrorLevel
		} else if param.StatusCode >= 400 {
			logLevel = logrus.WarnLevel
		} else {
			logLevel = logrus.InfoLevel
		}

		// 格式化延迟时间
		latency := param.Latency
		if latency > time.Minute {
			latency = latency.Truncate(time.Second)
		}

		// 构建日志条目
		entry := logger.WithFields(logrus.Fields{
			"method":     param.Method,
			"path":       param.Path,
			"status":     param.StatusCode,
			"latency":    latency,
			"client_ip":  param.ClientIP,
			"user_agent": param.Request.UserAgent(),
		})

		// 如果有错误消息，添加到日志中
		if param.ErrorMessage != "" {
			entry = entry.WithField("error", param.ErrorMessage)
		}

		// 记录日志
		message := fmt.Sprintf("%s %s %d", param.Method, param.Path, param.StatusCode)
		entry.Log(logLevel, message)

		return ""
	})
}

// CORS middleware
func CORS(origins []string) gin.HandlerFunc {
	config := cors.Config{
		AllowOrigins:     origins,
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}

	return cors.New(config)
}

// Session middleware
func Session(secret string) gin.HandlerFunc {
	store := cookie.NewStore([]byte(secret))
	store.Options(sessions.Options{
		MaxAge:   int(24 * time.Hour / time.Second), // 24 hours
		Path:     "/",
		HttpOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: http.SameSiteLaxMode,
	})

	return sessions.Sessions("crf-session", store)
}

// AuthRequired middleware
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for session
		session := sessions.Default(c)
		userID := session.Get("user_id")

		if userID == nil {
			// Check for Authorization header
			authHeader := c.GetHeader("Authorization")
			if authHeader == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
				c.Abort()
				return
			}

			// Extract token from Bearer token
			token := strings.TrimPrefix(authHeader, "Bearer ")
			if token == authHeader {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization format"})
				c.Abort()
				return
			}

			// TODO: Validate JWT token here
			// For now, we'll just check if token is not empty
			if token == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
				c.Abort()
				return
			}

			// TODO: Extract user ID from JWT token
			// For now, we'll set a dummy user ID
			c.Set("user_id", "dummy-user-id")
		} else {
			c.Set("user_id", userID)
		}

		c.Next()
	}
}

// RateLimit middleware (basic implementation)
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement rate limiting
		// For now, just pass through
		c.Next()
	}
}
