package middleware

import (
	"strings"

	"crf-backend/internal/models"
	"crf-backend/internal/response"
	"crf-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

func JWTAuth(jwtService *services.JWTService, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Header中获取Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Warn("Missing Authorization header")
			response.Unauthorized(c, "缺少认证Token")
			c.Abort()
			return
		}

		// 验证Bearer Token格式
		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			logger.Warn("Invalid Authorization header format")
			response.Unauthorized(c, "无效的Token格式")
			c.Abort()
			return
		}

		// 验证Token
		token := tokenParts[1]
		claims, err := jwtService.ValidateToken(c.Request.Context(), token)
		if err != nil {
			logger.WithError(err).Warn("Invalid JWT token")
			response.Unauthorized(c, "无效或过期的Token")
			c.Abort()
			return
		}

		// 将用户信息设置到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role) // 保留旧字段以兼容性
		c.Set("jwt_claims", claims)

		logger.WithFields(logrus.Fields{
			"user_id":  claims.UserID,
			"username": claims.Username,
			"role":     claims.Role,
			"path":     c.Request.URL.Path,
		}).Debug("JWT authentication successful")

		c.Next()
	}
}

// OptionalJWTAuth 可选的JWT认证中间件（不强制要求认证）
func OptionalJWTAuth(jwtService *services.JWTService, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		token := tokenParts[1]
		claims, err := jwtService.ValidateToken(c.Request.Context(), token)
		if err != nil {
			logger.WithError(err).Debug("Optional JWT validation failed")
			c.Next()
			return
		}

		// 设置用户信息到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("jwt_claims", claims)

		c.Next()
	}
}

// AdminRequired 要求管理员权限的中间件（保留向后兼容）
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		if role != "admin" {
			response.Forbidden(c, "需要管理员权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RoleRequired 要求特定角色的中间件
func RoleRequired(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userRole, ok := role.(string)
		if !ok {
			response.Unauthorized(c, "无效的角色信息")
			c.Abort()
			return
		}

		// 检查是否有所需角色
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				c.Next()
				return
			}
		}

		response.Forbidden(c, "权限不足")
		c.Abort()
	}
}

// PermissionRequired 要求特定权限的中间件
func PermissionRequired(rbacService *services.RBACService, resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr, exists := c.Get("user_id")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userID, err := uuid.Parse(userIDStr.(string))
		if err != nil {
			response.Unauthorized(c, "无效的用户ID")
			c.Abort()
			return
		}

		// 获取项目ID（如果有）
		var projectID *uuid.UUID
		if projectIDStr := c.Param("project_id"); projectIDStr != "" {
			if pid, err := uuid.Parse(projectIDStr); err == nil {
				projectID = &pid
			}
		}

		// 检查权限
		result, err := rbacService.CheckPermission(c.Request.Context(), userID, resource, action, projectID)
		if err != nil {
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !result.HasPermission {
			response.Forbidden(c, "权限不足")
			c.Abort()
			return
		}

		// 将权限检查结果设置到上下文
		c.Set("permission_result", result)
		c.Next()
	}
}

// ProjectAccessRequired 要求项目访问权限的中间件
func ProjectAccessRequired(rbacService *services.RBACService, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr, exists := c.Get("user_id")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userID, err := uuid.Parse(userIDStr.(string))
		if err != nil {
			response.Unauthorized(c, "无效的用户ID")
			c.Abort()
			return
		}

		// 获取项目ID
		projectIDStr := c.Param("project_id")
		if projectIDStr == "" {
			projectIDStr = c.Param("id") // 有时项目ID在id参数中
		}

		if projectIDStr == "" {
			response.BadRequest(c, "缺少项目ID")
			c.Abort()
			return
		}

		projectID, err := uuid.Parse(projectIDStr)
		if err != nil {
			response.BadRequest(c, "无效的项目ID")
			c.Abort()
			return
		}

		// 检查项目访问权限
		result, err := rbacService.CheckPermission(c.Request.Context(), userID, models.ResourceProject, action, &projectID)
		if err != nil {
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !result.HasPermission {
			response.Forbidden(c, "无权访问此项目")
			c.Abort()
			return
		}

		c.Set("project_id", projectID)
		c.Set("permission_result", result)
		c.Next()
	}
}

// ResourceOwnerRequired 要求资源所有者权限的中间件
func ResourceOwnerRequired(rbacService *services.RBACService, resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr, exists := c.Get("user_id")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userID, err := uuid.Parse(userIDStr.(string))
		if err != nil {
			response.Unauthorized(c, "无效的用户ID")
			c.Abort()
			return
		}

		// 检查own权限
		result, err := rbacService.CheckPermission(c.Request.Context(), userID, resource, action, nil)
		if err != nil {
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !result.HasPermission {
			response.Forbidden(c, "权限不足")
			c.Abort()
			return
		}

		c.Set("permission_result", result)
		c.Next()
	}
}

// SystemAdminRequired 要求系统管理员权限的中间件
func SystemAdminRequired(rbacService *services.RBACService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr, exists := c.Get("user_id")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userID, err := uuid.Parse(userIDStr.(string))
		if err != nil {
			response.Unauthorized(c, "无效的用户ID")
			c.Abort()
			return
		}

		// 检查系统管理员权限
		isAdmin, err := rbacService.IsSystemAdmin(c.Request.Context(), userID)
		if err != nil {
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !isAdmin {
			response.Forbidden(c, "需要系统管理员权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// UserManagementRequired 要求用户管理权限的中间件
func UserManagementRequired(rbacService *services.RBACService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr, exists := c.Get("user_id")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userID, err := uuid.Parse(userIDStr.(string))
		if err != nil {
			response.Unauthorized(c, "无效的用户ID")
			c.Abort()
			return
		}

		// 检查用户管理权限
		canManage, err := rbacService.CanManageUsers(c.Request.Context(), userID)
		if err != nil {
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !canManage {
			response.Forbidden(c, "无权管理用户")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RoleManagementRequired 要求角色管理权限的中间件
func RoleManagementRequired(rbacService *services.RBACService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDStr, exists := c.Get("user_id")
		if !exists {
			response.Unauthorized(c, "未认证")
			c.Abort()
			return
		}

		userID, err := uuid.Parse(userIDStr.(string))
		if err != nil {
			response.Unauthorized(c, "无效的用户ID")
			c.Abort()
			return
		}

		// 检查角色管理权限
		canManage, err := rbacService.CanManageRoles(c.Request.Context(), userID)
		if err != nil {
			response.InternalServerError(c, "权限检查失败")
			c.Abort()
			return
		}

		if !canManage {
			response.Forbidden(c, "无权管理角色")
			c.Abort()
			return
		}

		c.Next()
	}
}
