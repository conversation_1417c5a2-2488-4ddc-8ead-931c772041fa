package server

import (
	"context"
	"fmt"
	"net/http"

	"crf-backend/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type Server struct {
	config *config.Config
	logger *logrus.Logger
	router *gin.Engine
	server *http.Server
}

func New(cfg *config.Config, logger *logrus.Logger, router *gin.Engine) *Server {
	srv := &http.Server{
		Addr:           fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:        router,
		ReadTimeout:    cfg.Server.Timeout.Read,
		WriteTimeout:   cfg.Server.Timeout.Write,
		MaxHeaderBytes: cfg.Server.MaxHeaderBytes,
	}

	return &Server{
		config: cfg,
		logger: logger,
		router: router,
		server: srv,
	}
}

// Start 启动HTTP服务器（阻塞）
func (s *Server) Start() error {
	s.logger.Infof("🚀 HTTP服务器启动在 %s", s.server.Addr)
	if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("HTTP服务器启动失败: %w", err)
	}
	return nil
}

// Shutdown 优雅关闭HTTP服务器
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("🛑 正在关闭HTTP服务器...")

	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("HTTP服务器关闭失败: %w", err)
	}

	s.logger.Info("✅ HTTP服务器已关闭")
	return nil
}

// Stop 强制停止HTTP服务器
func (s *Server) Stop() error {
	return s.server.Close()
}
