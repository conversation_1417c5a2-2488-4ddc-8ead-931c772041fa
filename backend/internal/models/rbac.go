package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Role 角色模型
type Role struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Code        string    `json:"code" gorm:"uniqueIndex;not null"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null"`
	DisplayName string    `json:"display_name" gorm:"not null"`
	Description string    `json:"description"`
	IsSystem    bool      `json:"is_system" gorm:"default:false"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
	Users       []User       `json:"users,omitempty" gorm:"many2many:user_roles;"`
}

// Permission 权限模型
type Permission struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Resource    string    `json:"resource" gorm:"not null"`                    // 资源类型
	Action      string    `json:"action" gorm:"not null"`                      // 操作类型
	Scope       string    `json:"scope" gorm:"default:global"`                 // 权限范围
	Description string    `json:"description"`                                 // 权限描述
	IsSystem    bool      `json:"is_system" gorm:"default:true"`               // 是否系统权限

	CreatedAt time.Time `json:"created_at"`

	// 关联
	Roles []Role `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	RoleID       uuid.UUID `json:"role_id" gorm:"not null"`
	PermissionID uuid.UUID `json:"permission_id" gorm:"not null"`

	CreatedAt time.Time `json:"created_at"`

	// 关联
	Role       Role       `json:"role,omitempty" gorm:"foreignKey:RoleID;references:ID"`
	Permission Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID;references:ID"`
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID        uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID    uuid.UUID  `json:"user_id" gorm:"not null"`
	RoleID    uuid.UUID  `json:"role_id" gorm:"not null"`
	ProjectID *uuid.UUID `json:"project_id,omitempty"` // NULL表示全局角色

	AssignedBy uuid.UUID  `json:"assigned_by"`
	AssignedAt time.Time  `json:"assigned_at"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty"` // 角色过期时间

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联
	User      User     `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Role      Role     `json:"role,omitempty" gorm:"foreignKey:RoleID;references:ID"`
	Project   *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Assigner  User     `json:"assigner,omitempty" gorm:"foreignKey:AssignedBy;references:ID"`
}

// UserPermissionCache 用户权限缓存模型
type UserPermissionCache struct {
	ID            uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID        uuid.UUID  `json:"user_id" gorm:"not null"`
	Resource      string     `json:"resource" gorm:"not null"`
	Action        string     `json:"action" gorm:"not null"`
	ProjectID     *uuid.UUID `json:"project_id,omitempty"`
	HasPermission bool       `json:"has_permission" gorm:"not null"`

	CacheExpiresAt time.Time `json:"cache_expires_at"`
	CreatedAt      time.Time `json:"created_at"`

	// 关联
	User    User     `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

// RBAC相关的常量定义
const (
	// 系统角色
	RoleSuperAdmin = "super_admin"
	RoleAdmin      = "admin"
	RoleResearcher = "researcher"
	RoleDataEntry  = "data_entry"
	RoleReviewer   = "reviewer"
	RoleViewer     = "viewer"

	// 资源类型
	ResourceUser     = "user"
	ResourceRole     = "role"
	ResourceProject  = "project"
	ResourceTemplate = "template"
	ResourceInstance = "instance"
	ResourceData     = "data"
	ResourceSystem   = "system"

	// 操作类型
	ActionCreate        = "create"
	ActionRead          = "read"
	ActionUpdate        = "update"
	ActionDelete        = "delete"
	ActionPublish       = "publish"
	ActionSubmit        = "submit"
	ActionApprove       = "approve"
	ActionLock          = "lock"
	ActionExport        = "export"
	ActionAnalyze       = "analyze"
	ActionAssignRole    = "assign_role"
	ActionAssignPermission = "assign_permission"
	ActionConfig        = "config"
	ActionMonitor       = "monitor"
	ActionBackup        = "backup"
	ActionAudit         = "audit"

	// 权限范围
	ScopeGlobal  = "global"
	ScopeProject = "project"
	ScopeOwn     = "own"
)

// 权限检查结果
type PermissionResult struct {
	HasPermission bool   `json:"has_permission"`
	Reason        string `json:"reason,omitempty"`
	Source        string `json:"source,omitempty"` // global_role, project_role, cached
}

// 权限检查请求
type PermissionCheck struct {
	UserID    uuid.UUID  `json:"user_id" binding:"required"`
	Resource  string     `json:"resource" binding:"required"`
	Action    string     `json:"action" binding:"required"`
	ProjectID *uuid.UUID `json:"project_id,omitempty"`
	ResourceID *uuid.UUID `json:"resource_id,omitempty"` // 具体资源ID，用于own权限检查
}

// 角色分配请求
type RoleAssignmentRequest struct {
	UserID    uuid.UUID  `json:"user_id" binding:"required"`
	RoleID    uuid.UUID  `json:"role_id" binding:"required"`
	ProjectID *uuid.UUID `json:"project_id,omitempty"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// 角色权限分配请求
type RolePermissionRequest struct {
	RoleID        uuid.UUID   `json:"role_id" binding:"required"`
	PermissionIDs []uuid.UUID `json:"permission_ids" binding:"required"`
}

// 用户角色信息
type UserRoleInfo struct {
	UserID      uuid.UUID  `json:"user_id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	FullName    string     `json:"full_name"`
	Roles       []RoleInfo `json:"roles"`
	GlobalRoles []RoleInfo `json:"global_roles"`
}

// 角色信息
type RoleInfo struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	DisplayName string     `json:"display_name"`
	ProjectID   *uuid.UUID `json:"project_id,omitempty"`
	ProjectName string     `json:"project_name,omitempty"`
	AssignedAt  time.Time  `json:"assigned_at"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// 权限信息
type PermissionInfo struct {
	ID          uuid.UUID `json:"id"`
	Resource    string    `json:"resource"`
	Action      string    `json:"action"`
	Scope       string    `json:"scope"`
	Description string    `json:"description"`
}

// GORM hooks
func (r *Role) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

func (p *Permission) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

func (rp *RolePermission) BeforeCreate(tx *gorm.DB) error {
	if rp.ID == uuid.Nil {
		rp.ID = uuid.New()
	}
	return nil
}

func (ur *UserRole) BeforeCreate(tx *gorm.DB) error {
	if ur.ID == uuid.Nil {
		ur.ID = uuid.New()
	}
	if ur.AssignedAt.IsZero() {
		ur.AssignedAt = time.Now()
	}
	return nil
}

func (upc *UserPermissionCache) BeforeCreate(tx *gorm.DB) error {
	if upc.ID == uuid.Nil {
		upc.ID = uuid.New()
	}
	return nil
}

// 权限检查方法
func (p *Permission) MatchesRequest(resource, action, scope string) bool {
	return p.Resource == resource && p.Action == action && p.Scope == scope
}

// 角色是否有权限
func (r *Role) HasPermission(resource, action, scope string) bool {
	for _, perm := range r.Permissions {
		if perm.MatchesRequest(resource, action, scope) {
			return true
		}
	}
	return false
}

// 检查角色是否过期
func (ur *UserRole) IsExpired() bool {
	if ur.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*ur.ExpiresAt)
}

// 检查缓存是否过期
func (upc *UserPermissionCache) IsExpired() bool {
	return time.Now().After(upc.CacheExpiresAt)
}