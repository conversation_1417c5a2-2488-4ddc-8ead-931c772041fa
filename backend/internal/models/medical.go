package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// MedicalCode 医疗编码模型 (支持ICD-10/11, SNOMED CT, LOINC等)
type MedicalCode struct {
	ID            uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	CodeSystem    string     `json:"code_system" gorm:"not null;size:50"` // 'icd10', 'icd11', 'snomed', 'loinc', 'atc'
	Code          string     `json:"code" gorm:"not null;size:100"`
	DisplayName   string     `json:"display_name" gorm:"not null;size:500"`
	Description   string     `json:"description" gorm:"type:text"`
	ParentCode    *string    `json:"parent_code" gorm:"size:100"` // 父级编码，用于构建层级结构
	Level         int        `json:"level" gorm:"default:0"`      // 层级深度
	IsActive      bool       `json:"is_active" gorm:"default:true"`
	Language      string     `json:"language" gorm:"default:zh-CN;size:10"`
	Version       *string    `json:"version" gorm:"size:20"`          // 编码系统版本
	EffectiveDate *time.Time `json:"effective_date" gorm:"type:date"` // 生效日期
	ExpiryDate    *time.Time `json:"expiry_date" gorm:"type:date"`    // 失效日期
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

func (m *MedicalCode) BeforeCreate(tx *gorm.DB) error {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (MedicalCode) TableName() string {
	return "medical_codes"
}

// MedicalUnit 医疗单位模型 (支持单位转换)
type MedicalUnit struct {
	ID               uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UnitCode         string    `json:"unit_code" gorm:"unique;not null;size:50"`
	UnitName         string    `json:"unit_name" gorm:"not null;size:100"`
	UnitSymbol       string    `json:"unit_symbol" gorm:"not null;size:20"`
	Category         *string   `json:"category" gorm:"size:50"`                                // 'temperature', 'pressure', 'weight', 'volume', 'time', 'frequency'
	BaseUnit         *string   `json:"base_unit" gorm:"size:50"`                               // 基础单位用于转换
	ConversionFactor *float64  `json:"conversion_factor" gorm:"type:decimal(20,10)"`           // 转换系数到基础单位
	ConversionOffset *float64  `json:"conversion_offset" gorm:"type:decimal(20,10);default:0"` // 转换偏移量（如摄氏度到开尔文）
	IsSIUnit         bool      `json:"is_si_unit" gorm:"default:false"`                        // 是否为SI单位
	IsActive         bool      `json:"is_active" gorm:"default:true"`
	Description      *string   `json:"description" gorm:"type:text"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

func (m *MedicalUnit) BeforeCreate(tx *gorm.DB) error {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (MedicalUnit) TableName() string {
	return "medical_units"
}

// MedicalReferenceRange 医疗参考值模型 (按年龄性别分组的正常值范围)
type MedicalReferenceRange struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	TestCode        string    `json:"test_code" gorm:"not null;size:100"`
	TestName        string    `json:"test_name" gorm:"not null;size:200"`
	MinValue        *float64  `json:"min_value" gorm:"type:decimal(15,6)"`
	MaxValue        *float64  `json:"max_value" gorm:"type:decimal(15,6)"`
	Unit            *string   `json:"unit" gorm:"size:50"`
	AgeMin          *int      `json:"age_min"`                                   // 最小年龄（月）
	AgeMax          *int      `json:"age_max"`                                   // 最大年龄（月）
	Gender          string    `json:"gender" gorm:"size:10;default:all"`         // 'male', 'female', 'all'
	Population      string    `json:"population" gorm:"size:50;default:general"` // 'adult', 'pediatric', 'elderly', 'pregnant'
	ConditionNotes  *string   `json:"condition_notes" gorm:"type:text"`          // 特殊条件说明
	ReferenceSource *string   `json:"reference_source" gorm:"size:200"`          // 参考值来源
	IsActive        bool      `json:"is_active" gorm:"default:true"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

func (m *MedicalReferenceRange) BeforeCreate(tx *gorm.DB) error {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (MedicalReferenceRange) TableName() string {
	return "medical_reference_ranges"
}

// MedicalDrug 药物数据模型
type MedicalDrug struct {
	ID                uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	DrugCode          string         `json:"drug_code" gorm:"unique;not null;size:100"`
	GenericName       string         `json:"generic_name" gorm:"not null;size:200"`
	BrandNames        datatypes.JSON `json:"brand_names" gorm:"type:jsonb"` // 商品名数组
	DrugClass         *string        `json:"drug_class" gorm:"size:100"`
	ATCCode           *string        `json:"atc_code" gorm:"size:20"`        // ATC分类编码
	DosageForms       datatypes.JSON `json:"dosage_forms" gorm:"type:jsonb"` // 剂型数组 ['tablet', 'injection', 'capsule']
	Strengths         datatypes.JSON `json:"strengths" gorm:"type:jsonb"`    // 规格数组 ['5mg', '10mg', '20mg']
	Routes            datatypes.JSON `json:"routes" gorm:"type:jsonb"`       // 给药途径数组 ['oral', 'iv', 'im']
	Contraindications *string        `json:"contraindications" gorm:"type:text"`
	Interactions      *string        `json:"interactions" gorm:"type:text"`
	SideEffects       datatypes.JSON `json:"side_effects" gorm:"type:jsonb"`      // 副作用列表
	PregnancyCategory *string        `json:"pregnancy_category" gorm:"size:10"`   // 妊娠分级
	IsPrescription    bool           `json:"is_prescription" gorm:"default:true"` // 是否处方药
	IsActive          bool           `json:"is_active" gorm:"default:true"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
}

func (m *MedicalDrug) BeforeCreate(tx *gorm.DB) error {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (MedicalDrug) TableName() string {
	return "medical_drugs"
}

// MedicalCodeSearchRequest 医疗编码搜索请求
type MedicalCodeSearchRequest struct {
	CodeSystem string `json:"code_system" form:"code_system"`
	Search     string `json:"search" form:"search"`
	Language   string `json:"language" form:"language"`
	ParentCode string `json:"parent_code" form:"parent_code"`
	Level      *int   `json:"level" form:"level"`
	Limit      int    `json:"limit" form:"limit"`
	Offset     int    `json:"offset" form:"offset"`
}

// MedicalUnitSearchRequest 医疗单位搜索请求
type MedicalUnitSearchRequest struct {
	Category string `json:"category" form:"category"`
	Search   string `json:"search" form:"search"`
	Limit    int    `json:"limit" form:"limit"`
	Offset   int    `json:"offset" form:"offset"`
}

// MedicalReferenceRangeSearchRequest 医疗参考值搜索请求
type MedicalReferenceRangeSearchRequest struct {
	TestCode   string `json:"test_code" form:"test_code"`
	Age        *int   `json:"age" form:"age"`
	Gender     string `json:"gender" form:"gender"`
	Population string `json:"population" form:"population"`
}

// MedicalDrugSearchRequest 药物搜索请求
type MedicalDrugSearchRequest struct {
	Search         string `json:"search" form:"search"`
	DrugClass      string `json:"drug_class" form:"drug_class"`
	ATCCode        string `json:"atc_code" form:"atc_code"`
	IsPrescription *bool  `json:"is_prescription" form:"is_prescription"`
	Limit          int    `json:"limit" form:"limit"`
	Offset         int    `json:"offset" form:"offset"`
}

// UnitConversionRequest 单位转换请求
type UnitConversionRequest struct {
	Value    float64 `json:"value" binding:"required"`
	FromUnit string  `json:"from_unit" binding:"required"`
	ToUnit   string  `json:"to_unit" binding:"required"`
}

// UnitConversionResponse 单位转换响应
type UnitConversionResponse struct {
	OriginalValue   float64 `json:"original_value"`
	OriginalUnit    string  `json:"original_unit"`
	ConvertedValue  float64 `json:"converted_value"`
	ConvertedUnit   string  `json:"converted_unit"`
	ConversionRate  float64 `json:"conversion_rate"`
	ConversionNotes string  `json:"conversion_notes,omitempty"`
}

// AbnormalValueLevel 异常值级别
type AbnormalValueLevel string

const (
	AbnormalValueNormal   AbnormalValueLevel = "normal"
	AbnormalValueLow      AbnormalValueLevel = "low"
	AbnormalValueHigh     AbnormalValueLevel = "high"
	AbnormalValueCritical AbnormalValueLevel = "critical"
)

// ValueAssessmentResult 数值评估结果
type ValueAssessmentResult struct {
	Value          float64                `json:"value"`
	Unit           string                 `json:"unit"`
	ReferenceRange *MedicalReferenceRange `json:"reference_range,omitempty"`
	Level          AbnormalValueLevel     `json:"level"`
	Interpretation string                 `json:"interpretation"`
	Recommendation string                 `json:"recommendation,omitempty"`
	CriticalAlert  bool                   `json:"critical_alert"`
}

// MedicalValidationRule 医疗数据验证规则
type MedicalValidationRule struct {
	FieldType    string         `json:"field_type"`    // 字段类型
	DataType     string         `json:"data_type"`     // 数据类型
	Required     bool           `json:"required"`      // 是否必填
	MinValue     *float64       `json:"min_value"`     // 最小值
	MaxValue     *float64       `json:"max_value"`     // 最大值
	AllowedUnits []string       `json:"allowed_units"` // 允许的单位
	Pattern      string         `json:"pattern"`       // 正则表达式模式
	CustomRules  map[string]any `json:"custom_rules"`  // 自定义规则
}
