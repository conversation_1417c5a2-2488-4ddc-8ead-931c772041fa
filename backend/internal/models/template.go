package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type CRFTemplate struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ProjectID   uuid.UUID `json:"project_id" gorm:"not null"` // 恢复非空约束
	Name        string    `json:"name" gorm:"not null"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description"`
	Keyword     string    `json:"keyword"`
	Version     string    `json:"version" gorm:"default:1.0.0"`
	Status      string    `json:"status" gorm:"default:draft"`

	// 图标相关字段
	Icon      string `json:"icon" gorm:"default:Document"`
	IconColor string `json:"icon_color" gorm:"default:#3b82f6"`

	// 模板类型：区分标准模板和自定义表单
	TemplateType string `json:"template_type" gorm:"default:template"`

	// 模板来源：系统预置、用户创建、导入等
	SourceType string `json:"source_type" gorm:"default:user_created"`

	// 是否为公共模板（可被其他用户使用）
	IsPublic bool `json:"is_public" gorm:"default:false"`

	// 统一的模板数据存储（替代多个分散的JSON字段）
	TemplateData datatypes.JSON `json:"template_data" gorm:"type:jsonb;not null;default:'{}'"`

	Permissions datatypes.JSON `json:"permissions" gorm:"type:jsonb;default:'{}'"`

	// 标签，便于分类和搜索
	Tags string `json:"tags"`

	// 使用统计
	UsageCount int `json:"usage_count" gorm:"default:0"`

	// 发布相关字段
	PublishedAt *time.Time `json:"published_at"`
	PublishedBy *uuid.UUID `json:"published_by"`

	CreatedBy uuid.UUID  `json:"created_by" gorm:"not null"`
	UpdatedBy *uuid.UUID `json:"updated_by"`

	// 逻辑删除字段
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;index"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联关系 - 移除外键约束
	Project   *Project      `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Creator   User          `json:"creator" gorm:"foreignKey:CreatedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Updater   *User         `json:"updater,omitempty" gorm:"foreignKey:UpdatedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Publisher *User         `json:"publisher,omitempty" gorm:"foreignKey:PublishedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Versions  []CRFVersion  `json:"versions,omitempty" gorm:"foreignKey:TemplateID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Instances []CRFInstance `json:"instances,omitempty" gorm:"foreignKey:TemplateID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

type CRFVersion struct {
	ID           uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	TemplateID   uuid.UUID      `json:"template_id" gorm:"not null"`
	Version      string         `json:"version" gorm:"not null"`
	Title        string         `json:"title" gorm:"not null"`
	Description  string         `json:"description"`
	SnapshotData datatypes.JSON `json:"snapshot_data" gorm:"type:jsonb;not null"`
	ChangeLog    string         `json:"change_log"`
	Status       string         `json:"status" gorm:"default:draft"`
	PublishedAt  *time.Time     `json:"published_at"`
	PublishedBy  *uuid.UUID     `json:"published_by"`
	CreatedBy    uuid.UUID      `json:"created_by" gorm:"not null"`

	// 逻辑删除字段
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;index"`

	CreatedAt time.Time `json:"created_at"`

	// 移除外键约束的关联关系
	Template  CRFTemplate `json:"template" gorm:"foreignKey:TemplateID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Creator   User        `json:"creator" gorm:"foreignKey:CreatedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Publisher *User       `json:"publisher,omitempty" gorm:"foreignKey:PublishedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

func (t *CRFTemplate) BeforeCreate(tx *gorm.DB) error {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	return nil
}

func (v *CRFVersion) BeforeCreate(tx *gorm.DB) error {
	if v.ID == uuid.Nil {
		v.ID = uuid.New()
	}
	return nil
}
