package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Project struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Name        string         `json:"name" gorm:"not null"`
	Description string         `json:"description"`
	CreatedBy   uuid.UUID      `json:"created_by" gorm:"not null"`
	Status      string         `json:"status" gorm:"default:draft"`
	Settings    datatypes.JSON `json:"settings" gorm:"type:jsonb;default:'{}'"`

	// 逻辑删除字段
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;index"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 移除外键约束的关联关系
	Creator   User          `json:"creator" gorm:"foreignKey:CreatedBy;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Templates []CRFTemplate `json:"templates,omitempty" gorm:"foreignKey:ProjectID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

func (p *Project) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}
