package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type CRFInstance struct {
	ID                   uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	TemplateID           uuid.UUID      `json:"template_id" gorm:"not null"`
	TemplateVersion      string         `json:"template_version" gorm:"not null"`
	InstanceName         string         `json:"instance_name"`
	SubjectID            string         `json:"subject_id"`
	VisitID              string         `json:"visit_id"`
	FormData             datatypes.JSON `json:"form_data" gorm:"type:jsonb;default:'{}'"`
	ValidationResults    datatypes.JSON `json:"validation_results" gorm:"type:jsonb;default:'{}'"`
	Status               string         `json:"status" gorm:"default:draft"`
	CompletionPercentage float64        `json:"completion_percentage" gorm:"default:0.0"`
	LockedBy             *uuid.UUID     `json:"locked_by"`
	LockedAt             *time.Time     `json:"locked_at"`
	ReviewedBy           *uuid.UUID     `json:"reviewed_by"`
	ReviewedAt           *time.Time     `json:"reviewed_at"`
	SubmittedAt          *time.Time     `json:"submitted_at"`
	ReviewComment        string         `json:"review_comment"`
	CreatedBy            uuid.UUID      `json:"created_by" gorm:"not null"`
	UpdatedBy            *uuid.UUID     `json:"updated_by"`
	DeletedAt            *time.Time     `json:"deleted_at" gorm:"index"`
	IsDeleted            bool           `json:"is_deleted" gorm:"default:false;index"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`

	Template CRFTemplate `json:"template" gorm:"foreignKey:TemplateID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Creator  User        `json:"creator" gorm:"foreignKey:CreatedBy;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Updater  *User       `json:"updater,omitempty" gorm:"foreignKey:UpdatedBy;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Locker   *User       `json:"locker,omitempty" gorm:"foreignKey:LockedBy;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	Reviewer *User       `json:"reviewer,omitempty" gorm:"foreignKey:ReviewedBy;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

func (i *CRFInstance) BeforeCreate(tx *gorm.DB) error {
	if i.ID == uuid.Nil {
		i.ID = uuid.New()
	}
	return nil
}
