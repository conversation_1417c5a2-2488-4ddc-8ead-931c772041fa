package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Migration 数据库迁移记录表
type Migration struct {
	ID        uuid.UUID  `gorm:"type:uuid;default:gen_random_uuid();primaryKey" json:"id"`
	Version   string     `gorm:"uniqueIndex;not null" json:"version"`
	Name      string     `gorm:"not null" json:"name"`
	Applied   bool       `gorm:"default:false" json:"applied"`
	AppliedAt *time.Time `json:"applied_at"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;index"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

func (m *Migration) BeforeCreate(tx *gorm.DB) (err error) {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return
}
