package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type AutoSave struct {
	ID           uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID       uuid.UUID      `json:"user_id" gorm:"not null"`
	ResourceType string         `json:"resource_type" gorm:"not null"`
	ResourceID   uuid.UUID      `json:"resource_id" gorm:"not null"`
	SaveData     datatypes.JSON `json:"save_data" gorm:"type:jsonb;not null"`
	SavedAt      time.Time      `json:"saved_at"`
	ExpiresAt    time.Time      `json:"expires_at"`
	DeletedAt    *time.Time     `json:"deleted_at" gorm:"index"`
	IsDeleted    bool           `json:"is_deleted" gorm:"default:false;index"`

	User User `json:"user" gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

type OperationHistory struct {
	ID           uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID       uuid.UUID      `json:"user_id" gorm:"not null"`
	ResourceType string         `json:"resource_type" gorm:"not null"`
	ResourceID   uuid.UUID      `json:"resource_id" gorm:"not null"`
	Action       string         `json:"action" gorm:"not null"`
	Description  string         `json:"description"`
	BeforeData   datatypes.JSON `json:"before_data" gorm:"type:jsonb"`
	AfterData    datatypes.JSON `json:"after_data" gorm:"type:jsonb"`
	ClientInfo   datatypes.JSON `json:"client_info" gorm:"type:jsonb"`
	DeletedAt    *time.Time     `json:"deleted_at" gorm:"index"`
	IsDeleted    bool           `json:"is_deleted" gorm:"default:false;index"`
	CreatedAt    time.Time      `json:"created_at"`

	User User `json:"user" gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

// TableName specifies the table name for GORM
func (OperationHistory) TableName() string {
	return "operation_histories"
}

type Attachment struct {
	ID           uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	ResourceType string         `json:"resource_type" gorm:"not null"`
	ResourceID   uuid.UUID      `json:"resource_id" gorm:"not null"`
	Filename     string         `json:"filename" gorm:"not null"`
	OriginalName string         `json:"original_name" gorm:"not null"`
	FileType     string         `json:"file_type" gorm:"not null"`
	FileSize     int64          `json:"file_size" gorm:"not null"`
	FilePath     string         `json:"file_path" gorm:"not null"`
	UploadedBy   uuid.UUID      `json:"uploaded_by" gorm:"not null"`
	UploadedAt   time.Time      `json:"uploaded_at"`
	Metadata     datatypes.JSON `json:"metadata" gorm:"type:jsonb;default:'{}'"`
	DeletedAt    *time.Time     `json:"deleted_at" gorm:"index"`
	IsDeleted    bool           `json:"is_deleted" gorm:"default:false;index"`

	Uploader User `json:"uploader" gorm:"foreignKey:UploadedBy;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
}

type SystemSetting struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Key         string         `json:"key" gorm:"uniqueIndex;not null"`
	Value       datatypes.JSON `json:"value" gorm:"type:jsonb;not null"`
	Description string         `json:"description"`
	DeletedAt   *time.Time     `json:"deleted_at" gorm:"index"`
	IsDeleted   bool           `json:"is_deleted" gorm:"default:false;index"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

func (a *AutoSave) BeforeCreate(tx *gorm.DB) error {
	if a.ID == uuid.Nil {
		a.ID = uuid.New()
	}
	return nil
}

func (o *OperationHistory) BeforeCreate(tx *gorm.DB) error {
	if o.ID == uuid.Nil {
		o.ID = uuid.New()
	}
	return nil
}

func (a *Attachment) BeforeCreate(tx *gorm.DB) error {
	if a.ID == uuid.Nil {
		a.ID = uuid.New()
	}
	return nil
}

func (s *SystemSetting) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}