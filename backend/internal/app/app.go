package app

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"path"
	"runtime"
	"syscall"
	"time"

	"crf-backend/internal/config"
	"crf-backend/internal/database"
	"crf-backend/internal/handlers"
	"crf-backend/internal/router"
	"crf-backend/internal/server"
	"crf-backend/internal/services"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type App struct {
	config   *config.Config
	logger   *logrus.Logger
	db       *gorm.DB
	server   *server.Server
	services *Services
}

type Services struct {
	UserService             *services.UserService
	ProjectService          *services.ProjectService
	TemplateService         *services.TemplateService
	InstanceService         *services.InstanceService
	AutoSaveService         *services.AutoSaveService
	HistoryService          *services.HistoryService
	CacheService            *services.CacheService
	JWTService              *services.JWTService
	RBACService             *services.RBACService
	RateLimitService        *services.RateLimitService
	SMSService              *services.SMSService
	DataAnalysisService     *services.DataAnalysisService
	DataExportService       *services.DataExportService
	VersionAwareDataService *services.VersionAwareDataService
	MinIOService            *services.MinIOService
	MedicalService          *services.MedicalService
}

func New() (*App, error) {
	cfg, err := config.Load()
	if err != nil {
		return nil, err
	}

	logger := setupLogger(cfg)

	db, err := database.Connect(cfg.Database.URL)
	if err != nil {
		logger.Fatalf("Failed to connect to database: %v", err)
		return nil, err
	}

	// 验证数据库连接
	if err := database.CheckConnection(db); err != nil {
		logger.Fatalf("数据库连接检查失败: %v", err)
		return nil, err
	}
	logger.Info("数据库连接检查成功，使用手动创建的表结构")

	// Initialize cache service
	cacheService, err := services.NewCacheService(cfg, logger)
	if err != nil {
		logger.Fatalf("Failed to connect to Redis: %v", err)
		return nil, err
	}

	services := initializeServices(db, cacheService, cfg, logger)

	r := router.New(cfg)
	r.SetupMiddleware(logger)

	handlers := initializeHandlers(services, cfg, logger)
	r.SetupRoutes(
		handlers.UserHandler,
		handlers.ProjectHandler,
		handlers.TemplateHandler,
		handlers.InstanceHandler,
		nil, // autoSaveHandler
		handlers.HistoryHandler,
		handlers.DataManagementHandler,
		handlers.AvatarHandler,
		handlers.MedicalHandler,
		services.JWTService,
		services.RBACService,
		logger,
	)

	srv := server.New(cfg, logger, r.GetEngine())

	return &App{
		config:   cfg,
		logger:   logger,
		db:       db,
		server:   srv,
		services: services,
	}, nil
}

func (a *App) Run() error {
	a.printStartupBanner()
	go a.startBackgroundServices()

	// 创建一个channel来接收系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 在goroutine中启动服务器
	serverErrChan := make(chan error, 1)
	go func() {
		if err := a.server.Start(); err != nil {
			serverErrChan <- err
		}
	}()

	// 等待信号或服务器错误
	select {
	case err := <-serverErrChan:
		a.logger.WithError(err).Error("Server startup failed")
		return err
	case sig := <-sigChan:
		a.logger.WithField("signal", sig.String()).Info("收到退出信号，开始优雅关闭...")
		return a.gracefulShutdown()
	}
}

func (a *App) printStartupBanner() {
	banner := `
 ██████╗██████╗ ███████╗    ██████╗  █████╗  ██████╗██╗  ██╗███████╗███╗   ██╗██████╗ 
██╔════╝██╔══██╗██╔════╝    ██╔══██╗██╔══██╗██╔════╝██║ ██╔╝██╔════╝████╗  ██║██╔══██╗
██║     ██████╔╝█████╗      ██████╔╝███████║██║     █████╔╝ █████╗  ██╔██╗ ██║██║  ██║
██║     ██╔══██╗██╔══╝      ██╔══██╗██╔══██║██║     ██╔═██╗ ██╔══╝  ██║╚██╗██║██║  ██║
╚██████╗██║  ██║██║         ██████╔╝██║  ██║╚██████╗██║  ██╗███████╗██║ ╚████║██████╔╝
 ╚═════╝╚═╝  ╚═╝╚═╝         ╚═════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝╚═════╝ 
`
	a.logger.Info(banner)
	a.logger.Info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	a.logger.Infof("🚀 CRF Backend Server v1.0.0")
	a.logger.Infof("🌍 Environment: %s", a.config.Environment.Name)
	a.logger.Infof("🔗 Server URL: http://%s:%d", a.config.Server.Host, a.config.Server.Port)
	a.logger.Infof("📚 API Documentation: http://%s:%d/docs/API.md", a.config.Server.Host, a.config.Server.Port)
	a.logger.Infof("💖 Health Check: http://%s:%d/health", a.config.Server.Host, a.config.Server.Port)
	a.logger.Infof("🗄️  Database: %s:%d/%s", a.config.Database.Host, a.config.Database.Port, a.config.Database.Name)
	a.logger.Infof("🔴 Redis: %s:%d (DB: %d)", a.config.Redis.Host, a.config.Redis.Port, a.config.Redis.DB)
	a.logger.Infof("📝 Log Level: %s", a.config.Logging.Level)
	if a.config.Environment.Debug {
		a.logger.Info("🔧 Debug Mode: Enabled")
	}
	a.logger.Info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	a.logger.Info("✨ Server is ready to accept connections")
}

func (a *App) startBackgroundServices() {
	if a.services.AutoSaveService != nil {
		go a.services.AutoSaveService.StartCleanupTask(
			time.Duration(a.config.AutoSave.CleanupInterval) * time.Second,
		)
	}
}

// gracefulShutdown 优雅关闭应用程序
func (a *App) gracefulShutdown() error {
	a.logger.Info("🛑 开始优雅关闭...")

	// 创建一个带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭后台服务
	a.logger.Info("⏹️  停止后台服务...")
	// 这里可以添加停止后台服务的逻辑

	// 关闭HTTP服务器
	a.logger.Info("⏹️  关闭HTTP服务器...")
	if err := a.server.Shutdown(ctx); err != nil {
		a.logger.WithError(err).Error("HTTP服务器关闭失败")
		return err
	}

	// 关闭数据库连接
	a.logger.Info("⏹️  关闭数据库连接...")
	if err := a.closeDatabase(); err != nil {
		a.logger.WithError(err).Error("数据库连接关闭失败")
		return err
	}

	// 关闭Redis连接
	a.logger.Info("⏹️  关闭Redis连接...")
	if err := a.services.CacheService.Close(); err != nil {
		a.logger.WithError(err).Error("Redis连接关闭失败")
		return err
	}

	a.logger.Info("✅ 应用程序已优雅关闭")
	return nil
}

// closeDatabase 关闭数据库连接
func (a *App) closeDatabase() error {
	sqlDB, err := a.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying SQL database: %w", err)
	}
	return sqlDB.Close()
}

func setupLogger(cfg *config.Config) *logrus.Logger {
	logger := logrus.New()

	// 设置日志格式为文本格式，带颜色和时间戳
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
		ForceColors:     true,
		DisableColors:   false,
	})

	// 解析日志级别
	level, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		level = logrus.InfoLevel
		logger.Warnf("Invalid log level '%s', using 'info' instead", cfg.Logging.Level)
	}
	logger.SetLevel(level)

	// 在开发环境下显示更详细的信息
	if cfg.Environment.Name == "development" {
		logger.SetReportCaller(true)
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
			ForceColors:     true,
			DisableColors:   false,
			CallerPrettyfier: func(f *runtime.Frame) (string, string) {
				filename := path.Base(f.File)
				return fmt.Sprintf("%s()", f.Function), fmt.Sprintf("%s:%d", filename, f.Line)
			},
		})
	}

	logger.Infof("Logger initialized with level: %s", level.String())
	return logger
}

func initializeServices(db *gorm.DB, cacheService *services.CacheService, cfg *config.Config, logger *logrus.Logger) *Services {
	// 初始化数据分析服务
	dataAnalysisService := services.NewDataAnalysisService(db, logger)
	versionAwareDataService := services.NewVersionAwareDataService(db, logger)
	dataExportService := services.NewDataExportService(db, logger, dataAnalysisService)

	// 初始化MinIO服务
	minioService, err := services.NewMinIOService(&cfg.MinIO, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize MinIO service")
	}

	return &Services{
		UserService:             services.NewUserService(db, logger),
		ProjectService:          services.NewProjectService(db, logger),
		TemplateService:         services.NewTemplateService(db, logger),
		InstanceService:         services.NewInstanceService(db, logger),
		AutoSaveService:         services.NewAutoSaveService(db, logger),
		HistoryService:          services.NewHistoryService(db, logger),
		CacheService:            cacheService,
		JWTService:              services.NewJWTService(cfg, logger, cacheService),
		RBACService:             services.NewRBACService(db, logger, cacheService),
		RateLimitService:        services.NewRateLimitService(cacheService, logger),
		SMSService:              services.NewSMSService(cacheService, logger),
		DataAnalysisService:     dataAnalysisService,
		DataExportService:       dataExportService,
		VersionAwareDataService: versionAwareDataService,
		MinIOService:            minioService,
		MedicalService:          services.NewMedicalService(db, logger),
	}
}

type Handlers struct {
	UserHandler           *handlers.UserHandler
	ProjectHandler        *handlers.ProjectHandler
	TemplateHandler       *handlers.TemplateHandler
	InstanceHandler       *handlers.InstanceHandler
	HistoryHandler        *handlers.HistoryHandler
	DataManagementHandler *handlers.DataManagementHandler
	AvatarHandler         *handlers.AvatarHandler
	MedicalHandler        *handlers.MedicalHandler
}

func initializeHandlers(services *Services, cfg *config.Config, logger *logrus.Logger) *Handlers {
	return &Handlers{
		UserHandler:           handlers.NewUserHandler(services.UserService, services.JWTService, services.RateLimitService, services.SMSService, logger),
		ProjectHandler:        handlers.NewProjectHandler(services.ProjectService, logger),
		TemplateHandler:       handlers.NewTemplateHandler(services.TemplateService, services.ProjectService, logger, cfg),
		InstanceHandler:       handlers.NewInstanceHandler(services.InstanceService, logger),
		HistoryHandler:        handlers.NewHistoryHandler(services.HistoryService, logger),
		DataManagementHandler: handlers.NewDataManagementHandler(services.DataAnalysisService, services.DataExportService, services.VersionAwareDataService, logger),
		AvatarHandler:         handlers.NewAvatarHandler(services.UserService, services.MinIOService, logger),
		MedicalHandler:        handlers.NewMedicalHandler(services.MedicalService, logger),
	}
}
