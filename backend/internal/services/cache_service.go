package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"crf-backend/internal/config"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

type CacheService struct {
	client *redis.Client
	logger *logrus.Logger
}

func NewCacheService(cfg *config.Config, logger *logrus.Logger) (*CacheService, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
		PoolSize: cfg.Redis.PoolSize,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Redis.Timeout)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	logger.Info("Connected to Redis successfully")
	return &CacheService{
		client: client,
		logger: logger,
	}, nil
}

func (c *CacheService) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	if err := c.client.Set(ctx, key, jsonData, ttl).Err(); err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to set cache")
		return fmt.Errorf("failed to set cache: %w", err)
	}

	c.logger.WithFields(logrus.Fields{
		"key": key,
		"ttl": ttl,
	}).Debug("Cache set successfully")
	return nil
}

func (c *CacheService) Get(ctx context.Context, key string, dest interface{}) error {
	result, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("cache miss for key: %s", key)
		}
		c.logger.WithError(err).WithField("key", key).Error("Failed to get cache")
		return fmt.Errorf("failed to get cache: %w", err)
	}

	if err := json.Unmarshal([]byte(result), dest); err != nil {
		return fmt.Errorf("failed to unmarshal cache value: %w", err)
	}

	c.logger.WithField("key", key).Debug("Cache hit")
	return nil
}

func (c *CacheService) Delete(ctx context.Context, key string) error {
	if err := c.client.Del(ctx, key).Err(); err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to delete cache")
		return fmt.Errorf("failed to delete cache: %w", err)
	}

	c.logger.WithField("key", key).Debug("Cache deleted successfully")
	return nil
}

func (c *CacheService) DeletePattern(ctx context.Context, pattern string) error {
	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		c.logger.WithError(err).WithField("pattern", pattern).Error("Failed to get keys by pattern")
		return fmt.Errorf("failed to get keys by pattern: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	if err := c.client.Del(ctx, keys...).Err(); err != nil {
		c.logger.WithError(err).WithField("pattern", pattern).Error("Failed to delete cache by pattern")
		return fmt.Errorf("failed to delete cache by pattern: %w", err)
	}

	c.logger.WithFields(logrus.Fields{
		"pattern":   pattern,
		"key_count": len(keys),
	}).Debug("Cache pattern deleted successfully")
	return nil
}

func (c *CacheService) Exists(ctx context.Context, key string) (bool, error) {
	result, err := c.client.Exists(ctx, key).Result()
	if err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to check cache existence")
		return false, fmt.Errorf("failed to check cache existence: %w", err)
	}

	return result > 0, nil
}

func (c *CacheService) SetTTL(ctx context.Context, key string, ttl time.Duration) error {
	if err := c.client.Expire(ctx, key, ttl).Err(); err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to set TTL")
		return fmt.Errorf("failed to set TTL: %w", err)
	}

	c.logger.WithFields(logrus.Fields{
		"key": key,
		"ttl": ttl,
	}).Debug("TTL set successfully")
	return nil
}

func (c *CacheService) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	ttl, err := c.client.TTL(ctx, key).Result()
	if err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to get TTL")
		return 0, fmt.Errorf("failed to get TTL: %w", err)
	}

	return ttl, nil
}

// Hash operations for more complex caching
func (c *CacheService) HSet(ctx context.Context, key, field string, value interface{}) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	if err := c.client.HSet(ctx, key, field, jsonData).Err(); err != nil {
		c.logger.WithError(err).WithFields(logrus.Fields{
			"key":   key,
			"field": field,
		}).Error("Failed to set hash field")
		return fmt.Errorf("failed to set hash field: %w", err)
	}

	return nil
}

func (c *CacheService) HGet(ctx context.Context, key, field string, dest interface{}) error {
	result, err := c.client.HGet(ctx, key, field).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("hash field not found: %s.%s", key, field)
		}
		return fmt.Errorf("failed to get hash field: %w", err)
	}

	if err := json.Unmarshal([]byte(result), dest); err != nil {
		return fmt.Errorf("failed to unmarshal hash value: %w", err)
	}

	return nil
}

func (c *CacheService) HDel(ctx context.Context, key string, fields ...string) error {
	if err := c.client.HDel(ctx, key, fields...).Err(); err != nil {
		c.logger.WithError(err).WithFields(logrus.Fields{
			"key":    key,
			"fields": fields,
		}).Error("Failed to delete hash fields")
		return fmt.Errorf("failed to delete hash fields: %w", err)
	}

	return nil
}

// List operations
func (c *CacheService) LPush(ctx context.Context, key string, values ...interface{}) error {
	jsonValues := make([]interface{}, len(values))
	for i, value := range values {
		jsonData, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal value: %w", err)
		}
		jsonValues[i] = jsonData
	}

	if err := c.client.LPush(ctx, key, jsonValues...).Err(); err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to push to list")
		return fmt.Errorf("failed to push to list: %w", err)
	}

	return nil
}

func (c *CacheService) LRange(ctx context.Context, key string, start, stop int64, dest interface{}) error {
	results, err := c.client.LRange(ctx, key, start, stop).Result()
	if err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to get list range")
		return fmt.Errorf("failed to get list range: %w", err)
	}

	jsonData, err := json.Marshal(results)
	if err != nil {
		return fmt.Errorf("failed to marshal list results: %w", err)
	}

	if err := json.Unmarshal(jsonData, dest); err != nil {
		return fmt.Errorf("failed to unmarshal list results: %w", err)
	}

	return nil
}

// Counter operations
func (c *CacheService) Incr(ctx context.Context, key string) (int64, error) {
	result, err := c.client.Incr(ctx, key).Result()
	if err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to increment counter")
		return 0, fmt.Errorf("failed to increment counter: %w", err)
	}

	return result, nil
}

func (c *CacheService) IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	result, err := c.client.IncrBy(ctx, key, value).Result()
	if err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to increment counter by value")
		return 0, fmt.Errorf("failed to increment counter by value: %w", err)
	}

	return result, nil
}

func (c *CacheService) Decr(ctx context.Context, key string) (int64, error) {
	result, err := c.client.Decr(ctx, key).Result()
	if err != nil {
		c.logger.WithError(err).WithField("key", key).Error("Failed to decrement counter")
		return 0, fmt.Errorf("failed to decrement counter: %w", err)
	}

	return result, nil
}

// Close connection
func (c *CacheService) Close() error {
	return c.client.Close()
}

// Helper functions for common cache patterns
func (c *CacheService) GetOrSetFunc(ctx context.Context, key string, ttl time.Duration, fetchFunc func() (interface{}, error), dest interface{}) error {
	// Try to get from cache first
	err := c.Get(ctx, key, dest)
	if err == nil {
		return nil // Cache hit
	}

	// Cache miss, fetch data
	data, err := fetchFunc()
	if err != nil {
		return fmt.Errorf("failed to fetch data: %w", err)
	}

	// Set in cache
	if err := c.Set(ctx, key, data, ttl); err != nil {
		c.logger.WithError(err).WithField("key", key).Warn("Failed to set cache after fetch")
	}

	// Copy data to destination
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal fetched data: %w", err)
	}

	if err := json.Unmarshal(jsonData, dest); err != nil {
		return fmt.Errorf("failed to unmarshal fetched data: %w", err)
	}

	return nil
}
