package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"crf-backend/internal/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type RBACService struct {
	db           *gorm.DB
	logger       *logrus.Logger
	cacheService *CacheService
}

func NewRBACService(db *gorm.DB, logger *logrus.Logger, cacheService *CacheService) *RBACService {
	return &RBACService{
		db:           db,
		logger:       logger,
		cacheService: cacheService,
	}
}

// ===============================
// 权限检查核心方法
// ===============================

// CheckPermission 检查用户权限
func (s *RBACService) CheckPermission(ctx context.Context, userID uuid.UUID, resource, action string, projectID *uuid.UUID) (*models.PermissionResult, error) {
	// 先检查缓存
	if result, found := s.getPermissionFromCache(ctx, userID, resource, action, projectID); found {
		return result, nil
	}

	// 执行权限检查
	result, err := s.checkPermissionFromDB(ctx, userID, resource, action, projectID)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	s.cachePermissionResult(ctx, userID, resource, action, projectID, result)

	return result, nil
}

// checkPermissionFromDB 从数据库检查权限
func (s *RBACService) checkPermissionFromDB(ctx context.Context, userID uuid.UUID, resource, action string, projectID *uuid.UUID) (*models.PermissionResult, error) {
	// 检查全局权限
	globalPermission, err := s.checkGlobalPermission(ctx, userID, resource, action)
	if err != nil {
		return nil, err
	}
	if globalPermission.HasPermission {
		return globalPermission, nil
	}

	// 检查项目权限
	if projectID != nil {
		projectPermission, err := s.checkProjectPermission(ctx, userID, resource, action, *projectID)
		if err != nil {
			return nil, err
		}
		if projectPermission.HasPermission {
			return projectPermission, nil
		}
	}

	// 检查资源所有者权限
	ownPermission, err := s.checkOwnPermission(ctx, userID, resource, action)
	if err != nil {
		return nil, err
	}

	return ownPermission, nil
}

// checkGlobalPermission 检查全局权限
func (s *RBACService) checkGlobalPermission(ctx context.Context, userID uuid.UUID, resource, action string) (*models.PermissionResult, error) {
	var count int64
	err := s.db.WithContext(ctx).
		Table("user_roles ur").
		Joins("JOIN role_permissions rp ON ur.role_id = rp.role_id").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("ur.user_id = ? AND ur.project_id IS NULL", userID).
		Where("ur.deleted_at IS NULL").
		Where("rp.deleted_at IS NULL").
		Where("p.deleted_at IS NULL").
		Where("(ur.expires_at IS NULL OR ur.expires_at > ?)", time.Now()).
		Where("p.resource = ? AND p.action = ?", resource, action).
		Where("p.scope IN (?)", []string{models.ScopeGlobal, models.ScopeProject, models.ScopeOwn}).
		Count(&count).Error

	if err != nil {
		return nil, fmt.Errorf("failed to check global permission: %w", err)
	}

	return &models.PermissionResult{
		HasPermission: count > 0,
		Source:        "global_role",
		Reason:        fmt.Sprintf("Global permission check for %s:%s", resource, action),
	}, nil
}

// checkProjectPermission 检查项目权限
func (s *RBACService) checkProjectPermission(ctx context.Context, userID uuid.UUID, resource, action string, projectID uuid.UUID) (*models.PermissionResult, error) {
	var count int64
	err := s.db.WithContext(ctx).
		Table("user_roles ur").
		Joins("JOIN role_permissions rp ON ur.role_id = rp.role_id").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("ur.user_id = ? AND ur.project_id = ?", userID, projectID).
		Where("ur.deleted_at IS NULL").
		Where("rp.deleted_at IS NULL").
		Where("p.deleted_at IS NULL").
		Where("(ur.expires_at IS NULL OR ur.expires_at > ?)", time.Now()).
		Where("p.resource = ? AND p.action = ?", resource, action).
		Where("p.scope IN (?)", []string{models.ScopeProject, models.ScopeOwn}).
		Count(&count).Error

	if err != nil {
		return nil, fmt.Errorf("failed to check project permission: %w", err)
	}

	return &models.PermissionResult{
		HasPermission: count > 0,
		Source:        "project_role",
		Reason:        fmt.Sprintf("Project permission check for %s:%s in project %s", resource, action, projectID),
	}, nil
}

// checkOwnPermission 检查资源所有者权限
func (s *RBACService) checkOwnPermission(ctx context.Context, userID uuid.UUID, resource, action string) (*models.PermissionResult, error) {
	var count int64
	err := s.db.WithContext(ctx).
		Table("user_roles ur").
		Joins("JOIN role_permissions rp ON ur.role_id = rp.role_id").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("ur.user_id = ?", userID).
		Where("ur.deleted_at IS NULL").
		Where("rp.deleted_at IS NULL").
		Where("p.deleted_at IS NULL").
		Where("(ur.expires_at IS NULL OR ur.expires_at > ?)", time.Now()).
		Where("p.resource = ? AND p.action = ? AND p.scope = ?", resource, action, models.ScopeOwn).
		Count(&count).Error

	if err != nil {
		return nil, fmt.Errorf("failed to check own permission: %w", err)
	}

	return &models.PermissionResult{
		HasPermission: count > 0,
		Source:        "own_resource",
		Reason:        fmt.Sprintf("Own permission check for %s:%s", resource, action),
	}, nil
}

// ===============================
// 权限缓存方法
// ===============================

// getPermissionFromCache 从缓存获取权限
func (s *RBACService) getPermissionFromCache(ctx context.Context, userID uuid.UUID, resource, action string, projectID *uuid.UUID) (*models.PermissionResult, bool) {
	cacheKey := s.buildPermissionCacheKey(userID, resource, action, projectID)
	
	var result models.PermissionResult
	err := s.cacheService.Get(ctx, cacheKey, &result)
	if err != nil {
		return nil, false
	}

	return &result, true
}

// cachePermissionResult 缓存权限结果
func (s *RBACService) cachePermissionResult(ctx context.Context, userID uuid.UUID, resource, action string, projectID *uuid.UUID, result *models.PermissionResult) {
	cacheKey := s.buildPermissionCacheKey(userID, resource, action, projectID)
	
	// 缓存1小时
	ttl := time.Hour
	err := s.cacheService.Set(ctx, cacheKey, result, ttl)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to cache permission result")
	}
}

// buildPermissionCacheKey 构建权限缓存键
func (s *RBACService) buildPermissionCacheKey(userID uuid.UUID, resource, action string, projectID *uuid.UUID) string {
	if projectID != nil {
		return fmt.Sprintf("permission:%s:%s:%s:%s", userID, resource, action, *projectID)
	}
	return fmt.Sprintf("permission:%s:%s:%s", userID, resource, action)
}

// ClearUserPermissionCache 清除用户权限缓存
func (s *RBACService) ClearUserPermissionCache(ctx context.Context, userID uuid.UUID) error {
	pattern := fmt.Sprintf("permission:%s:*", userID)
	return s.cacheService.DeletePattern(ctx, pattern)
}

// ===============================
// 角色管理方法
// ===============================

// GetRoles 获取所有角色
func (s *RBACService) GetRoles(ctx context.Context, includePermissions bool) ([]models.Role, error) {
	var roles []models.Role
	query := s.db.WithContext(ctx)

	if includePermissions {
		query = query.Preload("Permissions")
	}

	err := query.Where("is_active = ?", true).Find(&roles).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get roles: %w", err)
	}

	return roles, nil
}

// GetRoleByID 根据ID获取角色
func (s *RBACService) GetRoleByID(ctx context.Context, roleID uuid.UUID) (*models.Role, error) {
	var role models.Role
	err := s.db.WithContext(ctx).
		Preload("Permissions").
		Where("id = ? AND is_active = ?", roleID, true).
		First(&role).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	return &role, nil
}

// CreateRole 创建角色
func (s *RBACService) CreateRole(ctx context.Context, role *models.Role) error {
	if role.Name == "" || role.DisplayName == "" {
		return errors.New("role name and display name are required")
	}

	// 检查名称是否重复
	var count int64
	err := s.db.WithContext(ctx).Model(&models.Role{}).Where("name = ?", role.Name).Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check role name: %w", err)
	}
	if count > 0 {
		return errors.New("role name already exists")
	}

	err = s.db.WithContext(ctx).Create(role).Error
	if err != nil {
		return fmt.Errorf("failed to create role: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"role_id":   role.ID,
		"role_name": role.Name,
	}).Info("Role created successfully")

	return nil
}

// UpdateRole 更新角色
func (s *RBACService) UpdateRole(ctx context.Context, roleID uuid.UUID, updates map[string]interface{}) error {
	// 检查角色是否存在
	var role models.Role
	err := s.db.WithContext(ctx).Where("id = ?", roleID).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 系统角色不能修改核心属性
	if role.IsSystem {
		if _, exists := updates["name"]; exists {
			return errors.New("cannot modify system role name")
		}
		if _, exists := updates["is_system"]; exists {
			return errors.New("cannot modify system role flag")
		}
	}

	err = s.db.WithContext(ctx).Model(&role).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("failed to update role: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"role_id": roleID,
		"updates": updates,
	}).Info("Role updated successfully")

	return nil
}

// DeleteRole 删除角色
func (s *RBACService) DeleteRole(ctx context.Context, roleID uuid.UUID) error {
	// 检查角色是否存在
	var role models.Role
	err := s.db.WithContext(ctx).Where("id = ?", roleID).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 系统角色不能删除
	if role.IsSystem {
		return errors.New("cannot delete system role")
	}

	// 检查是否有用户使用此角色
	var userRoleCount int64
	err = s.db.WithContext(ctx).Model(&models.UserRole{}).Where("role_id = ?", roleID).Count(&userRoleCount).Error
	if err != nil {
		return fmt.Errorf("failed to check user roles: %w", err)
	}
	if userRoleCount > 0 {
		return errors.New("cannot delete role that is assigned to users")
	}

	// 删除角色权限关联
	err = s.db.WithContext(ctx).Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete role permissions: %w", err)
	}

	// 删除角色
	err = s.db.WithContext(ctx).Delete(&role).Error
	if err != nil {
		return fmt.Errorf("failed to delete role: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"role_id":   roleID,
		"role_name": role.Name,
	}).Info("Role deleted successfully")

	return nil
}

// ===============================
// 权限管理方法
// ===============================

// GetPermissions 获取所有权限
func (s *RBACService) GetPermissions(ctx context.Context) ([]models.Permission, error) {
	var permissions []models.Permission
	err := s.db.WithContext(ctx).Find(&permissions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions: %w", err)
	}

	return permissions, nil
}

// GetPermissionsByResource 根据资源获取权限
func (s *RBACService) GetPermissionsByResource(ctx context.Context, resource string) ([]models.Permission, error) {
	var permissions []models.Permission
	err := s.db.WithContext(ctx).Where("resource = ?", resource).Find(&permissions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions by resource: %w", err)
	}

	return permissions, nil
}

// AssignPermissionsToRole 为角色分配权限
func (s *RBACService) AssignPermissionsToRole(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error {
	// 检查角色是否存在
	var role models.Role
	err := s.db.WithContext(ctx).Where("id = ?", roleID).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 事务执行
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除现有权限
		err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error
		if err != nil {
			return fmt.Errorf("failed to delete existing permissions: %w", err)
		}

		// 添加新权限
		for _, permissionID := range permissionIDs {
			rolePermission := &models.RolePermission{
				RoleID:       roleID,
				PermissionID: permissionID,
			}
			err := tx.Create(rolePermission).Error
			if err != nil {
				return fmt.Errorf("failed to create role permission: %w", err)
			}
		}

		return nil
	})
}

// ===============================
// 用户角色管理方法
// ===============================

// GetUserRoles 获取用户角色
func (s *RBACService) GetUserRoles(ctx context.Context, userID uuid.UUID) (*models.UserRoleInfo, error) {
	var userRoles []models.UserRole
	err := s.db.WithContext(ctx).
		Preload("Role").
		Preload("Project").
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Find(&userRoles).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	// 获取用户信息
	var user models.User
	err = s.db.WithContext(ctx).Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 构建响应
	userRoleInfo := &models.UserRoleInfo{
		UserID:   user.ID,
		Username: user.Username,
		Email:    user.Email,
		FullName: user.FullName,
		Roles:    make([]models.RoleInfo, 0),
		GlobalRoles: make([]models.RoleInfo, 0),
	}

	for _, ur := range userRoles {
		roleInfo := models.RoleInfo{
			ID:          ur.Role.ID,
			Name:        ur.Role.Name,
			DisplayName: ur.Role.DisplayName,
			AssignedAt:  ur.AssignedAt,
			ExpiresAt:   ur.ExpiresAt,
		}

		if ur.ProjectID != nil {
			roleInfo.ProjectID = ur.ProjectID
			if ur.Project != nil {
				roleInfo.ProjectName = ur.Project.Name
			}
			userRoleInfo.Roles = append(userRoleInfo.Roles, roleInfo)
		} else {
			userRoleInfo.GlobalRoles = append(userRoleInfo.GlobalRoles, roleInfo)
		}
	}

	return userRoleInfo, nil
}

// AssignRoleToUser 为用户分配角色
func (s *RBACService) AssignRoleToUser(ctx context.Context, req *models.RoleAssignmentRequest, assignedBy uuid.UUID) error {
	// 检查用户是否存在
	var user models.User
	err := s.db.WithContext(ctx).Where("id = ?", req.UserID).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 检查角色是否存在
	var role models.Role
	err = s.db.WithContext(ctx).Where("id = ? AND is_active = ?", req.RoleID, true).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 检查是否已经分配了相同角色
	var existingCount int64
	query := s.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ?", req.UserID, req.RoleID)
	
	if req.ProjectID != nil {
		query = query.Where("project_id = ?", *req.ProjectID)
	} else {
		query = query.Where("project_id IS NULL")
	}
	
	err = query.Count(&existingCount).Error
	if err != nil {
		return fmt.Errorf("failed to check existing role: %w", err)
	}
	if existingCount > 0 {
		return errors.New("user already has this role")
	}

	// 创建用户角色关联
	userRole := &models.UserRole{
		UserID:     req.UserID,
		RoleID:     req.RoleID,
		ProjectID:  req.ProjectID,
		AssignedBy: assignedBy,
		ExpiresAt:  req.ExpiresAt,
	}

	err = s.db.WithContext(ctx).Create(userRole).Error
	if err != nil {
		return fmt.Errorf("failed to assign role to user: %w", err)
	}

	// 清除用户权限缓存
	s.ClearUserPermissionCache(ctx, req.UserID)

	s.logger.WithFields(logrus.Fields{
		"user_id":     req.UserID,
		"role_id":     req.RoleID,
		"project_id":  req.ProjectID,
		"assigned_by": assignedBy,
	}).Info("Role assigned to user successfully")

	return nil
}

// RemoveRoleFromUser 移除用户角色
func (s *RBACService) RemoveRoleFromUser(ctx context.Context, userID, roleID uuid.UUID, projectID *uuid.UUID) error {
	query := s.db.WithContext(ctx).Where("user_id = ? AND role_id = ?", userID, roleID)
	
	if projectID != nil {
		query = query.Where("project_id = ?", *projectID)
	} else {
		query = query.Where("project_id IS NULL")
	}

	err := query.Delete(&models.UserRole{}).Error
	if err != nil {
		return fmt.Errorf("failed to remove role from user: %w", err)
	}

	// 清除用户权限缓存
	s.ClearUserPermissionCache(ctx, userID)

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"role_id":    roleID,
		"project_id": projectID,
	}).Info("Role removed from user successfully")

	return nil
}

// ===============================
// 系统权限检查方法
// ===============================

// IsSystemAdmin 检查是否是系统管理员
func (s *RBACService) IsSystemAdmin(ctx context.Context, userID uuid.UUID) (bool, error) {
	result, err := s.CheckPermission(ctx, userID, models.ResourceSystem, models.ActionConfig, nil)
	if err != nil {
		return false, err
	}
	return result.HasPermission, nil
}

// CanManageUsers 检查是否可以管理用户
func (s *RBACService) CanManageUsers(ctx context.Context, userID uuid.UUID) (bool, error) {
	result, err := s.CheckPermission(ctx, userID, models.ResourceUser, models.ActionCreate, nil)
	if err != nil {
		return false, err
	}
	return result.HasPermission, nil
}

// CanManageRoles 检查是否可以管理角色
func (s *RBACService) CanManageRoles(ctx context.Context, userID uuid.UUID) (bool, error) {
	result, err := s.CheckPermission(ctx, userID, models.ResourceRole, models.ActionCreate, nil)
	if err != nil {
		return false, err
	}
	return result.HasPermission, nil
}

// CanAccessProject 检查是否可以访问项目
func (s *RBACService) CanAccessProject(ctx context.Context, userID, projectID uuid.UUID) (bool, error) {
	result, err := s.CheckPermission(ctx, userID, models.ResourceProject, models.ActionRead, &projectID)
	if err != nil {
		return false, err
	}
	return result.HasPermission, nil
}

// ===============================
// 批量操作方法
// ===============================

// BatchAssignRoles 批量分配角色
func (s *RBACService) BatchAssignRoles(ctx context.Context, userID uuid.UUID, roleIDs []uuid.UUID, projectID *uuid.UUID, assignedBy uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, roleID := range roleIDs {
			req := &models.RoleAssignmentRequest{
				UserID:    userID,
				RoleID:    roleID,
				ProjectID: projectID,
			}
			
			err := s.AssignRoleToUser(ctx, req, assignedBy)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// CleanupExpiredRoles 清理过期角色
func (s *RBACService) CleanupExpiredRoles(ctx context.Context) error {
	err := s.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Delete(&models.UserRole{}).Error
	
	if err != nil {
		return fmt.Errorf("failed to cleanup expired roles: %w", err)
	}

	s.logger.Info("Expired roles cleaned up successfully")
	return nil
}

// CleanupExpiredPermissionCache 清理过期权限缓存
func (s *RBACService) CleanupExpiredPermissionCache(ctx context.Context) error {
	err := s.db.WithContext(ctx).
		Where("cache_expires_at < ?", time.Now()).
		Delete(&models.UserPermissionCache{}).Error
	
	if err != nil {
		return fmt.Errorf("failed to cleanup expired permission cache: %w", err)
	}

	s.logger.Info("Expired permission cache cleaned up successfully")
	return nil
}