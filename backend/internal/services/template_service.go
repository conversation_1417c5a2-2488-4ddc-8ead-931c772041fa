package services

import (
	"crf-backend/internal/models"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type TemplateService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewTemplateService(db *gorm.DB, logger *logrus.Logger) *TemplateService {
	return &TemplateService{
		db:     db,
		logger: logger,
	}
}

func (s *TemplateService) CreateTemplate(template *models.CRFTemplate) error {
	if err := s.db.Create(template).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create template")
		return fmt.Errorf("failed to create template: %w", err)
	}

	s.logger.WithField("template_id", template.ID).Info("Template created successfully")
	return nil
}

func (s *TemplateService) GetTemplateByID(id uuid.UUID) (*models.CRFTemplate, error) {
	var template models.CRFTemplate
	if err := s.db.Preload("Creator").Where("id = ? AND is_deleted = ?", id, false).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}
	return &template, nil
}

func (s *TemplateService) GetTemplates(projectID uuid.UUID, limit, offset int) ([]models.CRFTemplate, int64, error) {
	var templates []models.CRFTemplate
	var total int64

	query := s.db.Model(&models.CRFTemplate{}).Where("is_deleted = ?", false)
	if projectID != uuid.Nil {
		query = query.Where("project_id = ?", projectID)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}

	// Get templates with pagination
	if err := query.Preload("Creator").
		Limit(limit).Offset(offset).
		Order("updated_at DESC").
		Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get templates: %w", err)
	}

	return templates, total, nil
}

// GetDeletedTemplates 获取已删除的模板列表
func (s *TemplateService) GetDeletedTemplates(projectID uuid.UUID, limit, offset int) ([]models.CRFTemplate, int64, error) {
	var templates []models.CRFTemplate
	var total int64

	query := s.db.Model(&models.CRFTemplate{}).Where("is_deleted = ?", true)
	if projectID != uuid.Nil {
		query = query.Where("project_id = ?", projectID)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count deleted templates: %w", err)
	}

	// Get templates with pagination
	if err := query.Preload("Creator").
		Limit(limit).Offset(offset).
		Order("deleted_at DESC").
		Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get deleted templates: %w", err)
	}

	return templates, total, nil
}

func (s *TemplateService) UpdateTemplate(id uuid.UUID, updates map[string]interface{}) error {
	if err := s.db.Model(&models.CRFTemplate{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("template_id", id).Error("Failed to update template")
		return fmt.Errorf("failed to update template: %w", err)
	}

	s.logger.WithField("template_id", id).Info("Template updated successfully")
	return nil
}

func (s *TemplateService) DeleteTemplate(id uuid.UUID) error {
	// First check if template has any instances
	var instanceCount int64
	if err := s.db.Model(&models.CRFInstance{}).Where("template_id = ? AND is_deleted = ?", id, false).Count(&instanceCount).Error; err != nil {
		return fmt.Errorf("failed to check template instances: %w", err)
	}

	if instanceCount > 0 {
		return fmt.Errorf("cannot delete template with existing instances")
	}

	// 软删除：设置 is_deleted = true 和 deleted_at 时间戳
	now := time.Now()
	updates := map[string]interface{}{
		"is_deleted": true,
		"deleted_at": &now,
	}

	if err := s.db.Model(&models.CRFTemplate{}).Where("id = ? AND is_deleted = ?", id, false).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("template_id", id).Error("Failed to delete template")
		return fmt.Errorf("failed to delete template: %w", err)
	}

	s.logger.WithField("template_id", id).Info("Template deleted successfully")
	return nil
}

// RestoreTemplate 恢复已删除的模板
func (s *TemplateService) RestoreTemplate(id uuid.UUID) error {
	// 检查模板是否存在且已被删除
	var template models.CRFTemplate
	if err := s.db.Where("id = ? AND is_deleted = ?", id, true).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("deleted template not found")
		}
		return fmt.Errorf("failed to get deleted template: %w", err)
	}

	// 恢复模板：设置 is_deleted = false 并清除 deleted_at
	updates := map[string]interface{}{
		"is_deleted": false,
		"deleted_at": nil,
	}

	if err := s.db.Model(&models.CRFTemplate{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("template_id", id).Error("Failed to restore template")
		return fmt.Errorf("failed to restore template: %w", err)
	}

	s.logger.WithField("template_id", id).Info("Template restored successfully")
	return nil
}

func (s *TemplateService) PublishTemplate(id uuid.UUID, publishedBy uuid.UUID) (*models.CRFVersion, error) {
	// Get template
	template, err := s.GetTemplateByID(id)
	if err != nil {
		return nil, err
	}

	// 获取当前最新版本号
	var latestVersion string
	err = s.db.Model(&models.CRFVersion{}).
		Where("template_id = ?", id).
		Order("created_at DESC").
		Limit(1).
		Pluck("version", &latestVersion).Error

	// 如果没有版本或查询出错，使用模板的默认版本
	if err != nil || latestVersion == "" {
		latestVersion = template.Version
		if latestVersion == "" {
			latestVersion = "1.0.0"
		}
	}

	// 生成新版本号（递增补丁版本）
	newVersion, err := s.incrementVersion(latestVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new version: %w", err)
	}

	// Create version from current template
	now := time.Now()
	version := &models.CRFVersion{
		TemplateID:   id,
		Version:      newVersion,
		Title:        template.Title,
		Description:  template.Description,
		SnapshotData: template.TemplateData, // Store current template data as snapshot
		ChangeLog:    fmt.Sprintf("发布版本 %s", newVersion),
		Status:       "published",
		CreatedBy:    publishedBy,
		PublishedBy:  &publishedBy,
		PublishedAt:  &now,
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create version record
	if err := tx.Create(version).Error; err != nil {
		tx.Rollback()
		s.logger.WithError(err).Error("Failed to create version record")
		return nil, fmt.Errorf("failed to create version: %w", err)
	}

	// Update template status and published info
	updates := map[string]interface{}{
		"version":      newVersion,
		"published_at": &now,
		"published_by": &publishedBy,
	}

	// 如果模板还没有发布过，则设置为published状态
	if template.Status != "published" {
		updates["status"] = "published"
	}

	if err := tx.Model(&models.CRFTemplate{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		tx.Rollback()
		s.logger.WithError(err).Error("Failed to update template status")
		return nil, fmt.Errorf("failed to update template status: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		s.logger.WithError(err).Error("Failed to commit transaction")
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"template_id":  id,
		"version_id":   version.ID,
		"version":      version.Version,
		"published_by": publishedBy,
	}).Info("Template published successfully")

	return version, nil
}

func (s *TemplateService) GetTemplateVersions(templateID uuid.UUID) ([]models.CRFVersion, error) {
	var versions []models.CRFVersion
	if err := s.db.Where("template_id = ?", templateID).
		Preload("Creator").
		Preload("Publisher").
		Order("created_at DESC").
		Find(&versions).Error; err != nil {
		return nil, fmt.Errorf("failed to get template versions: %w", err)
	}
	return versions, nil
}

func (s *TemplateService) CreateTemplateVersion(version *models.CRFVersion) error {
	if err := s.db.Create(version).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create template version")
		return fmt.Errorf("failed to create template version: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"template_id": version.TemplateID,
		"version_id":  version.ID,
		"version":     version.Version,
	}).Info("Template version created successfully")

	return nil
}

// SaveTemplateDraft 手动保存模板草稿
func (s *TemplateService) SaveTemplateDraft(id uuid.UUID, templateData interface{}, userID uuid.UUID) error {
	// 检查模板是否存在
	var template models.CRFTemplate
	if err := s.db.Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("template not found")
		}
		return fmt.Errorf("failed to get template: %w", err)
	}

	// 只有创建者可以保存草稿
	if template.CreatedBy != userID {
		return fmt.Errorf("only template creator can save draft")
	}

	// 将templateData转换为JSON格式
	jsonData, err := json.Marshal(templateData)
	if err != nil {
		s.logger.WithError(err).Error("Failed to marshal template data")
		return fmt.Errorf("failed to marshal template data: %w", err)
	}

	// 更新模板数据
	updates := map[string]interface{}{
		"template_data": datatypes.JSON(jsonData),
		"updated_by":    userID,
	}

	if err := s.db.Model(&models.CRFTemplate{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("template_id", id).Error("Failed to save template draft")
		return fmt.Errorf("failed to save template draft: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"template_id": id,
		"user_id":     userID,
	}).Info("Template draft saved successfully")

	return nil
}

// GetPublicAccessLink 获取已发布模板的公共访问链接
func (s *TemplateService) GetPublicAccessLink(templateID uuid.UUID) (string, error) {
	template, err := s.GetTemplateByID(templateID)
	if err != nil {
		return "", err
	}

	if template.Status != "published" {
		return "", fmt.Errorf("template is not published")
	}

	// 生成访问链接
	accessLink := fmt.Sprintf("/form/fill/%s", templateID.String())

	s.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"access_link": accessLink,
	}).Info("Generated access link for published template")

	return accessLink, nil
}

// GetTemplateStats 获取模板统计信息
func (s *TemplateService) GetTemplateStats(templateID uuid.UUID) (map[string]interface{}, error) {
	var stats map[string]interface{}

	// 获取模板基本信息
	template, err := s.GetTemplateByID(templateID)
	if err != nil {
		return nil, err
	}

	// 获取版本统计
	var versionStats struct {
		Total     int64 `json:"total"`
		Published int64 `json:"published"`
		Draft     int64 `json:"draft"`
	}

	s.db.Model(&models.CRFVersion{}).Where("template_id = ?", templateID).Count(&versionStats.Total)
	s.db.Model(&models.CRFVersion{}).Where("template_id = ? AND status = ?", templateID, "published").Count(&versionStats.Published)
	s.db.Model(&models.CRFVersion{}).Where("template_id = ? AND status = ?", templateID, "draft").Count(&versionStats.Draft)

	// 获取实例统计
	var instanceCount int64
	s.db.Model(&models.CRFInstance{}).Where("template_id = ?", templateID).Count(&instanceCount)

	stats = map[string]interface{}{
		"template_id":    templateID,
		"status":         template.Status,
		"usage_count":    template.UsageCount,
		"instance_count": instanceCount,
		"version_stats":  versionStats,
		"published_at":   template.PublishedAt,
		"published_by":   template.PublishedBy,
	}

	return stats, nil
}

// incrementVersion 递增版本号（补丁版本）
func (s *TemplateService) incrementVersion(version string) (string, error) {
	// 解析版本号 (格式: major.minor.patch)
	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return "", fmt.Errorf("invalid version format: %s", version)
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return "", fmt.Errorf("invalid major version: %s", parts[0])
	}

	minor, err := strconv.Atoi(parts[1])
	if err != nil {
		return "", fmt.Errorf("invalid minor version: %s", parts[1])
	}

	patch, err := strconv.Atoi(parts[2])
	if err != nil {
		return "", fmt.Errorf("invalid patch version: %s", parts[2])
	}

	// 递增补丁版本
	patch++

	return fmt.Sprintf("%d.%d.%d", major, minor, patch), nil
}

// RollbackToVersion 回滚模板到指定版本
func (s *TemplateService) RollbackToVersion(templateID, versionID uuid.UUID, userID uuid.UUID) error {
	// 获取指定版本
	var version models.CRFVersion
	if err := s.db.Where("id = ?", versionID).First(&version).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("version not found")
		}
		return fmt.Errorf("failed to get version: %w", err)
	}

	// 验证版本是否属于指定模板
	if version.TemplateID != templateID {
		return fmt.Errorf("version does not belong to template")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新模板数据为版本快照数据
	updates := map[string]interface{}{
		"template_data": version.SnapshotData,
		"version":       version.Version,
		"updated_by":    userID,
	}

	if err := tx.Model(&models.CRFTemplate{}).Where("id = ?", templateID).Updates(updates).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update template: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"version_id":  versionID,
		"version":     version.Version,
		"user_id":     userID,
	}).Info("Template rolled back successfully")

	return nil
}
