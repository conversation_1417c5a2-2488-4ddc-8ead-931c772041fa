package services

import (
	"crf-backend/internal/models"
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type ProjectService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewProjectService(db *gorm.DB, logger *logrus.Logger) *ProjectService {
	return &ProjectService{
		db:     db,
		logger: logger,
	}
}

func (s *ProjectService) CreateProject(project *models.Project) error {
	if err := s.db.Create(project).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create project")
		return fmt.Errorf("failed to create project: %w", err)
	}

	s.logger.WithField("project_id", project.ID).Info("Project created successfully")
	return nil
}

func (s *ProjectService) GetProjectByID(id uuid.UUID) (*models.Project, error) {
	var project models.Project
	if err := s.db.Preload("Creator").Where("id = ?", id).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("project not found")
		}
		return nil, fmt.Errorf("failed to get project: %w", err)
	}
	return &project, nil
}

func (s *ProjectService) GetProjects(userID uuid.UUID, limit, offset int) ([]models.Project, int64, error) {
	var projects []models.Project
	var total int64

	query := s.db.Model(&models.Project{})
	if userID != uuid.Nil {
		query = query.Where("created_by = ?", userID)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count projects: %w", err)
	}

	// Get projects with pagination
	if err := query.Preload("Creator").
		Limit(limit).Offset(offset).
		Order("updated_at DESC").
		Find(&projects).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get projects: %w", err)
	}

	return projects, total, nil
}

func (s *ProjectService) UpdateProject(id uuid.UUID, updates map[string]interface{}) error {
	if err := s.db.Model(&models.Project{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("project_id", id).Error("Failed to update project")
		return fmt.Errorf("failed to update project: %w", err)
	}

	s.logger.WithField("project_id", id).Info("Project updated successfully")
	return nil
}

func (s *ProjectService) GetOrCreateDefaultProject(userID uuid.UUID) (*models.Project, error) {
	// 首先尝试获取用户的第一个项目
	projects, _, err := s.GetProjects(userID, 1, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get user projects: %w", err)
	}

	// 如果用户已有项目，返回第一个
	if len(projects) > 0 {
		return &projects[0], nil
	}

	// 如果用户没有项目，创建默认项目
	defaultProject := &models.Project{
		Name:        "默认项目",
		Description: "系统自动创建的默认项目",
		CreatedBy:   userID,
		Status:      "active",
	}

	if err := s.CreateProject(defaultProject); err != nil {
		return nil, fmt.Errorf("failed to create default project: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"project_id": defaultProject.ID,
	}).Info("Default project created for user")

	return defaultProject, nil
}

func (s *ProjectService) DeleteProject(id uuid.UUID) error {
	// Check if project has templates
	var templateCount int64
	if err := s.db.Model(&models.CRFTemplate{}).Where("project_id = ?", id).Count(&templateCount).Error; err != nil {
		return fmt.Errorf("failed to check project templates: %w", err)
	}

	if templateCount > 0 {
		return fmt.Errorf("cannot delete project with existing templates")
	}

	if err := s.db.Delete(&models.Project{}, "id = ?", id).Error; err != nil {
		s.logger.WithError(err).WithField("project_id", id).Error("Failed to delete project")
		return fmt.Errorf("failed to delete project: %w", err)
	}

	s.logger.WithField("project_id", id).Info("Project deleted successfully")
	return nil
}

type HistoryService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewHistoryService(db *gorm.DB, logger *logrus.Logger) *HistoryService {
	return &HistoryService{
		db:     db,
		logger: logger,
	}
}

func (s *HistoryService) CreateHistoryEntry(entry *models.OperationHistory) error {
	if err := s.db.Create(entry).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create history entry")
		return fmt.Errorf("failed to create history entry: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"history_id":    entry.ID,
		"user_id":       entry.UserID,
		"resource_type": entry.ResourceType,
		"resource_id":   entry.ResourceID,
		"action":        entry.Action,
	}).Debug("History entry created successfully")

	return nil
}

func (s *HistoryService) GetHistory(resourceType string, resourceID uuid.UUID, limit, offset int) ([]models.OperationHistory, int64, error) {
	var history []models.OperationHistory
	var total int64

	query := s.db.Model(&models.OperationHistory{}).
		Where("resource_type = ? AND resource_id = ?", resourceType, resourceID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count history entries: %w", err)
	}

	// Get history with pagination
	if err := query.Preload("User").
		Limit(limit).Offset(offset).
		Order("created_at DESC").
		Find(&history).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get history: %w", err)
	}

	return history, total, nil
}

func (s *HistoryService) GetUserHistory(userID uuid.UUID, limit, offset int) ([]models.OperationHistory, int64, error) {
	var history []models.OperationHistory
	var total int64

	query := s.db.Model(&models.OperationHistory{}).Where("user_id = ?", userID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count user history entries: %w", err)
	}

	// Get history with pagination
	if err := query.Limit(limit).Offset(offset).
		Order("created_at DESC").
		Find(&history).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get user history: %w", err)
	}

	return history, total, nil
}
