package services

import (
	"context"
	"fmt"
	"time"

	"crf-backend/internal/models"
	"github.com/sirupsen/logrus"
)

type UserSessionService struct {
	cacheService *CacheService
	logger       *logrus.Logger
}

type SessionData struct {
	UserID       string    `json:"user_id"`
	Username     string    `json:"username"`
	Role         string    `json:"role"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	LoginTime    time.Time `json:"login_time"`
	LastActivity time.Time `json:"last_activity"`
	TokenJTI     string    `json:"token_jti"`
}

func NewUserSessionService(cacheService *CacheService, logger *logrus.Logger) *UserSessionService {
	return &UserSessionService{
		cacheService: cacheService,
		logger:       logger,
	}
}

// CreateUserSession 创建用户会话
func (s *UserSessionService) CreateUserSession(ctx context.Context, user *models.User, tokenJTI, ipAddress, userAgent string) error {
	sessionData := &SessionData{
		UserID:       user.ID.String(),
		Username:     user.Username,
		Role:         user.Role,
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
		LoginTime:    time.Now(),
		LastActivity: time.Now(),
		TokenJTI:     tokenJTI,
	}

	// 存储会话数据
	sessionKey := fmt.Sprintf("user:session:%s", user.ID.String())
	err := s.cacheService.Set(ctx, sessionKey, sessionData, 7*24*time.Hour) // 7天过期
	if err != nil {
		s.logger.WithError(err).Error("Failed to create user session")
		return fmt.Errorf("failed to create user session: %w", err)
	}

	// 添加到用户活跃列表
	activeKey := fmt.Sprintf("user:active:%s", user.ID.String())
	err = s.cacheService.Set(ctx, activeKey, true, 30*time.Minute) // 30分钟活跃期
	if err != nil {
		s.logger.WithError(err).Warn("Failed to set user active status")
	}

	// 记录用户登录历史
	s.recordLoginHistory(ctx, user, ipAddress, userAgent)

	s.logger.WithFields(logrus.Fields{
		"user_id":    user.ID,
		"username":   user.Username,
		"ip":         ipAddress,
		"user_agent": userAgent,
	}).Info("User session created successfully")

	return nil
}

// GetUserSession 获取用户会话
func (s *UserSessionService) GetUserSession(ctx context.Context, userID string) (*SessionData, error) {
	sessionKey := fmt.Sprintf("user:session:%s", userID)

	var sessionData SessionData
	err := s.cacheService.Get(ctx, sessionKey, &sessionData)
	if err != nil {
		return nil, fmt.Errorf("user session not found: %w", err)
	}

	return &sessionData, nil
}

// UpdateUserActivity 更新用户活跃状态
func (s *UserSessionService) UpdateUserActivity(ctx context.Context, userID string) error {
	// 更新会话最后活跃时间
	sessionKey := fmt.Sprintf("user:session:%s", userID)

	var sessionData SessionData
	err := s.cacheService.Get(ctx, sessionKey, &sessionData)
	if err != nil {
		return fmt.Errorf("user session not found: %w", err)
	}

	sessionData.LastActivity = time.Now()
	err = s.cacheService.Set(ctx, sessionKey, sessionData, 7*24*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to update user session: %w", err)
	}

	// 更新活跃状态
	activeKey := fmt.Sprintf("user:active:%s", userID)
	err = s.cacheService.Set(ctx, activeKey, true, 30*time.Minute)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to update user active status")
	}

	return nil
}

// IsUserActive 检查用户是否在线
func (s *UserSessionService) IsUserActive(ctx context.Context, userID string) (bool, error) {
	activeKey := fmt.Sprintf("user:active:%s", userID)

	exists, err := s.cacheService.Exists(ctx, activeKey)
	if err != nil {
		return false, fmt.Errorf("failed to check user active status: %w", err)
	}

	return exists, nil
}

// DeleteUserSession 删除用户会话
func (s *UserSessionService) DeleteUserSession(ctx context.Context, userID string) error {
	sessionKey := fmt.Sprintf("user:session:%s", userID)
	activeKey := fmt.Sprintf("user:active:%s", userID)

	// 删除会话数据
	err := s.cacheService.Delete(ctx, sessionKey)
	if err != nil {
		s.logger.WithError(err).Error("Failed to delete user session")
		return fmt.Errorf("failed to delete user session: %w", err)
	}

	// 删除活跃状态
	err = s.cacheService.Delete(ctx, activeKey)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to delete user active status")
	}

	s.logger.WithField("user_id", userID).Info("User session deleted successfully")
	return nil
}

// GetActiveUsers 获取活跃用户列表
func (s *UserSessionService) GetActiveUsers(ctx context.Context) ([]string, error) {
	// 使用pattern匹配获取所有活跃用户

	// 这里需要实现一个获取匹配keys的方法
	// 由于当前CacheService没有这个方法，我们先返回空列表
	// 在实际实现中，你可能需要扩展CacheService来支持这个功能

	s.logger.Info("Getting active users (placeholder implementation)")
	return []string{}, nil
}

// GetUserLoginHistory 获取用户登录历史
func (s *UserSessionService) GetUserLoginHistory(ctx context.Context, userID string, limit int) ([]map[string]interface{}, error) {
	historyKey := fmt.Sprintf("user:login_history:%s", userID)

	var history []map[string]interface{}
	err := s.cacheService.LRange(ctx, historyKey, 0, int64(limit-1), &history)
	if err != nil {
		return nil, fmt.Errorf("failed to get user login history: %w", err)
	}

	return history, nil
}

// recordLoginHistory 记录用户登录历史
func (s *UserSessionService) recordLoginHistory(ctx context.Context, user *models.User, ipAddress, userAgent string) {
	historyKey := fmt.Sprintf("user:login_history:%s", user.ID.String())

	loginRecord := map[string]interface{}{
		"user_id":    user.ID.String(),
		"username":   user.Username,
		"ip_address": ipAddress,
		"user_agent": userAgent,
		"login_time": time.Now(),
	}

	// 添加到历史记录列表（最多保存100条）
	err := s.cacheService.LPush(ctx, historyKey, loginRecord)
	if err != nil {
		s.logger.WithError(err).Error("Failed to record login history")
		return
	}

	// 设置过期时间（30天）
	s.cacheService.SetTTL(ctx, historyKey, 30*24*time.Hour)

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
		"ip":       ipAddress,
	}).Debug("Login history recorded")
}

// ForceLogoutUser 强制用户下线
func (s *UserSessionService) ForceLogoutUser(ctx context.Context, userID string) error {
	// 获取用户会话信息
	sessionData, err := s.GetUserSession(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user session: %w", err)
	}

	// 将用户的token加入黑名单
	if sessionData.TokenJTI != "" {
		blacklistKey := fmt.Sprintf("jwt:blacklist:%s", sessionData.TokenJTI)
		// 设置较长的过期时间，确保token在其自然过期前都无效
		err = s.cacheService.Set(ctx, blacklistKey, true, 24*time.Hour)
		if err != nil {
			s.logger.WithError(err).Error("Failed to blacklist user token")
		}
	}

	// 删除用户会话
	err = s.DeleteUserSession(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to delete user session: %w", err)
	}

	s.logger.WithField("user_id", userID).Info("User force logged out successfully")
	return nil
}

// GetUserSessionStats 获取用户会话统计信息
func (s *UserSessionService) GetUserSessionStats(ctx context.Context, userID string) (map[string]interface{}, error) {
	sessionData, err := s.GetUserSession(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}

	isActive, err := s.IsUserActive(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check user active status: %w", err)
	}

	loginHistory, err := s.GetUserLoginHistory(ctx, userID, 10)
	if err != nil {
		loginHistory = []map[string]interface{}{} // 如果获取失败，返回空数组
	}

	stats := map[string]interface{}{
		"user_id":          sessionData.UserID,
		"username":         sessionData.Username,
		"role":             sessionData.Role,
		"is_active":        isActive,
		"login_time":       sessionData.LoginTime,
		"last_activity":    sessionData.LastActivity,
		"session_duration": time.Since(sessionData.LoginTime).String(),
		"ip_address":       sessionData.IPAddress,
		"user_agent":       sessionData.UserAgent,
		"login_history":    loginHistory,
	}

	return stats, nil
}

// CleanupExpiredSessions 清理过期会话
func (s *UserSessionService) CleanupExpiredSessions(ctx context.Context) error {
	// Redis会自动清理过期的key，这里只是记录日志
	s.logger.Info("Session cleanup completed (handled by Redis TTL)")
	return nil
}
