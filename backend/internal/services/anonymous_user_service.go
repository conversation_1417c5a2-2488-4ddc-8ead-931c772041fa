package services

import (
	"crf-backend/internal/models"
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type AnonymousUserService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewAnonymousUserService(db *gorm.DB, logger *logrus.Logger) *AnonymousUserService {
	return &AnonymousUserService{
		db:     db,
		logger: logger,
	}
}

// GetOrCreateAnonymousUser 获取或创建匿名用户
func (s *AnonymousUserService) GetOrCreateAnonymousUser() (*models.User, error) {
	// 固定的匿名用户ID
	anonymousUserID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	// 首先尝试获取现有的匿名用户
	var user models.User
	err := s.db.Where("id = ?", anonymousUserID).First(&user).Error

	if err == nil {
		// 用户已存在，直接返回
		return &user, nil
	}

	if err != gorm.ErrRecordNotFound {
		// 其他错误
		return nil, fmt.Errorf("failed to query anonymous user: %w", err)
	}

	// 用户不存在，创建新的匿名用户
	anonymousUser := &models.User{
		ID:           anonymousUserID,
		Username:     "anonymous_user",
		Email:        "<EMAIL>",
		PasswordHash: "no_password", // 匿名用户无需密码
		FullName:     "匿名用户",
		Role:         "guest",
		IsActive:     true,
	}

	err = s.db.Create(anonymousUser).Error
	if err != nil {
		// 可能是并发创建导致的冲突，再次尝试获取
		err = s.db.Where("id = ?", anonymousUserID).First(&user).Error
		if err == nil {
			return &user, nil
		}
		return nil, fmt.Errorf("failed to create anonymous user: %w", err)
	}

	s.logger.Info("Anonymous user created successfully")
	return anonymousUser, nil
}

// CreateGuestUser 为表单填写创建临时访客用户
func (s *AnonymousUserService) CreateGuestUser(sessionID string) (*models.User, error) {
	// 使用session ID作为用户标识的一部分
	guestUser := &models.User{
		ID:           uuid.New(),
		Username:     fmt.Sprintf("guest_%s", sessionID[:8]), // 使用session ID前8位
		Email:        fmt.Sprintf("<EMAIL>", sessionID[:8]),
		PasswordHash: "no_password",
		FullName:     "访客用户",
		Role:         "guest",
		IsActive:     true,
	}

	err := s.db.Create(guestUser).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create guest user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    guestUser.ID,
		"session_id": sessionID,
	}).Info("Guest user created successfully")

	return guestUser, nil
}

// CleanupGuestUsers 清理过期的访客用户（可以定期调用）
func (s *AnonymousUserService) CleanupGuestUsers(daysOld int) error {
	// 删除N天前创建的访客用户及其相关数据
	result := s.db.Where("role = ? AND created_at < NOW() - INTERVAL ? DAY", "guest", daysOld).
		Delete(&models.User{})

	if result.Error != nil {
		return fmt.Errorf("failed to cleanup guest users: %w", result.Error)
	}

	s.logger.WithField("deleted_count", result.RowsAffected).
		Info("Guest users cleanup completed")

	return nil
}
