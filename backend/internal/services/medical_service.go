package services

import (
	"fmt"
	"math"
	"strings"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"crf-backend/internal/models"
)

// MedicalService 医疗数据服务
type MedicalService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewMedicalService 创建医疗服务实例
func NewMedicalService(db *gorm.DB, logger *logrus.Logger) *MedicalService {
	return &MedicalService{
		db:     db,
		logger: logger,
	}
}

// GetMedicalCodes 获取医疗编码列表
func (s *MedicalService) GetMedicalCodes(req models.MedicalCodeSearchRequest) ([]models.MedicalCode, int64, error) {
	var codes []models.MedicalCode
	var total int64

	query := s.db.Model(&models.MedicalCode{}).Where("is_active = ?", true)

	// 编码系统过滤
	if req.CodeSystem != "" {
		query = query.Where("code_system = ?", req.CodeSystem)
	}

	// 语言过滤
	if req.Language != "" {
		query = query.Where("language = ?", req.Language)
	} else {
		query = query.Where("language = ?", "zh-CN")
	}

	// 父级编码过滤
	if req.ParentCode != "" {
		query = query.Where("parent_code = ?", req.ParentCode)
	}

	// 层级过滤
	if req.Level != nil {
		query = query.Where("level = ?", *req.Level)
	}

	// 搜索过滤
	if req.Search != "" {
		searchPattern := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where("code ILIKE ? OR display_name ILIKE ? OR description ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count medical codes")
		return nil, 0, err
	}

	// 分页和排序
	if req.Limit <= 0 {
		req.Limit = 50
	}
	if req.Offset < 0 {
		req.Offset = 0
	}

	if err := query.Order("level ASC, code ASC").
		Offset(req.Offset).
		Limit(req.Limit).
		Find(&codes).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get medical codes")
		return nil, 0, err
	}

	return codes, total, nil
}

// GetMedicalCodeByCode 根据编码获取医疗编码
func (s *MedicalService) GetMedicalCodeByCode(codeSystem, code, language string) (*models.MedicalCode, error) {
	var medicalCode models.MedicalCode

	if language == "" {
		language = "zh-CN"
	}

	err := s.db.Where("code_system = ? AND code = ? AND language = ? AND is_active = ?",
		codeSystem, code, language, true).First(&medicalCode).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("medical code not found: %s/%s", codeSystem, code)
		}
		s.logger.WithError(err).Error("Failed to get medical code by code")
		return nil, err
	}

	return &medicalCode, nil
}

// GetMedicalUnits 获取医疗单位列表
func (s *MedicalService) GetMedicalUnits(req models.MedicalUnitSearchRequest) ([]models.MedicalUnit, int64, error) {
	var units []models.MedicalUnit
	var total int64

	query := s.db.Model(&models.MedicalUnit{}).Where("is_active = ?", true)

	// 分类过滤
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}

	// 搜索过滤
	if req.Search != "" {
		searchPattern := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where("unit_code ILIKE ? OR unit_name ILIKE ? OR unit_symbol ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count medical units")
		return nil, 0, err
	}

	// 分页和排序
	if req.Limit <= 0 {
		req.Limit = 100
	}
	if req.Offset < 0 {
		req.Offset = 0
	}

	if err := query.Order("category ASC, unit_name ASC").
		Offset(req.Offset).
		Limit(req.Limit).
		Find(&units).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get medical units")
		return nil, 0, err
	}

	return units, total, nil
}

// GetMedicalUnitByCode 根据单位编码获取医疗单位
func (s *MedicalService) GetMedicalUnitByCode(unitCode string) (*models.MedicalUnit, error) {
	var unit models.MedicalUnit

	err := s.db.Where("unit_code = ? AND is_active = ?", unitCode, true).First(&unit).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("medical unit not found: %s", unitCode)
		}
		s.logger.WithError(err).Error("Failed to get medical unit by code")
		return nil, err
	}

	return &unit, nil
}

// ConvertUnit 单位转换
func (s *MedicalService) ConvertUnit(req models.UnitConversionRequest) (*models.UnitConversionResponse, error) {
	// 如果单位相同，直接返回
	if req.FromUnit == req.ToUnit {
		return &models.UnitConversionResponse{
			OriginalValue:  req.Value,
			OriginalUnit:   req.FromUnit,
			ConvertedValue: req.Value,
			ConvertedUnit:  req.ToUnit,
			ConversionRate: 1.0,
		}, nil
	}

	// 获取源单位和目标单位信息
	fromUnit, err := s.GetMedicalUnitByCode(req.FromUnit)
	if err != nil {
		return nil, fmt.Errorf("source unit not found: %s", req.FromUnit)
	}

	toUnit, err := s.GetMedicalUnitByCode(req.ToUnit)
	if err != nil {
		return nil, fmt.Errorf("target unit not found: %s", req.ToUnit)
	}

	// 检查是否可以转换（必须有相同的基础单位）
	if fromUnit.BaseUnit == nil || toUnit.BaseUnit == nil {
		return nil, fmt.Errorf("units cannot be converted: missing base unit information")
	}

	if *fromUnit.BaseUnit != *toUnit.BaseUnit {
		return nil, fmt.Errorf("cannot convert between %s and %s: different base units", req.FromUnit, req.ToUnit)
	}

	// 执行转换
	convertedValue, conversionRate, err := s.performUnitConversion(req.Value, fromUnit, toUnit)
	if err != nil {
		return nil, err
	}

	return &models.UnitConversionResponse{
		OriginalValue:  req.Value,
		OriginalUnit:   req.FromUnit,
		ConvertedValue: convertedValue,
		ConvertedUnit:  req.ToUnit,
		ConversionRate: conversionRate,
	}, nil
}

// performUnitConversion 执行单位转换计算
func (s *MedicalService) performUnitConversion(value float64, fromUnit, toUnit *models.MedicalUnit) (float64, float64, error) {
	// 获取转换系数
	fromFactor := 1.0
	if fromUnit.ConversionFactor != nil {
		fromFactor = *fromUnit.ConversionFactor
	}

	toFactor := 1.0
	if toUnit.ConversionFactor != nil {
		toFactor = *toUnit.ConversionFactor
	}

	// 获取转换偏移量（主要用于温度转换）
	fromOffset := 0.0
	if fromUnit.ConversionOffset != nil {
		fromOffset = *fromUnit.ConversionOffset
	}

	toOffset := 0.0
	if toUnit.ConversionOffset != nil {
		toOffset = *toUnit.ConversionOffset
	}

	// 执行转换：先转换到基础单位，再转换到目标单位
	// 对于温度等有偏移量的单位，需要特殊处理
	var convertedValue float64
	var conversionRate float64

	if fromOffset != 0 || toOffset != 0 {
		// 温度转换：先加偏移量，再乘以系数
		baseValue := (value + fromOffset) * fromFactor
		convertedValue = (baseValue / toFactor) - toOffset
		conversionRate = fromFactor / toFactor
	} else {
		// 普通单位转换：直接乘以转换系数
		convertedValue = value * fromFactor / toFactor
		conversionRate = fromFactor / toFactor
	}

	// 保留合理的精度
	convertedValue = math.Round(convertedValue*1000000) / 1000000

	return convertedValue, conversionRate, nil
}

// GetReferenceRange 获取参考值范围
func (s *MedicalService) GetReferenceRange(req models.MedicalReferenceRangeSearchRequest) (*models.MedicalReferenceRange, error) {
	var refRange models.MedicalReferenceRange

	query := s.db.Where("test_code = ? AND is_active = ?", req.TestCode, true)

	// 年龄过滤
	if req.Age != nil && *req.Age > 0 {
		query = query.Where("(age_min IS NULL OR age_min <= ?) AND (age_max IS NULL OR age_max >= ?)",
			*req.Age, *req.Age)
	}

	// 性别过滤
	if req.Gender != "" {
		query = query.Where("gender IN (?, 'all')", req.Gender)
	}

	// 人群过滤
	if req.Population != "" {
		query = query.Where("population = ?", req.Population)
	}

	// 优先选择最具体的匹配（性别匹配优于all，特定人群优于general）
	orderClause := fmt.Sprintf("CASE WHEN gender = '%s' THEN 1 ELSE 2 END, CASE WHEN population = '%s' THEN 1 ELSE 2 END",
		req.Gender, req.Population)
	query = query.Order(orderClause)

	err := query.First(&refRange).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("reference range not found for test: %s", req.TestCode)
		}
		s.logger.WithError(err).Error("Failed to get reference range")
		return nil, err
	}

	return &refRange, nil
}

// AssessValue 评估数值是否异常
func (s *MedicalService) AssessValue(testCode string, value float64, unit string, age *int, gender string) (*models.ValueAssessmentResult, error) {
	// 获取参考值范围
	refRange, err := s.GetReferenceRange(models.MedicalReferenceRangeSearchRequest{
		TestCode: testCode,
		Age:      age,
		Gender:   gender,
	})

	result := &models.ValueAssessmentResult{
		Value: value,
		Unit:  unit,
		Level: models.AbnormalValueNormal,
	}

	if err != nil {
		// 没有找到参考值范围，无法评估
		result.Interpretation = "无参考值范围，无法评估"
		return result, nil
	}

	result.ReferenceRange = refRange

	// 评估数值
	level, interpretation := s.assessValueLevel(value, refRange)
	result.Level = level
	result.Interpretation = interpretation

	// 设置危急值警报
	if level == models.AbnormalValueCritical {
		result.CriticalAlert = true
	}

	// 生成建议
	result.Recommendation = s.generateRecommendation(level, refRange.TestName)

	return result, nil
}

// assessValueLevel 评估数值级别
func (s *MedicalService) assessValueLevel(value float64, refRange *models.MedicalReferenceRange) (models.AbnormalValueLevel, string) {
	if refRange.MinValue == nil && refRange.MaxValue == nil {
		return models.AbnormalValueNormal, "正常范围"
	}

	// 检查低值
	if refRange.MinValue != nil && value < *refRange.MinValue {
		// 判断是否为危急低值（低于正常值的50%）
		if value < *refRange.MinValue*0.5 {
			return models.AbnormalValueCritical, "危急低值"
		}
		return models.AbnormalValueLow, "偏低"
	}

	// 检查高值
	if refRange.MaxValue != nil && value > *refRange.MaxValue {
		// 判断是否为危急高值（高于正常值的200%）
		if value > *refRange.MaxValue*2.0 {
			return models.AbnormalValueCritical, "危急高值"
		}
		return models.AbnormalValueHigh, "偏高"
	}

	return models.AbnormalValueNormal, "正常范围"
}

// generateRecommendation 生成建议
func (s *MedicalService) generateRecommendation(level models.AbnormalValueLevel, testName string) string {
	switch level {
	case models.AbnormalValueCritical:
		return "数值异常，建议立即就医或联系医生"
	case models.AbnormalValueHigh:
		return "数值偏高，建议复查或咨询医生"
	case models.AbnormalValueLow:
		return "数值偏低，建议复查或咨询医生"
	default:
		return "数值在正常范围内"
	}
}

// GetMedicalDrugs 获取药物列表
func (s *MedicalService) GetMedicalDrugs(req models.MedicalDrugSearchRequest) ([]models.MedicalDrug, int64, error) {
	var drugs []models.MedicalDrug
	var total int64

	query := s.db.Model(&models.MedicalDrug{}).Where("is_active = ?", true)

	// 药物分类过滤
	if req.DrugClass != "" {
		query = query.Where("drug_class = ?", req.DrugClass)
	}

	// ATC编码过滤
	if req.ATCCode != "" {
		query = query.Where("atc_code LIKE ?", req.ATCCode+"%")
	}

	// 处方药过滤
	if req.IsPrescription != nil {
		query = query.Where("is_prescription = ?", *req.IsPrescription)
	}

	// 搜索过滤
	if req.Search != "" {
		searchPattern := fmt.Sprintf("%%%s%%", req.Search)
		query = query.Where("drug_code ILIKE ? OR generic_name ILIKE ? OR brand_names::text ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count medical drugs")
		return nil, 0, err
	}

	// 分页和排序
	if req.Limit <= 0 {
		req.Limit = 50
	}
	if req.Offset < 0 {
		req.Offset = 0
	}

	if err := query.Order("generic_name ASC").
		Offset(req.Offset).
		Limit(req.Limit).
		Find(&drugs).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get medical drugs")
		return nil, 0, err
	}

	return drugs, total, nil
}

// GetMedicalDrugByCode 根据药物编码获取药物信息
func (s *MedicalService) GetMedicalDrugByCode(drugCode string) (*models.MedicalDrug, error) {
	var drug models.MedicalDrug

	err := s.db.Where("drug_code = ? AND is_active = ?", drugCode, true).First(&drug).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("medical drug not found: %s", drugCode)
		}
		s.logger.WithError(err).Error("Failed to get medical drug by code")
		return nil, err
	}

	return &drug, nil
}

// SearchMedicalDrugsByName 根据药物名称搜索
func (s *MedicalService) SearchMedicalDrugsByName(name string, limit int) ([]models.MedicalDrug, error) {
	var drugs []models.MedicalDrug

	if limit <= 0 {
		limit = 20
	}

	searchPattern := fmt.Sprintf("%%%s%%", strings.TrimSpace(name))

	err := s.db.Where("is_active = ? AND (generic_name ILIKE ? OR brand_names::text ILIKE ?)",
		true, searchPattern, searchPattern).
		Order("generic_name ASC").
		Limit(limit).
		Find(&drugs).Error

	if err != nil {
		s.logger.WithError(err).Error("Failed to search medical drugs by name")
		return nil, err
	}

	return drugs, nil
}

// ValidateMedicalData 验证医疗数据
func (s *MedicalService) ValidateMedicalData(fieldType string, value interface{}, rules models.MedicalValidationRule) error {
	// 必填验证
	if rules.Required && (value == nil || value == "") {
		return fmt.Errorf("field %s is required", fieldType)
	}

	if value == nil || value == "" {
		return nil // 非必填字段为空时跳过验证
	}

	// 根据数据类型进行验证
	switch rules.DataType {
	case "number":
		return s.validateNumericValue(value, rules)
	case "string":
		return s.validateStringValue(value, rules)
	case "medical_code":
		return s.validateMedicalCode(value, rules)
	case "medical_unit":
		return s.validateMedicalUnit(value, rules)
	default:
		return nil // 未知类型跳过验证
	}
}

// validateNumericValue 验证数值
func (s *MedicalService) validateNumericValue(value interface{}, rules models.MedicalValidationRule) error {
	var numValue float64
	var ok bool

	switch v := value.(type) {
	case float64:
		numValue = v
		ok = true
	case float32:
		numValue = float64(v)
		ok = true
	case int:
		numValue = float64(v)
		ok = true
	case int64:
		numValue = float64(v)
		ok = true
	case string:
		if parsed, err := fmt.Sscanf(v, "%f", &numValue); err == nil && parsed == 1 {
			ok = true
		}
	}

	if !ok {
		return fmt.Errorf("invalid numeric value")
	}

	// 范围验证
	if rules.MinValue != nil && numValue < *rules.MinValue {
		return fmt.Errorf("value %f is below minimum %f", numValue, *rules.MinValue)
	}

	if rules.MaxValue != nil && numValue > *rules.MaxValue {
		return fmt.Errorf("value %f is above maximum %f", numValue, *rules.MaxValue)
	}

	return nil
}

// validateStringValue 验证字符串
func (s *MedicalService) validateStringValue(value interface{}, rules models.MedicalValidationRule) error {
	_, ok := value.(string)
	if !ok {
		return fmt.Errorf("invalid string value")
	}

	// 模式验证
	if rules.Pattern != "" {
		// 这里可以添加正则表达式验证
		// 为了简化，暂时跳过
	}

	return nil
}

// validateMedicalCode 验证医疗编码
func (s *MedicalService) validateMedicalCode(value interface{}, rules models.MedicalValidationRule) error {
	codeValue, ok := value.(string)
	if !ok {
		return fmt.Errorf("invalid medical code value")
	}

	// 检查编码是否存在
	parts := strings.Split(codeValue, ":")
	if len(parts) != 2 {
		return fmt.Errorf("invalid medical code format, expected 'system:code'")
	}

	codeSystem, code := parts[0], parts[1]
	_, err := s.GetMedicalCodeByCode(codeSystem, code, "zh-CN")
	if err != nil {
		return fmt.Errorf("medical code not found: %s", codeValue)
	}

	return nil
}

// validateMedicalUnit 验证医疗单位
func (s *MedicalService) validateMedicalUnit(value interface{}, rules models.MedicalValidationRule) error {
	unitValue, ok := value.(string)
	if !ok {
		return fmt.Errorf("invalid medical unit value")
	}

	// 检查单位是否在允许列表中
	if len(rules.AllowedUnits) > 0 {
		found := false
		for _, allowedUnit := range rules.AllowedUnits {
			if unitValue == allowedUnit {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("unit %s is not allowed", unitValue)
		}
	}

	// 检查单位是否存在
	_, err := s.GetMedicalUnitByCode(unitValue)
	if err != nil {
		return fmt.Errorf("medical unit not found: %s", unitValue)
	}

	return nil
}
