package services

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/sirupsen/logrus"
)

type RateLimitService struct {
	cacheService *CacheService
	logger       *logrus.Logger
}

type RateLimitConfig struct {
	MaxAttempts int           // 最大尝试次数
	Window      time.Duration // 时间窗口
	LockoutTime time.Duration // 锁定时间
}

// 默认配置
var (
	LoginRateLimitConfig = RateLimitConfig{
		MaxAttempts: 5,                // 5次尝试
		Window:      15 * time.Minute, // 15分钟窗口
		LockoutTime: 30 * time.Minute, // 锁定30分钟
	}

	SMSRateLimitConfig = RateLimitConfig{
		MaxAttempts: 3,                // 3次尝试
		Window:      5 * time.Minute,  // 5分钟窗口
		LockoutTime: 10 * time.Minute, // 锁定10分钟
	}
)

func NewRateLimitService(cacheService *CacheService, logger *logrus.Logger) *RateLimitService {
	return &RateLimitService{
		cacheService: cacheService,
		logger:       logger,
	}
}

// CheckRateLimit 检查速率限制
func (r *RateLimitService) CheckRateLimit(ctx context.Context, key string, config RateLimitConfig) (bool, time.Duration, error) {
	// 检查是否被锁定
	if lockExpiry, locked := r.isLocked(ctx, key); locked {
		return false, lockExpiry, nil
	}

	// 检查尝试次数
	attemptKey := fmt.Sprintf("rate_limit:attempts:%s", key)
	attempts, err := r.getAttempts(ctx, attemptKey)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get rate limit attempts")
		return true, 0, nil // 如果Redis失败，允许通过
	}

	// 如果超过限制，锁定用户
	if attempts >= config.MaxAttempts {
		lockKey := fmt.Sprintf("rate_limit:lock:%s", key)
		err = r.cacheService.Set(ctx, lockKey, true, config.LockoutTime)
		if err != nil {
			r.logger.WithError(err).Error("Failed to set rate limit lock")
		}

		r.logger.WithFields(logrus.Fields{
			"key":      key,
			"attempts": attempts,
			"lockout":  config.LockoutTime,
		}).Warn("Rate limit exceeded, user locked out")

		return false, config.LockoutTime, nil
	}

	return true, 0, nil
}

// RecordAttempt 记录一次尝试
func (r *RateLimitService) RecordAttempt(ctx context.Context, key string, config RateLimitConfig) error {
	attemptKey := fmt.Sprintf("rate_limit:attempts:%s", key)

	// 增加尝试次数
	attempts, err := r.cacheService.Incr(ctx, attemptKey)
	if err != nil {
		r.logger.WithError(err).Error("Failed to increment rate limit attempts")
		return err
	}

	// 如果是第一次尝试，设置过期时间
	if attempts == 1 {
		err = r.cacheService.SetTTL(ctx, attemptKey, config.Window)
		if err != nil {
			r.logger.WithError(err).Error("Failed to set rate limit TTL")
		}
	}

	r.logger.WithFields(logrus.Fields{
		"key":      key,
		"attempts": attempts,
		"window":   config.Window,
	}).Debug("Rate limit attempt recorded")

	return nil
}

// ClearAttempts 清除尝试记录（成功登录后调用）
func (r *RateLimitService) ClearAttempts(ctx context.Context, key string) error {
	attemptKey := fmt.Sprintf("rate_limit:attempts:%s", key)
	lockKey := fmt.Sprintf("rate_limit:lock:%s", key)

	// 删除尝试记录和锁定记录
	r.cacheService.Delete(ctx, attemptKey)
	r.cacheService.Delete(ctx, lockKey)

	r.logger.WithField("key", key).Debug("Rate limit attempts cleared")
	return nil
}

// isLocked 检查是否被锁定
func (r *RateLimitService) isLocked(ctx context.Context, key string) (time.Duration, bool) {
	lockKey := fmt.Sprintf("rate_limit:lock:%s", key)

	exists, err := r.cacheService.Exists(ctx, lockKey)
	if err != nil || !exists {
		return 0, false
	}

	// 获取剩余锁定时间
	ttl, err := r.cacheService.GetTTL(ctx, lockKey)
	if err != nil || ttl <= 0 {
		return 0, false
	}

	return ttl, true
}

// getAttempts 获取尝试次数
func (r *RateLimitService) getAttempts(ctx context.Context, key string) (int, error) {
	var attempts string
	err := r.cacheService.Get(ctx, key, &attempts)
	if err != nil {
		return 0, nil // 如果没有记录，返回0
	}

	count, err := strconv.Atoi(attempts)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// GetRateLimitStatus 获取速率限制状态
func (r *RateLimitService) GetRateLimitStatus(ctx context.Context, key string, config RateLimitConfig) (int, int, time.Duration, bool) {
	// 检查是否被锁定
	if lockExpiry, locked := r.isLocked(ctx, key); locked {
		return config.MaxAttempts, config.MaxAttempts, lockExpiry, true
	}

	// 获取当前尝试次数
	attemptKey := fmt.Sprintf("rate_limit:attempts:%s", key)
	attempts, _ := r.getAttempts(ctx, attemptKey)

	remaining := config.MaxAttempts - attempts
	if remaining < 0 {
		remaining = 0
	}

	return attempts, remaining, 0, false
}

// 便捷方法：检查登录频率限制
func (r *RateLimitService) CheckLoginRateLimit(ctx context.Context, identifier string) (bool, time.Duration, error) {
	key := fmt.Sprintf("login:%s", identifier)
	return r.CheckRateLimit(ctx, key, LoginRateLimitConfig)
}

// 便捷方法：记录登录尝试
func (r *RateLimitService) RecordLoginAttempt(ctx context.Context, identifier string) error {
	key := fmt.Sprintf("login:%s", identifier)
	return r.RecordAttempt(ctx, key, LoginRateLimitConfig)
}

// 便捷方法：清除登录尝试记录
func (r *RateLimitService) ClearLoginAttempts(ctx context.Context, identifier string) error {
	key := fmt.Sprintf("login:%s", identifier)
	return r.ClearAttempts(ctx, key)
}

// 便捷方法：检查短信验证码频率限制
func (r *RateLimitService) CheckSMSRateLimit(ctx context.Context, phone string) (bool, time.Duration, error) {
	key := fmt.Sprintf("sms:%s", phone)
	return r.CheckRateLimit(ctx, key, SMSRateLimitConfig)
}

// 便捷方法：记录短信验证码尝试
func (r *RateLimitService) RecordSMSAttempt(ctx context.Context, phone string) error {
	key := fmt.Sprintf("sms:%s", phone)
	return r.RecordAttempt(ctx, key, SMSRateLimitConfig)
}
