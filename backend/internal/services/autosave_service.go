package services

import (
	"crf-backend/internal/models"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type AutoSaveService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewAutoSaveService(db *gorm.DB, logger *logrus.Logger) *AutoSaveService {
	return &AutoSaveService{
		db:     db,
		logger: logger,
	}
}

func (s *AutoSaveService) SaveData(userID, resourceID uuid.UUID, resourceType string, data interface{}) error {
	// Convert data to JSON
	jsonData, err := s.convertToJSON(data)
	if err != nil {
		return fmt.Errorf("failed to convert data to JSON: %w", err)
	}

	// Create auto save record
	autoSave := &models.AutoSave{
		UserID:       userID,
		ResourceType: resourceType,
		ResourceID:   resourceID,
		SaveData:     jsonData,
		SavedAt:      time.Now(),
		ExpiresAt:    time.Now().Add(7 * 24 * time.Hour), // Expire after 7 days
	}

	// Use UPSERT to replace existing auto save
	if err := s.db.Where("user_id = ? AND resource_type = ? AND resource_id = ?",
		userID, resourceType, resourceID).
		Assign(autoSave).
		FirstOrCreate(autoSave).Error; err != nil {
		s.logger.WithError(err).Error("Failed to save auto save data")
		return fmt.Errorf("failed to save auto save data: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":       userID,
		"resource_type": resourceType,
		"resource_id":   resourceID,
	}).Debug("Auto save data saved successfully")

	return nil
}

func (s *AutoSaveService) GetSavedData(userID, resourceID uuid.UUID, resourceType string) (*models.AutoSave, error) {
	var autoSave models.AutoSave
	if err := s.db.Where("user_id = ? AND resource_type = ? AND resource_id = ? AND expires_at > ?",
		userID, resourceType, resourceID, time.Now()).
		First(&autoSave).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No saved data found
		}
		return nil, fmt.Errorf("failed to get saved data: %w", err)
	}

	return &autoSave, nil
}

func (s *AutoSaveService) DeleteSavedData(userID, resourceID uuid.UUID, resourceType string) error {
	if err := s.db.Where("user_id = ? AND resource_type = ? AND resource_id = ?",
		userID, resourceType, resourceID).
		Delete(&models.AutoSave{}).Error; err != nil {
		s.logger.WithError(err).Error("Failed to delete auto save data")
		return fmt.Errorf("failed to delete auto save data: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":       userID,
		"resource_type": resourceType,
		"resource_id":   resourceID,
	}).Debug("Auto save data deleted successfully")

	return nil
}

func (s *AutoSaveService) GetUserSavedData(userID uuid.UUID, resourceType string) ([]models.AutoSave, error) {
	var autoSaves []models.AutoSave
	query := s.db.Where("user_id = ? AND expires_at > ?", userID, time.Now())

	if resourceType != "" {
		query = query.Where("resource_type = ?", resourceType)
	}

	if err := query.Order("saved_at DESC").Find(&autoSaves).Error; err != nil {
		return nil, fmt.Errorf("failed to get user saved data: %w", err)
	}

	return autoSaves, nil
}

func (s *AutoSaveService) StartCleanupTask(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	s.logger.Info("Starting auto save cleanup task")

	for {
		select {
		case <-ticker.C:
			s.cleanupExpiredData()
		}
	}
}

func (s *AutoSaveService) cleanupExpiredData() {
	result := s.db.Where("expires_at <= ?", time.Now()).Delete(&models.AutoSave{})
	if result.Error != nil {
		s.logger.WithError(result.Error).Error("Failed to cleanup expired auto save data")
		return
	}

	if result.RowsAffected > 0 {
		s.logger.WithField("deleted_count", result.RowsAffected).Info("Cleaned up expired auto save data")
	}
}

func (s *AutoSaveService) convertToJSON(data interface{}) ([]byte, error) {
	// Properly marshal data to JSON
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data to JSON: %w", err)
	}
	return jsonBytes, nil
}

// CreateDevUser creates a development user if it doesn't exist
func (s *AutoSaveService) CreateDevUser(user *models.User) error {
	return s.db.Where("id = ?", user.ID).FirstOrCreate(user).Error
}
