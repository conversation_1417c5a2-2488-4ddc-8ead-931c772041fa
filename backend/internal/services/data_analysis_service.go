package services

import (
	"crf-backend/internal/models"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// DataAnalysisService 数据分析服务
type DataAnalysisService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewDataAnalysisService(db *gorm.DB, logger *logrus.Logger) *DataAnalysisService {
	return &DataAnalysisService{
		db:     db,
		logger: logger,
	}
}

// InstanceDataSummary 实例数据摘要
type InstanceDataSummary struct {
	InstanceID        uuid.UUID              `json:"instance_id"`
	TemplateID        uuid.UUID              `json:"template_id"`
	TemplateName      string                 `json:"template_name"`
	TemplateVersion   string                 `json:"template_version"`
	Status            string                 `json:"status"`
	SubjectID         string                 `json:"subject_id"`
	VisitID           string                 `json:"visit_id"`
	CompletionPercent float64                `json:"completion_percent"`
	FormData          map[string]interface{} `json:"form_data"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	SubmittedAt       *time.Time             `json:"submitted_at"`
	ReviewedAt        *time.Time             `json:"reviewed_at"`
}

// GetInstanceDataSummary 获取实例数据摘要
func (s *DataAnalysisService) GetInstanceDataSummary(templateID uuid.UUID, filters map[string]interface{}) ([]InstanceDataSummary, error) {
	var instances []models.CRFInstance
	var results []InstanceDataSummary

	query := s.db.Where("template_id = ?", templateID)

	// 应用过滤器
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if subjectID, ok := filters["subject_id"]; ok {
		query = query.Where("subject_id LIKE ?", fmt.Sprintf("%%%s%%", subjectID))
	}
	if visitID, ok := filters["visit_id"]; ok {
		query = query.Where("visit_id LIKE ?", fmt.Sprintf("%%%s%%", visitID))
	}
	if dateFrom, ok := filters["date_from"]; ok {
		query = query.Where("created_at >= ?", dateFrom)
	}
	if dateTo, ok := filters["date_to"]; ok {
		query = query.Where("created_at <= ?", dateTo)
	}

	err := query.Preload("Template").Find(&instances).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query instances: %w", err)
	}

	for _, instance := range instances {
		var formData map[string]interface{}
		if err := json.Unmarshal(instance.FormData, &formData); err != nil {
			s.logger.WithError(err).Warn("Failed to unmarshal form data")
			formData = make(map[string]interface{})
		}

		summary := InstanceDataSummary{
			InstanceID:        instance.ID,
			TemplateID:        instance.TemplateID,
			TemplateName:      instance.Template.Name,
			TemplateVersion:   instance.TemplateVersion,
			Status:            instance.Status,
			SubjectID:         instance.SubjectID,
			VisitID:           instance.VisitID,
			CompletionPercent: instance.CompletionPercentage,
			FormData:          formData,
			CreatedAt:         instance.CreatedAt,
			UpdatedAt:         instance.UpdatedAt,
			SubmittedAt:       instance.SubmittedAt,
			ReviewedAt:        instance.ReviewedAt,
		}

		results = append(results, summary)
	}

	return results, nil
}

// FlattenedData 扁平化数据结构
type FlattenedData struct {
	InstanceID        string                 `json:"instance_id"`
	TemplateID        string                 `json:"template_id"`
	TemplateName      string                 `json:"template_name"`
	TemplateVersion   string                 `json:"template_version"`
	Status            string                 `json:"status"`
	SubjectID         string                 `json:"subject_id"`
	VisitID           string                 `json:"visit_id"`
	CompletionPercent float64                `json:"completion_percent"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	SubmittedAt       *time.Time             `json:"submitted_at"`
	ReviewedAt        *time.Time             `json:"reviewed_at"`
	Fields            map[string]interface{} `json:"fields"`
}

// GetFlattenedData 获取扁平化数据（用于导出）
func (s *DataAnalysisService) GetFlattenedData(templateID uuid.UUID, filters map[string]interface{}) ([]FlattenedData, error) {
	summaries, err := s.GetInstanceDataSummary(templateID, filters)
	if err != nil {
		return nil, err
	}

	var results []FlattenedData
	for _, summary := range summaries {
		flattened := FlattenedData{
			InstanceID:        summary.InstanceID.String(),
			TemplateID:        summary.TemplateID.String(),
			TemplateName:      summary.TemplateName,
			TemplateVersion:   summary.TemplateVersion,
			Status:            summary.Status,
			SubjectID:         summary.SubjectID,
			VisitID:           summary.VisitID,
			CompletionPercent: summary.CompletionPercent,
			CreatedAt:         summary.CreatedAt,
			UpdatedAt:         summary.UpdatedAt,
			SubmittedAt:       summary.SubmittedAt,
			ReviewedAt:        summary.ReviewedAt,
			Fields:            s.flattenFormData(summary.FormData),
		}
		results = append(results, flattened)
	}

	return results, nil
}

// flattenFormData 将嵌套的表单数据扁平化
func (s *DataAnalysisService) flattenFormData(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	s.flattenRecursive(data, "", result)
	return result
}

// flattenRecursive 递归扁平化数据
func (s *DataAnalysisService) flattenRecursive(data map[string]interface{}, prefix string, result map[string]interface{}) {
	for key, value := range data {
		newKey := key
		if prefix != "" {
			newKey = prefix + "." + key
		}

		switch v := value.(type) {
		case map[string]interface{}:
			s.flattenRecursive(v, newKey, result)
		case []interface{}:
			// 处理数组类型
			for i, item := range v {
				arrayKey := fmt.Sprintf("%s[%d]", newKey, i)
				if itemMap, ok := item.(map[string]interface{}); ok {
					s.flattenRecursive(itemMap, arrayKey, result)
				} else {
					result[arrayKey] = item
				}
			}
		default:
			result[newKey] = value
		}
	}
}

// DataStatistics 数据统计信息
type DataStatistics struct {
	TemplateID        uuid.UUID `json:"template_id"`
	TemplateName      string    `json:"template_name"`
	TotalInstances    int64     `json:"total_instances"`
	CompletedCount    int64     `json:"completed_count"`
	InProgressCount   int64     `json:"in_progress_count"`
	DraftCount        int64     `json:"draft_count"`
	RejectedCount     int64     `json:"rejected_count"`
	LockedCount       int64     `json:"locked_count"`
	AvgCompletionRate float64   `json:"avg_completion_rate"`
	LastUpdateTime    time.Time `json:"last_update_time"`
}

// GetDataStatistics 获取数据统计信息
func (s *DataAnalysisService) GetDataStatistics(templateID uuid.UUID) (*DataStatistics, error) {
	var template models.CRFTemplate
	err := s.db.First(&template, templateID).Error
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	var totalCount int64
	err = s.db.Model(&models.CRFInstance{}).Where("template_id = ?", templateID).Count(&totalCount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count total instances: %w", err)
	}

	// 按状态统计
	var statusCounts []struct {
		Status string
		Count  int64
	}
	err = s.db.Model(&models.CRFInstance{}).
		Where("template_id = ?", templateID).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusCounts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count by status: %w", err)
	}

	// 统计各状态数量
	var completedCount, inProgressCount, draftCount, rejectedCount, lockedCount int64
	for _, sc := range statusCounts {
		switch sc.Status {
		case "completed":
			completedCount = sc.Count
		case "in_progress":
			inProgressCount = sc.Count
		case "draft":
			draftCount = sc.Count
		case "rejected":
			rejectedCount = sc.Count
		case "locked":
			lockedCount = sc.Count
		}
	}

	// 计算平均完成率
	var avgCompletionRate float64
	err = s.db.Model(&models.CRFInstance{}).
		Where("template_id = ?", templateID).
		Select("AVG(completion_percentage)").
		Scan(&avgCompletionRate).Error
	if err != nil {
		s.logger.WithError(err).Warn("Failed to calculate average completion rate")
		avgCompletionRate = 0
	}

	// 获取最后更新时间
	var lastUpdateTime time.Time
	err = s.db.Model(&models.CRFInstance{}).
		Where("template_id = ?", templateID).
		Select("MAX(updated_at)").
		Scan(&lastUpdateTime).Error
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get last update time")
		lastUpdateTime = time.Time{}
	}

	return &DataStatistics{
		TemplateID:        templateID,
		TemplateName:      template.Name,
		TotalInstances:    totalCount,
		CompletedCount:    completedCount,
		InProgressCount:   inProgressCount,
		DraftCount:        draftCount,
		RejectedCount:     rejectedCount,
		LockedCount:       lockedCount,
		AvgCompletionRate: avgCompletionRate,
		LastUpdateTime:    lastUpdateTime,
	}, nil
}

// FieldAnalysis 字段分析结果
type FieldAnalysis struct {
	FieldPath    string                 `json:"field_path"`
	FieldType    string                 `json:"field_type"`
	ValueCounts  map[string]int         `json:"value_counts"`
	UniqueValues []interface{}          `json:"unique_values"`
	FillRate     float64                `json:"fill_rate"`
	Statistics   map[string]interface{} `json:"statistics,omitempty"`
}

// AnalyzeFields 分析字段数据
func (s *DataAnalysisService) AnalyzeFields(templateID uuid.UUID, fieldPaths []string) ([]FieldAnalysis, error) {
	flattenedData, err := s.GetFlattenedData(templateID, map[string]interface{}{})
	if err != nil {
		return nil, err
	}

	var results []FieldAnalysis
	totalInstances := len(flattenedData)

	for _, fieldPath := range fieldPaths {
		analysis := FieldAnalysis{
			FieldPath:    fieldPath,
			ValueCounts:  make(map[string]int),
			UniqueValues: make([]interface{}, 0),
		}

		var filledCount int
		var numericValues []float64
		var allValues []interface{}

		for _, data := range flattenedData {
			if value, exists := data.Fields[fieldPath]; exists && value != nil {
				filledCount++
				allValues = append(allValues, value)

				// 统计值频次
				valueStr := fmt.Sprintf("%v", value)
				analysis.ValueCounts[valueStr]++

				// 收集数值型数据
				if numValue, ok := value.(float64); ok {
					numericValues = append(numericValues, numValue)
				}
			}
		}

		// 计算填充率
		if totalInstances > 0 {
			analysis.FillRate = float64(filledCount) / float64(totalInstances)
		}

		// 获取唯一值
		uniqueMap := make(map[interface{}]bool)
		for _, value := range allValues {
			if !uniqueMap[value] {
				uniqueMap[value] = true
				analysis.UniqueValues = append(analysis.UniqueValues, value)
			}
		}

		// 数值统计
		if len(numericValues) > 0 {
			analysis.FieldType = "numeric"
			analysis.Statistics = s.calculateNumericStatistics(numericValues)
		} else {
			analysis.FieldType = "text"
		}

		results = append(results, analysis)
	}

	return results, nil
}

// calculateNumericStatistics 计算数值统计
func (s *DataAnalysisService) calculateNumericStatistics(values []float64) map[string]interface{} {
	if len(values) == 0 {
		return nil
	}

	var sum, min, max float64
	min = values[0]
	max = values[0]

	for _, value := range values {
		sum += value
		if value < min {
			min = value
		}
		if value > max {
			max = value
		}
	}

	mean := sum / float64(len(values))

	return map[string]interface{}{
		"count": len(values),
		"min":   min,
		"max":   max,
		"mean":  mean,
		"sum":   sum,
	}
}
