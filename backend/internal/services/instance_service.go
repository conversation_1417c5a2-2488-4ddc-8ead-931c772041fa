package services

import (
	"crf-backend/internal/models"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type InstanceService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewInstanceService(db *gorm.DB, logger *logrus.Logger) *InstanceService {
	return &InstanceService{
		db:     db,
		logger: logger,
	}
}

// CreateInstance 创建表单实例（需要用户登录）
func (s *InstanceService) CreateInstance(templateID uuid.UUID, userID uuid.UUID) (*models.CRFInstance, error) {
	s.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Starting to create instance")

	// 获取模板信息
	var template models.CRFTemplate
	if err := s.db.Where("id = ? AND status = ?", templateID, "published").First(&template).Error; err != nil {
		s.logger.WithError(err).WithField("template_id", templateID).Error("Failed to find template")
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found or not published")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"template_id":      templateID,
		"template_version": template.Version,
	}).Info("Template found, creating instance")

	// 创建实例
	instance := &models.CRFInstance{
		TemplateID:           templateID,
		TemplateVersion:      template.Version,
		FormData:             datatypes.JSON("{}"),
		ValidationResults:    datatypes.JSON("{}"),
		Status:               "draft",
		CompletionPercentage: 0.0,
		CreatedBy:            userID,
	}

	s.logger.Info("About to save instance to database")
	if err := s.db.Create(instance).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create instance in database")
		return nil, fmt.Errorf("failed to create instance: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"instance_id": instance.ID,
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Instance created successfully")

	return instance, nil
}

// GetInstanceByID 根据ID获取实例
func (s *InstanceService) GetInstanceByID(id uuid.UUID) (*models.CRFInstance, error) {
	var instance models.CRFInstance
	if err := s.db.Preload("Template").Preload("Creator").Where("id = ?", id).First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("instance not found")
		}
		return nil, fmt.Errorf("failed to get instance: %w", err)
	}

	return &instance, nil
}

// GetInstancesByTemplate 获取模板的所有实例
func (s *InstanceService) GetInstancesByTemplate(templateID uuid.UUID, limit, offset int) ([]models.CRFInstance, int64, error) {
	var instances []models.CRFInstance
	var total int64

	query := s.db.Model(&models.CRFInstance{}).Where("template_id = ?", templateID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count instances: %w", err)
	}

	// Get instances with pagination
	if err := query.Preload("Creator").Preload("Template").
		Limit(limit).Offset(offset).
		Order("updated_at DESC").
		Find(&instances).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get instances: %w", err)
	}

	return instances, total, nil
}

// UpdateInstanceData 更新实例数据（需要用户登录）
func (s *InstanceService) UpdateInstanceData(id uuid.UUID, formData interface{}, userID uuid.UUID) error {
	// 检查实例是否存在
	var instance models.CRFInstance
	if err := s.db.Where("id = ?", id).First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("instance not found")
		}
		return fmt.Errorf("failed to get instance: %w", err)
	}

	// 检查实例是否已锁定或已提交
	if instance.Status == "submitted" {
		return fmt.Errorf("cannot update submitted instance")
	}

	if instance.LockedBy != nil && *instance.LockedBy != userID {
		return fmt.Errorf("instance is locked by another user")
	}

	// 将formData转换为JSON格式
	jsonData, err := json.Marshal(formData)
	if err != nil {
		s.logger.WithError(err).Error("Failed to marshal form data")
		return fmt.Errorf("failed to marshal form data: %w", err)
	}

	// 记录调试信息
	s.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id": userID,
		"form_data_type": fmt.Sprintf("%T", formData),
		"form_data_json": string(jsonData),
		"json_length": len(jsonData),
	}).Info("Processing form data update")

	// 计算完成百分比
	completionPercentage := s.calculateCompletionPercentage(formData)

	// 确定状态
	status := "draft"
	if completionPercentage >= 100 {
		status = "completed"
	}

	// 更新实例数据
	updates := map[string]interface{}{
		"form_data":             datatypes.JSON(jsonData),
		"completion_percentage": completionPercentage,
		"status":                status,
		"updated_at":            time.Now(),
		"updated_by":            userID,
	}

	if err := s.db.Model(&models.CRFInstance{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("instance_id", id).Error("Failed to update instance data")
		return fmt.Errorf("failed to update instance data: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
		"completion":  completionPercentage,
	}).Info("Instance data updated successfully")

	return nil
}

// SubmitInstance 提交实例（需要用户登录）
func (s *InstanceService) SubmitInstance(id uuid.UUID, userID uuid.UUID) error {
	// 检查实例是否存在
	var instance models.CRFInstance
	if err := s.db.Where("id = ?", id).First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("instance not found")
		}
		return fmt.Errorf("failed to get instance: %w", err)
	}

	// 检查实例状态
	if instance.Status == "submitted" {
		return fmt.Errorf("instance already submitted")
	}

	// 验证表单数据
	validationResults, err := s.validateInstanceData(&instance)
	if err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// 更新实例状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":             "submitted",
		"validation_results": validationResults,
		"submitted_at":       &now,
		"updated_at":         now,
		"updated_by":         userID,
	}

	if err := s.db.Model(&models.CRFInstance{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("instance_id", id).Error("Failed to submit instance")
		return fmt.Errorf("failed to submit instance: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance submitted successfully")

	return nil
}

// LockInstance 锁定实例
func (s *InstanceService) LockInstance(id uuid.UUID, userID uuid.UUID) error {
	// 检查实例是否存在
	var instance models.CRFInstance
	if err := s.db.Where("id = ?", id).First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("instance not found")
		}
		return fmt.Errorf("failed to get instance: %w", err)
	}

	// 检查是否已被其他用户锁定
	if instance.LockedBy != nil && *instance.LockedBy != userID {
		return fmt.Errorf("instance is locked by another user")
	}

	// 锁定实例
	now := time.Now()
	updates := map[string]interface{}{
		"locked_by": userID,
		"locked_at": &now,
	}

	if err := s.db.Model(&models.CRFInstance{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("instance_id", id).Error("Failed to lock instance")
		return fmt.Errorf("failed to lock instance: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance locked successfully")

	return nil
}

// UnlockInstance 解锁实例
func (s *InstanceService) UnlockInstance(id uuid.UUID, userID uuid.UUID) error {
	// 检查实例是否存在且被当前用户锁定
	var instance models.CRFInstance
	if err := s.db.Where("id = ? AND locked_by = ?", id, userID).First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("instance not found or not locked by current user")
		}
		return fmt.Errorf("failed to get instance: %w", err)
	}

	// 解锁实例
	updates := map[string]interface{}{
		"locked_by": nil,
		"locked_at": nil,
	}

	if err := s.db.Model(&models.CRFInstance{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("instance_id", id).Error("Failed to unlock instance")
		return fmt.Errorf("failed to unlock instance: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance unlocked successfully")

	return nil
}

// DeleteInstance 删除实例（需要用户登录）
func (s *InstanceService) DeleteInstance(id uuid.UUID, userID uuid.UUID) error {
	// 检查实例是否存在
	var instance models.CRFInstance
	if err := s.db.Where("id = ?", id).First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("instance not found")
		}
		return fmt.Errorf("failed to get instance: %w", err)
	}

	// 检查权限（只有创建者可以删除）
	if instance.CreatedBy != userID {
		return fmt.Errorf("only instance creator can delete instance")
	}

	// 检查实例状态
	if instance.Status == "submitted" {
		return fmt.Errorf("cannot delete submitted instance")
	}

	if err := s.db.Delete(&models.CRFInstance{}, "id = ?", id).Error; err != nil {
		s.logger.WithError(err).WithField("instance_id", id).Error("Failed to delete instance")
		return fmt.Errorf("failed to delete instance: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"instance_id": id,
		"user_id":     userID,
	}).Info("Instance deleted successfully")

	return nil
}

// calculateCompletionPercentage 基于模板结构计算完成百分比
func (s *InstanceService) calculateCompletionPercentage(formData interface{}) float64 {
	dataMap, ok := formData.(map[string]interface{})
	if !ok {
		return 0.0
	}

	// 改进的算法：更智能地计算完成度
	totalFields := 0
	completedFields := 0

	// 递归检查所有字段值
	var checkValue func(interface{}) bool
	checkValue = func(value interface{}) bool {
		if value == nil {
			return false
		}
		
		switch v := value.(type) {
		case string:
			return strings.TrimSpace(v) != ""
		case []interface{}:
			// 数组至少有一个元素且不为空
			for _, item := range v {
				if checkValue(item) {
					return true
				}
			}
			return false
		case map[string]interface{}:
			// 对象至少有一个有效字段
			for _, item := range v {
				if checkValue(item) {
					return true
				}
			}
			return false
		case bool:
			return true // 布尔值总是有效的
		case float64, int, int64:
			return true // 数字值总是有效的
		default:
			return fmt.Sprintf("%v", v) != ""
		}
	}

	// 统计所有字段
	for _, value := range dataMap {
		totalFields++
		if checkValue(value) {
			completedFields++
		}
	}

	if totalFields == 0 {
		return 0.0
	}

	percentage := float64(completedFields) / float64(totalFields) * 100.0
	
	s.logger.WithFields(logrus.Fields{
		"total_fields":     totalFields,
		"completed_fields": completedFields,
		"percentage":       percentage,
	}).Debug("Calculated completion percentage")

	return percentage
}

// validateInstanceData 验证实例数据
func (s *InstanceService) validateInstanceData(instance *models.CRFInstance) (datatypes.JSON, error) {
	// 简化的验证实现
	// 实际应该根据模板的验证规则进行验证
	validationResults := map[string]interface{}{
		"valid":        true,
		"errors":       []string{},
		"warnings":     []string{},
		"validated_at": time.Now(),
	}

	jsonData, err := json.Marshal(validationResults)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal validation results: %w", err)
	}

	return datatypes.JSON(jsonData), nil
}

// GetTemplateForFill 获取用于填写的模板信息（需要用户登录）
func (s *InstanceService) GetTemplateForFill(templateID uuid.UUID, userID uuid.UUID) (*models.CRFTemplate, error) {
	var template models.CRFTemplate
	if err := s.db.Where("id = ? AND status = ?", templateID, "published").First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found or not published")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Template retrieved for filling")

	return &template, nil
}
