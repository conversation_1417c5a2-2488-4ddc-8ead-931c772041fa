package services

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type RememberMeService struct {
	cacheService *CacheService
	logger       *logrus.Logger
}

type RememberMeToken struct {
	TokenID    string    `json:"token_id"`
	UserID     string    `json:"user_id"`
	Selector   string    `json:"selector"`
	Validator  string    `json:"validator"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
	CreatedAt  time.Time `json:"created_at"`
	LastUsedAt time.Time `json:"last_used_at"`
	ExpiresAt  time.Time `json:"expires_at"`
}

func NewRememberMeService(cacheService *CacheService, logger *logrus.Logger) *RememberMeService {
	return &RememberMeService{
		cacheService: cacheService,
		logger:       logger,
	}
}

// GenerateRememberMeToken 生成记住我token
func (r *RememberMeService) GenerateRememberMeToken(ctx context.Context, userID, ipAddress, userAgent string) (*RememberMeToken, error) {
	// 生成选择器和验证器
	selector, err := r.generateRandomToken(32)
	if err != nil {
		return nil, fmt.Errorf("failed to generate selector: %w", err)
	}

	validator, err := r.generateRandomToken(32)
	if err != nil {
		return nil, fmt.Errorf("failed to generate validator: %w", err)
	}

	token := &RememberMeToken{
		TokenID:    uuid.New().String(),
		UserID:     userID,
		Selector:   selector,
		Validator:  validator,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		CreatedAt:  time.Now(),
		LastUsedAt: time.Now(),
		ExpiresAt:  time.Now().Add(30 * 24 * time.Hour), // 30天
	}

	// 存储到Redis
	key := fmt.Sprintf("remember_me:%s", token.Selector)
	err = r.cacheService.Set(ctx, key, token, 30*24*time.Hour)
	if err != nil {
		return nil, fmt.Errorf("failed to store remember me token: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"token_id": token.TokenID,
		"selector": token.Selector,
		"ip":       ipAddress,
	}).Info("Remember me token generated")

	return token, nil
}

// ValidateRememberMeToken 验证记住我token
func (r *RememberMeService) ValidateRememberMeToken(ctx context.Context, selector, validator, ipAddress, userAgent string) (*RememberMeToken, error) {
	key := fmt.Sprintf("remember_me:%s", selector)

	var token RememberMeToken
	err := r.cacheService.Get(ctx, key, &token)
	if err != nil {
		return nil, fmt.Errorf("remember me token not found: %w", err)
	}

	// 验证token是否过期
	if time.Now().After(token.ExpiresAt) {
		r.cacheService.Delete(ctx, key)
		return nil, fmt.Errorf("remember me token expired")
	}

	// 验证验证器
	if token.Validator != validator {
		r.cacheService.Delete(ctx, key)
		r.logger.WithFields(logrus.Fields{
			"user_id":  token.UserID,
			"selector": selector,
			"ip":       ipAddress,
		}).Warn("Invalid remember me token validator")
		return nil, fmt.Errorf("invalid remember me token")
	}

	// 安全检查：检查IP地址变化
	if token.IPAddress != ipAddress {
		r.logger.WithFields(logrus.Fields{
			"user_id":    token.UserID,
			"old_ip":     token.IPAddress,
			"new_ip":     ipAddress,
			"user_agent": userAgent,
		}).Warn("Remember me token used from different IP")

		// 可以选择是否允许不同IP使用记住我token
		// 这里我们记录警告但仍然允许
	}

	// 更新最后使用时间
	token.LastUsedAt = time.Now()
	err = r.cacheService.Set(ctx, key, token, time.Until(token.ExpiresAt))
	if err != nil {
		r.logger.WithError(err).Warn("Failed to update remember me token last used time")
	}

	r.logger.WithFields(logrus.Fields{
		"user_id":  token.UserID,
		"token_id": token.TokenID,
		"ip":       ipAddress,
	}).Info("Remember me token validated successfully")

	return &token, nil
}

// RevokeRememberMeToken 撤销记住我token
func (r *RememberMeService) RevokeRememberMeToken(ctx context.Context, selector string) error {
	key := fmt.Sprintf("remember_me:%s", selector)

	err := r.cacheService.Delete(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to revoke remember me token: %w", err)
	}

	r.logger.WithField("selector", selector).Info("Remember me token revoked")
	return nil
}

// RevokeAllUserRememberMeTokens 撤销用户所有记住我token
func (r *RememberMeService) RevokeAllUserRememberMeTokens(ctx context.Context, userID string) error {
	// 这里需要实现根据用户ID查找所有token的逻辑
	// 由于Redis的限制，我们可以使用一个索引来跟踪用户的token
	indexKey := fmt.Sprintf("remember_me_index:%s", userID)

	var selectors []string
	err := r.cacheService.Get(ctx, indexKey, &selectors)
	if err != nil {
		// 如果没有找到索引，说明用户没有记住我token
		return nil
	}

	// 删除所有token
	for _, selector := range selectors {
		tokenKey := fmt.Sprintf("remember_me:%s", selector)
		r.cacheService.Delete(ctx, tokenKey)
	}

	// 删除索引
	r.cacheService.Delete(ctx, indexKey)

	r.logger.WithFields(logrus.Fields{
		"user_id":     userID,
		"token_count": len(selectors),
	}).Info("All remember me tokens revoked for user")

	return nil
}

// CleanupExpiredTokens 清理过期的记住我token
func (r *RememberMeService) CleanupExpiredTokens(ctx context.Context) error {
	// Redis会自动清理过期的key，这里只是记录日志
	r.logger.Info("Remember me tokens cleanup completed (handled by Redis TTL)")
	return nil
}

// GetUserRememberMeTokens 获取用户的记住我token列表
func (r *RememberMeService) GetUserRememberMeTokens(ctx context.Context, userID string) ([]*RememberMeToken, error) {
	indexKey := fmt.Sprintf("remember_me_index:%s", userID)

	var selectors []string
	err := r.cacheService.Get(ctx, indexKey, &selectors)
	if err != nil {
		return []*RememberMeToken{}, nil
	}

	var tokens []*RememberMeToken
	for _, selector := range selectors {
		tokenKey := fmt.Sprintf("remember_me:%s", selector)
		var token RememberMeToken
		err := r.cacheService.Get(ctx, tokenKey, &token)
		if err == nil {
			tokens = append(tokens, &token)
		}
	}

	return tokens, nil
}

// generateRandomToken 生成随机token
func (r *RememberMeService) generateRandomToken(length int) (string, error) {
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	return base64.URLEncoding.EncodeToString(bytes), nil
}

// AddTokenToUserIndex 将token添加到用户索引
func (r *RememberMeService) AddTokenToUserIndex(ctx context.Context, userID, selector string) error {
	indexKey := fmt.Sprintf("remember_me_index:%s", userID)

	var selectors []string
	r.cacheService.Get(ctx, indexKey, &selectors)

	// 添加新的selector
	selectors = append(selectors, selector)

	// 更新索引
	err := r.cacheService.Set(ctx, indexKey, selectors, 30*24*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to update user remember me index: %w", err)
	}

	return nil
}

// RemoveTokenFromUserIndex 从用户索引中移除token
func (r *RememberMeService) RemoveTokenFromUserIndex(ctx context.Context, userID, selector string) error {
	indexKey := fmt.Sprintf("remember_me_index:%s", userID)

	var selectors []string
	err := r.cacheService.Get(ctx, indexKey, &selectors)
	if err != nil {
		return nil // 如果索引不存在，直接返回
	}

	// 移除selector
	var newSelectors []string
	for _, s := range selectors {
		if s != selector {
			newSelectors = append(newSelectors, s)
		}
	}

	// 更新索引
	if len(newSelectors) > 0 {
		err = r.cacheService.Set(ctx, indexKey, newSelectors, 30*24*time.Hour)
		if err != nil {
			return fmt.Errorf("failed to update user remember me index: %w", err)
		}
	} else {
		// 如果没有剩余的token，删除索引
		r.cacheService.Delete(ctx, indexKey)
	}

	return nil
}
