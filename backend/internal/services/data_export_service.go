package services

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// DataExportService 数据导出服务
type DataExportService struct {
	db                  *gorm.DB
	logger              *logrus.Logger
	dataAnalysisService *DataAnalysisService
}

func NewDataExportService(db *gorm.DB, logger *logrus.Logger, dataAnalysisService *DataAnalysisService) *DataExportService {
	return &DataExportService{
		db:                  db,
		logger:              logger,
		dataAnalysisService: dataAnalysisService,
	}
}

// ExportFormat 导出格式
type ExportFormat string

const (
	ExportFormatCSV   ExportFormat = "csv"
	ExportFormatJSON  ExportFormat = "json"
	ExportFormatExcel ExportFormat = "excel"
)

// ExportOptions 导出选项
type ExportOptions struct {
	Format          ExportFormat           `json:"format"`
	IncludeMetadata bool                   `json:"include_metadata"`
	IncludeEmpty    bool                   `json:"include_empty"`
	FieldMapping    map[string]string      `json:"field_mapping,omitempty"`
	Filters         map[string]interface{} `json:"filters,omitempty"`
}

// ExportResult 导出结果
type ExportResult struct {
	Filename    string    `json:"filename"`
	ContentType string    `json:"content_type"`
	Data        []byte    `json:"data"`
	Size        int64     `json:"size"`
	RecordCount int       `json:"record_count"`
	ExportedAt  time.Time `json:"exported_at"`
}

// ExportData 导出数据
func (s *DataExportService) ExportData(templateID uuid.UUID, options ExportOptions) (*ExportResult, error) {
	// 获取扁平化数据
	flattenedData, err := s.dataAnalysisService.GetFlattenedData(templateID, options.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get flattened data: %w", err)
	}

	if len(flattenedData) == 0 {
		return nil, fmt.Errorf("no data to export")
	}

	// 根据格式导出
	switch options.Format {
	case ExportFormatCSV:
		return s.exportToCSV(flattenedData, options)
	case ExportFormatJSON:
		return s.exportToJSON(flattenedData, options)
	case ExportFormatExcel:
		return s.exportToExcel(flattenedData, options)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", options.Format)
	}
}

// exportToCSV 导出为CSV格式
func (s *DataExportService) exportToCSV(data []FlattenedData, options ExportOptions) (*ExportResult, error) {
	var buffer bytes.Buffer
	writer := csv.NewWriter(&buffer)

	// 收集所有字段
	allFields := s.collectAllFields(data)

	// 创建表头
	headers := []string{}
	if options.IncludeMetadata {
		headers = append(headers, "instance_id", "template_id", "template_name", "template_version",
			"status", "subject_id", "visit_id", "completion_percent", "created_at", "updated_at", "submitted_at", "reviewed_at")
	}

	// 添加字段列
	for _, field := range allFields {
		displayName := field
		if mappedName, exists := options.FieldMapping[field]; exists {
			displayName = mappedName
		}
		headers = append(headers, displayName)
	}

	// 写入表头
	if err := writer.Write(headers); err != nil {
		return nil, fmt.Errorf("failed to write CSV headers: %w", err)
	}

	// 写入数据行
	for _, row := range data {
		record := []string{}

		if options.IncludeMetadata {
			record = append(record,
				row.InstanceID,
				row.TemplateID,
				row.TemplateName,
				row.TemplateVersion,
				row.Status,
				row.SubjectID,
				row.VisitID,
				fmt.Sprintf("%.2f", row.CompletionPercent),
				row.CreatedAt.Format("2006-01-02 15:04:05"),
				row.UpdatedAt.Format("2006-01-02 15:04:05"),
				formatTimePtr(row.SubmittedAt),
				formatTimePtr(row.ReviewedAt),
			)
		}

		// 添加字段值
		for _, field := range allFields {
			value := ""
			if fieldValue, exists := row.Fields[field]; exists && fieldValue != nil {
				value = fmt.Sprintf("%v", fieldValue)
			} else if !options.IncludeEmpty {
				value = ""
			}
			record = append(record, value)
		}

		if err := writer.Write(record); err != nil {
			return nil, fmt.Errorf("failed to write CSV record: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("failed to write CSV: %w", err)
	}

	csvData := buffer.Bytes()
	filename := fmt.Sprintf("crf_data_%s.csv", time.Now().Format("20060102_150405"))

	return &ExportResult{
		Filename:    filename,
		ContentType: "text/csv",
		Data:        csvData,
		Size:        int64(len(csvData)),
		RecordCount: len(data),
		ExportedAt:  time.Now(),
	}, nil
}

// exportToJSON 导出为JSON格式
func (s *DataExportService) exportToJSON(data []FlattenedData, options ExportOptions) (*ExportResult, error) {
	exportData := make([]map[string]interface{}, 0, len(data))

	for _, row := range data {
		record := make(map[string]interface{})

		if options.IncludeMetadata {
			record["instance_id"] = row.InstanceID
			record["template_id"] = row.TemplateID
			record["template_name"] = row.TemplateName
			record["template_version"] = row.TemplateVersion
			record["status"] = row.Status
			record["subject_id"] = row.SubjectID
			record["visit_id"] = row.VisitID
			record["completion_percent"] = row.CompletionPercent
			record["created_at"] = row.CreatedAt
			record["updated_at"] = row.UpdatedAt
			record["submitted_at"] = row.SubmittedAt
			record["reviewed_at"] = row.ReviewedAt
		}

		// 添加字段数据
		for field, value := range row.Fields {
			displayName := field
			if mappedName, exists := options.FieldMapping[field]; exists {
				displayName = mappedName
			}

			if value != nil || options.IncludeEmpty {
				record[displayName] = value
			}
		}

		exportData = append(exportData, record)
	}

	jsonData, err := json.MarshalIndent(exportData, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}

	filename := fmt.Sprintf("crf_data_%s.json", time.Now().Format("20060102_150405"))

	return &ExportResult{
		Filename:    filename,
		ContentType: "application/json",
		Data:        jsonData,
		Size:        int64(len(jsonData)),
		RecordCount: len(data),
		ExportedAt:  time.Now(),
	}, nil
}

// exportToExcel 导出为Excel格式（简化版，返回CSV格式）
func (s *DataExportService) exportToExcel(data []FlattenedData, options ExportOptions) (*ExportResult, error) {
	// 这里可以集成实际的Excel库，暂时返回CSV格式
	result, err := s.exportToCSV(data, options)
	if err != nil {
		return nil, err
	}

	// 修改文件名和内容类型
	result.Filename = strings.Replace(result.Filename, ".csv", ".xlsx", 1)
	result.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

	return result, nil
}

// collectAllFields 收集所有字段
func (s *DataExportService) collectAllFields(data []FlattenedData) []string {
	fieldSet := make(map[string]bool)

	for _, row := range data {
		for field := range row.Fields {
			fieldSet[field] = true
		}
	}

	var fields []string
	for field := range fieldSet {
		fields = append(fields, field)
	}

	// 排序字段
	sort.Strings(fields)
	return fields
}

// formatTimePtr 格式化时间指针
func formatTimePtr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// GetExportPreview 获取导出预览
func (s *DataExportService) GetExportPreview(templateID uuid.UUID, options ExportOptions) (map[string]interface{}, error) {
	// 限制预览数据量
	if options.Filters == nil {
		options.Filters = make(map[string]interface{})
	}

	// 获取少量数据进行预览
	flattenedData, err := s.dataAnalysisService.GetFlattenedData(templateID, options.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get flattened data: %w", err)
	}

	// 限制预览行数
	previewRows := 10
	if len(flattenedData) > previewRows {
		flattenedData = flattenedData[:previewRows]
	}

	// 收集字段信息
	allFields := s.collectAllFields(flattenedData)

	preview := map[string]interface{}{
		"total_records": len(flattenedData),
		"preview_rows":  previewRows,
		"fields":        allFields,
		"field_count":   len(allFields),
		"sample_data":   flattenedData,
	}

	// 添加字段类型信息
	fieldTypes := make(map[string]string)
	for _, field := range allFields {
		fieldType := s.detectFieldType(flattenedData, field)
		fieldTypes[field] = fieldType
	}
	preview["field_types"] = fieldTypes

	return preview, nil
}

// detectFieldType 检测字段类型
func (s *DataExportService) detectFieldType(data []FlattenedData, field string) string {
	for _, row := range data {
		if value, exists := row.Fields[field]; exists && value != nil {
			valueType := reflect.TypeOf(value)
			switch valueType.Kind() {
			case reflect.String:
				return "text"
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				return "integer"
			case reflect.Float32, reflect.Float64:
				return "number"
			case reflect.Bool:
				return "boolean"
			default:
				return "text"
			}
		}
	}
	return "text"
}
