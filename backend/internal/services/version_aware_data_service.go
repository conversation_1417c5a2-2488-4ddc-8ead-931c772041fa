package services

import (
	"crf-backend/internal/models"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// VersionAwareDataService 版本感知数据服务
type VersionAwareDataService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewVersionAwareDataService(db *gorm.DB, logger *logrus.Logger) *VersionAwareDataService {
	return &VersionAwareDataService{
		db:     db,
		logger: logger,
	}
}

// VersionedField 版本化字段信息
type VersionedField struct {
	FieldPath     string        `json:"field_path"`
	FieldType     string        `json:"field_type"`
	Label         string        `json:"label"`
	FirstVersion  string        `json:"first_version"`
	LastVersion   string        `json:"last_version"`
	IsActive      bool          `json:"is_active"`
	ChangeHistory []FieldChange `json:"change_history"`
}

// FieldChange 字段变更记录
type FieldChange struct {
	Version     string      `json:"version"`
	ChangeType  string      `json:"change_type"` // "added", "modified", "removed"
	ChangeTime  time.Time   `json:"change_time"`
	Description string      `json:"description"`
	OldValue    interface{} `json:"old_value,omitempty"`
	NewValue    interface{} `json:"new_value,omitempty"`
}

// UnifiedDataRow 统一数据行（包含所有版本的字段）
type UnifiedDataRow struct {
	InstanceID        string                 `json:"instance_id"`
	TemplateID        string                 `json:"template_id"`
	TemplateName      string                 `json:"template_name"`
	TemplateVersion   string                 `json:"template_version"`
	Status            string                 `json:"status"`
	SubjectID         string                 `json:"subject_id"`
	VisitID           string                 `json:"visit_id"`
	CompletionPercent float64                `json:"completion_percent"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	SubmittedAt       *time.Time             `json:"submitted_at"`
	ReviewedAt        *time.Time             `json:"reviewed_at"`
	UnifiedFields     map[string]interface{} `json:"unified_fields"`
	VersionMetadata   map[string]interface{} `json:"version_metadata"`
}

// GetVersionedFieldSchema 获取版本化字段结构
func (s *VersionAwareDataService) GetVersionedFieldSchema(templateID uuid.UUID) ([]VersionedField, error) {
	// 获取模板的所有版本
	var versions []models.CRFVersion
	err := s.db.Where("template_id = ?", templateID).
		Order("created_at ASC").
		Find(&versions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get template versions: %w", err)
	}

	if len(versions) == 0 {
		return nil, fmt.Errorf("no versions found for template")
	}

	// 分析每个版本的字段
	fieldEvolution := make(map[string]*VersionedField)

	for _, version := range versions {
		var schemaData map[string]interface{}
		if err := json.Unmarshal(version.SnapshotData, &schemaData); err != nil {
			s.logger.WithError(err).Warnf("Failed to unmarshal schema for version %s", version.Version)
			continue
		}

		// 提取字段信息
		versionFields := s.extractFieldsFromSchema(schemaData)

		// 更新字段演化信息
		for fieldPath, fieldInfo := range versionFields {
			if existing, exists := fieldEvolution[fieldPath]; exists {
				// 更新现有字段
				existing.LastVersion = version.Version
				existing.IsActive = true

				// 检查是否有变化
				if s.hasFieldChanged(existing, fieldInfo) {
					change := FieldChange{
						Version:     version.Version,
						ChangeType:  "modified",
						ChangeTime:  version.CreatedAt,
						Description: "Field properties updated",
						OldValue:    existing,
						NewValue:    fieldInfo,
					}
					existing.ChangeHistory = append(existing.ChangeHistory, change)
				}
			} else {
				// 新增字段
				fieldEvolution[fieldPath] = &VersionedField{
					FieldPath:    fieldPath,
					FieldType:    fieldInfo.FieldType,
					Label:        fieldInfo.Label,
					FirstVersion: version.Version,
					LastVersion:  version.Version,
					IsActive:     true,
					ChangeHistory: []FieldChange{
						{
							Version:     version.Version,
							ChangeType:  "added",
							ChangeTime:  version.CreatedAt,
							Description: "Field added",
							NewValue:    fieldInfo,
						},
					},
				}
			}
		}

		// 标记在此版本中删除的字段
		for fieldPath, field := range fieldEvolution {
			if _, exists := versionFields[fieldPath]; !exists && field.LastVersion != version.Version {
				field.IsActive = false
				change := FieldChange{
					Version:     version.Version,
					ChangeType:  "removed",
					ChangeTime:  version.CreatedAt,
					Description: "Field removed",
					OldValue:    field,
				}
				field.ChangeHistory = append(field.ChangeHistory, change)
			}
		}
	}

	// 转换为数组并排序
	var result []VersionedField
	for _, field := range fieldEvolution {
		result = append(result, *field)
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].FieldPath < result[j].FieldPath
	})

	return result, nil
}

// FieldInfo 字段信息
type FieldInfo struct {
	FieldPath string `json:"field_path"`
	FieldType string `json:"field_type"`
	Label     string `json:"label"`
}

// extractFieldsFromSchema 从schema中提取字段信息
func (s *VersionAwareDataService) extractFieldsFromSchema(schema map[string]interface{}) map[string]FieldInfo {
	fields := make(map[string]FieldInfo)
	s.extractFieldsRecursive(schema, "", fields)
	return fields
}

// extractFieldsRecursive 递归提取字段
func (s *VersionAwareDataService) extractFieldsRecursive(data map[string]interface{}, prefix string, fields map[string]FieldInfo) {
	if sections, ok := data["sections"].([]interface{}); ok {
		for _, section := range sections {
			if sectionMap, ok := section.(map[string]interface{}); ok {
				sectionID := fmt.Sprintf("%v", sectionMap["id"])
				sectionPrefix := sectionID
				if prefix != "" {
					sectionPrefix = prefix + "." + sectionID
				}

				if sectionFields, ok := sectionMap["fields"].([]interface{}); ok {
					for _, field := range sectionFields {
						if fieldMap, ok := field.(map[string]interface{}); ok {
							fieldID := fmt.Sprintf("%v", fieldMap["id"])
							fieldPath := sectionPrefix + "." + fieldID

							fieldType := "text"
							if ft, ok := fieldMap["type"].(string); ok {
								fieldType = ft
							}

							label := fieldID
							if l, ok := fieldMap["label"].(string); ok {
								label = l
							}

							fields[fieldPath] = FieldInfo{
								FieldPath: fieldPath,
								FieldType: fieldType,
								Label:     label,
							}
						}
					}
				}
			}
		}
	}
}

// hasFieldChanged 检查字段是否有变化
func (s *VersionAwareDataService) hasFieldChanged(existing *VersionedField, newField FieldInfo) bool {
	return existing.FieldType != newField.FieldType || existing.Label != newField.Label
}

// GetUnifiedData 获取统一数据（包含所有版本的字段）
func (s *VersionAwareDataService) GetUnifiedData(templateID uuid.UUID, filters map[string]interface{}) ([]UnifiedDataRow, error) {
	// 获取版本化字段结构
	versionedFields, err := s.GetVersionedFieldSchema(templateID)
	if err != nil {
		return nil, fmt.Errorf("failed to get versioned field schema: %w", err)
	}

	// 获取所有实例数据
	var instances []models.CRFInstance
	query := s.db.Where("template_id = ?", templateID)

	// 应用过滤器
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if subjectID, ok := filters["subject_id"]; ok {
		query = query.Where("subject_id LIKE ?", fmt.Sprintf("%%%s%%", subjectID))
	}
	if visitID, ok := filters["visit_id"]; ok {
		query = query.Where("visit_id LIKE ?", fmt.Sprintf("%%%s%%", visitID))
	}

	err = query.Preload("Template").Find(&instances).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query instances: %w", err)
	}

	// 创建统一数据行
	var result []UnifiedDataRow
	for _, instance := range instances {
		var formData map[string]interface{}
		if err := json.Unmarshal(instance.FormData, &formData); err != nil {
			s.logger.WithError(err).Warn("Failed to unmarshal form data")
			formData = make(map[string]interface{})
		}

		// 扁平化实例数据
		flattenedData := s.flattenFormData(formData)

		// 创建统一字段映射
		unifiedFields := make(map[string]interface{})
		versionMetadata := make(map[string]interface{})

		for _, field := range versionedFields {
			fieldPath := field.FieldPath

			// 检查当前版本是否有此字段
			if value, exists := flattenedData[fieldPath]; exists {
				unifiedFields[fieldPath] = value
				versionMetadata[fieldPath] = map[string]interface{}{
					"available_in_version": true,
					"field_type":           field.FieldType,
					"first_version":        field.FirstVersion,
					"last_version":         field.LastVersion,
				}
			} else {
				// 字段在当前版本不存在
				unifiedFields[fieldPath] = nil
				versionMetadata[fieldPath] = map[string]interface{}{
					"available_in_version": false,
					"field_type":           field.FieldType,
					"first_version":        field.FirstVersion,
					"last_version":         field.LastVersion,
					"reason":               s.getFieldUnavailableReason(field, instance.TemplateVersion),
				}
			}
		}

		row := UnifiedDataRow{
			InstanceID:        instance.ID.String(),
			TemplateID:        instance.TemplateID.String(),
			TemplateName:      instance.Template.Name,
			TemplateVersion:   instance.TemplateVersion,
			Status:            instance.Status,
			SubjectID:         instance.SubjectID,
			VisitID:           instance.VisitID,
			CompletionPercent: instance.CompletionPercentage,
			CreatedAt:         instance.CreatedAt,
			UpdatedAt:         instance.UpdatedAt,
			SubmittedAt:       instance.SubmittedAt,
			ReviewedAt:        instance.ReviewedAt,
			UnifiedFields:     unifiedFields,
			VersionMetadata:   versionMetadata,
		}

		result = append(result, row)
	}

	return result, nil
}

// getFieldUnavailableReason 获取字段不可用的原因
func (s *VersionAwareDataService) getFieldUnavailableReason(field VersionedField, instanceVersion string) string {
	// 版本比较逻辑（简化版）
	if s.compareVersions(instanceVersion, field.FirstVersion) < 0 {
		return "Field added in later version"
	}
	if !field.IsActive && s.compareVersions(instanceVersion, field.LastVersion) > 0 {
		return "Field removed in later version"
	}
	return "Field not available in this version"
}

// compareVersions 比较版本号（简化版）
func (s *VersionAwareDataService) compareVersions(v1, v2 string) int {
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}

	for i := 0; i < maxLen; i++ {
		var n1, n2 int

		if i < len(parts1) {
			n1, _ = strconv.Atoi(parts1[i])
		}
		if i < len(parts2) {
			n2, _ = strconv.Atoi(parts2[i])
		}

		if n1 < n2 {
			return -1
		}
		if n1 > n2 {
			return 1
		}
	}

	return 0
}

// flattenFormData 扁平化表单数据
func (s *VersionAwareDataService) flattenFormData(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	s.flattenRecursive(data, "", result)
	return result
}

// flattenRecursive 递归扁平化
func (s *VersionAwareDataService) flattenRecursive(data map[string]interface{}, prefix string, result map[string]interface{}) {
	for key, value := range data {
		newKey := key
		if prefix != "" {
			newKey = prefix + "." + key
		}

		switch v := value.(type) {
		case map[string]interface{}:
			s.flattenRecursive(v, newKey, result)
		case []interface{}:
			for i, item := range v {
				arrayKey := fmt.Sprintf("%s[%d]", newKey, i)
				if itemMap, ok := item.(map[string]interface{}); ok {
					s.flattenRecursive(itemMap, arrayKey, result)
				} else {
					result[arrayKey] = item
				}
			}
		default:
			result[newKey] = value
		}
	}
}

// VersionCompatibilityReport 版本兼容性报告
type VersionCompatibilityReport struct {
	TemplateID         uuid.UUID              `json:"template_id"`
	VersionRange       string                 `json:"version_range"`
	TotalFields        int                    `json:"total_fields"`
	CommonFields       int                    `json:"common_fields"`
	VersionSpecific    int                    `json:"version_specific"`
	FieldCompatibility map[string]interface{} `json:"field_compatibility"`
	DataMigrationPlan  []MigrationStep        `json:"data_migration_plan"`
}

// MigrationStep 迁移步骤
type MigrationStep struct {
	StepType       string      `json:"step_type"` // "add_field", "remove_field", "transform_field"
	FieldPath      string      `json:"field_path"`
	SourceVersion  string      `json:"source_version"`
	TargetVersion  string      `json:"target_version"`
	Description    string      `json:"description"`
	TransformRule  interface{} `json:"transform_rule,omitempty"`
	RequiresManual bool        `json:"requires_manual"`
}

// GenerateVersionCompatibilityReport 生成版本兼容性报告
func (s *VersionAwareDataService) GenerateVersionCompatibilityReport(templateID uuid.UUID) (*VersionCompatibilityReport, error) {
	versionedFields, err := s.GetVersionedFieldSchema(templateID)
	if err != nil {
		return nil, fmt.Errorf("failed to get versioned field schema: %w", err)
	}

	// 获取版本范围
	var versions []models.CRFVersion
	err = s.db.Where("template_id = ?", templateID).
		Order("created_at ASC").
		Find(&versions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get template versions: %w", err)
	}

	if len(versions) == 0 {
		return nil, fmt.Errorf("no versions found")
	}

	versionRange := fmt.Sprintf("%s - %s", versions[0].Version, versions[len(versions)-1].Version)

	// 分析字段兼容性
	commonFields := 0
	versionSpecific := 0
	fieldCompatibility := make(map[string]interface{})

	for _, field := range versionedFields {
		if field.FirstVersion == versions[0].Version && field.IsActive {
			commonFields++
		} else {
			versionSpecific++
		}

		fieldCompatibility[field.FieldPath] = map[string]interface{}{
			"first_version": field.FirstVersion,
			"last_version":  field.LastVersion,
			"is_active":     field.IsActive,
			"change_count":  len(field.ChangeHistory),
			"compatibility": s.calculateFieldCompatibility(field),
		}
	}

	// 生成迁移计划
	migrationPlan := s.generateMigrationPlan(versionedFields, versions)

	return &VersionCompatibilityReport{
		TemplateID:         templateID,
		VersionRange:       versionRange,
		TotalFields:        len(versionedFields),
		CommonFields:       commonFields,
		VersionSpecific:    versionSpecific,
		FieldCompatibility: fieldCompatibility,
		DataMigrationPlan:  migrationPlan,
	}, nil
}

// calculateFieldCompatibility 计算字段兼容性
func (s *VersionAwareDataService) calculateFieldCompatibility(field VersionedField) float64 {
	if len(field.ChangeHistory) == 0 {
		return 1.0
	}

	// 基于变更历史计算兼容性得分
	score := 1.0
	for _, change := range field.ChangeHistory {
		switch change.ChangeType {
		case "added":
			score -= 0.1
		case "modified":
			score -= 0.2
		case "removed":
			score -= 0.5
		}
	}

	if score < 0 {
		score = 0
	}

	return score
}

// generateMigrationPlan 生成迁移计划
func (s *VersionAwareDataService) generateMigrationPlan(fields []VersionedField, versions []models.CRFVersion) []MigrationStep {
	var steps []MigrationStep

	for _, field := range fields {
		for _, change := range field.ChangeHistory {
			step := MigrationStep{
				StepType:      change.ChangeType,
				FieldPath:     field.FieldPath,
				SourceVersion: s.getPreviousVersion(versions, change.Version),
				TargetVersion: change.Version,
				Description:   change.Description,
			}

			switch change.ChangeType {
			case "added":
				step.StepType = "add_field"
				step.RequiresManual = false
			case "modified":
				step.StepType = "transform_field"
				step.RequiresManual = true
			case "removed":
				step.StepType = "remove_field"
				step.RequiresManual = false
			}

			steps = append(steps, step)
		}
	}

	return steps
}

// getPreviousVersion 获取前一个版本
func (s *VersionAwareDataService) getPreviousVersion(versions []models.CRFVersion, currentVersion string) string {
	for i, version := range versions {
		if version.Version == currentVersion && i > 0 {
			return versions[i-1].Version
		}
	}
	return ""
}
