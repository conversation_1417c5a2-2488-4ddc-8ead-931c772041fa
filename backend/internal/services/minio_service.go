package services

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"crf-backend/internal/config"

	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/sirupsen/logrus"
)

type MinIOService struct {
	client *minio.Client
	config *config.MinIOConfig
	logger *logrus.Logger
}

func NewMinIOService(cfg *config.MinIOConfig, logger *logrus.Logger) (*MinIOService, error) {
	// 创建MinIO客户端
	minioClient, err := minio.New(cfg.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.AccessKey, cfg.SecretKey, ""),
		Secure: cfg.UseSSL,
		Region: cfg.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	service := &MinIOService{
		client: minioClient,
		config: cfg,
		logger: logger,
	}

	// 确保bucket存在
	if err := service.ensureBucketExists(); err != nil {
		return nil, fmt.Errorf("failed to ensure bucket exists: %w", err)
	}

	return service, nil
}

// 确保bucket存在
func (s *MinIOService) ensureBucketExists() error {
	ctx := context.Background()
	
	// 检查bucket是否存在
	exists, err := s.client.BucketExists(ctx, s.config.BucketName)
	if err != nil {
		return fmt.Errorf("failed to check if bucket exists: %w", err)
	}

	// 如果bucket不存在，创建它
	if !exists {
		err = s.client.MakeBucket(ctx, s.config.BucketName, minio.MakeBucketOptions{
			Region: s.config.Region,
		})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		s.logger.Infof("Created MinIO bucket: %s", s.config.BucketName)

		// 设置bucket为公开读取
		policy := fmt.Sprintf(`{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Effect": "Allow",
					"Principal": "*",
					"Action": ["s3:GetObject"],
					"Resource": ["arn:aws:s3:::%s/*"]
				}
			]
		}`, s.config.BucketName)

		err = s.client.SetBucketPolicy(ctx, s.config.BucketName, policy)
		if err != nil {
			s.logger.Warnf("Failed to set bucket policy: %v", err)
		}
	}

	return nil
}

// 上传头像文件
func (s *MinIOService) UploadAvatar(userID string, fileName string, fileReader io.Reader, contentType string, fileSize int64) (string, error) {
	ctx := context.Background()

	// 生成唯一的文件名
	ext := filepath.Ext(fileName)
	if ext == "" {
		// 根据content type推断扩展名
		switch contentType {
		case "image/jpeg":
			ext = ".jpg"
		case "image/png":
			ext = ".png"
		case "image/gif":
			ext = ".gif"
		case "image/webp":
			ext = ".webp"
		default:
			ext = ".jpg" // 默认
		}
	}

	// 对象名格式：avatars/user-{userID}/{uuid}{ext}
	objectName := fmt.Sprintf("avatars/user-%s/%s%s", userID, uuid.New().String(), ext)

	// 上传选项
	options := minio.PutObjectOptions{
		ContentType: contentType,
		UserMetadata: map[string]string{
			"user-id":     userID,
			"upload-time": time.Now().Format(time.RFC3339),
		},
	}

	// 上传文件
	info, err := s.client.PutObject(ctx, s.config.BucketName, objectName, fileReader, fileSize, options)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to MinIO: %w", err)
	}

	s.logger.Infof("Successfully uploaded avatar: %s (size: %d bytes)", info.Key, info.Size)

	// 返回文件的公开URL
	url := s.GetPublicURL(objectName)
	return url, nil
}

// 获取文件的公开URL
func (s *MinIOService) GetPublicURL(objectName string) string {
	protocol := "http"
	if s.config.UseSSL {
		protocol = "https"
	}
	return fmt.Sprintf("%s://%s/%s/%s", protocol, s.config.Endpoint, s.config.BucketName, objectName)
}

// 删除头像文件
func (s *MinIOService) DeleteAvatar(avatarURL string) error {
	ctx := context.Background()

	// 从URL中提取object name
	objectName := s.extractObjectNameFromURL(avatarURL)
	if objectName == "" {
		return fmt.Errorf("invalid avatar URL: %s", avatarURL)
	}

	// 删除对象
	err := s.client.RemoveObject(ctx, s.config.BucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete avatar from MinIO: %w", err)
	}

	s.logger.Infof("Successfully deleted avatar: %s", objectName)
	return nil
}

// 从URL中提取object name
func (s *MinIOService) extractObjectNameFromURL(avatarURL string) string {
	// URL格式: http://endpoint/bucket/object-name
	// 或者: https://endpoint/bucket/object-name
	
	// 移除协议部分
	url := strings.TrimPrefix(avatarURL, "http://")
	url = strings.TrimPrefix(url, "https://")
	
	// 移除endpoint部分
	url = strings.TrimPrefix(url, s.config.Endpoint)
	
	// 移除开头的斜杠
	url = strings.TrimPrefix(url, "/")
	
	// 移除bucket name
	bucketPrefix := s.config.BucketName + "/"
	if !strings.HasPrefix(url, bucketPrefix) {
		return ""
	}
	
	objectName := strings.TrimPrefix(url, bucketPrefix)
	return objectName
}

// 验证文件类型
func (s *MinIOService) ValidateImageType(contentType string) bool {
	allowedTypes := []string{
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/webp",
	}

	for _, allowedType := range allowedTypes {
		if contentType == allowedType {
			return true
		}
	}
	return false
}

// 验证文件大小
func (s *MinIOService) ValidateFileSize(size int64) bool {
	// 最大2MB
	maxSize := int64(2 * 1024 * 1024)
	return size <= maxSize
}

// 清理用户的旧头像（保留最新的N个）
func (s *MinIOService) CleanupOldAvatars(userID string, keepCount int) error {
	ctx := context.Background()

	// 列出用户的所有头像
	prefix := fmt.Sprintf("avatars/user-%s/", userID)
	objectCh := s.client.ListObjects(ctx, s.config.BucketName, minio.ListObjectsOptions{
		Prefix: prefix,
	})

	var objects []minio.ObjectInfo
	for object := range objectCh {
		if object.Err != nil {
			return fmt.Errorf("failed to list objects: %w", object.Err)
		}
		objects = append(objects, object)
	}

	// 如果头像数量超过保留数量，删除旧的
	if len(objects) > keepCount {
		// 按时间排序，保留最新的
		// 这里简化处理，只删除多余的文件
		for i := 0; i < len(objects)-keepCount; i++ {
			err := s.client.RemoveObject(ctx, s.config.BucketName, objects[i].Key, minio.RemoveObjectOptions{})
			if err != nil {
				s.logger.Warnf("Failed to delete old avatar %s: %v", objects[i].Key, err)
			} else {
				s.logger.Infof("Deleted old avatar: %s", objects[i].Key)
			}
		}
	}

	return nil
}