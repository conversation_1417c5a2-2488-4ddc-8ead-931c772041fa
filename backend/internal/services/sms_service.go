package services

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"github.com/sirupsen/logrus"
)

type SMSService struct {
	cacheService *CacheService
	logger       *logrus.Logger
}

type SMSConfig struct {
	CodeLength int           // 验证码长度
	TTL        time.Duration // 验证码有效期
	Template   string        // 短信模板
}

// 默认配置
var DefaultSMSConfig = SMSConfig{
	CodeLength: 6,
	TTL:        5 * time.Minute,
	Template:   "您的验证码是：%s，5分钟内有效。请勿泄露给他人。",
}

func NewSMSService(cacheService *CacheService, logger *logrus.Logger) *SMSService {
	return &SMSService{
		cacheService: cacheService,
		logger:       logger,
	}
}

// GenerateSMSCode 生成短信验证码
func (s *SMSService) GenerateSMSCode(ctx context.Context, phone string) (string, error) {
	// 生成随机验证码
	code, err := s.generateRandomCode(DefaultSMSConfig.CodeLength)
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate SMS code")
		return "", fmt.Errorf("failed to generate SMS code: %w", err)
	}

	// 存储验证码到Redis
	cacheKey := fmt.Sprintf("sms:code:%s", phone)
	err = s.cacheService.Set(ctx, cacheKey, code, DefaultSMSConfig.TTL)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cache SMS code")
		return "", fmt.Errorf("failed to cache SMS code: %w", err)
	}

	// 记录发送日志
	s.logger.WithFields(logrus.Fields{
		"phone":       phone,
		"code_length": DefaultSMSConfig.CodeLength,
		"ttl":         DefaultSMSConfig.TTL,
	}).Info("SMS code generated successfully")

	return code, nil
}

// SendSMSCode 发送短信验证码
func (s *SMSService) SendSMSCode(ctx context.Context, phone string) error {
	// 生成验证码
	code, err := s.GenerateSMSCode(ctx, phone)
	if err != nil {
		return err
	}

	// 构造短信内容
	message := fmt.Sprintf(DefaultSMSConfig.Template, code)

	// 这里应该调用真实的短信服务API
	// 为了演示，我们只是记录日志
	s.logger.WithFields(logrus.Fields{
		"phone":   phone,
		"message": message,
	}).Info("SMS sent (mock implementation)")

	// 在实际项目中，这里应该调用如阿里云短信、腾讯云短信等服务
	// 示例：
	// err = s.sendSMSViaProvider(phone, message)
	// if err != nil {
	//     return fmt.Errorf("failed to send SMS: %w", err)
	// }

	return nil
}

// ValidateSMSCode 验证短信验证码
func (s *SMSService) ValidateSMSCode(ctx context.Context, phone, inputCode string) (bool, error) {
	if phone == "" || inputCode == "" {
		return false, fmt.Errorf("phone and code cannot be empty")
	}

	// 从Redis获取存储的验证码
	cacheKey := fmt.Sprintf("sms:code:%s", phone)
	var storedCode string
	err := s.cacheService.Get(ctx, cacheKey, &storedCode)
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"phone": phone,
			"error": err.Error(),
		}).Warn("SMS code not found or expired")
		return false, fmt.Errorf("verification code not found or expired")
	}

	// 验证码比较
	isValid := storedCode == inputCode

	if isValid {
		// 验证成功，删除验证码
		s.cacheService.Delete(ctx, cacheKey)
		s.logger.WithField("phone", phone).Info("SMS code validated successfully")
	} else {
		s.logger.WithFields(logrus.Fields{
			"phone":       phone,
			"input_code":  inputCode,
			"stored_code": storedCode,
		}).Warn("SMS code validation failed")
	}

	return isValid, nil
}

// generateRandomCode 生成随机数字验证码
func (s *SMSService) generateRandomCode(length int) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("code length must be positive")
	}

	code := ""
	for i := 0; i < length; i++ {
		digit, err := rand.Int(rand.Reader, big.NewInt(10))
		if err != nil {
			return "", fmt.Errorf("failed to generate random digit: %w", err)
		}
		code += digit.String()
	}

	return code, nil
}

// GetSMSCodeStatus 获取短信验证码状态
func (s *SMSService) GetSMSCodeStatus(ctx context.Context, phone string) (bool, time.Duration, error) {
	cacheKey := fmt.Sprintf("sms:code:%s", phone)

	// 检查是否存在
	exists, err := s.cacheService.Exists(ctx, cacheKey)
	if err != nil {
		return false, 0, fmt.Errorf("failed to check SMS code status: %w", err)
	}

	if !exists {
		return false, 0, nil
	}

	// 获取剩余有效时间
	ttl, err := s.cacheService.GetTTL(ctx, cacheKey)
	if err != nil {
		return false, 0, fmt.Errorf("failed to get SMS code TTL: %w", err)
	}

	return true, ttl, nil
}

// CleanupExpiredCodes 清理过期的验证码（可以通过定时任务调用）
func (s *SMSService) CleanupExpiredCodes(ctx context.Context) error {
	// Redis会自动清理过期的key，这里只是记录日志
	s.logger.Info("SMS code cleanup completed (handled by Redis TTL)")
	return nil
}

// sendSMSViaProvider 通过短信服务提供商发送短信的示例实现
// 在实际项目中，你需要根据选择的短信服务提供商实现这个方法
func (s *SMSService) sendSMSViaProvider(phone, message string) error {
	// 这里应该调用实际的短信服务API
	// 例如：阿里云短信、腾讯云短信、华为云短信等

	// 示例：使用阿里云短信服务
	/*
		client, err := dysmsapi.NewClientWithAccessKey("cn-hangzhou", "your-access-key-id", "your-access-key-secret")
		if err != nil {
			return fmt.Errorf("failed to create SMS client: %w", err)
		}

		request := dysmsapi.CreateSendSmsRequest()
		request.PhoneNumbers = phone
		request.SignName = "你的签名"
		request.TemplateCode = "SMS_123456789"
		request.TemplateParam = fmt.Sprintf(`{"code":"%s"}`, extractCodeFromMessage(message))

		response, err := client.SendSms(request)
		if err != nil {
			return fmt.Errorf("failed to send SMS: %w", err)
		}

		if response.Code != "OK" {
			return fmt.Errorf("SMS send failed: %s", response.Message)
		}
	*/

	// 临时实现：只记录日志
	s.logger.WithFields(logrus.Fields{
		"phone":   phone,
		"message": message,
	}).Info("SMS would be sent via provider (mock implementation)")

	return nil
}
