package services

import (
	"context"
	"crf-backend/internal/models"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func NewUserService(db *gorm.DB, logger *logrus.Logger) *UserService {
	return &UserService{
		db:     db,
		logger: logger,
	}
}

func (s *UserService) CreateUser(user *models.User) error {
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.PasswordHash), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}
	user.PasswordHash = string(hashedPassword)

	// Create user
	if err := s.db.Create(user).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create user")
		return fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.WithField("user_id", user.ID).Info("User created successfully")
	return nil
}

func (s *UserService) GetUserByID(id uuid.UUID) (*models.User, error) {
	var user models.User
	if err := s.db.Where("id = ?", id).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

func (s *UserService) GetUserByEmail(email string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

func (s *UserService) GetUsers(limit, offset int) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	// Get total count
	if err := s.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Get users with pagination
	if err := s.db.Limit(limit).Offset(offset).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

func (s *UserService) UpdateUser(id uuid.UUID, updates map[string]interface{}) error {
	// If password is being updated, hash it
	if password, ok := updates["password"]; ok {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password.(string)), bcrypt.DefaultCost)
		if err != nil {
			return fmt.Errorf("failed to hash password: %w", err)
		}
		updates["password_hash"] = string(hashedPassword)
		delete(updates, "password")
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("user_id", id).Error("Failed to update user")
		return fmt.Errorf("failed to update user: %w", err)
	}

	s.logger.WithField("user_id", id).Info("User updated successfully")
	return nil
}

func (s *UserService) DeleteUser(id uuid.UUID) error {
	if err := s.db.Delete(&models.User{}, "id = ?", id).Error; err != nil {
		s.logger.WithError(err).WithField("user_id", id).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	s.logger.WithField("user_id", id).Info("User deleted successfully")
	return nil
}

func (s *UserService) ValidatePassword(user *models.User, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	return err == nil
}

// GetUserByPhone 通过手机号获取用户
func (s *UserService) GetUserByPhone(phone string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by phone: %w", err)
	}
	return &user, nil
}

// GetUserByWechatOpenID 通过微信OpenID获取用户
func (s *UserService) GetUserByWechatOpenID(openID string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("wechat_open_id = ?", openID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by wechat open id: %w", err)
	}
	return &user, nil
}

// ValidateSMSCode 验证短信验证码
func (s *UserService) ValidateSMSCode(phone, code string) bool {
	// TODO: 实现短信验证码验证逻辑
	// 这里应该从Redis或数据库中验证验证码
	// 为了演示，暂时返回true（实际开发中需要实现真实的验证逻辑）
	s.logger.WithFields(logrus.Fields{
		"phone": phone,
		"code":  code,
	}).Info("Validating SMS code (mock implementation)")

	// 简单的演示验证：验证码为"123456"
	return code == "123456"
}

// WechatUserInfo 微信用户信息结构
type WechatUserInfo struct {
	OpenID    string `json:"openid"`
	UnionID   string `json:"unionid,omitempty"`
	Nickname  string `json:"nickname"`
	AvatarURL string `json:"headimgurl"`
}

// GetWechatUserInfo 通过微信授权码获取用户信息
func (s *UserService) GetWechatUserInfo(code string) (*WechatUserInfo, error) {
	// TODO: 实现微信OAuth2.0授权码换取用户信息的逻辑
	// 这里应该调用微信API获取用户信息
	// 为了演示，返回模拟数据
	s.logger.WithField("code", code).Info("Getting WeChat user info (mock implementation)")

	// 简单的演示实现
	return &WechatUserInfo{
		OpenID:    "mock_openid_" + code,
		UnionID:   "mock_unionid_" + code,
		Nickname:  "微信用户",
		AvatarURL: "https://example.com/avatar.jpg",
	}, nil
}

// CreateSession 创建用户会话
func (s *UserService) CreateSession(session *models.UserSession) error {
	if err := s.db.Create(session).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create user session")
		return fmt.Errorf("failed to create user session: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    session.UserID,
		"session_id": session.ID,
	}).Info("User session created successfully")
	return nil
}

// CleanExpiredSessions 清理过期的会话
func (s *UserService) CleanExpiredSessions() error {
	result := s.db.Where("expires_at < NOW()").Delete(&models.UserSession{})
	if result.Error != nil {
		s.logger.WithError(result.Error).Error("Failed to clean expired sessions")
		return fmt.Errorf("failed to clean expired sessions: %w", result.Error)
	}

	if result.RowsAffected > 0 {
		s.logger.WithField("deleted_count", result.RowsAffected).Info("Cleaned expired sessions")
	}
	return nil
}

// CreateUserWithRoles 创建用户并分配角色
func (s *UserService) CreateUserWithRoles(ctx context.Context, user *models.User, roleIDs []uuid.UUID, projectID *uuid.UUID, createdBy uuid.UUID) (*models.User, error) {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建用户
	if err := s.CreateUser(user); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// 如果提供了角色ID，分配角色
	if len(roleIDs) > 0 {
		for _, roleID := range roleIDs {
			userRole := &models.UserRole{
				ID:         uuid.New(),
				UserID:     user.ID,
				RoleID:     roleID,
				ProjectID:  projectID,
				AssignedBy: createdBy,
				AssignedAt: time.Now(),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}
			if err := tx.Create(userRole).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("failed to assign role: %w", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    user.ID,
		"role_ids":   roleIDs,
		"project_id": projectID,
		"created_by": createdBy,
	}).Info("User created with roles successfully")

	return user, nil
}

// GetUsersWithRoles 获取用户列表（包含角色信息）
func (s *UserService) GetUsersWithRoles(ctx context.Context, limit, offset int, search string, roleFilter string) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	// 构建查询条件
	query := s.db.Model(&models.User{})
	
	// 搜索过滤
	if search != "" {
		searchPattern := "%" + strings.ToLower(search) + "%"
		query = query.Where(
			"LOWER(username) LIKE ? OR LOWER(email) LIKE ? OR LOWER(full_name) LIKE ?",
			searchPattern, searchPattern, searchPattern,
		)
	}

	// 角色过滤
	if roleFilter != "" {
		query = query.Joins("JOIN user_roles ON users.id = user_roles.user_id").
			Joins("JOIN roles ON user_roles.role_id = roles.id").
			Where("roles.code = ? AND user_roles.deleted_at IS NULL", roleFilter)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// 获取用户列表，预加载角色信息
	if err := query.Preload("UserRoles.Role").
		Limit(limit).Offset(offset).
		Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

// ActivateUser 激活用户
func (s *UserService) ActivateUser(ctx context.Context, userID uuid.UUID, updatedBy uuid.UUID) error {
	updates := map[string]interface{}{
		"is_active":  true,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to activate user")
		return fmt.Errorf("failed to activate user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"updated_by": updatedBy,
	}).Info("User activated successfully")

	return nil
}

// DeactivateUser 停用用户
func (s *UserService) DeactivateUser(ctx context.Context, userID uuid.UUID, updatedBy uuid.UUID) error {
	updates := map[string]interface{}{
		"is_active":  false,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to deactivate user")
		return fmt.Errorf("failed to deactivate user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"updated_by": updatedBy,
	}).Info("User deactivated successfully")

	return nil
}

// ResetPassword 重置用户密码
func (s *UserService) ResetPassword(ctx context.Context, userID uuid.UUID, newPassword string, updatedBy uuid.UUID) error {
	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	updates := map[string]interface{}{
		"password_hash": string(hashedPassword),
		"updated_by":    updatedBy,
		"updated_at":    time.Now(),
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to reset password")
		return fmt.Errorf("failed to reset password: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"updated_by": updatedBy,
	}).Info("User password reset successfully")

	return nil
}

// BatchUpdateUsers 批量更新用户状态
func (s *UserService) BatchUpdateUsers(ctx context.Context, userIDs []uuid.UUID, action string, updatedBy uuid.UUID) error {
	if len(userIDs) == 0 {
		return fmt.Errorf("no user IDs provided")
	}

	var updates map[string]interface{}
	
	switch action {
	case "activate":
		updates = map[string]interface{}{
			"is_active":  true,
			"updated_by": updatedBy,
			"updated_at": time.Now(),
		}
	case "deactivate":
		updates = map[string]interface{}{
			"is_active":  false,
			"updated_by": updatedBy,
			"updated_at": time.Now(),
		}
	default:
		return fmt.Errorf("unsupported action: %s", action)
	}

	if err := s.db.Model(&models.User{}).Where("id IN ?", userIDs).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_ids":   userIDs,
			"action":     action,
			"updated_by": updatedBy,
		}).Error("Failed to batch update users")
		return fmt.Errorf("failed to batch update users: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_ids":   userIDs,
		"action":     action,
		"updated_by": updatedBy,
	}).Info("Users updated successfully")

	return nil
}

// GetUserStats 获取用户统计信息
func (s *UserService) GetUserStats(ctx context.Context) (map[string]interface{}, error) {
	var stats = make(map[string]interface{})

	// 获取总用户数
	var totalUsers int64
	if err := s.db.Model(&models.User{}).Count(&totalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}
	stats["total_users"] = totalUsers

	// 获取活跃用户数
	var activeUsers int64
	if err := s.db.Model(&models.User{}).Where("is_active = ?", true).Count(&activeUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}
	stats["active_users"] = activeUsers
	stats["inactive_users"] = totalUsers - activeUsers

	// 获取角色分布
	var roleDistribution []struct {
		RoleCode string `json:"role_code"`
		Count    int64  `json:"count"`
	}
	
	if err := s.db.Table("user_roles").
		Select("roles.code as role_code, count(*) as count").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("user_roles.deleted_at IS NULL").
		Group("roles.code").
		Find(&roleDistribution).Error; err != nil {
		return nil, fmt.Errorf("failed to get role distribution: %w", err)
	}

	roleStats := make(map[string]int64)
	for _, item := range roleDistribution {
		roleStats[item.RoleCode] = item.Count
	}
	stats["role_distribution"] = roleStats

	return stats, nil
}

// SoftDeleteUser 软删除用户
func (s *UserService) SoftDeleteUser(ctx context.Context, userID uuid.UUID, deletedBy uuid.UUID) error {
	// 使用GORM的软删除，同时记录删除者
	updates := map[string]interface{}{
		"deleted_by": deletedBy,
		"deleted_at": time.Now(),
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to soft delete user")
		return fmt.Errorf("failed to soft delete user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"deleted_by": deletedBy,
	}).Info("User soft deleted successfully")

	return nil
}
