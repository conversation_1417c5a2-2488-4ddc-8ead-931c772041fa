server:
  port: 3001
  host: "localhost"
  timeout:
    read: 10s
    write: 10s
  max_header_bytes: 1048576

database:
  host: "*************"
  port: 5432
  user: "postgres"
  password: "Ytsnjzhq@2025"
  name: "crf_db"
  url: "******************************************************/crf_db?sslmode=disable"
  ssl_mode: "disable"

redis:
  host: "*************"
  port: 6379
  password: "Ytsnjzhq@2025"
  db: 0
  pool_size: 10
  timeout: 5s

security:
  jwt:
    secret: "jzsy-web-crf-20220324"
    access_token_ttl: "15m"       # 访问令牌15分钟有效期
    refresh_token_ttl: "168h"     # 刷新令牌7天有效期
    refresh_threshold: "5m"       # 剩余5分钟时允许刷新
    long_term_token_ttl: "720h"   # 记住我令牌30天有效期
    remember_me_threshold: "24h"  # 记住我令牌在24小时内刷新
  session:
    secret: "jzys-web-crf-session-key"

minio:
  endpoint: "*************:9000"
  access_key: "minio_rRkfbm"
  secret_key: "minio_BcMiih"
  bucket_name: "avatars"
  use_ssl: false
  region: "us-east-1"

environment:
  name: "development"
  debug: true

auto_save:
  interval: 30
  cleanup_interval: 3600

logging:
  level: "info"
  format: "text"
  file: "./logs/app.log"