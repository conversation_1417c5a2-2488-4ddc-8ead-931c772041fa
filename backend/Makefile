.PHONY: help build run test clean deps migrate docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  deps        Download dependencies"
	@echo "  build       Build the application"
	@echo "  run         Run the application"
	@echo "  test        Run tests"
	@echo "  clean       Clean build artifacts"
	@echo "  migrate     Run database migrations"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run  Run with Docker"

# Download dependencies
deps:
	go mod download
	go mod tidy

# Build the application
build: deps
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/crf-backend .

# Build for current platform
build-local: deps
	go build -o bin/crf-backend .

# Run the application
run: build-local
	./bin/crf-backend

# Run in development mode
dev:
	go run main.go

# Run tests
test:
	go test -v ./...

# Run tests with coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Clean build artifacts
clean:
	rm -rf bin/
	rm -f coverage.out coverage.html

# Run database migrations
migrate:
	psql "$(DATABASE_URL)" -f database/schema.sql

# Lint code
lint:
	golangci-lint run

# Format code
fmt:
	go fmt ./...

# Security check
security:
	gosec ./...

# Docker build
docker-build:
	docker build -t crf-backend .

# Docker run
docker-run:
	docker run -p 3000:3000 --env-file .env crf-backend

# Development server with auto-reload
dev-watch:
	air

# Install development tools
install-tools:
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest