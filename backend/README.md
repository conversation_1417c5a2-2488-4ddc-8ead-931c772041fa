# CRF Backend API Server

基于 Go + Gin + GORM + PostgreSQL 的医疗 CRF 表单编辑器后端服务。

## 功能特性

- 用户认证与授权
- 项目管理
- CRF 模板设计与管理
- 表单实例填写与提交
- 版本控制与发布
- 自动保存功能
- 操作历史记录
- 文件上传管理

## 快速开始

### 环境要求

- Go 1.21+
- PostgreSQL 13+
- Redis (可选，用于缓存)

### 安装依赖

```bash
make deps
```

### 配置环境变量

复制 `.env.example` 到 `.env` 并修改配置：

```bash
cp .env.example .env
```

### 数据库设置

1. 创建数据库：
```sql
CREATE DATABASE crf_db;
```

2. 运行数据库迁移：
```bash
make migrate
```

### 启动服务

```bash
# 开发模式
make dev

# 生产模式
make build
make run
```

## API 文档

### 认证接口

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 项目管理

- `GET /api/projects` - 获取项目列表
- `POST /api/projects` - 创建项目
- `GET /api/projects/:id` - 获取项目详情
- `PUT /api/projects/:id` - 更新项目
- `DELETE /api/projects/:id` - 删除项目

### CRF 模板

- `GET /api/templates` - 获取模板列表
- `POST /api/templates` - 创建模板
- `GET /api/templates/:id` - 获取模板详情
- `PUT /api/templates/:id` - 更新模板
- `DELETE /api/templates/:id` - 删除模板
- `POST /api/templates/:id/publish` - 发布模板
- `GET /api/templates/:id/versions` - 获取模板版本列表
- `POST /api/templates/:id/versions` - 创建模板版本

### 表单实例

- `GET /api/instances` - 获取实例列表
- `POST /api/instances` - 创建实例
- `GET /api/instances/:id` - 获取实例详情
- `PUT /api/instances/:id` - 更新实例
- `DELETE /api/instances/:id` - 删除实例
- `POST /api/instances/:id/submit` - 提交实例
- `POST /api/instances/:id/lock` - 锁定实例
- `POST /api/instances/:id/unlock` - 解锁实例

### 自动保存

- `POST /api/autosave` - 保存数据
- `GET /api/autosave/:resource_type/:resource_id` - 获取保存的数据
- `DELETE /api/autosave/:resource_type/:resource_id` - 删除保存的数据

### 操作历史

- `GET /api/history/:resource_type/:resource_id` - 获取操作历史
- `POST /api/history` - 创建历史记录

## 数据库架构

主要表结构：

- `users` - 用户表
- `projects` - 项目表
- `crf_templates` - CRF 模板表
- `crf_versions` - 模板版本表
- `crf_instances` - 表单实例表
- `auto_saves` - 自动保存表
- `operation_history` - 操作历史表
- `attachments` - 附件表
- `user_sessions` - 用户会话表
- `system_settings` - 系统设置表

## 开发指南

### 项目结构

```
backend/
├── cmd/                    # 应用入口
├── internal/
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接
│   ├── handlers/          # HTTP 处理器
│   ├── middleware/        # 中间件
│   ├── models/           # 数据模型
│   └── services/         # 业务逻辑
├── database/
│   └── schema.sql        # 数据库架构
├── .env                  # 环境变量
├── go.mod               # Go 模块
├── main.go             # 主程序
└── Makefile           # 构建脚本
```

### 代码规范

- 使用 `gofmt` 格式化代码
- 使用 `golangci-lint` 进行代码检查
- 遵循 Go 代码规范
- 添加必要的注释和文档

### 测试

```bash
# 运行所有测试
make test

# 运行测试并生成覆盖率报告
make test-coverage
```

## 部署

### Docker 部署

```bash
# 构建镜像
make docker-build

# 运行容器
make docker-run
```

### 生产环境

1. 设置环境变量
2. 构建应用：`make build`
3. 运行数据库迁移：`make migrate`
4. 启动服务：`./bin/crf-backend`

## 安全注意事项

- 生产环境请修改 JWT 密钥
- 使用 HTTPS
- 配置防火墙
- 定期更新依赖包
- 启用数据库 SSL 连接

## 监控与日志

- 使用结构化日志
- 集成健康检查接口
- 支持 Prometheus 指标
- 错误追踪和报警
