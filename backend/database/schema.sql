-- CRF (Case Report Form) Web Editor Database Schema
-- 医疗CRF表单编辑器数据库架构
-- Version: 2.0.0
-- Updated: 2025-07-08

-- 创建数据库扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================================================
-- 用户管理相关表
-- ================================================================

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'editor', 'viewer', 'user')),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    
    -- 多种登录方式支持
    phone VARCHAR(20) UNIQUE,
    wechat_open_id VARCHAR(100) UNIQUE,
    wechat_union_id VARCHAR(100) UNIQUE,
    login_type VARCHAR(20) DEFAULT 'password' CHECK (login_type IN ('password', 'sms', 'wechat')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    
    -- 会话信息
    ip_address INET,
    user_agent TEXT,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 项目管理相关表
-- ================================================================

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'archived')),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- CRF模板管理相关表
-- ================================================================

-- CRF表单模板表
CREATE TABLE IF NOT EXISTS crf_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    keyword VARCHAR(100),
    version VARCHAR(20) DEFAULT '1.0.0',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- 模板类型：区分标准模板和自定义表单
    template_type VARCHAR(20) DEFAULT 'template' CHECK (template_type IN ('template', 'custom_form')),
    
    -- 模板来源：系统预置、用户创建、导入等
    source_type VARCHAR(20) DEFAULT 'user_created' CHECK (source_type IN ('system_preset', 'user_created', 'imported', 'copied')),
    
    -- 是否为公共模板（可被其他用户使用）
    is_public BOOLEAN DEFAULT false,
    
    -- 统一的模板数据存储
    template_data JSONB NOT NULL DEFAULT '{}',
    
    -- 权限设置
    permissions JSONB DEFAULT '{}',
    
    -- 标签，便于分类和搜索
    tags VARCHAR(500),
    
    -- 使用统计
    usage_count INTEGER DEFAULT 0,
    
    -- 发布相关字段
    published_at TIMESTAMP WITH TIME ZONE,
    published_by UUID REFERENCES users(id) ON DELETE SET NULL,
    
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(project_id, name, version)
);

-- CRF表单版本表
CREATE TABLE IF NOT EXISTS crf_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES crf_templates(id) ON DELETE CASCADE,
    version VARCHAR(20) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    
    -- 版本快照数据
    snapshot_data JSONB NOT NULL,
    
    -- 变更日志
    change_log TEXT,
    
    -- 版本状态
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- 发布信息
    published_at TIMESTAMP WITH TIME ZONE,
    published_by UUID REFERENCES users(id) ON DELETE SET NULL,
    
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(template_id, version)
);

-- ================================================================
-- CRF实例管理相关表
-- ================================================================

-- CRF表单实例表（用户填写的表单）
CREATE TABLE IF NOT EXISTS crf_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES crf_templates(id) ON DELETE CASCADE,
    template_version VARCHAR(20) NOT NULL,
    
    -- 实例信息
    instance_name VARCHAR(255),
    subject_id VARCHAR(100), -- 受试者ID
    visit_id VARCHAR(100),   -- 访问ID
    
    -- 表单数据
    form_data JSONB NOT NULL DEFAULT '{}',
    
    -- 验证结果
    validation_results JSONB DEFAULT '{}',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'completed', 'locked', 'rejected')),
    
    -- 完成度
    completion_percentage DECIMAL(5,2) DEFAULT 0.0,
    
    -- 锁定信息
    locked_by UUID REFERENCES users(id) ON DELETE SET NULL,
    locked_at TIMESTAMP WITH TIME ZONE,
    
    -- 审核信息
    reviewed_by UUID REFERENCES users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_comment TEXT,
    
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 系统功能相关表
-- ================================================================

-- 自动保存记录表
CREATE TABLE IF NOT EXISTS auto_saves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance')),
    resource_id UUID NOT NULL,
    
    -- 自动保存的数据
    save_data JSONB NOT NULL,
    
    -- 保存时间
    saved_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 过期时间（用于清理旧数据）
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days')
);

-- 操作历史记录表
CREATE TABLE IF NOT EXISTS operation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance', 'project')),
    resource_id UUID NOT NULL,
    
    -- 操作信息
    action VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- 操作数据
    before_data JSONB,
    after_data JSONB,
    
    -- 客户端信息
    client_info JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 文件附件表
CREATE TABLE IF NOT EXISTS attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('template', 'instance', 'project')),
    resource_id UUID NOT NULL,
    
    -- 文件信息
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    
    -- 上传信息
    uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 元数据
    metadata JSONB DEFAULT '{}'
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 任务管理相关表
-- ================================================================

-- ================================================================
-- 数据库迁移跟踪表
-- ================================================================

-- 数据库迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    applied BOOLEAN DEFAULT false,
    applied_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================================================
-- 创建索引以提高查询性能
-- ================================================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_wechat_open_id ON users(wechat_open_id);
CREATE INDEX IF NOT EXISTS idx_users_wechat_union_id ON users(wechat_union_id);
CREATE INDEX IF NOT EXISTS idx_users_login_type ON users(login_type);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- 用户会话表索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 项目表索引
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON projects(updated_at);

-- CRF模板表索引
CREATE INDEX IF NOT EXISTS idx_crf_templates_project_id ON crf_templates(project_id);
CREATE INDEX IF NOT EXISTS idx_crf_templates_status ON crf_templates(status);
CREATE INDEX IF NOT EXISTS idx_crf_templates_template_type ON crf_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_crf_templates_source_type ON crf_templates(source_type);
CREATE INDEX IF NOT EXISTS idx_crf_templates_is_public ON crf_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_crf_templates_created_by ON crf_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_crf_templates_updated_by ON crf_templates(updated_by);
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_at ON crf_templates(published_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_published_by ON crf_templates(published_by);
CREATE INDEX IF NOT EXISTS idx_crf_templates_updated_at ON crf_templates(updated_at);
CREATE INDEX IF NOT EXISTS idx_crf_templates_usage_count ON crf_templates(usage_count);

-- 为模板数据JSON字段创建GIN索引以支持高效查询
CREATE INDEX IF NOT EXISTS idx_crf_templates_template_data ON crf_templates USING GIN (template_data);
CREATE INDEX IF NOT EXISTS idx_crf_templates_permissions ON crf_templates USING GIN (permissions);
CREATE INDEX IF NOT EXISTS idx_crf_templates_tags ON crf_templates USING GIN (to_tsvector('english', tags));

-- CRF版本表索引
CREATE INDEX IF NOT EXISTS idx_crf_versions_template_id ON crf_versions(template_id);
CREATE INDEX IF NOT EXISTS idx_crf_versions_status ON crf_versions(status);
CREATE INDEX IF NOT EXISTS idx_crf_versions_published_at ON crf_versions(published_at);
CREATE INDEX IF NOT EXISTS idx_crf_versions_published_by ON crf_versions(published_by);
CREATE INDEX IF NOT EXISTS idx_crf_versions_created_by ON crf_versions(created_by);

-- CRF实例表索引
CREATE INDEX IF NOT EXISTS idx_crf_instances_template_id ON crf_instances(template_id);
CREATE INDEX IF NOT EXISTS idx_crf_instances_status ON crf_instances(status);
CREATE INDEX IF NOT EXISTS idx_crf_instances_subject_id ON crf_instances(subject_id);
CREATE INDEX IF NOT EXISTS idx_crf_instances_visit_id ON crf_instances(visit_id);
CREATE INDEX IF NOT EXISTS idx_crf_instances_created_by ON crf_instances(created_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_updated_by ON crf_instances(updated_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_locked_by ON crf_instances(locked_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_reviewed_by ON crf_instances(reviewed_by);
CREATE INDEX IF NOT EXISTS idx_crf_instances_completion_percentage ON crf_instances(completion_percentage);
CREATE INDEX IF NOT EXISTS idx_crf_instances_updated_at ON crf_instances(updated_at);

-- 为实例数据JSON字段创建GIN索引
CREATE INDEX IF NOT EXISTS idx_crf_instances_form_data ON crf_instances USING GIN (form_data);
CREATE INDEX IF NOT EXISTS idx_crf_instances_validation_results ON crf_instances USING GIN (validation_results);

-- 自动保存表索引
CREATE INDEX IF NOT EXISTS idx_auto_saves_user_resource ON auto_saves(user_id, resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_auto_saves_expires_at ON auto_saves(expires_at);
CREATE INDEX IF NOT EXISTS idx_auto_saves_saved_at ON auto_saves(saved_at);

-- 操作历史表索引
CREATE INDEX IF NOT EXISTS idx_operation_history_user_resource ON operation_history(user_id, resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_operation_history_action ON operation_history(action);
CREATE INDEX IF NOT EXISTS idx_operation_history_created_at ON operation_history(created_at);

-- 附件表索引
CREATE INDEX IF NOT EXISTS idx_attachments_resource ON attachments(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_by ON attachments(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_at ON attachments(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_attachments_file_type ON attachments(file_type);

-- 系统设置表索引
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key);

-- 迁移表索引
CREATE INDEX IF NOT EXISTS idx_migrations_version ON migrations(version);
CREATE INDEX IF NOT EXISTS idx_migrations_applied ON migrations(applied);
CREATE INDEX IF NOT EXISTS idx_migrations_applied_at ON migrations(applied_at);

-- ================================================================
-- 创建更新时间的触发器函数
-- ================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crf_templates_updated_at BEFORE UPDATE ON crf_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crf_instances_updated_at BEFORE UPDATE ON crf_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================================================
-- 插入默认系统设置
-- ================================================================

INSERT INTO system_settings (key, value, description) VALUES
    ('app_name', '"CRF Web Editor"', '应用程序名称'),
    ('app_version', '"2.0.0"', '应用程序版本'),
    ('auto_save_interval', '30', '自动保存间隔（秒）'),
    ('max_file_size', '10485760', '最大文件上传大小（字节）'),
    ('session_timeout', '7200', '会话超时时间（秒）'),
    ('enable_audit_log', 'true', '是否启用审计日志'),
    ('backup_retention_days', '30', '备份保留天数'),
    ('template_types_enabled', 'true', '是否启用模板类型区分功能'),
    ('multi_login_enabled', 'true', '是否启用多种登录方式'),
    ('sms_provider', '"aliyun"', '短信服务提供商'),
    ('wechat_app_id', '""', '微信应用ID'),
    ('template_import_max_size', '10485760', '模板导入文件最大大小（字节）'),
    ('public_template_enabled', 'true', '是否允许创建公共模板'),
    ('auto_save_enabled', 'true', '是否启用自动保存功能'),
    ('operation_history_enabled', 'true', '是否启用操作历史记录'),
    ('attachment_enabled', 'true', '是否启用文件附件功能'),
    ('notification_enabled', 'true', '是否启用通知功能')
ON CONFLICT (key) DO NOTHING;

-- ================================================================
-- 创建默认用户和示例数据
-- ================================================================

-- 创建默认管理员用户（密码：admin123）
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
    ('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewqLmkjnHnL1K2/2', '系统管理员', 'admin'),
    ('editor', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewqLmkjnHnL1K2/2', '表单编辑员', 'editor'),
    ('viewer', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewqLmkjnHnL1K2/2', '表单查看员', 'viewer')
ON CONFLICT (username) DO NOTHING;

-- 创建示例项目
INSERT INTO projects (name, description, created_by, status) VALUES
    ('示例CRF项目', '这是一个示例的CRF项目，用于演示系统功能', 
     (SELECT id FROM users WHERE username = 'admin' LIMIT 1), 'active'),
    ('临床试验项目A', '用于临床试验A的CRF表单管理', 
     (SELECT id FROM users WHERE username = 'admin' LIMIT 1), 'active'),
    ('药物安全性评估项目', '专门用于药物安全性评估的表单系统', 
     (SELECT id FROM users WHERE username = 'editor' LIMIT 1), 'draft')
ON CONFLICT DO NOTHING;

-- 创建示例模板
INSERT INTO crf_templates (
    name, title, description, keyword, version, status,
    template_type, source_type, is_public, tags, usage_count,
    project_id, created_by, template_data
) VALUES
    -- 系统预置标准模板
    (
        'patient_baseline_template',
        '患者基线信息采集模板',
        '用于收集患者基本信息、病史、体征等基线数据的标准化模板',
        'baseline,patient,demographics',
        '1.0.0',
        'published',
        'template',
        'system_preset',
        true,
        '患者信息,基线数据,标准模板',
        0,
        (SELECT id FROM projects WHERE name = '示例CRF项目' LIMIT 1),
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
        '{
            "pageConfig": {
                "theme": "medical",
                "layout": "vertical",
                "language": "zh-CN",
                "title": "患者基线信息采集",
                "showProgress": true,
                "allowSave": true
            },
            "formStructure": {
                "sections": [
                    {
                        "id": "demographics",
                        "title": "人口学信息",
                        "components": [
                            {"id": "subject_id", "type": "input", "label": "受试者编号", "required": true},
                            {"id": "age", "type": "number", "label": "年龄", "required": true},
                            {"id": "gender", "type": "radio", "label": "性别", "required": true, "options": [{"label": "男", "value": "male"}, {"label": "女", "value": "female"}]}
                        ]
                    },
                    {
                        "id": "medical_history",
                        "title": "病史信息",
                        "components": [
                            {"id": "primary_diagnosis", "type": "textarea", "label": "主要诊断", "required": true},
                            {"id": "medical_history", "type": "textarea", "label": "既往病史"},
                            {"id": "allergies", "type": "checkbox", "label": "过敏史", "options": [{"label": "药物过敏", "value": "drug"}, {"label": "食物过敏", "value": "food"}, {"label": "无过敏史", "value": "none"}]}
                        ]
                    }
                ]
            },
            "styleConfig": {
                "primaryColor": "#1976d2",
                "secondaryColor": "#42a5f5"
            }
        }'
    ),
    -- 用户创建的自定义表单
    (
        'adverse_event_form',
        '不良事件报告表',
        '专门用于记录临床试验中发生的不良事件',
        'adverse,event,safety',
        '1.0.0',
        'draft',
        'custom_form',
        'user_created',
        false,
        '不良事件,安全性,临床试验',
        0,
        (SELECT id FROM projects WHERE name = '示例CRF项目' LIMIT 1),
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
        '{
            "pageConfig": {
                "theme": "medical",
                "layout": "vertical",
                "title": "不良事件报告",
                "showProgress": false
            },
            "formStructure": {
                "sections": [
                    {
                        "id": "event_info",
                        "title": "事件信息",
                        "components": [
                            {"id": "event_date", "type": "date", "label": "事件发生日期", "required": true},
                            {"id": "event_description", "type": "textarea", "label": "事件描述", "required": true},
                            {"id": "severity", "type": "select", "label": "严重程度", "required": true, "options": [{"label": "轻度", "value": "mild"}, {"label": "中度", "value": "moderate"}, {"label": "重度", "value": "severe"}]}
                        ]
                    }
                ]
            }
        }'
    ),
    -- 导入的模板示例
    (
        'lab_results_template',
        '实验室检查结果模板',
        '从外部系统导入的实验室检查标准模板',
        'laboratory,lab,results',
        '2.1.0',
        'published',
        'template',
        'imported',
        true,
        '实验室检查,化验结果,导入模板',
        5,
        (SELECT id FROM projects WHERE name = '示例CRF项目' LIMIT 1),
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1),
        '{
            "pageConfig": {
                "theme": "medical",
                "layout": "two-column",
                "title": "实验室检查结果"
            },
            "formStructure": {
                "sections": [
                    {
                        "id": "blood_test",
                        "title": "血液检查",
                        "components": [
                            {"id": "wbc", "type": "number", "label": "白细胞计数", "unit": "×10⁹/L"},
                            {"id": "rbc", "type": "number", "label": "红细胞计数", "unit": "×10¹²/L"},
                            {"id": "hemoglobin", "type": "number", "label": "血红蛋白", "unit": "g/L"}
                        ]
                    }
                ]
            }
        }'
    )
ON CONFLICT (project_id, name, version) DO NOTHING;

-- 创建示例版本记录
INSERT INTO crf_versions (
    template_id, version, title, description, snapshot_data, status, created_by
) VALUES
    (
        (SELECT id FROM crf_templates WHERE name = 'patient_baseline_template' LIMIT 1),
        '1.0.0',
        '患者基线信息采集模板',
        '初始版本，包含基本的人口学信息和病史收集',
        '{"version": "1.0.0", "snapshot": "Initial version"}',
        'published',
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1)
    )
ON CONFLICT (template_id, version) DO NOTHING;

-- 记录初始数据库版本
INSERT INTO migrations (version, name, applied, applied_at) VALUES
    ('2.0.0', 'Initial schema with full CRF system', true, CURRENT_TIMESTAMP)
ON CONFLICT (version) DO NOTHING;

-- ================================================================
-- 数据库架构说明
-- ================================================================

/*
数据库架构说明 (Database Schema Documentation)

1. 用户管理 (User Management):
   - users: 用户基本信息，支持多种登录方式
   - user_sessions: 用户会话管理

2. 项目管理 (Project Management):
   - projects: 项目信息和设置

3. CRF模板管理 (CRF Template Management):
   - crf_templates: CRF表单模板主表
   - crf_versions: 模板版本管理

4. CRF实例管理 (CRF Instance Management):
   - crf_instances: 填写的表单实例

5. 系统功能 (System Features):
   - auto_saves: 自动保存记录
   - operation_history: 操作历史审计
   - attachments: 文件附件管理
   - system_settings: 系统配置

6. 系统管理 (System Management):
   - migrations: 数据库迁移追踪

主要特性 (Key Features):
- UUID主键确保全局唯一性
- JSONB字段支持灵活的数据存储
- 完整的索引策略提升查询性能
- 外键约束确保数据完整性
- 触发器自动更新时间戳
- 多种登录方式支持
- 版本控制系统
- 审计日志记录
- 自动保存功能
*/

-- ================================================================
-- 架构版本信息
-- ================================================================

INSERT INTO system_settings (key, value, description) VALUES
    ('schema_version', '"2.0.0"', '数据库架构版本'),
    ('schema_updated_at', '"' || CURRENT_TIMESTAMP || '"', '架构最后更新时间'),
    ('schema_description', '"Complete CRF Web Editor Database Schema with full feature support"', '架构描述')
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = CURRENT_TIMESTAMP;

-- ============================================================================
-- 医疗数据相关表 (Medical Data Tables)
-- ============================================================================

-- 医疗编码字典表 (支持ICD-10/11, SNOMED CT, LOINC等)
CREATE TABLE IF NOT EXISTS medical_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code_system VARCHAR(50) NOT NULL, -- 'icd10', 'icd11', 'snomed', 'loinc', 'atc'
    code VARCHAR(100) NOT NULL,
    display_name VARCHAR(500) NOT NULL,
    description TEXT,
    parent_code VARCHAR(100), -- 父级编码，用于构建层级结构
    level INTEGER DEFAULT 0, -- 层级深度
    is_active BOOLEAN DEFAULT true,
    language VARCHAR(10) DEFAULT 'zh-CN',
    version VARCHAR(20), -- 编码系统版本
    effective_date DATE, -- 生效日期
    expiry_date DATE, -- 失效日期
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- 复合唯一索引
    UNIQUE(code_system, code, language, version)
);

-- 医疗编码索引
CREATE INDEX IF NOT EXISTS idx_medical_codes_system_code ON medical_codes(code_system, code);
CREATE INDEX IF NOT EXISTS idx_medical_codes_parent ON medical_codes(parent_code);
CREATE INDEX IF NOT EXISTS idx_medical_codes_display_name ON medical_codes USING gin(to_tsvector('simple', display_name));
CREATE INDEX IF NOT EXISTS idx_medical_codes_active ON medical_codes(is_active) WHERE is_active = true;

-- 医疗单位字典表 (支持单位转换)
CREATE TABLE IF NOT EXISTS medical_units (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    unit_code VARCHAR(50) NOT NULL UNIQUE,
    unit_name VARCHAR(100) NOT NULL,
    unit_symbol VARCHAR(20) NOT NULL,
    category VARCHAR(50), -- 'temperature', 'pressure', 'weight', 'volume', 'time', 'frequency'
    base_unit VARCHAR(50), -- 基础单位用于转换
    conversion_factor DECIMAL(20,10), -- 转换系数到基础单位
    conversion_offset DECIMAL(20,10) DEFAULT 0, -- 转换偏移量（如摄氏度到开尔文）
    is_si_unit BOOLEAN DEFAULT false, -- 是否为SI单位
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 医疗单位索引
CREATE INDEX IF NOT EXISTS idx_medical_units_category ON medical_units(category);
CREATE INDEX IF NOT EXISTS idx_medical_units_active ON medical_units(is_active) WHERE is_active = true;

-- 医疗参考值表 (按年龄性别分组的正常值范围)
CREATE TABLE IF NOT EXISTS medical_reference_ranges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    test_code VARCHAR(100) NOT NULL,
    test_name VARCHAR(200) NOT NULL,
    min_value DECIMAL(15,6),
    max_value DECIMAL(15,6),
    unit VARCHAR(50),
    age_min INTEGER, -- 最小年龄（月）
    age_max INTEGER, -- 最大年龄（月）
    gender VARCHAR(10), -- 'male', 'female', 'all'
    population VARCHAR(50) DEFAULT 'general', -- 'adult', 'pediatric', 'elderly', 'pregnant'
    condition_notes TEXT, -- 特殊条件说明
    reference_source VARCHAR(200), -- 参考值来源
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 医疗参考值索引
CREATE INDEX IF NOT EXISTS idx_medical_reference_ranges_test_code ON medical_reference_ranges(test_code);
CREATE INDEX IF NOT EXISTS idx_medical_reference_ranges_age_gender ON medical_reference_ranges(age_min, age_max, gender);
CREATE INDEX IF NOT EXISTS idx_medical_reference_ranges_active ON medical_reference_ranges(is_active) WHERE is_active = true;

-- 药物数据表
CREATE TABLE IF NOT EXISTS medical_drugs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    drug_code VARCHAR(100) NOT NULL,
    generic_name VARCHAR(200) NOT NULL,
    brand_names JSONB, -- 商品名数组
    drug_class VARCHAR(100),
    atc_code VARCHAR(20), -- ATC分类编码
    dosage_forms JSONB, -- 剂型数组 ['tablet', 'injection', 'capsule']
    strengths JSONB, -- 规格数组 ['5mg', '10mg', '20mg']
    routes JSONB, -- 给药途径数组 ['oral', 'iv', 'im']
    contraindications TEXT,
    interactions TEXT,
    side_effects JSONB, -- 副作用列表
    pregnancy_category VARCHAR(10), -- 妊娠分级
    is_prescription BOOLEAN DEFAULT true, -- 是否处方药
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(drug_code)
);

-- 药物数据索引
CREATE INDEX IF NOT EXISTS idx_medical_drugs_generic_name ON medical_drugs USING gin(to_tsvector('simple', generic_name));
CREATE INDEX IF NOT EXISTS idx_medical_drugs_drug_class ON medical_drugs(drug_class);
CREATE INDEX IF NOT EXISTS idx_medical_drugs_atc_code ON medical_drugs(atc_code);
CREATE INDEX IF NOT EXISTS idx_medical_drugs_active ON medical_drugs(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_medical_drugs_brand_names ON medical_drugs USING gin(brand_names);

-- 医疗表更新时间触发器
CREATE TRIGGER update_medical_codes_updated_at BEFORE UPDATE ON medical_codes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medical_units_updated_at BEFORE UPDATE ON medical_units FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medical_reference_ranges_updated_at BEFORE UPDATE ON medical_reference_ranges FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medical_drugs_updated_at BEFORE UPDATE ON medical_drugs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 医疗数据初始化 (Medical Data Initialization)
-- ============================================================================

-- 插入常用医疗单位
INSERT INTO medical_units (unit_code, unit_name, unit_symbol, category, base_unit, conversion_factor, is_si_unit, description) VALUES
    -- 温度单位
    ('celsius', '摄氏度', '°C', 'temperature', 'kelvin', 1.0, false, '摄氏温度单位'),
    ('fahrenheit', '华氏度', '°F', 'temperature', 'kelvin', 0.5556, false, '华氏温度单位'),
    ('kelvin', '开尔文', 'K', 'temperature', 'kelvin', 1.0, true, 'SI温度基本单位'),

    -- 压力单位
    ('mmhg', '毫米汞柱', 'mmHg', 'pressure', 'pascal', 133.322, false, '血压常用单位'),
    ('kpa', '千帕', 'kPa', 'pressure', 'pascal', 1000.0, false, '压力单位'),
    ('pascal', '帕斯卡', 'Pa', 'pressure', 'pascal', 1.0, true, 'SI压力基本单位'),

    -- 重量单位
    ('kg', '千克', 'kg', 'weight', 'kilogram', 1.0, true, 'SI质量基本单位'),
    ('g', '克', 'g', 'weight', 'kilogram', 0.001, false, '质量单位'),
    ('mg', '毫克', 'mg', 'weight', 'kilogram', 0.000001, false, '药物剂量常用单位'),
    ('ug', '微克', 'μg', 'weight', 'kilogram', 0.000000001, false, '微量药物单位'),
    ('lb', '磅', 'lb', 'weight', 'kilogram', 0.453592, false, '英制重量单位'),

    -- 体积单位
    ('l', '升', 'L', 'volume', 'liter', 1.0, false, '体积单位'),
    ('ml', '毫升', 'mL', 'volume', 'liter', 0.001, false, '液体药物常用单位'),
    ('ul', '微升', 'μL', 'volume', 'liter', 0.000001, false, '微量体积单位'),

    -- 时间单位
    ('min', '分钟', 'min', 'time', 'second', 60.0, false, '时间单位'),
    ('hour', '小时', 'h', 'time', 'second', 3600.0, false, '时间单位'),
    ('day', '天', 'd', 'time', 'second', 86400.0, false, '时间单位'),
    ('week', '周', 'w', 'time', 'second', 604800.0, false, '时间单位'),

    -- 频率单位
    ('per_min', '次每分钟', '/min', 'frequency', 'per_second', 0.0167, false, '心率、呼吸频率单位'),
    ('per_hour', '次每小时', '/h', 'frequency', 'per_second', 0.000278, false, '频率单位'),
    ('per_day', '次每天', '/d', 'frequency', 'per_second', 0.0000116, false, '给药频率单位'),

    -- 浓度单位
    ('mg_per_l', '毫克每升', 'mg/L', 'concentration', 'mg_per_l', 1.0, false, '血药浓度单位'),
    ('ug_per_ml', '微克每毫升', 'μg/mL', 'concentration', 'mg_per_l', 1.0, false, '血药浓度单位'),
    ('mmol_per_l', '毫摩尔每升', 'mmol/L', 'concentration', 'mmol_per_l', 1.0, false, '生化指标单位'),
    ('umol_per_l', '微摩尔每升', 'μmol/L', 'concentration', 'mmol_per_l', 0.001, false, '生化指标单位')
ON CONFLICT (unit_code) DO NOTHING;

-- 插入常用ICD-10编码示例
INSERT INTO medical_codes (code_system, code, display_name, description, parent_code, level, language, version) VALUES
    -- 循环系统疾病 (I00-I99)
    ('icd10', 'I00-I99', '循环系统疾病', '心血管系统相关疾病', NULL, 0, 'zh-CN', '2019'),
    ('icd10', 'I10-I15', '高血压病', '原发性和继发性高血压', 'I00-I99', 1, 'zh-CN', '2019'),
    ('icd10', 'I10', '原发性高血压', '特发性高血压', 'I10-I15', 2, 'zh-CN', '2019'),
    ('icd10', 'I11', '高血压性心脏病', '由高血压引起的心脏病变', 'I10-I15', 2, 'zh-CN', '2019'),
    ('icd10', 'I20-I25', '缺血性心脏病', '冠心病相关疾病', 'I00-I99', 1, 'zh-CN', '2019'),
    ('icd10', 'I21', '急性心肌梗死', 'ST段抬高型心肌梗死', 'I20-I25', 2, 'zh-CN', '2019'),
    ('icd10', 'I25', '慢性缺血性心脏病', '慢性冠心病', 'I20-I25', 2, 'zh-CN', '2019'),

    -- 内分泌疾病 (E00-E89)
    ('icd10', 'E00-E89', '内分泌、营养和代谢疾病', '内分泌系统相关疾病', NULL, 0, 'zh-CN', '2019'),
    ('icd10', 'E10-E14', '糖尿病', '各型糖尿病', 'E00-E89', 1, 'zh-CN', '2019'),
    ('icd10', 'E10', '1型糖尿病', '胰岛素依赖型糖尿病', 'E10-E14', 2, 'zh-CN', '2019'),
    ('icd10', 'E11', '2型糖尿病', '非胰岛素依赖型糖尿病', 'E10-E14', 2, 'zh-CN', '2019'),

    -- 呼吸系统疾病 (J00-J99)
    ('icd10', 'J00-J99', '呼吸系统疾病', '呼吸道和肺部疾病', NULL, 0, 'zh-CN', '2019'),
    ('icd10', 'J40-J47', '慢性下呼吸道疾病', '慢性阻塞性肺病等', 'J00-J99', 1, 'zh-CN', '2019'),
    ('icd10', 'J44', '其他慢性阻塞性肺病', 'COPD', 'J40-J47', 2, 'zh-CN', '2019'),
    ('icd10', 'J45', '哮喘', '支气管哮喘', 'J40-J47', 2, 'zh-CN', '2019')
ON CONFLICT (code_system, code, language, version) DO NOTHING;

-- 插入常用医疗参考值
INSERT INTO medical_reference_ranges (test_code, test_name, min_value, max_value, unit, age_min, age_max, gender, population, reference_source) VALUES
    -- 生命体征参考值
    ('systolic_bp', '收缩压', 90, 140, 'mmHg', 216, NULL, 'all', 'adult', 'ESH/ESC Guidelines'),
    ('diastolic_bp', '舒张压', 60, 90, 'mmHg', 216, NULL, 'all', 'adult', 'ESH/ESC Guidelines'),
    ('heart_rate', '心率', 60, 100, '/min', 216, NULL, 'all', 'adult', 'AHA Guidelines'),
    ('respiratory_rate', '呼吸频率', 12, 20, '/min', 216, NULL, 'all', 'adult', 'Clinical Standards'),
    ('body_temperature', '体温', 36.1, 37.2, 'celsius', 216, NULL, 'all', 'adult', 'Clinical Standards'),
    ('oxygen_saturation', '血氧饱和度', 95, 100, '%', 216, NULL, 'all', 'adult', 'Clinical Standards'),

    -- 血常规参考值 - 成人男性
    ('wbc_count', '白细胞计数', 4.0, 10.0, '×10⁹/L', 216, NULL, 'male', 'adult', 'WHO Standards'),
    ('rbc_count_male', '红细胞计数', 4.5, 5.5, '×10¹²/L', 216, NULL, 'male', 'adult', 'WHO Standards'),
    ('hemoglobin_male', '血红蛋白', 130, 175, 'g/L', 216, NULL, 'male', 'adult', 'WHO Standards'),
    ('hematocrit_male', '红细胞压积', 0.40, 0.50, 'ratio', 216, NULL, 'male', 'adult', 'WHO Standards'),
    ('platelet_count', '血小板计数', 100, 300, '×10⁹/L', 216, NULL, 'all', 'adult', 'WHO Standards'),

    -- 血常规参考值 - 成人女性
    ('rbc_count_female', '红细胞计数', 4.0, 5.0, '×10¹²/L', 216, NULL, 'female', 'adult', 'WHO Standards'),
    ('hemoglobin_female', '血红蛋白', 115, 150, 'g/L', 216, NULL, 'female', 'adult', 'WHO Standards'),
    ('hematocrit_female', '红细胞压积', 0.36, 0.46, 'ratio', 216, NULL, 'female', 'adult', 'WHO Standards'),

    -- 生化指标参考值
    ('glucose_fasting', '空腹血糖', 3.9, 6.1, 'mmol/L', 216, NULL, 'all', 'adult', 'ADA Guidelines'),
    ('glucose_random', '随机血糖', 3.9, 7.8, 'mmol/L', 216, NULL, 'all', 'adult', 'ADA Guidelines'),
    ('hba1c', '糖化血红蛋白', 4.0, 6.0, '%', 216, NULL, 'all', 'adult', 'ADA Guidelines'),
    ('total_cholesterol', '总胆固醇', 3.1, 5.2, 'mmol/L', 216, NULL, 'all', 'adult', 'ESC Guidelines'),
    ('ldl_cholesterol', '低密度脂蛋白胆固醇', 1.8, 3.4, 'mmol/L', 216, NULL, 'all', 'adult', 'ESC Guidelines'),
    ('hdl_cholesterol_male', '高密度脂蛋白胆固醇', 1.0, 1.6, 'mmol/L', 216, NULL, 'male', 'adult', 'ESC Guidelines'),
    ('hdl_cholesterol_female', '高密度脂蛋白胆固醇', 1.3, 1.9, 'mmol/L', 216, NULL, 'female', 'adult', 'ESC Guidelines'),
    ('triglycerides', '甘油三酯', 0.45, 1.70, 'mmol/L', 216, NULL, 'all', 'adult', 'ESC Guidelines'),

    -- 肝功能指标
    ('alt', '丙氨酸氨基转移酶', 7, 40, 'U/L', 216, NULL, 'all', 'adult', 'Clinical Standards'),
    ('ast', '天冬氨酸氨基转移酶', 13, 35, 'U/L', 216, NULL, 'all', 'adult', 'Clinical Standards'),
    ('total_bilirubin', '总胆红素', 5.1, 22.2, 'μmol/L', 216, NULL, 'all', 'adult', 'Clinical Standards'),
    ('albumin', '白蛋白', 40, 55, 'g/L', 216, NULL, 'all', 'adult', 'Clinical Standards'),

    -- 肾功能指标
    ('creatinine_male', '肌酐', 59, 104, 'μmol/L', 216, NULL, 'male', 'adult', 'KDIGO Guidelines'),
    ('creatinine_female', '肌酐', 45, 84, 'μmol/L', 216, NULL, 'female', 'adult', 'KDIGO Guidelines'),
    ('urea', '尿素', 2.6, 7.5, 'mmol/L', 216, NULL, 'all', 'adult', 'Clinical Standards'),
    ('uric_acid_male', '尿酸', 208, 428, 'μmol/L', 216, NULL, 'male', 'adult', 'Clinical Standards'),
    ('uric_acid_female', '尿酸', 155, 357, 'μmol/L', 216, NULL, 'female', 'adult', 'Clinical Standards')
ON CONFLICT (test_code, age_min, age_max, gender, population) DO NOTHING;

-- 插入常用药物数据
INSERT INTO medical_drugs (drug_code, generic_name, brand_names, drug_class, atc_code, dosage_forms, strengths, routes, contraindications, interactions, side_effects, pregnancy_category, is_prescription) VALUES
    -- 心血管药物
    ('amlodipine', '氨氯地平', '["络活喜", "安内真", "压氏达"]', '钙通道阻滞剂', 'C08CA01', '["tablet"]', '["2.5mg", "5mg", "10mg"]', '["oral"]', '对氨氯地平过敏者禁用', '与西柚汁同服可增强药效', '["踝部水肿", "头痛", "疲劳", "恶心"]', 'C', true),
    ('metoprolol', '美托洛尔', '["倍他乐克", "美多心安"]', 'β受体阻滞剂', 'C07AB02', '["tablet", "injection"]', '["25mg", "50mg", "100mg"]', '["oral", "iv"]', '严重心动过缓、房室传导阻滞禁用', '与胰岛素合用需监测血糖', '["心动过缓", "疲劳", "头晕", "恶心"]', 'C', true),
    ('atorvastatin', '阿托伐他汀', '["立普妥", "阿乐"]', 'HMG-CoA还原酶抑制剂', 'C10AA05', '["tablet"]', '["10mg", "20mg", "40mg", "80mg"]', '["oral"]', '活动性肝病患者禁用', '与华法林合用需监测凝血功能', '["肌痛", "头痛", "恶心", "腹泻"]', 'X', true),

    -- 糖尿病药物
    ('metformin', '二甲双胍', '["格华止", "美迪康"]', '双胍类降糖药', 'A10BA02', '["tablet", "extended_release"]', '["250mg", "500mg", "850mg", "1000mg"]', '["oral"]', '严重肾功能不全、酮症酸中毒禁用', '与造影剂合用需暂停用药', '["胃肠道反应", "乳酸酸中毒", "维生素B12缺乏"]', 'B', true),
    ('insulin_glargine', '甘精胰岛素', '["来得时", "长秀霖"]', '长效胰岛素', 'A10AE04', '["injection"]', '["100U/mL"]', '["sc"]', '低血糖症禁用', '与β受体阻滞剂合用可掩盖低血糖症状', '["低血糖", "注射部位反应", "体重增加"]', 'B', true),

    -- 抗生素
    ('amoxicillin', '阿莫西林', '["安莫西林", "再林"]', 'β-内酰胺类抗生素', 'J01CA04', '["capsule", "tablet", "suspension", "injection"]', '["250mg", "500mg", "1g"]', '["oral", "iv"]', '对青霉素过敏者禁用', '与华法林合用需监测凝血功能', '["过敏反应", "腹泻", "恶心", "皮疹"]', 'B', true),
    ('azithromycin', '阿奇霉素', '["希舒美", "舒美特"]', '大环内酯类抗生素', 'J01FA10', '["tablet", "capsule", "suspension", "injection"]', '["250mg", "500mg"]', '["oral", "iv"]', '对大环内酯类过敏者禁用', '与华法林合用需监测凝血功能', '["胃肠道反应", "头痛", "头晕"]', 'B', true),

    -- 解热镇痛药
    ('paracetamol', '对乙酰氨基酚', '["泰诺林", "必理通", "扑热息痛"]', '解热镇痛药', 'N02BE01', '["tablet", "capsule", "suspension", "suppository"]', '["325mg", "500mg", "650mg"]', '["oral", "rectal"]', '严重肝功能不全禁用', '与华法林合用需监测凝血功能', '["肝毒性", "皮疹", "血小板减少"]', 'B', false),
    ('ibuprofen', '布洛芬', '["芬必得", "美林"]', '非甾体抗炎药', 'M01AE01', '["tablet", "capsule", "suspension", "gel"]', '["200mg", "400mg", "600mg"]', '["oral", "topical"]', '对阿司匹林过敏、严重心衰禁用', '与华法林合用增加出血风险', '["胃肠道反应", "头痛", "头晕", "水肿"]', 'C', false),

    -- 抗过敏药
    ('loratadine', '氯雷他定', '["开瑞坦", "息斯敏"]', '第二代抗组胺药', 'R06AX13', '["tablet", "syrup"]', '["10mg"]', '["oral"]', '对氯雷他定过敏者禁用', '与酮康唑合用需减量', '["头痛", "疲劳", "口干"]', 'B', false),
    ('cetirizine', '西替利嗪', '["仙特明", "赛特赞"]', '第二代抗组胺药', 'R06AE07', '["tablet", "syrup", "drops"]', '["5mg", "10mg"]', '["oral"]', '对西替利嗪过敏者禁用', '与中枢抑制剂合用需谨慎', '["嗜睡", "头痛", "口干", "疲劳"]', 'B', false)
ON CONFLICT (drug_code) DO NOTHING;

-- 更新系统设置以包含医疗功能
INSERT INTO system_settings (key, value, description) VALUES
    ('medical_codes_enabled', 'true', '是否启用医疗编码功能'),
    ('medical_units_enabled', 'true', '是否启用医疗单位转换功能'),
    ('reference_ranges_enabled', 'true', '是否启用医疗参考值功能'),
    ('drug_database_enabled', 'true', '是否启用药物数据库功能'),
    ('icd_version', '"icd10"', '默认ICD编码版本'),
    ('default_language', '"zh-CN"', '默认医疗编码语言'),
    ('auto_unit_conversion', 'true', '是否启用自动单位转换'),
    ('abnormal_value_alert', 'true', '是否启用异常值提醒'),
    ('medical_validation_strict', 'false', '是否启用严格医疗数据验证')
ON CONFLICT (key) DO NOTHING;

-- 输出完成信息
SELECT 'CRF Web Editor Database Schema v2.1.0 with Medical Extensions 创建完成!' as message;
SELECT 'Medical Features: ICD Codes, Medical Units, Reference Ranges, Drug Database' as medical_features;
SELECT 'Total tables created: 17 (13 core + 4 medical)' as table_count;
SELECT 'Ready for medical CRF applications with comprehensive medical data support' as status;