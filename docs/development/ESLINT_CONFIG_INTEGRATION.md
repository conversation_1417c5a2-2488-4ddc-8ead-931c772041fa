# ESLint 配置整合优化

## 整合概述

本次整合将项目中的多个 ESLint 配置文件统一为现代化的扁平配置格式，删除了冗余配置，提高了维护效率。

## 整合前的配置状况

### 原有配置文件：
1. **`.eslintrc.json`** - 根目录的传统配置，继承 `@crf/eslint-config`
2. **`eslint.config.js`** - 根目录的新扁平配置格式（功能重复）
3. **`eslint.config.enhanced.js`** - 增强版扁平配置（已删除）
4. **`internal/eslint-config/index.js`** - 共享的传统格式配置包
5. **`packages/i18n-medical/eslint.config.js`** - 子包的扁平配置

### 存在的问题：
- 配置文件冗余，维护成本高
- 传统格式与扁平格式混用
- 规则定义重复，容易产生冲突

## 整合后的配置架构

### 新的配置结构：
```
├── eslint.config.js                    # 根目录配置（继承共享配置）
├── internal/eslint-config/
│   └── index.js                        # 统一的扁平配置包
└── packages/i18n-medical/
    └── eslint.config.js                # 子包配置（继承共享配置）
```

### 配置层次：
1. **`@crf/eslint-config`** - 核心共享配置包
   - 支持 Vue 3 + TypeScript + Composition API
   - 使用 ESLint 9.x 扁平配置格式
   - 包含完整的规则集和最佳实践

2. **根目录配置** - 继承共享配置，可添加项目特定规则
3. **子包配置** - 继承共享配置，可添加包特定规则

## 配置特性

### 支持的文件类型：
- **Vue 文件** (`.vue`) - 完整的 Vue 3 支持
- **TypeScript 文件** (`.ts`, `.tsx`) - 严格的类型检查
- **JavaScript 文件** (`.js`, `.jsx`) - 通用规则
- **测试文件** - 特殊的测试环境规则
- **配置文件** - 宽松的配置文件规则

### 核心规则集：
- **Vue 3 最佳实践** - 组件命名、Composition API 规范
- **TypeScript 严格检查** - 类型安全、未使用变量检测
- **Import 排序** - 统一的导入顺序和分组
- **代码质量** - 现代 JavaScript 最佳实践
- **Prettier 集成** - 统一的代码格式化

### 医疗项目特定优化：
- **@crf 包导入优先级** - CRF 包优先排序
- **Vue 组件命名规范** - kebab-case 模板，PascalCase 定义
- **医疗术语支持** - 支持医疗相关的命名模式

## 使用方法

### 在新包中使用：
```javascript
// eslint.config.js
import crfEslintConfig from '@crf/eslint-config'

export default [
  ...crfEslintConfig,
  
  // 包特定的规则覆盖
  {
    rules: {
      // 自定义规则
    }
  }
]
```

### 运行 ESLint：
```bash
# 检查所有文件
npx eslint .

# 检查特定文件
npx eslint src/**/*.{vue,ts,js}

# 自动修复
npx eslint . --fix
```

## 迁移说明

### 已删除的文件：
- ✅ `eslint.config.enhanced.js` - 功能冗余
- ✅ `.eslintrc.json` - 传统格式

### 已更新的文件：
- ✅ `internal/eslint-config/index.js` - 升级为扁平配置
- ✅ `internal/eslint-config/package.json` - 支持 ES 模块
- ✅ `eslint.config.js` - 简化为继承配置
- ✅ `packages/i18n-medical/eslint.config.js` - 统一继承方式

### 兼容性：
- ✅ 支持 ESLint 9.x
- ✅ 向后兼容现有代码
- ✅ 保持所有原有规则功能

## 验证结果

配置整合已通过测试：
- ✅ 配置文件可正常加载
- ✅ 规则检测正常工作
- ✅ 未使用变量检测
- ✅ Console 语句警告
- ✅ 代码格式化集成

## 维护建议

1. **统一使用扁平配置** - 新增包都应使用扁平配置格式
2. **规则集中管理** - 通用规则在 `@crf/eslint-config` 中维护
3. **最小化覆盖** - 子包配置只添加必要的特定规则
4. **定期更新** - 跟随 ESLint 版本更新配置格式

## 性能优化

- 减少了配置文件数量，降低解析开销
- 统一的规则集，避免重复计算
- 扁平配置格式，提高加载效率
- 精确的文件匹配，减少不必要的检查

整合完成后，项目的 ESLint 配置更加统一、高效和易于维护。
