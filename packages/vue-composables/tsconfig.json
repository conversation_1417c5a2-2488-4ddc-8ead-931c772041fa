{"extends": "@crf/tsconfig/package-build.json", "compilerOptions": {"baseUrl": ".", "declaration": true, "declarationMap": true, "outDir": "dist", "paths": {"@crf/type-definitions": ["../type-definitions/index.ts"], "@crf/type-definitions/*": ["../type-definitions/*"], "@crf/shared-utils": ["../shared-utils/index.ts"], "@crf/shared-utils/*": ["../shared-utils/*"], "@crf/vue-hooks": ["../vue-hooks/index.ts"], "@crf/vue-hooks/*": ["../vue-hooks/*"]}}, "references": [{"path": "../type-definitions"}, {"path": "../shared-utils"}], "include": ["src/**/*.ts"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}