import { defineBuildConfig } from 'unbuild'

export default defineBuildConfig({
  entries: [
    'src/index',
    'src/history-manager/index',
    'src/useDragEnhanced',
    'src/usePerformance',
    'src/useFormComponent',
    'src/useOptionManager'
  ],
  declaration: true,
  clean: true,
  rollup: {
    emitCJS: true,
    inlineDependencies: true,
  },
  externals: [
    'vue',
    '@vueuse/core',
    '@vueuse/shared',
    '@crf/type-definitions',
    '@crf/shared-utils',
    '@crf/app-constants'
  ]
})