/**
 * 设计令牌 - 医疗CRF专业设计系统
 * 统一管理所有设计系统的基础值，专为医疗行业优化
 */

// =============================================================================
// 医疗专业色彩系统
// =============================================================================

// 基础色彩调色板
export const baseColors = {
  // 医疗蓝 - 主色调，代表专业、信任、稳定
  medical: {
    50: '#f0f8ff', // 极浅医疗蓝
    100: '#e0f2fe', // 浅医疗蓝
    200: '#bae6fd', // 较浅医疗蓝
    300: '#7dd3fc', // 中浅医疗蓝
    400: '#38bdf8', // 中医疗蓝
    500: '#0ea5e9', // 标准医疗蓝
    600: '#0284c7', // 深医疗蓝
    700: '#0369a1', // 较深医疗蓝
    800: '#075985', // 深医疗蓝
    900: '#0c4a6e', // 极深医疗蓝
  },

  // 医疗绿 - 健康、安全、正常状态
  health: {
    50: '#f0fdf4', // 极浅健康绿
    100: '#dcfce7', // 浅健康绿
    200: '#bbf7d0', // 较浅健康绿
    300: '#86efac', // 中浅健康绿
    400: '#4ade80', // 中健康绿
    500: '#22c55e', // 标准健康绿
    600: '#16a34a', // 深健康绿
    700: '#15803d', // 较深健康绿
    800: '#166534', // 深健康绿
    900: '#14532d', // 极深健康绿
  },

  // 警告橙 - 注意、警告、需要关注
  warning: {
    50: '#fffbeb', // 极浅警告橙
    100: '#fef3c7', // 浅警告橙
    200: '#fde68a', // 较浅警告橙
    300: '#fcd34d', // 中浅警告橙
    400: '#fbbf24', // 中警告橙
    500: '#f59e0b', // 标准警告橙
    600: '#d97706', // 深警告橙
    700: '#b45309', // 较深警告橙
    800: '#92400e', // 深警告橙
    900: '#78350f', // 极深警告橙
  },

  // 危险红 - 错误、危险、紧急状态
  danger: {
    50: '#fef2f2', // 极浅危险红
    100: '#fee2e2', // 浅危险红
    200: '#fecaca', // 较浅危险红
    300: '#fca5a5', // 中浅危险红
    400: '#f87171', // 中危险红
    500: '#ef4444', // 标准危险红
    600: '#dc2626', // 深危险红
    700: '#b91c1c', // 较深危险红
    800: '#991b1b', // 深危险红
    900: '#7f1d1d', // 极深危险红
  },

  // 中性灰 - 文本、边框、背景
  neutral: {
    50: '#f8fafc', // 极浅灰
    100: '#f1f5f9', // 浅灰
    200: '#e2e8f0', // 较浅灰
    300: '#cbd5e1', // 中浅灰
    400: '#94a3b8', // 中灰
    500: '#64748b', // 标准灰
    600: '#475569', // 深灰
    700: '#334155', // 较深灰
    800: '#1e293b', // 深灰
    900: '#0f172a', // 极深灰
  },
}

// 医疗语义色彩
export const medicalSemanticColors = {
  // 患者状态色彩
  patient: {
    stable: baseColors.health[500], // 稳定 - 绿色
    critical: baseColors.danger[500], // 危重 - 红色
    monitoring: baseColors.warning[500], // 监护 - 橙色
    discharged: baseColors.neutral[500], // 出院 - 灰色
  },

  // 生命体征色彩
  vitals: {
    normal: baseColors.health[500], // 正常 - 绿色
    abnormal: baseColors.warning[500], // 异常 - 橙色
    critical: baseColors.danger[500], // 危险 - 红色
    unknown: baseColors.neutral[400], // 未知 - 灰色
  },

  // 药物相关色彩
  medication: {
    active: baseColors.medical[500], // 在用 - 医疗蓝
    discontinued: baseColors.neutral[400], // 停用 - 灰色
    allergic: baseColors.danger[500], // 过敏 - 红色
    contraindicated: baseColors.warning[600], // 禁忌 - 深橙
  },

  // 检查结果色彩
  lab: {
    normal: baseColors.health[500], // 正常 - 绿色
    high: baseColors.warning[500], // 偏高 - 橙色
    low: baseColors.medical[500], // 偏低 - 蓝色
    critical: baseColors.danger[500], // 危急 - 红色
  },
}

// 主题色彩定义（向后兼容）
export const colors = {
  // 主色调
  primary: baseColors.medical,
  secondary: baseColors.neutral,

  // 状态色
  success: baseColors.health,
  warning: baseColors.warning,
  error: baseColors.danger,
  info: baseColors.medical,

  // 中性色
  gray: baseColors.neutral,

  // 医疗语义色
  ...medicalSemanticColors,

  // 基础色彩
  medical: baseColors.medical,
  health: baseColors.health,
  danger: baseColors.danger,
  neutral: baseColors.neutral,
}

// 间距令牌
export const spacing = {
  0: '0px',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  7: '28px',
  8: '32px',
  9: '36px',
  10: '40px',
  11: '44px',
  12: '48px',
  14: '56px',
  16: '64px',
  20: '80px',
  24: '96px',
  28: '112px',
  32: '128px',
  36: '144px',
  40: '160px',
  44: '176px',
  48: '192px',
  52: '208px',
  56: '224px',
  60: '240px',
  64: '256px',
  72: '288px',
  80: '320px',
  96: '384px',
}

// =============================================================================
// 字体系统
// =============================================================================
export const typography = {
  fontFamily: {
    // 无衬线字体 - 用于界面文本
    sans: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Roboto',
      'Helvetica Neue',
      'Arial',
      'sans-serif',
    ],
    // 等宽字体 - 用于代码和数据
    mono: [
      'JetBrains Mono',
      'SF Mono',
      'Monaco',
      'Inconsolata',
      'Roboto Mono',
      'Consolas',
      'monospace',
    ],
    // 医疗专用字体 - 用于医疗数据显示
    medical: ['SF Pro Display', 'Inter', 'system-ui', 'sans-serif'],
  },

  fontSize: {
    xs: ['12px', { lineHeight: '16px' }],
    sm: ['14px', { lineHeight: '20px' }],
    base: ['16px', { lineHeight: '24px' }],
    lg: ['18px', { lineHeight: '28px' }],
    xl: ['20px', { lineHeight: '28px' }],
    '2xl': ['24px', { lineHeight: '32px' }],
    '3xl': ['30px', { lineHeight: '36px' }],
    '4xl': ['36px', { lineHeight: '40px' }],
    '5xl': ['48px', { lineHeight: '1' }],
    '6xl': ['60px', { lineHeight: '1' }],
    '7xl': ['72px', { lineHeight: '1' }],
    '8xl': ['96px', { lineHeight: '1' }],
    '9xl': ['128px', { lineHeight: '1' }],
  },

  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
}

// =============================================================================
// 阴影系统
// =============================================================================
export const shadows = {
  // 基础阴影
  none: '0 0 #0000',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',

  // 内阴影
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',

  // 医疗专用阴影
  medical: {
    card: '0 2px 8px 0 rgb(14 165 233 / 0.1)', // 医疗卡片阴影
    modal: '0 20px 40px -4px rgb(14 165 233 / 0.15)', // 医疗模态框阴影
    alert: '0 4px 12px 0 rgb(239 68 68 / 0.15)', // 医疗警告阴影
  },
}

// =============================================================================
// 边框半径系统
// =============================================================================
export const borderRadius = {
  none: '0',
  sm: '0.125rem', // 2px
  base: '0.25rem', // 4px
  md: '0.375rem', // 6px
  lg: '0.5rem', // 8px
  xl: '0.75rem', // 12px
  '2xl': '1rem', // 16px
  '3xl': '1.5rem', // 24px
  full: '9999px',

  // 医疗专用圆角
  medical: {
    card: '0.5rem', // 医疗卡片
    button: '0.375rem', // 医疗按钮
    input: '0.25rem', // 医疗输入框
    modal: '0.75rem', // 医疗模态框
  },
}

// 边框令牌
export const borderWidth = {
  0: '0px',
  base: '1px',
  2: '2px',
  4: '4px',
  8: '8px',
}

// 透明度令牌
export const opacity = {
  0: '0',
  5: '0.05',
  10: '0.1',
  20: '0.2',
  25: '0.25',
  30: '0.3',
  40: '0.4',
  50: '0.5',
  60: '0.6',
  70: '0.7',
  75: '0.75',
  80: '0.8',
  90: '0.9',
  95: '0.95',
  100: '1',
}

// =============================================================================
// 动画系统
// =============================================================================
export const animation = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
    // 具体时长
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },

  easing: {
    linear: 'linear',
    ease: 'ease',
    'ease-in': 'ease-in',
    'ease-out': 'ease-out',
    'ease-in-out': 'ease-in-out',
    medical: 'cubic-bezier(0.4, 0, 0.2, 1)', // 医疗专用缓动
  },
}

// 向后兼容
export const transitions = animation

// =============================================================================
// Z-index 系统
// =============================================================================
export const zIndex = {
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  auto: 'auto',

  // 语义化 z-index
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modalBackdrop: '1040',
  modal: '1050',
  popover: '1060',
  tooltip: '1070',
}

// =============================================================================
// 导出所有设计令牌
// =============================================================================
export const designTokens = {
  colors: {
    base: baseColors,
    semantic: medicalSemanticColors,
    theme: colors,
  },
  spacing,
  typography,
  shadows,
  borderRadius,
  borderWidth,
  opacity,
  animation,
  transitions,
  zIndex,
}

// 向后兼容的别名导出
export { baseColors as medicalColors }
export { medicalSemanticColors as semanticColors }
