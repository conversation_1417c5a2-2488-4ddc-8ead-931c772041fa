/* CRF 统一颜色变量 */
:root {
  /* 主色系 */
  --crf-color-primary: #3b82f6;
  --crf-color-primary-hover: #2563eb;
  --crf-color-primary-active: #1d4ed8;
  --crf-color-primary-light: #dbeafe;
  
  /* 功能色系 */
  --crf-color-success: #10b981;
  --crf-color-success-light: #d1fae5;
  --crf-color-warning: #f59e0b;
  --crf-color-warning-light: #fef3c7;
  --crf-color-error: #ef4444;
  --crf-color-error-light: #fee2e2;
  --crf-color-info: #3b82f6;
  --crf-color-info-light: #dbeafe;
  
  /* 文本色系 */
  --crf-color-text-primary: #1f2937;
  --crf-color-text-secondary: #6b7280;
  --crf-color-text-tertiary: #9ca3af;
  --crf-color-text-disabled: #d1d5db;
  
  /* 背景色系 */
  --crf-color-bg-primary: #ffffff;
  --crf-color-bg-secondary: #f9fafb;
  --crf-color-bg-tertiary: #f3f4f6;
  --crf-color-bg-disabled: #f9fafb;
  
  /* 边框色系 */
  --crf-color-border: #e5e7eb;
  --crf-color-border-light: #f3f4f6;
  --crf-color-border-dark: #d1d5db;
  
  /* 阴影 */
  --crf-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --crf-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --crf-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  
  /* 圆角 */
  --crf-radius-sm: 0.125rem;
  --crf-radius: 0.25rem;
  --crf-radius-md: 0.375rem;
  --crf-radius-lg: 0.5rem;
  --crf-radius-xl: 0.75rem;
  
  /* 间距 */
  --crf-spacing-xs: 0.25rem;
  --crf-spacing-sm: 0.5rem;
  --crf-spacing: 1rem;
  --crf-spacing-md: 1.5rem;
  --crf-spacing-lg: 2rem;
  --crf-spacing-xl: 3rem;
}

/* 暗色主题 */
[data-theme="dark"] {
  --crf-color-text-primary: #f9fafb;
  --crf-color-text-secondary: #d1d5db;
  --crf-color-bg-primary: #1f2937;
  --crf-color-bg-secondary: #111827;
  --crf-color-border: #374151;
}
