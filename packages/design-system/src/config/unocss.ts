/**
 * UnoCSS配置 - 医疗CRF专用配置
 * 集成设计令牌和医疗主题
 */

import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'
import {
  baseColors,
  medicalSemanticColors,
  spacing,
  typography,
  shadows,
  borderRadius,
} from '../tokens'

// =============================================================================
// 医疗专用UnoCSS配置
// =============================================================================
export const medicalUnoConfig = defineConfig({
  // 预设
  presets: [
    presetUno(),
    presetAttributify(),
    // 暂时移除图标预设以避免构建问题
    // presetIcons({
    //   collections: {
    //     // 医疗图标集
    //     medical: () => import('@iconify-json/medical-icon/icons.json').then(i => i.default),
    //     // 健康图标集
    //     health: () => import('@iconify-json/healthicons/icons.json').then(i => i.default),
    //     // 基础图标集
    //     carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
    //     // 材料设计图标
    //     mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
    //   },
    // }),
  ],

  // 主题配置
  theme: {
    // 颜色系统
    colors: {
      // 基础医疗色彩
      medical: baseColors.medical,
      health: baseColors.health,
      warning: baseColors.warning,
      danger: baseColors.danger,
      neutral: baseColors.neutral,

      // 医疗语义色彩
      'patient-stable': medicalSemanticColors.patient.stable,
      'patient-critical': medicalSemanticColors.patient.critical,
      'patient-monitoring': medicalSemanticColors.patient.monitoring,
      'patient-discharged': medicalSemanticColors.patient.discharged,

      'vitals-normal': medicalSemanticColors.vitals.normal,
      'vitals-abnormal': medicalSemanticColors.vitals.abnormal,
      'vitals-critical': medicalSemanticColors.vitals.critical,
      'vitals-unknown': medicalSemanticColors.vitals.unknown,

      'medication-active': medicalSemanticColors.medication.active,
      'medication-discontinued': medicalSemanticColors.medication.discontinued,
      'medication-allergic': medicalSemanticColors.medication.allergic,
      'medication-contraindicated':
        medicalSemanticColors.medication.contraindicated,

      'lab-normal': medicalSemanticColors.lab.normal,
      'lab-high': medicalSemanticColors.lab.high,
      'lab-low': medicalSemanticColors.lab.low,
      'lab-critical': medicalSemanticColors.lab.critical,
    },

    // 间距系统
    spacing,

    // 字体系统
    fontFamily: typography.fontFamily,
    fontSize: typography.fontSize,
    fontWeight: typography.fontWeight,
    lineHeight: typography.lineHeight,

    // 阴影系统
    boxShadow: shadows,

    // 圆角系统
    borderRadius,
  },

  // 快捷方式
  shortcuts: {
    // 医疗卡片样式
    'medical-card':
      'bg-white rounded-lg shadow-md border border-neutral-200 p-6',
    'medical-card-hover':
      'medical-card hover:shadow-lg transition-shadow duration-300',

    // 患者状态指示器
    'patient-status-stable':
      'bg-patient-stable text-white px-2 py-1 rounded-full text-xs font-medium',
    'patient-status-critical':
      'bg-patient-critical text-white px-2 py-1 rounded-full text-xs font-medium',
    'patient-status-monitoring':
      'bg-patient-monitoring text-white px-2 py-1 rounded-full text-xs font-medium',
    'patient-status-discharged':
      'bg-patient-discharged text-white px-2 py-1 rounded-full text-xs font-medium',

    // 生命体征指示器
    'vitals-normal': 'text-vitals-normal font-semibold',
    'vitals-abnormal': 'text-vitals-abnormal font-semibold',
    'vitals-critical': 'text-vitals-critical font-semibold animate-pulse',
    'vitals-unknown': 'text-vitals-unknown',

    // 医疗按钮
    'btn-medical':
      'bg-medical-500 hover:bg-medical-600 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200',
    'btn-medical-outline':
      'border border-medical-500 text-medical-500 hover:bg-medical-50 px-4 py-2 rounded-md font-medium transition-colors duration-200',

    // 医疗输入框
    'input-medical':
      'border border-neutral-300 rounded-md px-3 py-2 focus:border-medical-500 focus:ring-1 focus:ring-medical-500 outline-none transition-colors duration-200',

    // 医疗表格
    'table-medical': 'w-full border-collapse',
    'table-medical-header':
      'bg-neutral-50 border-b border-neutral-200 px-4 py-3 text-left font-semibold text-neutral-700',
    'table-medical-cell':
      'border-b border-neutral-100 px-4 py-3 text-neutral-600',

    // 医疗警告
    'alert-medical-info':
      'bg-medical-50 border border-medical-200 text-medical-800 p-4 rounded-md',
    'alert-medical-warning':
      'bg-warning-50 border border-warning-200 text-warning-800 p-4 rounded-md',
    'alert-medical-error':
      'bg-danger-50 border border-danger-200 text-danger-800 p-4 rounded-md',
    'alert-medical-success':
      'bg-health-50 border border-health-200 text-health-800 p-4 rounded-md',

    // 医疗标签
    'tag-medical':
      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
    'tag-medication-active': 'tag-medical bg-medication-active text-white',
    'tag-medication-discontinued':
      'tag-medical bg-medication-discontinued text-white',
    'tag-medication-allergic': 'tag-medical bg-medication-allergic text-white',

    // 布局快捷方式
    'flex-center': 'flex items-center justify-center',
    'flex-between': 'flex items-center justify-between',
    'flex-start': 'flex items-center justify-start',
    'flex-end': 'flex items-center justify-end',

    // 响应式容器
    'container-medical': 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
    'container-medical-sm': 'max-w-3xl mx-auto px-4 sm:px-6',

    // 医疗表单
    'form-medical': 'space-y-6',
    'form-group-medical': 'space-y-2',
    'form-label-medical': 'block text-sm font-medium text-neutral-700',
    'form-error-medical': 'text-sm text-danger-600 mt-1',
    'form-help-medical': 'text-sm text-neutral-500 mt-1',
  },

  // 规则
  rules: [
    // 医疗专用渐变
    [
      'bg-gradient-medical',
      {
        background: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
      },
    ],
    [
      'bg-gradient-health',
      {
        background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
      },
    ],
    [
      'bg-gradient-warning',
      {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
      },
    ],
    [
      'bg-gradient-danger',
      {
        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
      },
    ],

    // 医疗专用动画
    [
      /^animate-pulse-medical$/,
      () => ({
        animation: 'pulse-medical 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      }),
    ],
    [
      /^animate-fade-in-medical$/,
      () => ({
        animation: 'fade-in-medical 0.3s ease-out',
      }),
    ],
  ],

  // 变体
  variants: [
    // 医疗状态变体
    (matcher) => {
      if (!matcher.startsWith('medical:')) return matcher
      return {
        matcher: matcher.slice(8),
        selector: (s) => `[data-medical-theme] ${s}`,
      }
    },

    // 患者状态变体
    (matcher) => {
      if (!matcher.startsWith('patient:')) return matcher
      return {
        matcher: matcher.slice(8),
        selector: (s) => `[data-patient-status] ${s}`,
      }
    },
  ],

  // 安全列表 - 确保这些类不会被清除
  safelist: [
    'medical-card',
    'medical-card-hover',
    'btn-medical',
    'btn-medical-outline',
    'input-medical',
    'patient-status-stable',
    'patient-status-critical',
    'patient-status-monitoring',
    'patient-status-discharged',
    'vitals-normal',
    'vitals-abnormal',
    'vitals-critical',
    'vitals-unknown',
  ],
})

// 导出默认配置
export default medicalUnoConfig
