/**
 * 主题配置
 * UnoCSS 和 SCSS 配置
 */

import {
  colors,
  spacing,
  typography,
  shadows,
  borderRadius,
  transitions,
  zIndex,
} from '../tokens'

// UnoCSS 主题配置
export const unocssTheme = {
  colors,
  spacing,
  fontFamily: typography.fontFamily,
  fontSize: typography.fontSize,
  fontWeight: typography.fontWeight,
  boxShadow: shadows,
  borderRadius,
  transitionDuration: transitions.duration,
  transitionTimingFunction: transitions.timing,
  zIndex,
}

// 医疗主题配置
export const medicalTheme = {
  name: 'medical',
  colors: {
    primary: colors.medical,
    secondary: colors.primary,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    gray: colors.gray,
  },
  components: {
    input: {
      background: 'linear-gradient(135deg, #FDF2F8 0%, #FCE7F3 100%)',
      borderColor: colors.medical[300],
      focusBorderColor: colors.medical[500],
      focusBoxShadow: `0 0 0 2px ${colors.medical[500]}20`,
    },
    button: {
      primary: {
        background: colors.medical[500],
        hoverBackground: colors.medical[600],
        activeBackground: colors.medical[700],
      },
    },
  },
}

// 默认主题配置
export const defaultTheme = {
  name: 'default',
  colors: {
    primary: colors.primary,
    secondary: colors.gray,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    gray: colors.gray,
  },
  components: {
    input: {
      background: '#FFFFFF',
      borderColor: colors.gray[300],
      focusBorderColor: colors.primary[500],
      focusBoxShadow: `0 0 0 2px ${colors.primary[500]}20`,
    },
    button: {
      primary: {
        background: colors.primary[500],
        hoverBackground: colors.primary[600],
        activeBackground: colors.primary[700],
      },
    },
  },
}

// 深色主题配置
export const darkTheme = {
  name: 'dark',
  colors: {
    primary: colors.primary,
    secondary: colors.gray,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    gray: {
      50: colors.gray[950],
      100: colors.gray[900],
      200: colors.gray[800],
      300: colors.gray[700],
      400: colors.gray[600],
      500: colors.gray[500],
      600: colors.gray[400],
      700: colors.gray[300],
      800: colors.gray[200],
      900: colors.gray[100],
      950: colors.gray[50],
    },
  },
  components: {
    input: {
      background: colors.gray[800],
      borderColor: colors.gray[600],
      focusBorderColor: colors.primary[500],
      focusBoxShadow: `0 0 0 2px ${colors.primary[500]}20`,
    },
    button: {
      primary: {
        background: colors.primary[500],
        hoverBackground: colors.primary[600],
        activeBackground: colors.primary[700],
      },
    },
  },
}

// 主题集合
export const themes = {
  default: defaultTheme,
  medical: medicalTheme,
  dark: darkTheme,
}

// 主题类型
export type ThemeName = keyof typeof themes
export type Theme = typeof defaultTheme
