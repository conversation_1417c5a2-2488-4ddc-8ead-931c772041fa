/**
 * @crf/design-system 入口文件
 * 医疗CRF专业设计系统 - 统一设计令牌、主题和配置
 * 用于替换项目中所有分散的样式系统
 */

// =============================================================================
// 设计令牌
// =============================================================================
export * from './tokens'
export {
  designTokens,
  baseColors,
  medicalSemanticColors,
  colors,
  spacing,
  typography,
  shadows,
  borderRadius,
  animation,
  zIndex,
} from './tokens'

// =============================================================================
// 主题系统
// =============================================================================
export * from './themes'
export {
  lightTheme,
  darkTheme,
  medicalTheme,
  themes,
  defaultTheme,
  getTheme,
  getThemeNames,
  generateCSSVariables,
} from './themes'
export type { Theme, ThemeName } from './themes'

// =============================================================================
// UnoCSS配置 (暂时禁用以避免构建问题)
// =============================================================================
// export { medicalUnoConfig as unoConfig } from './config/unocss'

// =============================================================================
// 配置和工具
// =============================================================================
export * from './config'

// 导出工具函数
export const createCssVar = (
  name: string,
  namespace: string = 'crf',
): string => {
  return `--${namespace}-${name}`
}

export const getCssVarValue = (
  name: string,
  namespace: string = 'crf',
): string => {
  return `var(${createCssVar(name, namespace)})`
}

export const createCssVars = (
  vars: Record<string, string>,
  namespace: string = 'crf',
): Record<string, string> => {
  const cssVars: Record<string, string> = {}

  for (const [key, value] of Object.entries(vars)) {
    cssVars[createCssVar(key, namespace)] = value
  }

  return cssVars
}

// 导出常用的CSS变量名
export const cssVars = {
  // 颜色
  colorPrimary: getCssVarValue('color-primary'),
  colorSuccess: getCssVarValue('color-success'),
  colorWarning: getCssVarValue('color-warning'),
  colorError: getCssVarValue('color-error'),
  colorTextPrimary: getCssVarValue('color-text-primary'),
  colorTextSecondary: getCssVarValue('color-text-secondary'),
  colorBgPrimary: getCssVarValue('color-bg-primary'),
  colorBgSecondary: getCssVarValue('color-bg-secondary'),
  colorBorderPrimary: getCssVarValue('color-border-primary'),

  // 间距
  spacingXs: getCssVarValue('spacing-xs'),
  spacingSm: getCssVarValue('spacing-sm'),
  spacingMd: getCssVarValue('spacing-md'),
  spacingLg: getCssVarValue('spacing-lg'),
  spacingXl: getCssVarValue('spacing-xl'),

  // 字体
  fontSizeXs: getCssVarValue('font-size-xs'),
  fontSizeSm: getCssVarValue('font-size-sm'),
  fontSizeBase: getCssVarValue('font-size-base'),
  fontSizeLg: getCssVarValue('font-size-lg'),

  // 圆角
  radiusButton: getCssVarValue('radius-button'),
  radiusInput: getCssVarValue('radius-input'),
  radiusCard: getCssVarValue('radius-card'),

  // 阴影
  shadowSm: getCssVarValue('shadow-sm'),
  shadowBase: getCssVarValue('shadow-base'),
  shadowMd: getCssVarValue('shadow-md'),

  // 过渡
  transitionColors: getCssVarValue('transition-colors'),
  transitionAll: getCssVarValue('transition-all'),
}

// =============================================================================
// 类型定义和工具函数
// =============================================================================
export * from './types'
export * from './utils'

// =============================================================================
// 默认导出
// =============================================================================
import { designTokens } from './tokens'
import { themes, defaultTheme } from './themes'

export default {
  tokens: designTokens,
  themes,
  defaultTheme,
}
