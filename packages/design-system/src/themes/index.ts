/**
 * 主题系统 - 医疗CRF专业主题
 * 支持亮色、暗色和医疗专用主题
 */

import { baseColors, medicalSemanticColors } from '../tokens'

// =============================================================================
// 主题类型定义
// =============================================================================
export interface Theme {
  name: string
  colors: {
    // 基础色彩
    primary: string
    secondary: string
    background: string
    surface: string

    // 文本色彩
    text: {
      primary: string
      secondary: string
      disabled: string
      inverse: string
    }

    // 边框色彩
    border: {
      primary: string
      secondary: string
      focus: string
    }

    // 状态色彩
    status: {
      success: string
      warning: string
      error: string
      info: string
    }

    // 医疗语义色彩
    medical: {
      patient: {
        stable: string
        critical: string
        monitoring: string
        discharged: string
      }
      vitals: {
        normal: string
        abnormal: string
        critical: string
        unknown: string
      }
      medication: {
        active: string
        discontinued: string
        allergic: string
        contraindicated: string
      }
      lab: {
        normal: string
        high: string
        low: string
        critical: string
      }
    }
  }
}

// =============================================================================
// 亮色主题
// =============================================================================
export const lightTheme: Theme = {
  name: 'light',
  colors: {
    // 基础色彩
    primary: baseColors.medical[500],
    secondary: baseColors.neutral[500],
    background: '#ffffff',
    surface: baseColors.neutral[50],

    // 文本色彩
    text: {
      primary: baseColors.neutral[900],
      secondary: baseColors.neutral[600],
      disabled: baseColors.neutral[400],
      inverse: '#ffffff',
    },

    // 边框色彩
    border: {
      primary: baseColors.neutral[200],
      secondary: baseColors.neutral[100],
      focus: baseColors.medical[500],
    },

    // 状态色彩
    status: {
      success: baseColors.health[500],
      warning: baseColors.warning[500],
      error: baseColors.danger[500],
      info: baseColors.medical[500],
    },

    // 医疗语义色彩
    medical: medicalSemanticColors,
  },
}

// =============================================================================
// 暗色主题
// =============================================================================
export const darkTheme: Theme = {
  name: 'dark',
  colors: {
    // 基础色彩
    primary: baseColors.medical[400],
    secondary: baseColors.neutral[400],
    background: baseColors.neutral[900],
    surface: baseColors.neutral[800],

    // 文本色彩
    text: {
      primary: baseColors.neutral[50],
      secondary: baseColors.neutral[300],
      disabled: baseColors.neutral[500],
      inverse: baseColors.neutral[900],
    },

    // 边框色彩
    border: {
      primary: baseColors.neutral[700],
      secondary: baseColors.neutral[800],
      focus: baseColors.medical[400],
    },

    // 状态色彩
    status: {
      success: baseColors.health[400],
      warning: baseColors.warning[400],
      error: baseColors.danger[400],
      info: baseColors.medical[400],
    },

    // 医疗语义色彩（暗色调整）
    medical: {
      patient: {
        stable: baseColors.health[400],
        critical: baseColors.danger[400],
        monitoring: baseColors.warning[400],
        discharged: baseColors.neutral[400],
      },
      vitals: {
        normal: baseColors.health[400],
        abnormal: baseColors.warning[400],
        critical: baseColors.danger[400],
        unknown: baseColors.neutral[400],
      },
      medication: {
        active: baseColors.medical[400],
        discontinued: baseColors.neutral[400],
        allergic: baseColors.danger[400],
        contraindicated: baseColors.warning[500],
      },
      lab: {
        normal: baseColors.health[400],
        high: baseColors.warning[400],
        low: baseColors.medical[400],
        critical: baseColors.danger[400],
      },
    },
  },
}

// =============================================================================
// 医疗专用主题
// =============================================================================
export const medicalTheme: Theme = {
  name: 'medical',
  colors: {
    // 基础色彩 - 更专业的医疗配色
    primary: baseColors.medical[600],
    secondary: baseColors.neutral[500],
    background: '#fafbfc',
    surface: '#ffffff',

    // 文本色彩
    text: {
      primary: baseColors.neutral[800],
      secondary: baseColors.neutral[600],
      disabled: baseColors.neutral[400],
      inverse: '#ffffff',
    },

    // 边框色彩
    border: {
      primary: baseColors.neutral[200],
      secondary: baseColors.neutral[100],
      focus: baseColors.medical[600],
    },

    // 状态色彩 - 医疗专用调色
    status: {
      success: baseColors.health[600],
      warning: baseColors.warning[600],
      error: baseColors.danger[600],
      info: baseColors.medical[600],
    },

    // 医疗语义色彩 - 增强对比度
    medical: {
      patient: {
        stable: baseColors.health[600],
        critical: baseColors.danger[600],
        monitoring: baseColors.warning[600],
        discharged: baseColors.neutral[500],
      },
      vitals: {
        normal: baseColors.health[600],
        abnormal: baseColors.warning[600],
        critical: baseColors.danger[600],
        unknown: baseColors.neutral[400],
      },
      medication: {
        active: baseColors.medical[600],
        discontinued: baseColors.neutral[400],
        allergic: baseColors.danger[600],
        contraindicated: baseColors.warning[700],
      },
      lab: {
        normal: baseColors.health[600],
        high: baseColors.warning[600],
        low: baseColors.medical[600],
        critical: baseColors.danger[600],
      },
    },
  },
}

// =============================================================================
// 主题管理
// =============================================================================
export const themes = {
  light: lightTheme,
  dark: darkTheme,
  medical: medicalTheme,
} as const

export type ThemeName = keyof typeof themes

// 默认主题
export const defaultTheme = lightTheme

// 主题工具函数
export function getTheme(name: ThemeName): Theme {
  return themes[name] || defaultTheme
}

export function getThemeNames(): ThemeName[] {
  return Object.keys(themes) as ThemeName[]
}

// CSS变量生成器
export function generateCSSVariables(theme: Theme): Record<string, string> {
  const variables: Record<string, string> = {}

  // 基础色彩
  variables['--color-primary'] = theme.colors.primary
  variables['--color-secondary'] = theme.colors.secondary
  variables['--color-background'] = theme.colors.background
  variables['--color-surface'] = theme.colors.surface

  // 文本色彩
  variables['--color-text-primary'] = theme.colors.text.primary
  variables['--color-text-secondary'] = theme.colors.text.secondary
  variables['--color-text-disabled'] = theme.colors.text.disabled
  variables['--color-text-inverse'] = theme.colors.text.inverse

  // 边框色彩
  variables['--color-border-primary'] = theme.colors.border.primary
  variables['--color-border-secondary'] = theme.colors.border.secondary
  variables['--color-border-focus'] = theme.colors.border.focus

  // 状态色彩
  variables['--color-success'] = theme.colors.status.success
  variables['--color-warning'] = theme.colors.status.warning
  variables['--color-error'] = theme.colors.status.error
  variables['--color-info'] = theme.colors.status.info

  // 医疗语义色彩
  const medical = theme.colors.medical
  variables['--color-patient-stable'] = medical.patient.stable
  variables['--color-patient-critical'] = medical.patient.critical
  variables['--color-patient-monitoring'] = medical.patient.monitoring
  variables['--color-patient-discharged'] = medical.patient.discharged

  variables['--color-vitals-normal'] = medical.vitals.normal
  variables['--color-vitals-abnormal'] = medical.vitals.abnormal
  variables['--color-vitals-critical'] = medical.vitals.critical
  variables['--color-vitals-unknown'] = medical.vitals.unknown

  return variables
}
