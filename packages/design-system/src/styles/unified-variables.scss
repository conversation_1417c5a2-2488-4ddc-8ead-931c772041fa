/**
 * 统一CSS变量系统
 * 基于设计令牌生成的CSS变量，用于替换项目中所有硬编码样式
 * 这个文件将逐步替换所有现有的变量系统（JZ、CRF、Naive UI等）
 */

:root {
  /* ===== 颜色系统 ===== */
  
  /* 主色调系统 - 医疗专业蓝色 */
  --crf-color-primary-50: #f0f9ff;
  --crf-color-primary-100: #e0f2fe;
  --crf-color-primary-200: #bae6fd;
  --crf-color-primary-300: #7dd3fc;
  --crf-color-primary-400: #38bdf8;
  --crf-color-primary-500: #667eea; /* 主色调 - 替换所有 #667eea */
  --crf-color-primary-600: #5a67d8; /* hover 状态 */
  --crf-color-primary-700: #4c51bf; /* pressed 状态 */
  --crf-color-primary-800: #434190;
  --crf-color-primary-900: #3730a3;
  --crf-color-primary-950: #1e1b4b;
  
  /* 主色调语义化别名 */
  --crf-color-primary: var(--crf-color-primary-500);
  --crf-color-primary-hover: var(--crf-color-primary-600);
  --crf-color-primary-pressed: var(--crf-color-primary-700);
  --crf-color-primary-disabled: var(--crf-color-primary-300);
  
  /* 功能色系统 */
  --crf-color-success-50: #f0fdf4;
  --crf-color-success-100: #dcfce7;
  --crf-color-success-200: #bbf7d0;
  --crf-color-success-300: #86efac;
  --crf-color-success-400: #4ade80;
  --crf-color-success-500: #10b981; /* 成功色 */
  --crf-color-success-600: #059669;
  --crf-color-success-700: #047857;
  --crf-color-success-800: #065f46;
  --crf-color-success-900: #064e3b;
  
  --crf-color-warning-50: #fffbeb;
  --crf-color-warning-100: #fef3c7;
  --crf-color-warning-200: #fde68a;
  --crf-color-warning-300: #fcd34d;
  --crf-color-warning-400: #fbbf24;
  --crf-color-warning-500: #f59e0b; /* 警告色 */
  --crf-color-warning-600: #d97706;
  --crf-color-warning-700: #b45309;
  --crf-color-warning-800: #92400e;
  --crf-color-warning-900: #78350f;
  
  --crf-color-error-50: #fef2f2;
  --crf-color-error-100: #fee2e2;
  --crf-color-error-200: #fecaca;
  --crf-color-error-300: #fca5a5;
  --crf-color-error-400: #f87171;
  --crf-color-error-500: #ef4444; /* 错误色 - 替换所有 #f53f3f, #ef4444 */
  --crf-color-error-600: #dc2626;
  --crf-color-error-700: #b91c1c;
  --crf-color-error-800: #991b1b;
  --crf-color-error-900: #7f1d1d;
  
  /* 功能色语义化别名 */
  --crf-color-success: var(--crf-color-success-500);
  --crf-color-warning: var(--crf-color-warning-500);
  --crf-color-error: var(--crf-color-error-500);
  --crf-color-danger: var(--crf-color-error-500); /* 兼容性别名 */
  
  /* 中性色系统 */
  --crf-color-gray-50: #f9fafb;   /* 最浅背景 */
  --crf-color-gray-100: #f3f4f6;  /* 浅背景 */
  --crf-color-gray-200: #e5e7eb;  /* 边框色 - 替换所有 #e8eaec, #e5e7eb */
  --crf-color-gray-300: #d1d5db;  /* 次要边框 */
  --crf-color-gray-400: #9ca3af;  /* 占位符文本 */
  --crf-color-gray-500: #6b7280;  /* 次要文本 */
  --crf-color-gray-600: #4b5563;  /* 常规文本 */
  --crf-color-gray-700: #374151;  /* 深色文本 */
  --crf-color-gray-800: #1f2937;  /* 主要文本 - 替换所有 #2d3748, #1f2329 */
  --crf-color-gray-900: #111827;  /* 最深文本 */
  --crf-color-gray-950: #030712;  /* 纯黑 */
  
  /* 文本颜色语义化别名 */
  --crf-color-text-primary: var(--crf-color-gray-800);    /* 主要文本 */
  --crf-color-text-secondary: var(--crf-color-gray-600);  /* 次要文本 */
  --crf-color-text-tertiary: var(--crf-color-gray-500);   /* 辅助文本 */
  --crf-color-text-disabled: var(--crf-color-gray-400);   /* 禁用文本 */
  --crf-color-text-placeholder: var(--crf-color-gray-400); /* 占位符文本 */
  
  /* 背景颜色语义化别名 */
  --crf-color-bg-primary: #ffffff;                        /* 主要背景 - 替换所有 #fff, #ffffff */
  --crf-color-bg-secondary: var(--crf-color-gray-50);     /* 次要背景 */
  --crf-color-bg-tertiary: var(--crf-color-gray-100);     /* 第三级背景 */
  --crf-color-bg-quaternary: var(--crf-color-gray-200);   /* 第四级背景 */
  --crf-color-bg-overlay: rgba(0, 0, 0, 0.5);             /* 遮罩背景 */
  
  /* 边框颜色语义化别名 */
  --crf-color-border-primary: var(--crf-color-gray-200);   /* 主要边框 */
  --crf-color-border-secondary: var(--crf-color-gray-300); /* 次要边框 */
  --crf-color-border-tertiary: var(--crf-color-gray-400);  /* 第三级边框 */
  --crf-color-border-focus: var(--crf-color-primary);      /* 聚焦边框 */
  
  /* ===== 间距系统 ===== */
  /* 基于 4px 网格系统 */
  --crf-spacing-0: 0px;
  --crf-spacing-px: 1px;
  --crf-spacing-0-5: 2px;
  --crf-spacing-1: 4px;    /* xs - 替换所有 4px */
  --crf-spacing-1-5: 6px;
  --crf-spacing-2: 8px;    /* sm - 替换所有 8px */
  --crf-spacing-2-5: 10px;
  --crf-spacing-3: 12px;   /* md - 替换所有 12px */
  --crf-spacing-3-5: 14px;
  --crf-spacing-4: 16px;   /* lg - 替换所有 16px */
  --crf-spacing-5: 20px;
  --crf-spacing-6: 24px;   /* xl - 替换所有 24px */
  --crf-spacing-7: 28px;
  --crf-spacing-8: 32px;   /* 2xl */
  --crf-spacing-9: 36px;
  --crf-spacing-10: 40px;
  --crf-spacing-11: 44px;
  --crf-spacing-12: 48px;
  --crf-spacing-14: 56px;
  --crf-spacing-16: 64px;
  --crf-spacing-20: 80px;
  --crf-spacing-24: 96px;
  --crf-spacing-28: 112px;
  --crf-spacing-32: 128px;
  
  /* 间距语义化别名 */
  --crf-spacing-xs: var(--crf-spacing-1);   /* 4px */
  --crf-spacing-sm: var(--crf-spacing-2);   /* 8px */
  --crf-spacing-md: var(--crf-spacing-3);   /* 12px */
  --crf-spacing-lg: var(--crf-spacing-4);   /* 16px */
  --crf-spacing-xl: var(--crf-spacing-6);   /* 24px */
  --crf-spacing-2xl: var(--crf-spacing-8);  /* 32px */
  
  /* ===== 字体系统 ===== */
  --crf-font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --crf-font-family-mono: 'JetBrains Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  
  --crf-font-size-xs: 12px;    /* 替换所有 11px, 12px */
  --crf-font-size-sm: 14px;    /* 替换所有 13px, 14px */
  --crf-font-size-base: 16px;  /* 基础字体大小 */
  --crf-font-size-lg: 18px;
  --crf-font-size-xl: 20px;
  --crf-font-size-2xl: 24px;
  --crf-font-size-3xl: 30px;
  --crf-font-size-4xl: 36px;
  
  --crf-font-weight-normal: 400;
  --crf-font-weight-medium: 500;
  --crf-font-weight-semibold: 600;
  --crf-font-weight-bold: 700;
  
  --crf-line-height-tight: 1.25;
  --crf-line-height-normal: 1.5;
  --crf-line-height-relaxed: 1.75;
  
  /* ===== 圆角系统 ===== */
  --crf-border-radius-none: 0px;
  --crf-border-radius-sm: 4px;     /* 替换所有 4px 圆角 */
  --crf-border-radius-base: 6px;   /* 替换所有 6px 圆角 */
  --crf-border-radius-md: 8px;     /* 替换所有 8px 圆角 */
  --crf-border-radius-lg: 12px;    /* 替换所有 12px 圆角 */
  --crf-border-radius-xl: 16px;
  --crf-border-radius-2xl: 24px;
  --crf-border-radius-full: 9999px;
  
  /* 圆角语义化别名 */
  --crf-radius-button: var(--crf-border-radius-md);     /* 按钮圆角 */
  --crf-radius-input: var(--crf-border-radius-base);    /* 输入框圆角 */
  --crf-radius-card: var(--crf-border-radius-lg);       /* 卡片圆角 */
  --crf-radius-modal: var(--crf-border-radius-xl);      /* 模态框圆角 */
  
  /* ===== 阴影系统 ===== */
  --crf-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --crf-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --crf-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --crf-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --crf-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --crf-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --crf-shadow-none: none;
  
  /* ===== 过渡动画系统 ===== */
  --crf-transition-duration-fast: 150ms;
  --crf-transition-duration-base: 200ms;  /* 替换所有 0.2s */
  --crf-transition-duration-slow: 300ms;
  
  --crf-transition-timing-ease: ease;
  --crf-transition-timing-ease-in: ease-in;
  --crf-transition-timing-ease-out: ease-out;
  --crf-transition-timing-ease-in-out: ease-in-out;
  
  /* 常用过渡组合 */
  --crf-transition-colors: color var(--crf-transition-duration-base) var(--crf-transition-timing-ease),
                           background-color var(--crf-transition-duration-base) var(--crf-transition-timing-ease),
                           border-color var(--crf-transition-duration-base) var(--crf-transition-timing-ease);
  
  --crf-transition-transform: transform var(--crf-transition-duration-base) var(--crf-transition-timing-ease);
  
  --crf-transition-all: all var(--crf-transition-duration-base) var(--crf-transition-timing-ease);
  
  /* ===== Z-index 系统 ===== */
  --crf-z-index-dropdown: 1000;
  --crf-z-index-sticky: 1020;
  --crf-z-index-fixed: 1030;
  --crf-z-index-modal-backdrop: 1040;
  --crf-z-index-modal: 1050;
  --crf-z-index-popover: 1060;
  --crf-z-index-tooltip: 1070;
}

/* ===== 向后兼容性别名 ===== */
/* 为了平滑迁移，保留一些常用的旧变量名 */
:root {
  /* 兼容旧的主色调变量 */
  --primary-color: var(--crf-color-primary);
  --primary-hover: var(--crf-color-primary-hover);
  --primary-pressed: var(--crf-color-primary-pressed);
  
  /* 兼容旧的功能色变量 */
  --success-color: var(--crf-color-success);
  --warning-color: var(--crf-color-warning);
  --error-color: var(--crf-color-error);
  
  /* 兼容旧的文本颜色变量 */
  --text-primary: var(--crf-color-text-primary);
  --text-secondary: var(--crf-color-text-secondary);
  
  /* 兼容旧的背景颜色变量 */
  --body-color: var(--crf-color-bg-primary);
  --page-background: var(--crf-color-bg-secondary);
  --card-background: var(--crf-color-bg-primary);
  
  /* 兼容旧的边框颜色变量 */
  --border-color: var(--crf-color-border-primary);
  --divider-color: var(--crf-color-border-primary);
}
