/**
 * CRF 统一样式系统入口文件
 * 整合所有样式模块，提供一致的设计系统
 * 用于替换项目中所有分散的样式系统
 * 
 * 使用方式：
 * 1. 在 main.ts 中导入：import '@crf/theme/styles'
 * 2. 在组件中使用统一的 CSS 变量和工具类
 * 3. 逐步替换硬编码样式
 */

/* ===== 基础样式系统 ===== */
/* 统一CSS变量 - 替换所有硬编码颜色、间距、字体等 */
@use './unified-variables.scss';

/* 公共组件样式库 - 提供可复用的组件样式 */
@use './common-components.scss';

/* 工具类库 - 提供原子级样式类 */
@use './utility-classes.scss';

/* ===== 全局重置和基础样式 ===== */
/* 现代化的 CSS 重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--crf-font-family-sans);
  font-size: var(--crf-font-size-base);
  line-height: var(--crf-line-height-normal);
  color: var(--crf-color-text-primary);
  background-color: var(--crf-color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式重置 */
a {
  color: var(--crf-color-primary);
  text-decoration: none;
  transition: var(--crf-transition-colors);
  
  &:hover {
    color: var(--crf-color-primary-hover);
    text-decoration: underline;
  }
  
  &:focus {
    outline: 2px solid var(--crf-color-primary-200);
    outline-offset: 2px;
  }
}

/* 按钮样式重置 */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

/* 输入框样式重置 */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

/* 图片样式重置 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 列表样式重置 */
ul,
ol {
  list-style: none;
}

/* 表格样式重置 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* ===== 医疗主题特定样式 ===== */
/* 医疗表单容器 */
.crf-medical-theme {
  /* 医疗专业的视觉风格 */
  --crf-medical-accent: var(--crf-color-primary);
  --crf-medical-bg: var(--crf-color-bg-primary);
  --crf-medical-border: var(--crf-color-border-primary);
  
  /* 增强的可访问性 */
  &:focus-within {
    outline: 2px solid var(--crf-color-primary-200);
    outline-offset: 2px;
  }
  
  /* 医疗数据类型标识 */
  &[data-medical-type="vital-signs"] {
    border-left: 4px solid var(--crf-color-success);
  }
  
  &[data-medical-type="medication"] {
    border-left: 4px solid var(--crf-color-warning);
  }
  
  &[data-medical-type="diagnosis"] {
    border-left: 4px solid var(--crf-color-error);
  }
  
  &[data-medical-type="procedure"] {
    border-left: 4px solid var(--crf-color-primary);
  }
}

/* ===== 响应式设计 ===== */
/* 移动端优化 */
@media (max-width: 640px) {
  body {
    font-size: var(--crf-font-size-sm);
  }
  
  .crf-base-container {
    padding: var(--crf-spacing-3);
    margin: var(--crf-spacing-2);
  }
  
  .crf-base-content--horizontal {
    flex-direction: column;
    gap: var(--crf-spacing-2);
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .crf-base-container {
    padding: var(--crf-spacing-4);
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .crf-base-container {
    padding: var(--crf-spacing-6);
  }
}

/* ===== 打印样式 ===== */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .crf-base-container {
    border: 1px solid #000 !important;
    page-break-inside: avoid;
  }
  
  .crf-option-actions {
    display: none !important;
  }
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
  :root {
    --crf-color-border-primary: #000000;
    --crf-color-text-primary: #000000;
    --crf-color-bg-primary: #ffffff;
  }
}

/* ===== 减少动画模式支持 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
