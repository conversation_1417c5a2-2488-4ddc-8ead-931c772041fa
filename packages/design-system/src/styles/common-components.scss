/**
 * 公共组件样式库
 * 提取重复的样式定义，提供可复用的基础组件样式
 * 减少代码重复，提高样式一致性
 */

/* 导入统一的变量系统 */
@use './unified-variables.scss';

/* ===== 基础容器样式 ===== */
.crf-base-container {
  border: 1px solid var(--crf-color-border-primary);
  border-radius: var(--crf-radius-card);
  padding: var(--crf-spacing-4);
  background-color: var(--crf-color-bg-primary);
  min-height: 60px;
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-2);
  width: 100%;
  box-sizing: border-box;
  transition: var(--crf-transition-colors);
  
  &:hover {
    border-color: var(--crf-color-border-focus);
  }
  
  &:focus-within {
    border-color: var(--crf-color-primary);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
  
  /* 状态变体 */
  &.crf-state-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    background-color: var(--crf-color-bg-secondary);
  }
  
  &.crf-state-error {
    border-color: var(--crf-color-error);
    
    &:focus-within {
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    }
  }
  
  &.crf-state-readonly {
    background-color: var(--crf-color-bg-secondary);
    cursor: default;
  }
}

/* ===== 组件头部样式 ===== */
.crf-base-header {
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-1);
  margin-bottom: var(--crf-spacing-2);
}

.crf-base-title {
  display: flex;
  align-items: center;
  gap: var(--crf-spacing-1);
  
  .title-text {
    font-size: var(--crf-font-size-sm);
    font-weight: var(--crf-font-weight-medium);
    color: var(--crf-color-text-primary);
    line-height: var(--crf-line-height-tight);
  }
  
  .required-mark {
    color: var(--crf-color-error);
    font-weight: var(--crf-font-weight-bold);
    font-size: var(--crf-font-size-sm);
    line-height: 1;
  }
}

.crf-base-description {
  font-size: var(--crf-font-size-xs);
  color: var(--crf-color-text-secondary);
  line-height: var(--crf-line-height-normal);
  margin-top: var(--crf-spacing-1);
}

/* ===== 组件内容区域样式 ===== */
.crf-base-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-2);
  
  /* 内容布局变体 */
  &.crf-base-content--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--crf-spacing-4);
  }
  
  &.crf-base-content--vertical {
    flex-direction: column;
    gap: var(--crf-spacing-2);
  }
  
  &.crf-base-content--grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--crf-spacing-3);
  }
}

/* ===== 错误信息样式 ===== */
.crf-base-error {
  color: var(--crf-color-error);
  font-size: var(--crf-font-size-xs);
  line-height: var(--crf-line-height-normal);
  margin-top: var(--crf-spacing-1);
  display: flex;
  align-items: center;
  gap: var(--crf-spacing-1);
  
  &::before {
    content: '⚠';
    font-size: var(--crf-font-size-sm);
  }
}

/* ===== 选项包装器样式 ===== */
.crf-option-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--crf-spacing-2);
  border-radius: var(--crf-border-radius-base);
  transition: var(--crf-transition-colors);
  min-height: 40px;
  
  &:hover {
    background-color: var(--crf-color-bg-secondary);
  }
  
  /* 拖拽状态 */
  &.crf-state-draggable {
    cursor: move;
    
    &:hover {
      background-color: var(--crf-color-primary-50);
      border: 1px dashed var(--crf-color-primary);
    }
  }
  
  &.crf-state-editing {
    background-color: var(--crf-color-primary-50);
    border: 1px solid var(--crf-color-primary);
  }
}

/* ===== 选项操作按钮样式 ===== */
.crf-option-actions {
  display: flex;
  align-items: center;
  gap: var(--crf-spacing-1);
  opacity: 0;
  transition: opacity var(--crf-transition-duration-base);
  
  .crf-option-wrapper:hover & {
    opacity: 1;
  }
}

.crf-option-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  border-radius: var(--crf-border-radius-sm);
  color: var(--crf-color-text-secondary);
  cursor: pointer;
  transition: var(--crf-transition-colors);
  
  &:hover {
    background-color: var(--crf-color-bg-tertiary);
    color: var(--crf-color-text-primary);
  }
  
  &:active {
    background-color: var(--crf-color-bg-quaternary);
  }
  
  /* 按钮变体 */
  &.crf-option-button--danger:hover {
    background-color: var(--crf-color-error-50);
    color: var(--crf-color-error);
  }
  
  &.crf-option-button--primary:hover {
    background-color: var(--crf-color-primary-50);
    color: var(--crf-color-primary);
  }
}

/* ===== 拖拽状态样式 ===== */
.crf-drag-ghost {
  opacity: 0.5;
  background-color: var(--crf-color-primary-50);
  border: 2px dashed var(--crf-color-primary);
  transform: rotate(2deg);
}

.crf-drag-chosen {
  background-color: var(--crf-color-primary-100);
  border: 1px solid var(--crf-color-primary);
  box-shadow: var(--crf-shadow-md);
}

.crf-drag-active {
  opacity: 0.8;
  transform: scale(1.02);
  z-index: var(--crf-z-index-dropdown);
}

/* ===== 按钮组件样式 ===== */
.crf-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--crf-spacing-2);
  padding: var(--crf-spacing-2) var(--crf-spacing-4);
  border: 1px solid var(--crf-color-border-primary);
  border-radius: var(--crf-radius-button);
  background-color: var(--crf-color-bg-primary);
  color: var(--crf-color-text-primary);
  font-size: var(--crf-font-size-sm);
  font-weight: var(--crf-font-weight-medium);
  line-height: var(--crf-line-height-tight);
  cursor: pointer;
  transition: var(--crf-transition-colors);
  text-decoration: none;
  user-select: none;
  
  &:hover {
    background-color: var(--crf-color-bg-secondary);
    border-color: var(--crf-color-border-secondary);
  }
  
  &:active {
    background-color: var(--crf-color-bg-tertiary);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--crf-color-primary-200);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  /* 按钮变体 */
  &.crf-button--primary {
    background-color: var(--crf-color-primary);
    border-color: var(--crf-color-primary);
    color: white;
    
    &:hover {
      background-color: var(--crf-color-primary-hover);
      border-color: var(--crf-color-primary-hover);
    }
    
    &:active {
      background-color: var(--crf-color-primary-pressed);
      border-color: var(--crf-color-primary-pressed);
    }
  }
  
  &.crf-button--success {
    background-color: var(--crf-color-success);
    border-color: var(--crf-color-success);
    color: white;
    
    &:hover {
      background-color: var(--crf-color-success-600);
      border-color: var(--crf-color-success-600);
    }
  }
  
  &.crf-button--warning {
    background-color: var(--crf-color-warning);
    border-color: var(--crf-color-warning);
    color: white;
    
    &:hover {
      background-color: var(--crf-color-warning-600);
      border-color: var(--crf-color-warning-600);
    }
  }
  
  &.crf-button--danger {
    background-color: var(--crf-color-error);
    border-color: var(--crf-color-error);
    color: white;
    
    &:hover {
      background-color: var(--crf-color-error-600);
      border-color: var(--crf-color-error-600);
    }
  }
  
  /* 按钮尺寸 */
  &.crf-button--small {
    padding: var(--crf-spacing-1) var(--crf-spacing-3);
    font-size: var(--crf-font-size-xs);
  }
  
  &.crf-button--large {
    padding: var(--crf-spacing-3) var(--crf-spacing-6);
    font-size: var(--crf-font-size-base);
  }
}

/* ===== 输入框组件样式 ===== */
.crf-input {
  width: 100%;
  padding: var(--crf-spacing-2) var(--crf-spacing-3);
  border: 1px solid var(--crf-color-border-primary);
  border-radius: var(--crf-radius-input);
  background-color: var(--crf-color-bg-primary);
  color: var(--crf-color-text-primary);
  font-size: var(--crf-font-size-sm);
  line-height: var(--crf-line-height-normal);
  transition: var(--crf-transition-colors);
  
  &::placeholder {
    color: var(--crf-color-text-placeholder);
  }
  
  &:hover {
    border-color: var(--crf-color-border-secondary);
  }
  
  &:focus {
    outline: none;
    border-color: var(--crf-color-primary);
    box-shadow: 0 0 0 2px var(--crf-color-primary-200);
  }
  
  &:disabled {
    background-color: var(--crf-color-bg-secondary);
    color: var(--crf-color-text-disabled);
    cursor: not-allowed;
  }
  
  &.crf-input--error {
    border-color: var(--crf-color-error);
    
    &:focus {
      box-shadow: 0 0 0 2px var(--crf-color-error-200);
    }
  }
}

/* ===== 卡片组件样式 ===== */
.crf-card {
  background-color: var(--crf-color-bg-primary);
  border: 1px solid var(--crf-color-border-primary);
  border-radius: var(--crf-radius-card);
  box-shadow: var(--crf-shadow-sm);
  overflow: hidden;
  transition: var(--crf-transition-colors);
  
  &:hover {
    box-shadow: var(--crf-shadow-md);
  }
  
  .crf-card-header {
    padding: var(--crf-spacing-4);
    border-bottom: 1px solid var(--crf-color-border-primary);
    background-color: var(--crf-color-bg-secondary);
  }
  
  .crf-card-body {
    padding: var(--crf-spacing-4);
  }
  
  .crf-card-footer {
    padding: var(--crf-spacing-4);
    border-top: 1px solid var(--crf-color-border-primary);
    background-color: var(--crf-color-bg-secondary);
  }
}
