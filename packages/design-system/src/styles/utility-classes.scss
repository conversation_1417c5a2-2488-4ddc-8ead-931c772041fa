/**
 * 工具类系统
 * 提供原子级的CSS工具类，减少重复样式定义
 * 基于统一的设计令牌系统
 */

/* ===== 颜色工具类 ===== */

/* 文本颜色 */
.crf-text-primary { color: var(--crf-color-text-primary) !important; }
.crf-text-secondary { color: var(--crf-color-text-secondary) !important; }
.crf-text-tertiary { color: var(--crf-color-text-tertiary) !important; }
.crf-text-disabled { color: var(--crf-color-text-disabled) !important; }
.crf-text-placeholder { color: var(--crf-color-text-placeholder) !important; }

.crf-text-primary-500 { color: var(--crf-color-primary) !important; }
.crf-text-success { color: var(--crf-color-success) !important; }
.crf-text-warning { color: var(--crf-color-warning) !important; }
.crf-text-error { color: var(--crf-color-error) !important; }

/* 背景颜色 */
.crf-bg-primary { background-color: var(--crf-color-bg-primary) !important; }
.crf-bg-secondary { background-color: var(--crf-color-bg-secondary) !important; }
.crf-bg-tertiary { background-color: var(--crf-color-bg-tertiary) !important; }

.crf-bg-primary-500 { background-color: var(--crf-color-primary) !important; }
.crf-bg-primary-50 { background-color: var(--crf-color-primary-50) !important; }
.crf-bg-success { background-color: var(--crf-color-success) !important; }
.crf-bg-warning { background-color: var(--crf-color-warning) !important; }
.crf-bg-error { background-color: var(--crf-color-error) !important; }

/* 边框颜色 */
.crf-border-primary { border-color: var(--crf-color-border-primary) !important; }
.crf-border-secondary { border-color: var(--crf-color-border-secondary) !important; }
.crf-border-focus { border-color: var(--crf-color-border-focus) !important; }

/* ===== 间距工具类 ===== */

/* Padding */
.crf-p-0 { padding: var(--crf-spacing-0) !important; }
.crf-p-1 { padding: var(--crf-spacing-1) !important; }
.crf-p-2 { padding: var(--crf-spacing-2) !important; }
.crf-p-3 { padding: var(--crf-spacing-3) !important; }
.crf-p-4 { padding: var(--crf-spacing-4) !important; }
.crf-p-5 { padding: var(--crf-spacing-5) !important; }
.crf-p-6 { padding: var(--crf-spacing-6) !important; }
.crf-p-8 { padding: var(--crf-spacing-8) !important; }

.crf-px-0 { padding-left: var(--crf-spacing-0) !important; padding-right: var(--crf-spacing-0) !important; }
.crf-px-1 { padding-left: var(--crf-spacing-1) !important; padding-right: var(--crf-spacing-1) !important; }
.crf-px-2 { padding-left: var(--crf-spacing-2) !important; padding-right: var(--crf-spacing-2) !important; }
.crf-px-3 { padding-left: var(--crf-spacing-3) !important; padding-right: var(--crf-spacing-3) !important; }
.crf-px-4 { padding-left: var(--crf-spacing-4) !important; padding-right: var(--crf-spacing-4) !important; }
.crf-px-6 { padding-left: var(--crf-spacing-6) !important; padding-right: var(--crf-spacing-6) !important; }

.crf-py-0 { padding-top: var(--crf-spacing-0) !important; padding-bottom: var(--crf-spacing-0) !important; }
.crf-py-1 { padding-top: var(--crf-spacing-1) !important; padding-bottom: var(--crf-spacing-1) !important; }
.crf-py-2 { padding-top: var(--crf-spacing-2) !important; padding-bottom: var(--crf-spacing-2) !important; }
.crf-py-3 { padding-top: var(--crf-spacing-3) !important; padding-bottom: var(--crf-spacing-3) !important; }
.crf-py-4 { padding-top: var(--crf-spacing-4) !important; padding-bottom: var(--crf-spacing-4) !important; }
.crf-py-6 { padding-top: var(--crf-spacing-6) !important; padding-bottom: var(--crf-spacing-6) !important; }

/* Margin */
.crf-m-0 { margin: var(--crf-spacing-0) !important; }
.crf-m-1 { margin: var(--crf-spacing-1) !important; }
.crf-m-2 { margin: var(--crf-spacing-2) !important; }
.crf-m-3 { margin: var(--crf-spacing-3) !important; }
.crf-m-4 { margin: var(--crf-spacing-4) !important; }
.crf-m-5 { margin: var(--crf-spacing-5) !important; }
.crf-m-6 { margin: var(--crf-spacing-6) !important; }
.crf-m-8 { margin: var(--crf-spacing-8) !important; }

.crf-mx-0 { margin-left: var(--crf-spacing-0) !important; margin-right: var(--crf-spacing-0) !important; }
.crf-mx-1 { margin-left: var(--crf-spacing-1) !important; margin-right: var(--crf-spacing-1) !important; }
.crf-mx-2 { margin-left: var(--crf-spacing-2) !important; margin-right: var(--crf-spacing-2) !important; }
.crf-mx-3 { margin-left: var(--crf-spacing-3) !important; margin-right: var(--crf-spacing-3) !important; }
.crf-mx-4 { margin-left: var(--crf-spacing-4) !important; margin-right: var(--crf-spacing-4) !important; }
.crf-mx-auto { margin-left: auto !important; margin-right: auto !important; }

.crf-my-0 { margin-top: var(--crf-spacing-0) !important; margin-bottom: var(--crf-spacing-0) !important; }
.crf-my-1 { margin-top: var(--crf-spacing-1) !important; margin-bottom: var(--crf-spacing-1) !important; }
.crf-my-2 { margin-top: var(--crf-spacing-2) !important; margin-bottom: var(--crf-spacing-2) !important; }
.crf-my-3 { margin-top: var(--crf-spacing-3) !important; margin-bottom: var(--crf-spacing-3) !important; }
.crf-my-4 { margin-top: var(--crf-spacing-4) !important; margin-bottom: var(--crf-spacing-4) !important; }
.crf-my-6 { margin-top: var(--crf-spacing-6) !important; margin-bottom: var(--crf-spacing-6) !important; }

/* Gap */
.crf-gap-0 { gap: var(--crf-spacing-0) !important; }
.crf-gap-1 { gap: var(--crf-spacing-1) !important; }
.crf-gap-2 { gap: var(--crf-spacing-2) !important; }
.crf-gap-3 { gap: var(--crf-spacing-3) !important; }
.crf-gap-4 { gap: var(--crf-spacing-4) !important; }
.crf-gap-6 { gap: var(--crf-spacing-6) !important; }

/* ===== 字体工具类 ===== */
.crf-text-xs { font-size: var(--crf-font-size-xs) !important; }
.crf-text-sm { font-size: var(--crf-font-size-sm) !important; }
.crf-text-base { font-size: var(--crf-font-size-base) !important; }
.crf-text-lg { font-size: var(--crf-font-size-lg) !important; }
.crf-text-xl { font-size: var(--crf-font-size-xl) !important; }
.crf-text-2xl { font-size: var(--crf-font-size-2xl) !important; }

.crf-font-normal { font-weight: var(--crf-font-weight-normal) !important; }
.crf-font-medium { font-weight: var(--crf-font-weight-medium) !important; }
.crf-font-semibold { font-weight: var(--crf-font-weight-semibold) !important; }
.crf-font-bold { font-weight: var(--crf-font-weight-bold) !important; }

.crf-leading-tight { line-height: var(--crf-line-height-tight) !important; }
.crf-leading-normal { line-height: var(--crf-line-height-normal) !important; }
.crf-leading-relaxed { line-height: var(--crf-line-height-relaxed) !important; }

/* ===== 布局工具类 ===== */
.crf-flex { display: flex !important; }
.crf-inline-flex { display: inline-flex !important; }
.crf-grid { display: grid !important; }
.crf-block { display: block !important; }
.crf-inline-block { display: inline-block !important; }
.crf-hidden { display: none !important; }

.crf-flex-row { flex-direction: row !important; }
.crf-flex-col { flex-direction: column !important; }
.crf-flex-wrap { flex-wrap: wrap !important; }
.crf-flex-nowrap { flex-wrap: nowrap !important; }

.crf-items-start { align-items: flex-start !important; }
.crf-items-center { align-items: center !important; }
.crf-items-end { align-items: flex-end !important; }
.crf-items-stretch { align-items: stretch !important; }

.crf-justify-start { justify-content: flex-start !important; }
.crf-justify-center { justify-content: center !important; }
.crf-justify-end { justify-content: flex-end !important; }
.crf-justify-between { justify-content: space-between !important; }
.crf-justify-around { justify-content: space-around !important; }

.crf-flex-1 { flex: 1 1 0% !important; }
.crf-flex-auto { flex: 1 1 auto !important; }
.crf-flex-none { flex: none !important; }

/* ===== 边框工具类 ===== */
.crf-border { border: 1px solid var(--crf-color-border-primary) !important; }
.crf-border-0 { border: 0 !important; }
.crf-border-t { border-top: 1px solid var(--crf-color-border-primary) !important; }
.crf-border-r { border-right: 1px solid var(--crf-color-border-primary) !important; }
.crf-border-b { border-bottom: 1px solid var(--crf-color-border-primary) !important; }
.crf-border-l { border-left: 1px solid var(--crf-color-border-primary) !important; }

/* 圆角 */
.crf-rounded-none { border-radius: var(--crf-border-radius-none) !important; }
.crf-rounded-sm { border-radius: var(--crf-border-radius-sm) !important; }
.crf-rounded { border-radius: var(--crf-border-radius-base) !important; }
.crf-rounded-md { border-radius: var(--crf-border-radius-md) !important; }
.crf-rounded-lg { border-radius: var(--crf-border-radius-lg) !important; }
.crf-rounded-xl { border-radius: var(--crf-border-radius-xl) !important; }
.crf-rounded-full { border-radius: var(--crf-border-radius-full) !important; }

/* ===== 阴影工具类 ===== */
.crf-shadow-none { box-shadow: var(--crf-shadow-none) !important; }
.crf-shadow-sm { box-shadow: var(--crf-shadow-sm) !important; }
.crf-shadow { box-shadow: var(--crf-shadow-base) !important; }
.crf-shadow-md { box-shadow: var(--crf-shadow-md) !important; }
.crf-shadow-lg { box-shadow: var(--crf-shadow-lg) !important; }
.crf-shadow-xl { box-shadow: var(--crf-shadow-xl) !important; }

/* ===== 过渡工具类 ===== */
.crf-transition-none { transition: none !important; }
.crf-transition-all { transition: var(--crf-transition-all) !important; }
.crf-transition-colors { transition: var(--crf-transition-colors) !important; }
.crf-transition-transform { transition: var(--crf-transition-transform) !important; }

/* ===== 定位工具类 ===== */
.crf-relative { position: relative !important; }
.crf-absolute { position: absolute !important; }
.crf-fixed { position: fixed !important; }
.crf-sticky { position: sticky !important; }

/* ===== 宽高工具类 ===== */
.crf-w-full { width: 100% !important; }
.crf-w-auto { width: auto !important; }
.crf-h-full { height: 100% !important; }
.crf-h-auto { height: auto !important; }
.crf-min-h-0 { min-height: 0 !important; }

/* ===== 溢出工具类 ===== */
.crf-overflow-hidden { overflow: hidden !important; }
.crf-overflow-auto { overflow: auto !important; }
.crf-overflow-scroll { overflow: scroll !important; }
.crf-overflow-x-hidden { overflow-x: hidden !important; }
.crf-overflow-y-hidden { overflow-y: hidden !important; }
.crf-overflow-x-auto { overflow-x: auto !important; }
.crf-overflow-y-auto { overflow-y: auto !important; }

/* ===== 光标工具类 ===== */
.crf-cursor-pointer { cursor: pointer !important; }
.crf-cursor-not-allowed { cursor: not-allowed !important; }
.crf-cursor-move { cursor: move !important; }
.crf-cursor-grab { cursor: grab !important; }
.crf-cursor-grabbing { cursor: grabbing !important; }

/* ===== 选择工具类 ===== */
.crf-select-none { user-select: none !important; }
.crf-select-text { user-select: text !important; }
.crf-select-all { user-select: all !important; }

/* ===== 透明度工具类 ===== */
.crf-opacity-0 { opacity: 0 !important; }
.crf-opacity-25 { opacity: 0.25 !important; }
.crf-opacity-50 { opacity: 0.5 !important; }
.crf-opacity-75 { opacity: 0.75 !important; }
.crf-opacity-100 { opacity: 1 !important; }

/* ===== 状态工具类 ===== */
.crf-disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.crf-loading {
  opacity: 0.7 !important;
  cursor: wait !important;
  pointer-events: none !important;
}

/* ===== 响应式工具类 ===== */
@media (max-width: 640px) {
  .crf-sm\:hidden { display: none !important; }
  .crf-sm\:block { display: block !important; }
  .crf-sm\:flex { display: flex !important; }
  .crf-sm\:flex-col { flex-direction: column !important; }
  .crf-sm\:text-sm { font-size: var(--crf-font-size-sm) !important; }
  .crf-sm\:p-2 { padding: var(--crf-spacing-2) !important; }
  .crf-sm\:px-2 { padding-left: var(--crf-spacing-2) !important; padding-right: var(--crf-spacing-2) !important; }
  .crf-sm\:py-2 { padding-top: var(--crf-spacing-2) !important; padding-bottom: var(--crf-spacing-2) !important; }
}

@media (min-width: 768px) {
  .crf-md\:block { display: block !important; }
  .crf-md\:flex { display: flex !important; }
  .crf-md\:flex-row { flex-direction: row !important; }
  .crf-md\:text-base { font-size: var(--crf-font-size-base) !important; }
  .crf-md\:p-4 { padding: var(--crf-spacing-4) !important; }
  .crf-md\:px-4 { padding-left: var(--crf-spacing-4) !important; padding-right: var(--crf-spacing-4) !important; }
  .crf-md\:py-4 { padding-top: var(--crf-spacing-4) !important; padding-bottom: var(--crf-spacing-4) !important; }
}

@media (min-width: 1024px) {
  .crf-lg\:block { display: block !important; }
  .crf-lg\:flex { display: flex !important; }
  .crf-lg\:text-lg { font-size: var(--crf-font-size-lg) !important; }
  .crf-lg\:p-6 { padding: var(--crf-spacing-6) !important; }
  .crf-lg\:px-6 { padding-left: var(--crf-spacing-6) !important; padding-right: var(--crf-spacing-6) !important; }
  .crf-lg\:py-6 { padding-top: var(--crf-spacing-6) !important; padding-bottom: var(--crf-spacing-6) !important; }
}
