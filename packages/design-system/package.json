{"name": "@crf/design-system", "version": "1.0.1", "private": true, "description": "Design system and theming for CRF applications", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}, "./styles": {"import": "./src/styles/index.scss"}, "./tokens": {"import": "./src/tokens/index.ts", "types": "./src/tokens/index.ts"}, "./config": {"import": "./src/config/index.ts", "types": "./src/config/index.ts"}, "./src/styles/index.scss": {"import": "./src/styles/index.scss"}}, "files": ["src", "dist"], "scripts": {"build": "unbuild", "stub": "unbuild --stub", "dev": "unbuild --stub --watch", "type-check": "vue-tsc --noEmit"}, "peerDependencies": {"vue": "^3.5.13"}, "dependencies": {"@crf/type-definitions": "workspace:*", "@crf/app-constants": "workspace:*"}, "devDependencies": {"@crf/build-config": "workspace:*", "@types/node": "^22.15.19", "@unocss/core": "^66.1.2", "sass": "^1.89.0", "typescript": "^5.8.3", "vite": "^6.2.4", "vue-tsc": "^2.2.8"}}