{"extends": "@crf/tsconfig/package-build.json", "compilerOptions": {"baseUrl": ".", "declaration": true, "declarationMap": true, "paths": {"@crf/type-definitions": ["../type-definitions/index.ts"], "@crf/type-definitions/*": ["../type-definitions/*"], "@crf/build-config": ["../build-config/index.ts"], "@crf/build-config/*": ["../build-config/*"]}}, "include": ["src/**/*.ts"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}