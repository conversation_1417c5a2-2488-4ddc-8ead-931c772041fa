/**
 * 验证类型定义 - 完全类型安全
 */

import type { FieldId, FormData, FormFieldValue } from '../core'

// 验证状态 - 完全类型安全
export interface ValidationState {
  readonly results: ReadonlyMap<FieldId, ValidationResult>
  readonly isFormValid: boolean
  readonly pendingValidations: readonly FieldId[]
  readonly validationQueue: readonly ValidationTask[]
}

// 验证结果 - 完全类型安全
export interface ValidationResult {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly status: ValidationStatus
  readonly lastValidated: number
  readonly fieldId: FieldId
}

// 验证状态枚举
export enum ValidationStatus {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  VALIDATING = 'validating',
  PENDING = 'pending',
}

// 验证任务 - 完全类型安全
export interface ValidationTask {
  readonly fieldId: FieldId
  readonly value: FormFieldValue
  readonly rules: readonly ValidationRule[]
  readonly priority: ValidationPriority
  readonly timestamp: number
}

// 验证优先级枚举
export enum ValidationPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4,
}

// 验证规则 - 完全类型安全
export interface ValidationRule {
  readonly type: ValidationRuleType
  readonly message: string
  readonly value?: FormFieldValue
  readonly trigger: ValidationTrigger
  readonly priority: ValidationPriority
  readonly customValidator?: CustomValidator
}

// 简化的验证规则接口 - 用于组件级别的验证
export interface SimpleValidationRule {
  /** 是否必填 */
  required?: boolean
  /** 最小长度 */
  minLength?: number
  /** 最大长度 */
  maxLength?: number
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 正则表达式模式 */
  pattern?: string | RegExp
  /** 自定义验证器 */
  validator?: (value: unknown) => string | null
  /** 错误消息 */
  message?: string
  /** 验证触发时机 */
  trigger?: 'blur' | 'change' | 'input' | 'submit'
}

// 简化的验证结果接口
export interface SimpleValidationResult {
  /** 是否验证通过 */
  isValid: boolean
  /** 验证状态 */
  status: 'success' | 'warning' | 'error'
  /** 错误消息列表 */
  errors: string[]
  /** 警告消息列表 */
  warnings?: string[]
  /** 单个错误消息（兼容旧接口） */
  message?: string
}

// 验证规则类型枚举
export enum ValidationRuleType {
  REQUIRED = 'required',
  PATTERN = 'pattern',
  MIN = 'min',
  MAX = 'max',
  MIN_LENGTH = 'minLength',
  MAX_LENGTH = 'maxLength',
  EMAIL = 'email',
  URL = 'url',
  NUMBER = 'number',
  INTEGER = 'integer',
  PHONE = 'phone',
  CUSTOM = 'custom',
  RANGE = 'range',
  ENUM = 'enum',
  FORMAT = 'format',
}

// 验证触发器枚举
export enum ValidationTrigger {
  BLUR = 'blur',
  CHANGE = 'change',
  INPUT = 'input',
  SUBMIT = 'submit',
  MOUNT = 'mount',
  FOCUS = 'focus',
}

// 自定义验证器 - 完全类型安全
export interface CustomValidator {
  readonly name: string
  readonly validate: (
    value: FormFieldValue,
    fieldId: FieldId,
    formData: FormData,
  ) => ValidationResult
  readonly message: string
  readonly async?: boolean
}

// 验证器配置 - 完全类型安全
export interface ValidatorConfig {
  readonly validateOnChange: boolean
  readonly validateOnBlur: boolean
  readonly validateOnSubmit: boolean
  readonly validateOnMount: boolean
  readonly showValidationMessages: boolean
  readonly showWarnings: boolean
  readonly debounceTime: number
  readonly maxConcurrentValidations: number
  readonly customValidators: readonly CustomValidator[]
}

// 验证器结果 - 完全类型安全
export interface ValidatorResult {
  readonly success: boolean
  readonly result?: ValidationResult
  readonly error?: string
  readonly warnings?: readonly string[]
}

// 批量验证结果 - 完全类型安全
export interface BatchValidationResult {
  readonly results: ReadonlyMap<FieldId, ValidationResult>
  readonly isFormValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly summary: ValidationSummary
}

// 验证摘要 - 完全类型安全
export interface ValidationSummary {
  readonly totalFields: number
  readonly validFields: number
  readonly invalidFields: number
  readonly warningFields: number
  readonly pendingFields: number
  readonly errorCount: number
  readonly warningCount: number
}

// 验证器工厂 - 完全类型安全
export interface ValidatorFactory {
  readonly createValidator: (rule: ValidationRule) => FieldValidator
  readonly createCustomValidator: (validator: CustomValidator) => FieldValidator
  readonly createBatchValidator: (
    rules: readonly ValidationRule[],
  ) => BatchValidator
}

// 字段验证器 - 完全类型安全
export interface FieldValidator {
  readonly validate: (
    value: FormFieldValue,
    fieldId: FieldId,
    formData: FormData,
  ) => ValidationResult
  readonly validateAsync?: (
    value: FormFieldValue,
    fieldId: FieldId,
    formData: FormData,
  ) => Promise<ValidationResult>
}

// 批量验证器 - 完全类型安全
export interface BatchValidator {
  readonly validate: (data: FormData) => BatchValidationResult
  readonly validateAsync?: (data: FormData) => Promise<BatchValidationResult>
}

// 常用验证器工厂
export const CommonValidators = {
  /** 邮箱验证器 */
  email: (message = '请输入有效的邮箱地址'): SimpleValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message,
    trigger: 'blur',
  }),

  /** 手机号验证器 */
  phone: (message = '请输入有效的手机号码'): SimpleValidationRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message,
    trigger: 'blur',
  }),

  /** URL验证器 */
  url: (message = '请输入有效的网址'): SimpleValidationRule => ({
    validator: (value: unknown) => {
      if (!value) return null
      try {
        new URL(String(value))
        return null
      } catch {
        return message
      }
    },
    trigger: 'blur',
  }),

  /** 身份证验证器 */
  idCard: (message = '请输入有效的身份证号码'): SimpleValidationRule => ({
    pattern:
      /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/,
    message,
    trigger: 'blur',
  }),

  /** 数字验证器 */
  number: (message = '请输入有效的数字'): SimpleValidationRule => ({
    pattern: /^-?\d+(\.\d+)?$/,
    message,
    trigger: 'change',
  }),

  /** 整数验证器 */
  integer: (message = '请输入有效的整数'): SimpleValidationRule => ({
    pattern: /^-?\d+$/,
    message,
    trigger: 'change',
  }),

  /** 正数验证器 */
  positiveNumber: (message = '请输入大于0的数字'): SimpleValidationRule => ({
    validator: (value: unknown) => {
      const num = Number(value)
      return !isNaN(num) && num > 0 ? null : message
    },
    trigger: 'change',
  }),

  /** 必填验证器 */
  required: (message = '此字段为必填项'): SimpleValidationRule => ({
    required: true,
    message,
    trigger: 'blur',
  }),

  /** 长度范围验证器 */
  length: (
    min?: number,
    max?: number,
    message?: string,
  ): SimpleValidationRule => {
    const defaultMessage =
      min && max
        ? `长度应在${min}-${max}个字符之间`
        : min
          ? `长度不能少于${min}个字符`
          : max
            ? `长度不能超过${max}个字符`
            : '长度不符合要求'

    return {
      minLength: min,
      maxLength: max,
      message: message || defaultMessage,
      trigger: 'blur',
    }
  },

  /** 数值范围验证器 */
  range: (
    min?: number,
    max?: number,
    message?: string,
  ): SimpleValidationRule => {
    const defaultMessage =
      min !== undefined && max !== undefined
        ? `值应在${min}-${max}之间`
        : min !== undefined
          ? `值不能小于${min}`
          : max !== undefined
            ? `值不能大于${max}`
            : '值不在有效范围内'

    return {
      min,
      max,
      message: message || defaultMessage,
      trigger: 'change',
    }
  },
} as const
