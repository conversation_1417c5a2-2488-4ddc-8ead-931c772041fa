/**
 * 增强的类型定义
 * 提供更严格的类型检查和更好的开发体验
 */

// 基础类型增强
export type Prettify<T> = {
  [K in keyof T]: T[K]
} & {}

export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K
}[keyof T]

export type OptionalKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never
}[keyof T]

// 组件属性类型增强
export interface BaseComponentProps {
  /** 组件唯一标识 */
  id?: string
  /** 组件类名 */
  class?: string | string[] | Record<string, boolean>
  /** 组件样式 */
  style?: string | Record<string, string | number>
  /** 是否可见 */
  visible?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 测试标识 */
  'data-testid'?: string
}

// 表单组件属性增强
export interface FormComponentProps<T = any> extends BaseComponentProps {
  /** 组件值 */
  modelValue?: T
  /** 组件名称 */
  name?: string
  /** 是否必填 */
  required?: boolean
  /** 验证规则 */
  rules?: ValidationRule[]
  /** 错误信息 */
  error?: string
  /** 帮助文本 */
  help?: string
  /** 占位符 */
  placeholder?: string
}

// 验证规则类型
export interface ValidationRule {
  /** 规则类型 */
  type?:
    | 'required'
    | 'email'
    | 'url'
    | 'number'
    | 'integer'
    | 'float'
    | 'pattern'
    | 'custom'
  /** 是否必填 */
  required?: boolean
  /** 最小值/长度 */
  min?: number
  /** 最大值/长度 */
  max?: number
  /** 正则表达式 */
  pattern?: RegExp | string
  /** 自定义验证函数 */
  validator?: (value: any) => boolean | string | Promise<boolean | string>
  /** 错误信息 */
  message?: string
  /** 触发时机 */
  trigger?: 'change' | 'blur' | 'input'
}

// 医疗数据类型
export type MedicalDataType =
  | 'vital-signs' // 生命体征
  | 'medication' // 药物信息
  | 'diagnosis' // 诊断信息
  | 'procedure' // 医疗程序
  | 'laboratory' // 实验室检查
  | 'imaging' // 影像检查
  | 'pathology' // 病理检查
  | 'allergy' // 过敏信息
  | 'family-history' // 家族史
  | 'social-history' // 社会史
  | 'general' // 一般信息

// 组件尺寸类型
export type ComponentSize = 'small' | 'medium' | 'large'

// 组件状态类型
export type ComponentStatus =
  | 'default'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'

// 布局方向类型
export type LayoutDirection = 'horizontal' | 'vertical'

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto'

// 语言类型
export type Locale = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR'

// 事件处理器类型增强
export type EventHandler<T = Event> = (event: T) => void | Promise<void>

export type ChangeHandler<T = any> = (
  value: T,
  oldValue?: T,
) => void | Promise<void>

export type ValidateHandler = (result: ValidationResult) => void | Promise<void>

// 验证结果类型
export interface ValidationResult {
  /** 验证状态 */
  status: 'success' | 'warning' | 'error'
  /** 验证消息 */
  message: string
  /** 详细错误信息 */
  errors?: string[]
  /** 验证字段 */
  field?: string
}

// 组件配置类型
export interface ComponentConfig {
  /** 组件名称 */
  name: string
  /** 组件标签 */
  label: string
  /** 组件图标 */
  icon?: string
  /** 组件分类 */
  category: string
  /** 组件描述 */
  description?: string
  /** 是否可见 */
  visible?: boolean
  /** 是否可用 */
  enabled?: boolean
  /** 默认属性 */
  defaultProps?: Record<string, any>
  /** 属性配置 */
  propConfig?: Record<string, PropConfig>
}

// 属性配置类型
export interface PropConfig {
  /** 属性类型 */
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'function'
  /** 默认值 */
  default?: any
  /** 是否必填 */
  required?: boolean
  /** 属性描述 */
  description?: string
  /** 可选值 */
  options?: Array<{ label: string; value: any }>
  /** 验证规则 */
  validator?: (value: any) => boolean | string
}

// API 响应类型增强
export interface ApiResponse<T = any> {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 响应时间戳 */
  timestamp: number
  /** 请求ID */
  requestId?: string
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  /** 分页信息 */
  pagination: {
    /** 当前页码 */
    current: number
    /** 每页大小 */
    pageSize: number
    /** 总记录数 */
    total: number
    /** 总页数 */
    totalPages: number
  }
}

// 错误类型增强
export interface AppError extends Error {
  /** 错误代码 */
  code?: string | number
  /** 错误详情 */
  details?: any
  /** 错误堆栈 */
  stack?: string
  /** 错误时间 */
  timestamp?: number
}

// 性能监控类型
export interface PerformanceMetrics {
  /** 帧率 */
  fps: number
  /** 内存使用量 */
  memory: number
  /** 渲染时间 */
  renderTime: number
  /** 更新时间 */
  updateTime: number
  /** 网络延迟 */
  networkLatency?: number
  /** 资源加载时间 */
  resourceLoadTime?: number
}

// 开发工具类型已移除

// 工具函数类型
export type DeepMerge<T, U> = {
  [K in keyof T | keyof U]: K extends keyof U
    ? U[K]
    : K extends keyof T
      ? T[K]
      : never
}

export type Awaited<T> = T extends PromiseLike<infer U> ? U : T

export type NonNullable<T> = T extends null | undefined ? never : T

// 条件类型工具
export type If<C extends boolean, T, F> = C extends true ? T : F

export type IsEqual<T, U> = T extends U ? (U extends T ? true : false) : false

export type IsArray<T> = T extends readonly any[] ? true : false

export type IsFunction<T> = T extends (...args: any[]) => any ? true : false

// 字符串类型工具
export type Capitalize<S extends string> = S extends `${infer F}${infer R}`
  ? `${Uppercase<F>}${R}`
  : S

export type Uncapitalize<S extends string> = S extends `${infer F}${infer R}`
  ? `${Lowercase<F>}${R}`
  : S

// 对象类型工具
export type PickByType<T, U> = {
  [K in keyof T as T[K] extends U ? K : never]: T[K]
}

export type OmitByType<T, U> = {
  [K in keyof T as T[K] extends U ? never : K]: T[K]
}

// 联合类型工具
export type UnionToIntersection<U> = (
  U extends any ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never

export type LastOf<T> =
  UnionToIntersection<T extends any ? () => T : never> extends () => infer R
    ? R
    : never

// 导出所有类型
// export type * from './validation'
// export type * from './component'
// export type * from './api'
// export type * from './theme'
