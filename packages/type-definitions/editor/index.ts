/**
 * 编辑器类型定义 - 完全类型安全
 */

import type {
  ComponentId,
  SectionId,
  EditorMode,
  FormData,
  FormFieldValue,
  WithId,
  WithTimestamp,
} from '../core'
import type { ValidationResult } from '../validation'
import type { FormSection } from '../form'

// 编辑器状态 - 完全类型安全
export interface EditorState {
  readonly mode: EditorMode
  readonly isReadOnly: boolean
  readonly showConfigPanel: boolean
  readonly showBlockPanel: boolean
  readonly selectedComponentId: ComponentId | null
  readonly selectedSectionId: SectionId | null
  readonly isDirty: boolean
  readonly overlayMessage: string
  readonly showOverlay: boolean
}

// 组件配置 - 完全类型安全
export interface ComponentConfig extends WithId, WithTimestamp {
  readonly type: string
  readonly code: string
  readonly name: string
  readonly title: string
  readonly description?: string
  readonly props: ComponentProps
  readonly validation: ComponentValidationConfig
  readonly layout: ComponentLayoutConfig
  readonly metadata: ComponentMetadata
  readonly sectionId: SectionId
  readonly order: number
}

// 组件属性 - 完全类型安全
export interface ComponentProps {
  readonly [key: string]: FormFieldValue
}

// 组件验证配置 - 完全类型安全
export interface ComponentValidationConfig {
  readonly rules: readonly ValidationRule[]
  readonly trigger: 'blur' | 'change' | 'input' | 'submit'
  readonly autoValidate: boolean
  readonly customErrorMessage?: string
  readonly enableCustomError: boolean
}

// 组件布局配置 - 完全类型安全
export interface ComponentLayoutConfig {
  readonly width?: string
  readonly height?: string
  readonly margin?: string
  readonly padding?: string
  readonly display?: 'block' | 'inline' | 'inline-block' | 'flex' | 'grid'
  readonly flexDirection?: 'row' | 'column'
  readonly justifyContent?:
    | 'start'
    | 'center'
    | 'end'
    | 'space-between'
    | 'space-around'
  readonly alignItems?: 'start' | 'center' | 'end' | 'stretch'
  readonly gridTemplateColumns?: string
  readonly gridTemplateRows?: string
  readonly gridGap?: string
}

// 组件元数据 - 完全类型安全
export interface ComponentMetadata {
  readonly category: string
  readonly tags: readonly string[]
  readonly icon?: string
  readonly iconColor?: string
  readonly helpText?: string
  readonly fieldCode?: string
  readonly dataCollection?: string
  readonly medicalType?: string
  readonly codingSystem?: string
  readonly unit?: string
}

// 验证规则 - 完全类型安全
export interface ValidationRule {
  readonly type:
    | 'required'
    | 'pattern'
    | 'min'
    | 'max'
    | 'minLength'
    | 'maxLength'
    | 'email'
    | 'url'
    | 'number'
    | 'integer'
    | 'phone'
    | 'custom'
  readonly message: string
  readonly value?: FormFieldValue
  readonly trigger?: 'blur' | 'change' | 'input' | 'submit'
}

// 组件实例 - 完全类型安全
export interface ComponentInstance extends ComponentConfig {
  readonly validate: () => ValidationResult
  readonly clearValidation: () => void
  readonly reset: () => void
  readonly focus: () => void
  readonly blur: () => void
}

// 历史状态 - 完全类型安全
export interface HistoryState {
  readonly past: readonly HistoryEntry[]
  readonly future: readonly HistoryEntry[]
  readonly canUndo: boolean
  readonly canRedo: boolean
}

// 历史条目 - 完全类型安全
export interface HistoryEntry {
  readonly type:
    | 'component_add'
    | 'component_remove'
    | 'component_update'
    | 'section_add'
    | 'section_remove'
    | 'section_update'
    | 'form_data_change'
  readonly data: FormData | ComponentConfig | FormSection
  readonly timestamp: number
  readonly description: string
}

// 编辑器操作结果 - 完全类型安全
export interface EditorActionResult {
  readonly success: boolean
  readonly data?: FormData | ComponentConfig | FormSection
  readonly error?: string
  readonly warnings?: readonly string[]
}
