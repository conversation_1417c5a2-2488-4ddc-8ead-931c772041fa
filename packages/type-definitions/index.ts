/**
 * CRF Frontend 类型系统统一导出
 *
 * 分层架构：
 * - base: 基础类型定义
 * - shared: 共享类型定义
 * - core: 核心业务类型
 * - components: 组件相关类型
 * - editor: 编辑器相关类型
 * - form: 表单相关类型
 * - validation: 验证相关类型
 * - application: 应用层类型
 */

// =============================================================================
// 基础类型层 - 最底层的类型定义
// =============================================================================
export * from './base'
export * from './shared'

// =============================================================================
// 核心业务类型层 - 核心业务逻辑类型
// =============================================================================
export type {
  // 基础类型
  ComponentId,
  FieldId,
  SectionId,
  FormFieldValue,
  // FormArrayValue, // 类型不存在，暂时移除
  FormObjectValue,

  // 核心接口（使用别名避免冲突）
  ValidationResult as CoreValidationResult,
  ComponentType as CoreComponentType,
  FormData as CoreFormData,
  ValidationState as CoreValidationState,

  // 其他核心类型
  ValidationStatus,
  WithTimestamp,
  // WithMetadata,
  // WithValidation
} from './core'

// =============================================================================
// 组件类型层 - 所有组件相关类型
// =============================================================================
// 基础组件类型
export type {
  BaseComponentProps,
  BaseComponentSchema,
  ComponentConfig,
  ComponentSize,
  ComponentVariant,
  ComponentInstance,
  MedicalValidationStatus,
} from './components/base'

// 具体组件类型
export * from './components'

// =============================================================================
// 编辑器类型层 - 编辑器功能相关类型
// =============================================================================
export type {
  EditorState,
  ComponentConfig as EditorComponentConfig,
  ComponentProps as EditorComponentProps,
  ComponentValidationConfig,
  ComponentLayoutConfig,
  ComponentMetadata,
  HistoryState,
  HistoryEntry,
  EditorActionResult,
} from './editor'

// =============================================================================
// 表单类型层 - 表单系统相关类型
// =============================================================================
export type {
  FormSchema,
  FormProperty,
  FormConfig,
  FormValidationConfig,
  FormLayoutConfig,
  FormMetadata,
  FormActionResult,
  FormData,
} from './form'

// =============================================================================
// 验证类型层 - 验证系统相关类型（使用命名空间避免冲突）
// =============================================================================
export type {
  // 验证规则和配置
  ValidationRule,
  // SimpleValidationRule, // 暂时注释，构建有问题
  ValidationTrigger,

  // 验证结果（使用别名区分）
  ValidationResult as ValidationValidationResult,
  // SimpleValidationResult, // 暂时注释，构建有问题
  BatchValidationResult,
  ValidatorResult,

  // 验证状态
  ValidationState as ValidationValidationState,
  ValidationSummary,

  // 验证器接口
  CustomValidator,
  FieldValidator,
  BatchValidator,
  ValidatorFactory,
} from './validation'

// 导出常用验证器 - 暂时注释，构建有问题
// export { CommonValidators } from './validation'

// =============================================================================
// 应用类型层 - 最上层的应用类型
// =============================================================================
export type {
  ValidationResult as ApplicationValidationResult,
  EditorState as ApplicationEditorState,
  BaseBlock,
  PageSchemaFormData,
  BlockSchemaFormData,
  FormSection,
  GlobalConfig,
  FormSchema as ApplicationFormSchema,
  ExportConfig,
  ImportConfig,
  EditorEvent,
  ConfigPanelData,
} from './application'

// =============================================================================
// 统一类型导出 - 便于外部使用的统一接口
// =============================================================================
export * from './unified'

// =============================================================================
// 命名空间导出 - 解决命名冲突的类型组织方式
// =============================================================================
// export * from './namespaces' // 暂时禁用，存在语法错误

// =============================================================================
// 向后兼容的类型别名
// =============================================================================
/**
 * @deprecated 使用 Core.ValidationResult 替代
 */
// export type { ValidationResult as CoreValidationResult } from './core' // 已在上面导出

/**
 * @deprecated 使用 Validation.ValidationResult 替代
 */
// export type { ValidationResult as ValidationValidationResult } from './validation' // 已在上面导出

/**
 * @deprecated 使用 Core.ComponentType 替代
 */
// export type { ComponentType as CoreComponentType } from './core' // 已在上面导出

/**
 * @deprecated 使用 Core.FormData 替代
 */
// export type { FormData as CoreFormData } from './core' // 已在上面导出
