# 类型导出策略

本文档描述了CRF项目的类型定义导出策略，旨在解决命名冲突并提供清晰的类型组织结构。

## 问题背景

在大型TypeScript项目中，类型定义的命名冲突是一个常见问题：

1. **命名冲突**: 不同模块中存在同名类型（如`ValidationResult`）
2. **类型混淆**: 相似功能的类型难以区分
3. **导入复杂**: 需要使用别名来避免冲突
4. **维护困难**: 类型关系不清晰

## 解决方案

### 1. 命名空间组织

使用TypeScript命名空间将相关类型分组：

```typescript
// 使用命名空间避免冲突
import { Core, Validation, Components } from '@crf/types/namespaces'

// 清晰的类型引用
type CoreValidation = Core.ValidationResult
type ValidationValidation = Validation.ValidationResult
type ComponentProps = Components.CrfTextProps
```

### 2. 分层导出策略

#### 主导出文件 (`index.ts`)

- 导出所有类型，使用别名避免冲突
- 提供向后兼容的类型别名
- 标记废弃的导出方式

#### 命名空间导出 (`namespaces.ts`)

- 按功能模块组织类型
- 提供类型工具函数
- 清晰的类型关系

#### 子模块导出

- 每个子模块独立导出
- 支持按需导入
- 保持模块边界清晰

### 3. 导出层次结构

```
@crf/types
├── index.ts           # 主导出（兼容性）
├── namespaces.ts      # 命名空间导出（推荐）
├── core/              # 核心类型
├── validation/        # 验证类型
├── components/        # 组件类型
├── editor/           # 编辑器类型
├── form/             # 表单类型
├── application/      # 应用类型
└── unified/          # 统一类型映射
```

## 使用指南

### 推荐用法（命名空间）

```typescript
// 导入命名空间
import { Core, Validation, Components } from '@crf/types/namespaces'

// 使用类型
function validateField(
  value: Core.FormFieldValue,
  rule: Validation.ValidationRule,
): Validation.ValidationResult {
  // 实现
}

// 组件类型
const textProps: Components.CrfTextProps = {
  // 属性
}
```

### 兼容用法（别名导出）

```typescript
// 导入别名类型
import type {
  CoreValidationResult,
  ValidationValidationResult,
  CrfTextProps,
} from '@crf/types'

// 使用类型
function validateField(
  value: string,
  rule: ValidationRule,
): CoreValidationResult {
  // 实现
}
```

### 按需导入

```typescript
// 只导入需要的类型
import type { ValidationRule } from '@crf/types/validation'
import type { CrfTextProps } from '@crf/types/components'
```

## 命名空间详解

### Core 命名空间

包含核心业务类型：

- `ComponentId`, `FieldId`, `SectionId`
- `FormData`, `FormFieldValue`
- `ComponentType`, `ValidationResult`

### Validation 命名空间

包含验证系统类型：

- `ValidationRule`, `ValidationResult`
- `CustomValidator`, `FieldValidator`
- `CommonValidators` 工厂函数

### Components 命名空间

包含组件相关类型：

- 基础组件类型
- 具体组件Props和Schema
- 组件配置类型

### Editor 命名空间

包含编辑器功能类型：

- `EditorState`, `ComponentConfig`
- `HistoryState`, `EditorActionResult`

### Form 命名空间

包含表单系统类型：

- `FormSchema`, `FormConfig`
- `FormValidationConfig`, `FormLayoutConfig`

### Application 命名空间

包含应用层类型：

- 应用级别的类型定义
- 全局配置类型

## 类型工具

### TypeUtils 命名空间

提供常用的类型操作工具：

```typescript
import { TypeUtils } from '@crf/types/namespaces'

// 提取组件Props类型
type TextProps = TypeUtils.ExtractProps<'crf-text'>

// 创建可选属性
type OptionalProps = TypeUtils.Optional<TextProps, 'placeholder'>

// 创建只读类型
type ReadonlyProps = TypeUtils.DeepReadonly<TextProps>
```

## 迁移指南

### 从旧版本迁移

1. **更新导入语句**:

   ```typescript
   // 旧版本
   import { ValidationResult } from '@crf/types'

   // 新版本（推荐）
   import { Validation } from '@crf/types/namespaces'
   type ValidationResult = Validation.ValidationResult

   // 新版本（兼容）
   import { ValidationValidationResult } from '@crf/types'
   ```

2. **处理命名冲突**:

   ```typescript
   // 明确指定类型来源
   import { Core, Validation } from '@crf/types/namespaces'

   const coreResult: Core.ValidationResult = {
     /* ... */
   }
   const validationResult: Validation.ValidationResult = {
     /* ... */
   }
   ```

3. **更新类型注解**:
   ```typescript
   // 使用命名空间限定类型
   function processForm(data: Core.FormData): Validation.ValidationResult {
     // 实现
   }
   ```

## 最佳实践

1. **优先使用命名空间**: 新代码应使用命名空间导出
2. **明确类型来源**: 使用命名空间明确类型的来源模块
3. **避免全局导入**: 避免使用 `import * from '@crf/types'`
4. **按需导入**: 只导入实际使用的类型
5. **文档化类型**: 为复杂类型添加注释说明

## 向后兼容性

- 保留所有现有的类型导出
- 使用 `@deprecated` 标记废弃的导出方式
- 提供迁移指南和工具
- 渐进式迁移，不强制立即更新

## 未来规划

1. **自动化工具**: 开发类型导入的自动化重构工具
2. **类型检查**: 添加自定义ESLint规则检查类型导入
3. **文档生成**: 自动生成类型文档
4. **性能优化**: 优化类型编译性能
