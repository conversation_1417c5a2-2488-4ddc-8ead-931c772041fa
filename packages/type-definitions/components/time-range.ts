/**
 * CrfTimeRange 组件类型定义
 */

import type { ComponentInstance } from 'vue'
import type {
  BaseComponentProps,
  BaseComponentEmits,
  BaseComponentConfig,
  ValidationRule,
} from '@crf/type-definitions/base'

export type TimeRangeValue = [string, string] | null

export interface CrfTimeRangeProps extends BaseComponentProps<TimeRangeValue> {
  placeholder?: string | [string, string]
  startPlaceholder?: string
  endPlaceholder?: string
  format?: string
  size?: 'small' | 'medium' | 'large'
  editable?: boolean
  clearable?: boolean
  separator?: string
  medicalType?:
    | 'general'
    | 'treatment'
    | 'medication'
    | 'procedure'
    | 'monitoring'
  fieldCode?: string
}

export interface CrfTimeRangeEmits extends BaseComponentEmits<TimeRangeValue> {
  'visible-change': [visible: boolean]
}

export interface CrfTimeRangeConfig
  extends BaseComponentConfig<TimeRangeValue> {
  type: 'time-range'
  props: CrfTimeRangeProps
}

export interface CrfTimeRangeSchema {
  type: 'array'
  title?: string
  description?: string
  items?: {
    type: 'string'
    format: 'time'
  }
}

export interface CrfTimeRangeEvents {
  'update:modelValue': (value: TimeRangeValue) => void
  change: (value: TimeRangeValue) => void
  'visible-change': (visible: boolean) => void
  focus: (event: FocusEvent) => void
  blur: (event: FocusEvent) => void
}

export interface CrfTimeRangeSlots {}

export type CrfTimeRangeDefaultConfig = Required<CrfTimeRangeConfig>

export type CrfTimeRangeInstance = ComponentInstance<{
  props: CrfTimeRangeProps
  slots: CrfTimeRangeSlots
  events: CrfTimeRangeEvents
}>

export interface CrfTimeRangeValidationRule
  extends ValidationRule<TimeRangeValue> {
  type: 'required' | 'timeRange' | 'custom'
}

export interface CrfTimeRangeValidationResult {
  valid: boolean
  errors: string[]
  warnings?: string[]
}

export type CrfTimeRangeSchemaType = CrfTimeRangeConfig
