{"name": "@crf/i18n-medical", "version": "1.0.0", "description": "医疗CRF国际化系统 - 专业医疗术语翻译和多语言支持", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./locales/*": {"import": "./dist/locales/*.mjs", "require": "./dist/locales/*.cjs", "types": "./dist/locales/*.d.ts"}, "./medical-terms/*": {"import": "./dist/medical-terms/*.mjs", "require": "./dist/medical-terms/*.cjs", "types": "./dist/medical-terms/*.d.ts"}}, "files": ["dist", "README.md", "CHANGELOG.md"], "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint src --ext .ts,.vue --config ../../eslint.config.js", "lint:fix": "eslint src --ext .ts,.vue --fix --config ../../eslint.config.js", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "keywords": ["i18n", "internationalization", "medical", "healthcare", "crf", "clinical", "terminology", "translation", "vue3", "typescript"], "author": "CRF Team", "license": "MIT", "dependencies": {"@crf/shared-utils": "workspace:*", "@crf/type-definitions": "workspace:*", "vue": "^3.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "unbuild": "^2.0.0", "vue-tsc": "^1.8.0"}, "peerDependencies": {"vue": "^3.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/crf-team/crf-frontend.git", "directory": "packages/i18n-medical"}, "bugs": {"url": "https://github.com/crf-team/crf-frontend/issues"}, "homepage": "https://github.com/crf-team/crf-frontend/tree/main/packages/i18n-medical#readme"}