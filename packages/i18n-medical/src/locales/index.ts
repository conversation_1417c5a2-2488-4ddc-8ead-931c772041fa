/**
 * 语言包导出文件
 */

// 动态导入语言包的工具函数
export async function loadMedicalLocale(locale: string) {
  try {
    const medicalModule = await import(`./${locale}/medical.json`)
    return {
      medical: medicalModule.default || medicalModule,
    }
  } catch (error) {
    console.warn(`Failed to load medical locale: ${locale}`, error)
    return { medical: {} }
  }
}

// 支持的语言列表
export const AVAILABLE_LOCALES = [
  'zh-CN',
  'zh-TW',
  'en-US',
  'ja-JP',
  'ko-KR',
  'de-DE',
  'fr-FR',
  'es-ES',
] as const
