/**
 * @crf/i18n-medical - 医疗CRF国际化系统
 *
 * 专业的医疗术语翻译和多语言支持包
 * 提供完整的医疗国际化解决方案
 */

// =============================================================================
// 类型定义导出
// =============================================================================
export type {
  MedicalLocale,
  MedicalTermCategory,
  MedicalTerm,
  MedicalTermTranslation,
  ICD10Code,
  SNOMEDTerm,
  LOINCTerm,
  MedicalFieldType,
  MedicalValidationRule,
  MedicalUnit,
  MedicalNumberFormatOptions,
  MedicalDateFormatOptions,
  MedicalI18nConfig,
  MedicalTranslationContext,
  ValidationRule,
} from './types/medical.types'

// =============================================================================
// Composables 导出
// =============================================================================
export { useMedicalI18n } from './composables/useMedicalI18n'
export {
  useMedicalTerms,
  createMedicalTranslator,
} from './composables/useMedicalTerms'
export { useMedicalFormat } from './composables/useMedicalFormat'
export { useMedicalValidation } from './composables/useMedicalValidation'

// =============================================================================
// 医疗术语数据导出
// =============================================================================
export {
  commonMedicalTerms,
  medicalTermsByCategory,
  medicalTermsIndex,
  vitalSignsTerms,
  symptomTerms,
  anatomyTerms,
} from './medical-terms/common-terms'

// =============================================================================
// 便捷创建函数
// =============================================================================
import { useMedicalI18n } from './composables/useMedicalI18n'
import type { MedicalI18nConfig, MedicalLocale } from './types/medical.types'

/**
 * 创建医疗国际化实例
 */
export function createMedicalI18n(config: Partial<MedicalI18nConfig> = {}) {
  const i18n = useMedicalI18n(config)

  // 全局医疗翻译函数
  const $mt = i18n.mt

  return {
    ...i18n,
    $mt,

    // Vue 插件安装方法
    install(app: any) {
      // 提供全局属性
      app.config.globalProperties.$mt = $mt
      app.config.globalProperties.$medicalI18n = i18n

      // 提供依赖注入
      app.provide('medicalI18n', i18n)
      app.provide('$mt', $mt)

      // 初始化
      i18n.init()
    },
  }
}

/**
 * 创建简单的医疗翻译器
 */
export function createSimpleMedicalTranslator(locale: MedicalLocale = 'zh-CN') {
  const i18n = useMedicalI18n({
    defaultLocale: locale,
    lazy: false,
    enableCache: true,
  })

  // 初始化
  i18n.init()

  return {
    // 翻译函数
    mt: i18n.mt,

    // 术语翻译
    getTermTranslation: i18n.getTermTranslation,
    searchTerms: i18n.searchTerms,

    // 格式化
    formatWeight: i18n.formatWeight,
    formatHeight: i18n.formatHeight,
    formatTemperature: i18n.formatTemperature,
    formatBloodPressure: i18n.formatBloodPressure,
    formatHeartRate: i18n.formatHeartRate,
    formatMedicalDate: i18n.formatMedicalDate,

    // 验证
    validateField: i18n.validateField,
    validateForm: i18n.validateForm,

    // 语言管理
    setLocale: i18n.setLocale,
    currentLocale: i18n.currentLocale,
  }
}

// =============================================================================
// 默认实例
// =============================================================================
export const medicalI18n = createMedicalI18n()

// =============================================================================
// 常用常量导出
// =============================================================================

/** 支持的医疗语言列表 */
export const SUPPORTED_MEDICAL_LOCALES: MedicalLocale[] = [
  'zh-CN',
  'zh-TW',
  'en-US',
  'en-GB',
  'ja-JP',
  'ko-KR',
  'de-DE',
  'fr-FR',
  'es-ES',
  'pt-BR',
  'ru-RU',
  'ar-SA',
]

/** 医疗术语分类列表 */
export const MEDICAL_TERM_CATEGORIES = [
  'anatomy',
  'disease',
  'symptom',
  'medication',
  'procedure',
  'laboratory',
  'imaging',
  'vital-signs',
  'diagnosis',
  'treatment',
  'specialty',
  'equipment',
] as const

/** 医疗字段类型列表 */
export const MEDICAL_FIELD_TYPES = [
  'patient-info',
  'vital-signs',
  'medical-history',
  'physical-exam',
  'laboratory',
  'imaging',
  'diagnosis',
  'treatment',
  'medication',
  'adverse-event',
  'follow-up',
] as const

// =============================================================================
// 工具函数导出
// =============================================================================

/**
 * 检查是否为支持的医疗语言
 */
export function isSupportedMedicalLocale(
  locale: string,
): locale is MedicalLocale {
  return SUPPORTED_MEDICAL_LOCALES.includes(locale as MedicalLocale)
}

/**
 * 获取语言的显示名称
 */
export function getMedicalLocaleDisplayName(
  locale: MedicalLocale,
  displayLocale: MedicalLocale = 'zh-CN',
): string {
  const names: Record<MedicalLocale, Record<MedicalLocale, string>> = {
    'zh-CN': {
      'zh-CN': '简体中文',
      'en-US': 'Simplified Chinese',
    },
    'zh-TW': {
      'zh-CN': '繁体中文',
      'en-US': 'Traditional Chinese',
    },
    'en-US': {
      'zh-CN': '英语(美国)',
      'en-US': 'English (US)',
    },
    'en-GB': {
      'zh-CN': '英语(英国)',
      'en-US': 'English (UK)',
    },
    'ja-JP': {
      'zh-CN': '日语',
      'en-US': 'Japanese',
    },
    'ko-KR': {
      'zh-CN': '韩语',
      'en-US': 'Korean',
    },
    'de-DE': {
      'zh-CN': '德语',
      'en-US': 'German',
    },
    'fr-FR': {
      'zh-CN': '法语',
      'en-US': 'French',
    },
    'es-ES': {
      'zh-CN': '西班牙语',
      'en-US': 'Spanish',
    },
    'pt-BR': {
      'zh-CN': '葡萄牙语(巴西)',
      'en-US': 'Portuguese (Brazil)',
    },
    'ru-RU': {
      'zh-CN': '俄语',
      'en-US': 'Russian',
    },
    'ar-SA': {
      'zh-CN': '阿拉伯语',
      'en-US': 'Arabic',
    },
  } as any

  return names[locale]?.[displayLocale] || locale
}

/**
 * 获取语言的旗帜图标
 */
export function getMedicalLocaleFlag(locale: MedicalLocale): string {
  const flags: Record<MedicalLocale, string> = {
    'zh-CN': '🇨🇳',
    'zh-TW': '🇹🇼',
    'en-US': '🇺🇸',
    'en-GB': '🇬🇧',
    'ja-JP': '🇯🇵',
    'ko-KR': '🇰🇷',
    'de-DE': '🇩🇪',
    'fr-FR': '🇫🇷',
    'es-ES': '🇪🇸',
    'pt-BR': '🇧🇷',
    'ru-RU': '🇷🇺',
    'ar-SA': '🇸🇦',
  }

  return flags[locale] || '🌐'
}

// =============================================================================
// 版本信息
// =============================================================================
export const VERSION = '1.0.0'
export const PACKAGE_NAME = '@crf/i18n-medical'

// =============================================================================
// 默认导出
// =============================================================================
export default medicalI18n
