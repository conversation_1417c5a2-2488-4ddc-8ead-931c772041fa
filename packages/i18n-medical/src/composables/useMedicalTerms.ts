/**
 * 医疗术语管理 Composable
 * 提供医疗术语的查询、翻译和管理功能
 */

import { ref, computed, reactive } from 'vue'
import type {
  MedicalTerm,
  MedicalLocale,
  MedicalTermCategory,
  MedicalTranslationContext,
} from '../types/medical.types'
import {
  commonMedicalTerms,
  medicalTermsIndex,
} from '../medical-terms/common-terms'

// =============================================================================
// 全局状态
// =============================================================================
const currentLocale = ref<MedicalLocale>('zh-CN')
const termsCache = reactive(new Map<string, MedicalTerm[]>())
const translationCache = reactive(new Map<string, string>())

// =============================================================================
// 医疗术语管理 Composable
// =============================================================================
export function useMedicalTerms() {
  /**
   * 获取术语翻译
   */
  function getTermTranslation(
    termId: string,
    locale: MedicalLocale = currentLocale.value,
    context?: MedicalTranslationContext,
  ): string {
    const cacheKey = `${termId}-${locale}-${JSON.stringify(context || {})}`

    // 检查缓存
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!
    }

    const term = medicalTermsIndex[termId]
    if (!term) {
      console.warn(`Medical term not found: ${termId}`)
      return termId
    }

    const translation = term.translations[locale]
    if (!translation) {
      // 尝试回退到英语
      const fallbackTranslation = term.translations['en-US']
      if (fallbackTranslation) {
        const result = fallbackTranslation.primary
        translationCache.set(cacheKey, result)
        return result
      }
      return termId
    }

    // 根据上下文选择合适的翻译
    let result = translation.primary

    // 处理性别变化
    if (context?.patientGender && translation.gender) {
      switch (context.patientGender) {
        case 'male':
          result = translation.gender.masculine || result
          break
        case 'female':
          result = translation.gender.feminine || result
          break
        default:
          result = translation.gender.neuter || result
      }
    }

    // 缓存结果
    translationCache.set(cacheKey, result)
    return result
  }

  /**
   * 批量获取术语翻译
   */
  function getTermTranslations(
    termIds: string[],
    locale: MedicalLocale = currentLocale.value,
    context?: MedicalTranslationContext,
  ): Record<string, string> {
    const translations: Record<string, string> = {}

    termIds.forEach((termId) => {
      translations[termId] = getTermTranslation(termId, locale, context)
    })

    return translations
  }

  /**
   * 搜索医疗术语
   */
  function searchTerms(
    query: string,
    options: {
      locale?: MedicalLocale
      category?: MedicalTermCategory
      limit?: number
      includeAbbreviations?: boolean
      includeSynonyms?: boolean
    } = {},
  ): MedicalTerm[] {
    const {
      locale = currentLocale.value,
      category,
      limit = 20,
      includeAbbreviations = true,
      includeSynonyms = true,
    } = options

    const cacheKey = `search-${query}-${JSON.stringify(options)}`

    // 检查缓存
    if (termsCache.has(cacheKey)) {
      return termsCache.get(cacheKey)!
    }

    const queryLower = query.toLowerCase()
    const results: MedicalTerm[] = []

    for (const term of commonMedicalTerms) {
      // 分类过滤
      if (category && term.category !== category) {
        continue
      }

      let matched = false

      // 检查主要翻译
      const translation = term.translations[locale]
      if (
        translation &&
        translation.primary.toLowerCase().includes(queryLower)
      ) {
        matched = true
      }

      // 检查备选翻译
      if (!matched && translation?.alternatives) {
        matched = translation.alternatives.some((alt) =>
          alt.toLowerCase().includes(queryLower),
        )
      }

      // 检查缩写
      if (!matched && includeAbbreviations && term.abbreviations?.[locale]) {
        matched = term.abbreviations[locale].some((abbr) =>
          abbr.toLowerCase().includes(queryLower),
        )
      }

      // 检查同义词
      if (!matched && includeSynonyms && term.synonyms?.[locale]) {
        matched = term.synonyms[locale].some((synonym) =>
          synonym.toLowerCase().includes(queryLower),
        )
      }

      if (matched) {
        results.push(term)
        if (results.length >= limit) {
          break
        }
      }
    }

    // 按频率和相关性排序
    results.sort((a, b) => {
      const aFreq = a.frequency || 0
      const bFreq = b.frequency || 0
      return bFreq - aFreq
    })

    // 缓存结果
    termsCache.set(cacheKey, results)

    return results
  }

  /**
   * 根据分类获取术语
   */
  function getTermsByCategory(
    category: MedicalTermCategory,
    locale: MedicalLocale = currentLocale.value,
  ): MedicalTerm[] {
    const cacheKey = `category-${category}-${locale}`

    if (termsCache.has(cacheKey)) {
      return termsCache.get(cacheKey)!
    }

    const results = commonMedicalTerms.filter(
      (term) => term.category === category,
    )

    // 按频率排序
    results.sort((a, b) => (b.frequency || 0) - (a.frequency || 0))

    termsCache.set(cacheKey, results)
    return results
  }

  /**
   * 获取术语详情
   */
  function getTermDetails(termId: string): MedicalTerm | null {
    return medicalTermsIndex[termId] || null
  }

  /**
   * 获取术语的所有翻译
   */
  function getAllTranslations(
    termId: string,
  ): Record<MedicalLocale, string> | null {
    const term = medicalTermsIndex[termId]
    if (!term) return null

    const translations: Record<string, string> = {}

    Object.entries(term.translations).forEach(([locale, translation]) => {
      translations[locale] = translation.primary
    })

    return translations as Record<MedicalLocale, string>
  }

  /**
   * 获取术语缩写
   */
  function getTermAbbreviations(
    termId: string,
    locale: MedicalLocale = currentLocale.value,
  ): string[] {
    const term = medicalTermsIndex[termId]
    return term?.abbreviations?.[locale] || []
  }

  /**
   * 获取术语同义词
   */
  function getTermSynonyms(
    termId: string,
    locale: MedicalLocale = currentLocale.value,
  ): string[] {
    const term = medicalTermsIndex[termId]
    return term?.synonyms?.[locale] || []
  }

  /**
   * 设置当前语言
   */
  function setLocale(locale: MedicalLocale) {
    currentLocale.value = locale
    // 清除翻译缓存
    translationCache.clear()
  }

  /**
   * 清除缓存
   */
  function clearCache() {
    termsCache.clear()
    translationCache.clear()
  }

  /**
   * 获取支持的分类列表
   */
  const availableCategories = computed(() => {
    const categories = new Set<MedicalTermCategory>()
    commonMedicalTerms.forEach((term) => {
      categories.add(term.category)
    })
    return Array.from(categories)
  })

  /**
   * 获取术语统计信息
   */
  const termStats = computed(() => {
    const stats = {
      total: commonMedicalTerms.length,
      byCategory: {} as Record<MedicalTermCategory, number>,
      byLevel: {
        basic: 0,
        intermediate: 0,
        advanced: 0,
      },
    }

    commonMedicalTerms.forEach((term) => {
      // 按分类统计
      stats.byCategory[term.category] =
        (stats.byCategory[term.category] || 0) + 1

      // 按级别统计
      if (term.level) {
        stats.byLevel[term.level]++
      }
    })

    return stats
  })

  return {
    // 状态
    currentLocale,
    availableCategories,
    termStats,

    // 翻译方法
    getTermTranslation,
    getTermTranslations,
    getAllTranslations,

    // 搜索和查询
    searchTerms,
    getTermsByCategory,
    getTermDetails,

    // 术语信息
    getTermAbbreviations,
    getTermSynonyms,

    // 管理方法
    setLocale,
    clearCache,
  }
}

/**
 * 创建医疗术语翻译函数
 */
export function createMedicalTranslator(locale: MedicalLocale = 'zh-CN') {
  const { getTermTranslation, setLocale } = useMedicalTerms()

  // 设置语言
  setLocale(locale)

  /**
   * 医疗术语翻译函数
   */
  function mt(termId: string, context?: MedicalTranslationContext): string {
    return getTermTranslation(termId, locale, context)
  }

  return {
    mt,
    setLocale: (newLocale: MedicalLocale) => {
      setLocale(newLocale)
    },
  }
}
