/**
 * 医疗国际化主 Composable
 * 整合所有医疗国际化功能
 */

import { ref, computed, reactive } from 'vue'
import type {
  MedicalLocale,
  MedicalI18nConfig,
  MedicalTranslationContext,
} from '../types/medical.types'
import { useMedicalTerms } from './useMedicalTerms'
import { useMedicalFormat } from './useMedicalFormat'
import { useMedicalValidation } from './useMedicalValidation'

// =============================================================================
// 语言包加载器
// =============================================================================
const localeMessages = reactive<Record<MedicalLocale, Record<string, any>>>(
  {} as any,
)
const loadedLocales = reactive(new Set<MedicalLocale>())
const loadingLocales = reactive(new Set<MedicalLocale>())

/**
 * 动态加载语言包
 */
async function loadLocaleMessages(locale: MedicalLocale): Promise<void> {
  if (loadedLocales.has(locale) || loadingLocales.has(locale)) {
    return
  }

  loadingLocales.add(locale)

  try {
    // 动态导入语言包
    const [medicalModule] = await Promise.allSettled([
      import(`../locales/${locale}/medical.json`),
    ])

    const messages: Record<string, any> = {}

    if (medicalModule.status === 'fulfilled') {
      messages.medical = medicalModule.value.default || medicalModule.value
    } else {
      console.warn(
        `Failed to load medical messages for ${locale}:`,
        medicalModule.reason,
      )
      messages.medical = {}
    }

    localeMessages[locale] = messages
    loadedLocales.add(locale)
  } catch (error) {
    console.error(`Error loading locale ${locale}:`, error)
    localeMessages[locale] = { medical: {} }
  } finally {
    loadingLocales.delete(locale)
  }
}

// =============================================================================
// 医疗国际化主 Composable
// =============================================================================
export function useMedicalI18n(config: Partial<MedicalI18nConfig> = {}) {
  // 默认配置
  const defaultConfig: MedicalI18nConfig = {
    defaultLocale: 'zh-CN',
    fallbackLocale: 'en-US',
    supportedLocales: [
      'zh-CN',
      'zh-TW',
      'en-US',
      'ja-JP',
      'ko-KR',
      'de-DE',
      'fr-FR',
      'es-ES',
    ],
    lazy: true,
    enableCache: true,
    cacheExpiry: 3600000, // 1小时
    autoDetect: true,
    termSources: {},
  }

  const finalConfig = { ...defaultConfig, ...config }
  const currentLocale = ref<MedicalLocale>(finalConfig.defaultLocale)

  // 集成其他composables
  const medicalTerms = useMedicalTerms()
  const medicalFormat = useMedicalFormat()
  const medicalValidation = useMedicalValidation()

  /**
   * 医疗翻译函数
   */
  function mt(
    key: string,
    params?: Record<string, any>,
    context?: MedicalTranslationContext,
  ): string {
    // 检查是否为术语ID
    if (key.startsWith('term:')) {
      const termId = key.replace('term:', '')
      return medicalTerms.getTermTranslation(
        termId,
        currentLocale.value,
        context,
      )
    }

    // 常规翻译
    const locale = currentLocale.value
    const messages = localeMessages[locale]

    if (!messages) {
      console.warn(`Messages not loaded for locale: ${locale}`)
      return key
    }

    // 解析嵌套键名，如 'medical.symptoms.pain'
    const keys = key.split('.')
    let value: any = messages

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        // 尝试回退语言
        if (locale !== finalConfig.fallbackLocale) {
          const fallbackMessages = localeMessages[finalConfig.fallbackLocale]
          if (fallbackMessages) {
            let fallbackValue: any = fallbackMessages
            for (const fk of keys) {
              if (
                fallbackValue &&
                typeof fallbackValue === 'object' &&
                fk in fallbackValue
              ) {
                fallbackValue = fallbackValue[fk]
              } else {
                return key
              }
            }
            value = fallbackValue
            break
          }
        }
        return key
      }
    }

    if (typeof value !== 'string') {
      return key
    }

    // 参数插值
    if (params) {
      return interpolateMessage(value, params)
    }

    return value
  }

  /**
   * 消息插值
   */
  function interpolateMessage(
    message: string,
    params: Record<string, any>,
  ): string {
    return message.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match
    })
  }

  /**
   * 设置当前语言
   */
  async function setLocale(locale: MedicalLocale): Promise<void> {
    if (!finalConfig.supportedLocales.includes(locale)) {
      console.warn(`Unsupported locale: ${locale}`)
      return
    }

    if (locale === currentLocale.value) {
      return
    }

    // 懒加载语言包
    if (finalConfig.lazy && !loadedLocales.has(locale)) {
      await loadLocaleMessages(locale)
    }

    const oldLocale = currentLocale.value
    currentLocale.value = locale

    // 同步更新其他composables的语言
    medicalTerms.setLocale(locale)
    medicalFormat.setLocale(locale)
    medicalValidation.setLocale(locale)

    // 持久化语言设置
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('medical-locale', locale)
    }

    console.log(`Medical locale changed: ${oldLocale} -> ${locale}`)
  }

  /**
   * 检测浏览器语言
   */
  function detectBrowserLocale(): MedicalLocale {
    if (typeof navigator === 'undefined') {
      return finalConfig.defaultLocale
    }

    const browserLang = navigator.language || (navigator as any).userLanguage

    // 精确匹配
    if (finalConfig.supportedLocales.includes(browserLang as MedicalLocale)) {
      return browserLang as MedicalLocale
    }

    // 语言前缀匹配
    const langPrefix = browserLang.split('-')[0]
    const matchedLocale = finalConfig.supportedLocales.find((locale) =>
      locale.startsWith(langPrefix),
    )

    return matchedLocale || finalConfig.defaultLocale
  }

  /**
   * 预加载语言包
   */
  async function preloadLocales(locales: MedicalLocale[]): Promise<void> {
    const loadPromises = locales
      .filter((locale) => !loadedLocales.has(locale))
      .map((locale) => loadLocaleMessages(locale))

    await Promise.allSettled(loadPromises)
  }

  /**
   * 获取可用语言列表
   */
  const availableLocales = computed(() => {
    return finalConfig.supportedLocales.map((locale) => ({
      code: locale,
      name: getLocaleName(locale, 'en-US'),
      nativeName: getLocaleName(locale, locale),
      flag: getLocaleFlag(locale),
    }))
  })

  /**
   * 获取语言名称
   */
  function getLocaleName(
    locale: MedicalLocale,
    displayLocale: MedicalLocale,
  ): string {
    const names: Record<MedicalLocale, Record<MedicalLocale, string>> = {
      'zh-CN': {
        'zh-CN': '简体中文',
        'zh-TW': '简体中文',
        'en-US': 'Simplified Chinese',
        'en-GB': 'Simplified Chinese',
        'ja-JP': '簡体字中国語',
        'ko-KR': '중국어 간체',
        'de-DE': 'Vereinfachtes Chinesisch',
        'fr-FR': 'Chinois simplifié',
        'es-ES': 'Chino simplificado',
        'pt-BR': 'Chinês simplificado',
        'ru-RU': 'Упрощенный китайский',
        'ar-SA': 'الصينية المبسطة',
      },
      'en-US': {
        'zh-CN': '英语',
        'zh-TW': '英語',
        'en-US': 'English',
        'en-GB': 'English',
        'ja-JP': '英語',
        'ko-KR': '영어',
        'de-DE': 'Englisch',
        'fr-FR': 'Anglais',
        'es-ES': 'Inglés',
        'pt-BR': 'Inglês',
        'ru-RU': 'Английский',
        'ar-SA': 'الإنجليزية',
      },
      // 可以继续添加其他语言...
    } as any

    return names[locale]?.[displayLocale] || locale
  }

  /**
   * 获取语言旗帜
   */
  function getLocaleFlag(locale: MedicalLocale): string {
    const flags: Record<MedicalLocale, string> = {
      'zh-CN': '🇨🇳',
      'zh-TW': '🇹🇼',
      'en-US': '🇺🇸',
      'en-GB': '🇬🇧',
      'ja-JP': '🇯🇵',
      'ko-KR': '🇰🇷',
      'de-DE': '🇩🇪',
      'fr-FR': '🇫🇷',
      'es-ES': '🇪🇸',
      'pt-BR': '🇧🇷',
      'ru-RU': '🇷🇺',
      'ar-SA': '🇸🇦',
    }

    return flags[locale] || '🌐'
  }

  /**
   * 初始化
   */
  async function init(): Promise<void> {
    // 从本地存储恢复语言设置
    if (typeof localStorage !== 'undefined') {
      const savedLocale = localStorage.getItem(
        'medical-locale',
      ) as MedicalLocale
      if (savedLocale && finalConfig.supportedLocales.includes(savedLocale)) {
        await setLocale(savedLocale)
        return
      }
    }

    // 自动检测浏览器语言
    if (finalConfig.autoDetect) {
      const detectedLocale = detectBrowserLocale()
      if (detectedLocale !== currentLocale.value) {
        await setLocale(detectedLocale)
      }
    }

    // 预加载常用语言包
    const commonLocales: MedicalLocale[] = ['zh-CN', 'en-US']
    await preloadLocales(
      commonLocales.filter((locale) => locale !== currentLocale.value),
    )
  }

  /**
   * 清理资源
   */
  function cleanup(): void {
    medicalTerms.clearCache()
    // 清理其他资源...
  }

  return {
    // 状态
    currentLocale,
    availableLocales,
    loadedLocales: computed(() => Array.from(loadedLocales)),
    loadingLocales: computed(() => Array.from(loadingLocales)),

    // 翻译函数
    mt,

    // 语言管理
    setLocale,
    detectBrowserLocale,
    preloadLocales,

    // 集成的功能
    ...medicalTerms,
    ...medicalFormat,
    ...medicalValidation,

    // 配置
    config: finalConfig,

    // 生命周期
    init,
    cleanup,
  }
}
