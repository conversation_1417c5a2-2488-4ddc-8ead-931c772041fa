/**
 * 医疗表单验证 Composable
 * 提供医疗数据的验证规则和错误消息国际化
 */

import { ref, computed } from 'vue'
import type {
  MedicalLocale,
  MedicalValidationRule,
  MedicalFieldType,
} from '../types/medical.types'

// =============================================================================
// 医疗验证规则定义
// =============================================================================
export interface ValidationRule {
  type: 'required' | 'range' | 'format' | 'custom'
  validator?: (value: any) => boolean
  params?: Record<string, any>
  message: Record<MedicalLocale, string>
}

// 预定义的医疗验证规则
const medicalValidationRules: Record<string, ValidationRule> = {
  // 必填验证
  required: {
    type: 'required',
    validator: (value: any) =>
      value !== null && value !== undefined && value !== '',
    message: {
      'zh-CN': '此项为必填项',
      'zh-TW': '此項為必填項',
      'en-US': 'This field is required',
      'en-GB': 'This field is required',
      'ja-JP': 'この項目は必須です',
      'ko-KR': '이 필드는 필수입니다',
      'de-DE': 'Dieses Feld ist erforderlich',
      'fr-FR': 'Ce champ est obligatoire',
      'es-ES': 'Este campo es obligatorio',
      'pt-BR': 'Este campo é obrigatório',
      'ru-RU': 'Это поле обязательно',
      'ar-SA': 'هذا الحقل مطلوب',
    },
  },

  // 体重验证 (0.1-1000 kg)
  weight: {
    type: 'range',
    validator: (value: number) => value >= 0.1 && value <= 1000,
    params: { min: 0.1, max: 1000, unit: 'kg' },
    message: {
      'zh-CN': '体重应在0.1-1000公斤之间',
      'zh-TW': '體重應在0.1-1000公斤之間',
      'en-US': 'Weight should be between 0.1-1000 kg',
      'en-GB': 'Weight should be between 0.1-1000 kg',
      'ja-JP': '体重は0.1-1000kgの間である必要があります',
      'ko-KR': '체중은 0.1-1000kg 사이여야 합니다',
      'de-DE': 'Das Gewicht sollte zwischen 0,1-1000 kg liegen',
      'fr-FR': 'Le poids doit être entre 0,1-1000 kg',
      'es-ES': 'El peso debe estar entre 0,1-1000 kg',
      'pt-BR': 'O peso deve estar entre 0,1-1000 kg',
      'ru-RU': 'Вес должен быть между 0,1-1000 кг',
      'ar-SA': 'يجب أن يكون الوزن بين 0.1-1000 كجم',
    },
  },

  // 身高验证 (10-300 cm)
  height: {
    type: 'range',
    validator: (value: number) => value >= 10 && value <= 300,
    params: { min: 10, max: 300, unit: 'cm' },
    message: {
      'zh-CN': '身高应在10-300厘米之间',
      'zh-TW': '身高應在10-300厘米之間',
      'en-US': 'Height should be between 10-300 cm',
      'en-GB': 'Height should be between 10-300 cm',
      'ja-JP': '身長は10-300cmの間である必要があります',
      'ko-KR': '키는 10-300cm 사이여야 합니다',
      'de-DE': 'Die Größe sollte zwischen 10-300 cm liegen',
      'fr-FR': 'La taille doit être entre 10-300 cm',
      'es-ES': 'La altura debe estar entre 10-300 cm',
      'pt-BR': 'A altura deve estar entre 10-300 cm',
      'ru-RU': 'Рост должен быть между 10-300 см',
      'ar-SA': 'يجب أن يكون الطول بين 10-300 سم',
    },
  },

  // 体温验证 (30-50°C)
  temperature: {
    type: 'range',
    validator: (value: number) => value >= 30 && value <= 50,
    params: { min: 30, max: 50, unit: '°C' },
    message: {
      'zh-CN': '体温应在30-50°C之间',
      'zh-TW': '體溫應在30-50°C之間',
      'en-US': 'Temperature should be between 30-50°C',
      'en-GB': 'Temperature should be between 30-50°C',
      'ja-JP': '体温は30-50°Cの間である必要があります',
      'ko-KR': '체온은 30-50°C 사이여야 합니다',
      'de-DE': 'Die Temperatur sollte zwischen 30-50°C liegen',
      'fr-FR': 'La température doit être entre 30-50°C',
      'es-ES': 'La temperatura debe estar entre 30-50°C',
      'pt-BR': 'A temperatura deve estar entre 30-50°C',
      'ru-RU': 'Температура должна быть между 30-50°C',
      'ar-SA': 'يجب أن تكون درجة الحرارة بين 30-50°C',
    },
  },

  // 血压验证
  bloodPressureSystolic: {
    type: 'range',
    validator: (value: number) => value >= 50 && value <= 300,
    params: { min: 50, max: 300, unit: 'mmHg' },
    message: {
      'zh-CN': '收缩压应在50-300mmHg之间',
      'zh-TW': '收縮壓應在50-300mmHg之間',
      'en-US': 'Systolic pressure should be between 50-300 mmHg',
      'en-GB': 'Systolic pressure should be between 50-300 mmHg',
      'ja-JP': '収縮期血圧は50-300mmHgの間である必要があります',
      'ko-KR': '수축기 혈압은 50-300mmHg 사이여야 합니다',
      'de-DE': 'Der systolische Druck sollte zwischen 50-300 mmHg liegen',
      'fr-FR': 'La pression systolique doit être entre 50-300 mmHg',
      'es-ES': 'La presión sistólica debe estar entre 50-300 mmHg',
      'pt-BR': 'A pressão sistólica deve estar entre 50-300 mmHg',
      'ru-RU': 'Систолическое давление должно быть между 50-300 мм рт.ст.',
      'ar-SA': 'يجب أن يكون الضغط الانقباضي بين 50-300 ملم زئبق',
    },
  },

  bloodPressureDiastolic: {
    type: 'range',
    validator: (value: number) => value >= 20 && value <= 200,
    params: { min: 20, max: 200, unit: 'mmHg' },
    message: {
      'zh-CN': '舒张压应在20-200mmHg之间',
      'zh-TW': '舒張壓應在20-200mmHg之間',
      'en-US': 'Diastolic pressure should be between 20-200 mmHg',
      'en-GB': 'Diastolic pressure should be between 20-200 mmHg',
      'ja-JP': '拡張期血圧は20-200mmHgの間である必要があります',
      'ko-KR': '이완기 혈압은 20-200mmHg 사이여야 합니다',
      'de-DE': 'Der diastolische Druck sollte zwischen 20-200 mmHg liegen',
      'fr-FR': 'La pression diastolique doit être entre 20-200 mmHg',
      'es-ES': 'La presión diastólica debe estar entre 20-200 mmHg',
      'pt-BR': 'A pressão diastólica deve estar entre 20-200 mmHg',
      'ru-RU': 'Диастолическое давление должно быть между 20-200 мм рт.ст.',
      'ar-SA': 'يجب أن يكون الضغط الانبساطي بين 20-200 ملم زئبق',
    },
  },

  // 心率验证 (20-300 bpm)
  heartRate: {
    type: 'range',
    validator: (value: number) => value >= 20 && value <= 300,
    params: { min: 20, max: 300, unit: 'bpm' },
    message: {
      'zh-CN': '心率应在20-300次/分之间',
      'zh-TW': '心率應在20-300次/分之間',
      'en-US': 'Heart rate should be between 20-300 bpm',
      'en-GB': 'Heart rate should be between 20-300 bpm',
      'ja-JP': '心拍数は20-300bpmの間である必要があります',
      'ko-KR': '심박수는 20-300bpm 사이여야 합니다',
      'de-DE': 'Die Herzfrequenz sollte zwischen 20-300 bpm liegen',
      'fr-FR': 'La fréquence cardiaque doit être entre 20-300 bpm',
      'es-ES': 'La frecuencia cardíaca debe estar entre 20-300 bpm',
      'pt-BR': 'A frequência cardíaca deve estar entre 20-300 bpm',
      'ru-RU': 'Частота сердечных сокращений должна быть между 20-300 уд/мин',
      'ar-SA': 'يجب أن يكون معدل ضربات القلب بين 20-300 نبضة/دقيقة',
    },
  },

  // 年龄验证 (0-150 years)
  age: {
    type: 'range',
    validator: (value: number) => value >= 0 && value <= 150,
    params: { min: 0, max: 150, unit: 'years' },
    message: {
      'zh-CN': '年龄应在0-150岁之间',
      'zh-TW': '年齡應在0-150歲之間',
      'en-US': 'Age should be between 0-150 years',
      'en-GB': 'Age should be between 0-150 years',
      'ja-JP': '年齢は0-150歳の間である必要があります',
      'ko-KR': '나이는 0-150세 사이여야 합니다',
      'de-DE': 'Das Alter sollte zwischen 0-150 Jahren liegen',
      'fr-FR': "L'âge doit être entre 0-150 ans",
      'es-ES': 'La edad debe estar entre 0-150 años',
      'pt-BR': 'A idade deve estar entre 0-150 anos',
      'ru-RU': 'Возраст должен быть между 0-150 годами',
      'ar-SA': 'يجب أن يكون العمر بين 0-150 سنة',
    },
  },

  // 电子邮件格式验证
  email: {
    type: 'format',
    validator: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message: {
      'zh-CN': '请输入有效的电子邮件地址',
      'zh-TW': '請輸入有效的電子郵件地址',
      'en-US': 'Please enter a valid email address',
      'en-GB': 'Please enter a valid email address',
      'ja-JP': '有効なメールアドレスを入力してください',
      'ko-KR': '유효한 이메일 주소를 입력하세요',
      'de-DE': 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
      'fr-FR': 'Veuillez saisir une adresse e-mail valide',
      'es-ES': 'Por favor ingrese una dirección de correo válida',
      'pt-BR': 'Por favor, insira um endereço de e-mail válido',
      'ru-RU': 'Пожалуйста, введите действительный адрес электронной почты',
      'ar-SA': 'يرجى إدخال عنوان بريد إلكتروني صالح',
    },
  },

  // 电话号码格式验证
  phone: {
    type: 'format',
    validator: (value: string) =>
      /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, '')),
    message: {
      'zh-CN': '请输入有效的电话号码',
      'zh-TW': '請輸入有效的電話號碼',
      'en-US': 'Please enter a valid phone number',
      'en-GB': 'Please enter a valid phone number',
      'ja-JP': '有効な電話番号を入力してください',
      'ko-KR': '유효한 전화번호를 입력하세요',
      'de-DE': 'Bitte geben Sie eine gültige Telefonnummer ein',
      'fr-FR': 'Veuillez saisir un numéro de téléphone valide',
      'es-ES': 'Por favor ingrese un número de teléfono válido',
      'pt-BR': 'Por favor, insira um número de telefone válido',
      'ru-RU': 'Пожалуйста, введите действительный номер телефона',
      'ar-SA': 'يرجى إدخال رقم هاتف صالح',
    },
  },
}

// =============================================================================
// 医疗验证 Composable
// =============================================================================
export function useMedicalValidation() {
  const currentLocale = ref<MedicalLocale>('zh-CN')

  /**
   * 验证单个字段
   */
  function validateField(
    value: any,
    rules: string[],
    locale: MedicalLocale = currentLocale.value,
  ): { valid: boolean; message?: string } {
    for (const ruleName of rules) {
      const rule = medicalValidationRules[ruleName]
      if (!rule) {
        console.warn(`Validation rule not found: ${ruleName}`)
        continue
      }

      if (rule.validator && !rule.validator(value)) {
        return {
          valid: false,
          message: rule.message[locale] || rule.message['en-US'],
        }
      }
    }

    return { valid: true }
  }

  /**
   * 批量验证表单
   */
  function validateForm(
    formData: Record<string, any>,
    fieldRules: Record<string, string[]>,
    locale: MedicalLocale = currentLocale.value,
  ): { valid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {}
    let valid = true

    Object.entries(fieldRules).forEach(([fieldName, rules]) => {
      const fieldValue = formData[fieldName]
      const result = validateField(fieldValue, rules, locale)

      if (!result.valid) {
        valid = false
        errors[fieldName] = result.message!
      }
    })

    return { valid, errors }
  }

  /**
   * 获取验证规则信息
   */
  function getValidationRule(ruleName: string): ValidationRule | null {
    return medicalValidationRules[ruleName] || null
  }

  /**
   * 获取验证消息
   */
  function getValidationMessage(
    ruleName: string,
    locale: MedicalLocale = currentLocale.value,
  ): string {
    const rule = medicalValidationRules[ruleName]
    return rule?.message[locale] || rule?.message['en-US'] || ''
  }

  /**
   * 添加自定义验证规则
   */
  function addValidationRule(name: string, rule: ValidationRule): void {
    medicalValidationRules[name] = rule
  }

  /**
   * 设置当前语言
   */
  function setLocale(locale: MedicalLocale) {
    currentLocale.value = locale
  }

  /**
   * 获取所有可用的验证规则
   */
  const availableRules = computed(() => {
    return Object.keys(medicalValidationRules)
  })

  return {
    // 状态
    currentLocale,
    availableRules,

    // 验证方法
    validateField,
    validateForm,

    // 规则管理
    getValidationRule,
    getValidationMessage,
    addValidationRule,

    // 设置
    setLocale,
  }
}
