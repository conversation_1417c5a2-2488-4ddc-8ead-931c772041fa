/**
 * 医疗数据格式化 Composable
 * 提供医疗数据的本地化格式化功能
 */

import { ref, computed } from 'vue'
import type {
  MedicalLocale,
  MedicalNumberFormatOptions,
  MedicalDateFormatOptions,
  MedicalUnit,
} from '../types/medical.types'

// =============================================================================
// 医疗单位定义
// =============================================================================
const medicalUnits: Record<string, MedicalUnit> = {
  // 体重单位
  kg: {
    code: 'kg',
    name: {
      'zh-CN': '千克',
      'zh-TW': '千克',
      'en-US': 'Kilogram',
      'en-GB': 'Kilogram',
      'ja-JP': 'キログラム',
      'ko-KR': '킬로그램',
      'de-DE': 'Kilogramm',
      'fr-FR': 'Kilogramme',
      'es-ES': 'Kilogramo',
      'pt-BR': 'Quilograma',
      'ru-RU': 'Килограмм',
      'ar-SA': 'كيلوغرام',
    },
    symbol: 'kg',
    category: 'weight',
    baseUnit: 'kg',
    conversionFactor: 1,
  },
  lb: {
    code: 'lb',
    name: {
      'zh-CN': '磅',
      'zh-TW': '磅',
      'en-US': 'Pound',
      'en-GB': 'Pound',
      'ja-JP': 'ポンド',
      'ko-KR': '파운드',
      'de-DE': 'Pfund',
      'fr-FR': 'Livre',
      'es-ES': 'Libra',
      'pt-BR': 'Libra',
      'ru-RU': 'Фунт',
      'ar-SA': 'رطل',
    },
    symbol: 'lb',
    category: 'weight',
    baseUnit: 'kg',
    conversionFactor: 0.453592,
  },

  // 身高单位
  cm: {
    code: 'cm',
    name: {
      'zh-CN': '厘米',
      'zh-TW': '厘米',
      'en-US': 'Centimeter',
      'en-GB': 'Centimetre',
      'ja-JP': 'センチメートル',
      'ko-KR': '센티미터',
      'de-DE': 'Zentimeter',
      'fr-FR': 'Centimètre',
      'es-ES': 'Centímetro',
      'pt-BR': 'Centímetro',
      'ru-RU': 'Сантиметр',
      'ar-SA': 'سنتيمتر',
    },
    symbol: 'cm',
    category: 'height',
    baseUnit: 'cm',
    conversionFactor: 1,
  },
  ft: {
    code: 'ft',
    name: {
      'zh-CN': '英尺',
      'zh-TW': '英尺',
      'en-US': 'Foot',
      'en-GB': 'Foot',
      'ja-JP': 'フィート',
      'ko-KR': '피트',
      'de-DE': 'Fuß',
      'fr-FR': 'Pied',
      'es-ES': 'Pie',
      'pt-BR': 'Pé',
      'ru-RU': 'Фут',
      'ar-SA': 'قدم',
    },
    symbol: 'ft',
    category: 'height',
    baseUnit: 'cm',
    conversionFactor: 30.48,
  },

  // 温度单位
  celsius: {
    code: 'celsius',
    name: {
      'zh-CN': '摄氏度',
      'zh-TW': '攝氏度',
      'en-US': 'Celsius',
      'en-GB': 'Celsius',
      'ja-JP': '摂氏',
      'ko-KR': '섭씨',
      'de-DE': 'Celsius',
      'fr-FR': 'Celsius',
      'es-ES': 'Celsius',
      'pt-BR': 'Celsius',
      'ru-RU': 'Цельсий',
      'ar-SA': 'مئوية',
    },
    symbol: '°C',
    category: 'temperature',
    baseUnit: 'celsius',
    conversionFactor: 1,
  },
  fahrenheit: {
    code: 'fahrenheit',
    name: {
      'zh-CN': '华氏度',
      'zh-TW': '華氏度',
      'en-US': 'Fahrenheit',
      'en-GB': 'Fahrenheit',
      'ja-JP': '華氏',
      'ko-KR': '화씨',
      'de-DE': 'Fahrenheit',
      'fr-FR': 'Fahrenheit',
      'es-ES': 'Fahrenheit',
      'pt-BR': 'Fahrenheit',
      'ru-RU': 'Фаренгейт',
      'ar-SA': 'فهرنهايت',
    },
    symbol: '°F',
    category: 'temperature',
    baseUnit: 'celsius',
    conversionFactor: 1, // 特殊转换公式
  },

  // 血压单位
  mmHg: {
    code: 'mmHg',
    name: {
      'zh-CN': '毫米汞柱',
      'zh-TW': '毫米汞柱',
      'en-US': 'Millimeter of Mercury',
      'en-GB': 'Millimetre of Mercury',
      'ja-JP': 'ミリメートル水銀柱',
      'ko-KR': '밀리미터 수은주',
      'de-DE': 'Millimeter Quecksilbersäule',
      'fr-FR': 'Millimètre de mercure',
      'es-ES': 'Milímetro de mercurio',
      'pt-BR': 'Milímetro de mercúrio',
      'ru-RU': 'Миллиметр ртутного столба',
      'ar-SA': 'ملليمتر زئبق',
    },
    symbol: 'mmHg',
    category: 'pressure',
    baseUnit: 'mmHg',
    conversionFactor: 1,
  },
}

// =============================================================================
// 医疗格式化 Composable
// =============================================================================
export function useMedicalFormat() {
  const currentLocale = ref<MedicalLocale>('zh-CN')

  /**
   * 格式化医疗数值
   */
  function formatMedicalNumber(
    value: number,
    options: MedicalNumberFormatOptions,
  ): string {
    const {
      locale = currentLocale.value,
      unit,
      precision = 1,
      showUnit = true,
      unitPosition = 'after',
      separator = ' ',
    } = options

    // 格式化数值
    const formatter = new Intl.NumberFormat(locale, {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    })

    const formattedNumber = formatter.format(value)

    // 添加单位
    if (!showUnit || !unit) {
      return formattedNumber
    }

    const unitInfo = medicalUnits[unit]
    const unitSymbol = unitInfo?.symbol || unit

    if (unitPosition === 'before') {
      return `${unitSymbol}${separator}${formattedNumber}`
    } else {
      return `${formattedNumber}${separator}${unitSymbol}`
    }
  }

  /**
   * 格式化体重
   */
  function formatWeight(
    value: number,
    locale: MedicalLocale = currentLocale.value,
    unit: 'kg' | 'lb' = 'kg',
  ): string {
    return formatMedicalNumber(value, {
      locale,
      unit,
      precision: 1,
      showUnit: true,
    })
  }

  /**
   * 格式化身高
   */
  function formatHeight(
    value: number,
    locale: MedicalLocale = currentLocale.value,
    unit: 'cm' | 'ft' = 'cm',
  ): string {
    if (unit === 'ft') {
      // 转换为英尺和英寸
      const totalInches = value / 2.54
      const feet = Math.floor(totalInches / 12)
      const inches = Math.round(totalInches % 12)
      return `${feet}' ${inches}"`
    }

    return formatMedicalNumber(value, {
      locale,
      unit,
      precision: 0,
      showUnit: true,
    })
  }

  /**
   * 格式化体温
   */
  function formatTemperature(
    value: number,
    locale: MedicalLocale = currentLocale.value,
    unit: 'celsius' | 'fahrenheit' = 'celsius',
  ): string {
    let displayValue = value

    // 温度单位转换
    if (unit === 'fahrenheit') {
      displayValue = (value * 9) / 5 + 32
    }

    return formatMedicalNumber(displayValue, {
      locale,
      unit,
      precision: 1,
      showUnit: true,
    })
  }

  /**
   * 格式化血压
   */
  function formatBloodPressure(
    systolic: number,
    diastolic: number,
    locale: MedicalLocale = currentLocale.value,
  ): string {
    const sys = formatMedicalNumber(systolic, {
      locale,
      precision: 0,
      showUnit: false,
    })

    const dia = formatMedicalNumber(diastolic, {
      locale,
      precision: 0,
      showUnit: false,
    })

    return `${sys}/${dia} mmHg`
  }

  /**
   * 格式化心率
   */
  function formatHeartRate(
    value: number,
    locale: MedicalLocale = currentLocale.value,
  ): string {
    const formattedValue = formatMedicalNumber(value, {
      locale,
      precision: 0,
      showUnit: false,
    })

    const bpmText = locale.startsWith('zh') ? '次/分' : 'bpm'
    return `${formattedValue} ${bpmText}`
  }

  /**
   * 格式化医疗日期
   */
  function formatMedicalDate(
    date: Date | string | number,
    options: MedicalDateFormatOptions,
  ): string {
    const {
      locale = currentLocale.value,
      format = 'medium',
      customFormat,
      timezone,
      includeTime = false,
    } = options

    const dateObj = new Date(date)

    if (customFormat) {
      // 自定义格式化逻辑
      return customFormat
        .replace('YYYY', dateObj.getFullYear().toString())
        .replace('MM', (dateObj.getMonth() + 1).toString().padStart(2, '0'))
        .replace('DD', dateObj.getDate().toString().padStart(2, '0'))
        .replace('HH', dateObj.getHours().toString().padStart(2, '0'))
        .replace('mm', dateObj.getMinutes().toString().padStart(2, '0'))
    }

    const formatOptions: Intl.DateTimeFormatOptions = {
      timeZone: timezone,
    }

    // 设置日期格式
    switch (format) {
      case 'short':
        formatOptions.dateStyle = 'short'
        break
      case 'medium':
        formatOptions.dateStyle = 'medium'
        break
      case 'long':
        formatOptions.dateStyle = 'long'
        break
      case 'full':
        formatOptions.dateStyle = 'full'
        break
    }

    // 包含时间
    if (includeTime) {
      formatOptions.timeStyle = 'short'
    }

    return new Intl.DateTimeFormat(locale, formatOptions).format(dateObj)
  }

  /**
   * 格式化年龄
   */
  function formatAge(
    birthDate: Date | string,
    locale: MedicalLocale = currentLocale.value,
  ): string {
    const birth = new Date(birthDate)
    const now = new Date()
    const ageInMs = now.getTime() - birth.getTime()
    const ageInYears = Math.floor(ageInMs / (1000 * 60 * 60 * 24 * 365.25))

    const yearText = locale.startsWith('zh')
      ? '岁'
      : locale.startsWith('en')
        ? ageInYears === 1
          ? 'year'
          : 'years'
        : 'years'

    return `${ageInYears} ${yearText}`
  }

  /**
   * 格式化BMI
   */
  function formatBMI(
    weight: number, // kg
    height: number, // cm
    locale: MedicalLocale = currentLocale.value,
  ): string {
    const heightInM = height / 100
    const bmi = weight / (heightInM * heightInM)

    return formatMedicalNumber(bmi, {
      locale,
      precision: 1,
      showUnit: false,
    })
  }

  /**
   * 获取单位信息
   */
  function getUnitInfo(unitCode: string): MedicalUnit | null {
    return medicalUnits[unitCode] || null
  }

  /**
   * 获取单位名称
   */
  function getUnitName(
    unitCode: string,
    locale: MedicalLocale = currentLocale.value,
  ): string {
    const unit = medicalUnits[unitCode]
    return unit?.name[locale] || unitCode
  }

  /**
   * 设置当前语言
   */
  function setLocale(locale: MedicalLocale) {
    currentLocale.value = locale
  }

  /**
   * 获取支持的单位列表
   */
  const availableUnits = computed(() => {
    return Object.keys(medicalUnits)
  })

  /**
   * 按分类获取单位
   */
  function getUnitsByCategory(category: MedicalUnit['category']) {
    return Object.values(medicalUnits).filter(
      (unit) => unit.category === category,
    )
  }

  return {
    // 状态
    currentLocale,
    availableUnits,

    // 数值格式化
    formatMedicalNumber,
    formatWeight,
    formatHeight,
    formatTemperature,
    formatBloodPressure,
    formatHeartRate,
    formatBMI,

    // 日期格式化
    formatMedicalDate,
    formatAge,

    // 单位管理
    getUnitInfo,
    getUnitName,
    getUnitsByCategory,

    // 设置
    setLocale,
  }
}
