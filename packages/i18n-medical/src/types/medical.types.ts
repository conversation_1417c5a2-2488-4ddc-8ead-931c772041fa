/**
 * 医疗术语类型定义
 */

// =============================================================================
// 基础医疗术语类型
// =============================================================================

/** 支持的医疗语言 */
export type MedicalLocale =
  | 'zh-CN' // 简体中文
  | 'zh-TW' // 繁体中文
  | 'en-US' // 美式英语
  | 'en-GB' // 英式英语
  | 'ja-JP' // 日语
  | 'ko-KR' // 韩语
  | 'de-DE' // 德语
  | 'fr-FR' // 法语
  | 'es-ES' // 西班牙语
  | 'pt-BR' // 巴西葡萄牙语
  | 'ru-RU' // 俄语
  | 'ar-SA' // 阿拉伯语

/** 医疗术语分类 */
export type MedicalTermCategory =
  | 'anatomy' // 解剖学
  | 'disease' // 疾病
  | 'symptom' // 症状
  | 'medication' // 药物
  | 'procedure' // 医疗程序
  | 'laboratory' // 实验室检查
  | 'imaging' // 影像学
  | 'vital-signs' // 生命体征
  | 'diagnosis' // 诊断
  | 'treatment' // 治疗
  | 'specialty' // 专科
  | 'equipment' // 医疗设备

/** 医疗术语条目 */
export interface MedicalTerm {
  /** 术语ID */
  id: string
  /** 术语分类 */
  category: MedicalTermCategory
  /** 标准编码 (如ICD-10, SNOMED CT等) */
  codes?: {
    icd10?: string
    snomed?: string
    loinc?: string
    atc?: string
    [key: string]: string | undefined
  }
  /** 多语言翻译 */
  translations: Record<MedicalLocale, MedicalTermTranslation>
  /** 同义词 */
  synonyms?: Record<MedicalLocale, string[]>
  /** 缩写 */
  abbreviations?: Record<MedicalLocale, string[]>
  /** 术语描述 */
  description?: Record<MedicalLocale, string>
  /** 使用频率 (1-5, 5为最常用) */
  frequency?: number
  /** 专业级别 (basic, intermediate, advanced) */
  level?: 'basic' | 'intermediate' | 'advanced'
  /** 创建时间 */
  createdAt?: string
  /** 更新时间 */
  updatedAt?: string
}

/** 医疗术语翻译 */
export interface MedicalTermTranslation {
  /** 主要翻译 */
  primary: string
  /** 备选翻译 */
  alternatives?: string[]
  /** 性别变化 (某些语言需要) */
  gender?: {
    masculine?: string
    feminine?: string
    neuter?: string
  }
  /** 复数形式 */
  plural?: string
  /** 发音 (IPA) */
  pronunciation?: string
  /** 本地化注释 */
  notes?: string
}

// =============================================================================
// 医疗标准编码系统
// =============================================================================

/** ICD-10 疾病编码 */
export interface ICD10Code {
  code: string
  title: Record<MedicalLocale, string>
  category: string
  subcategory?: string
  description?: Record<MedicalLocale, string>
}

/** SNOMED CT 术语 */
export interface SNOMEDTerm {
  conceptId: string
  term: Record<MedicalLocale, string>
  semanticTag: string
  hierarchy: string[]
  relationships?: {
    type: string
    target: string
  }[]
}

/** LOINC 实验室术语 */
export interface LOINCTerm {
  code: string
  longName: Record<MedicalLocale, string>
  shortName: Record<MedicalLocale, string>
  component: string
  property: string
  timeAspect: string
  system: string
  scale: string
  method?: string
  units?: Record<MedicalLocale, string>
}

// =============================================================================
// 医疗表单相关类型
// =============================================================================

/** 医疗表单字段类型 */
export type MedicalFieldType =
  | 'patient-info' // 患者信息
  | 'vital-signs' // 生命体征
  | 'medical-history' // 病史
  | 'physical-exam' // 体格检查
  | 'laboratory' // 实验室检查
  | 'imaging' // 影像检查
  | 'diagnosis' // 诊断
  | 'treatment' // 治疗
  | 'medication' // 用药
  | 'adverse-event' // 不良事件
  | 'follow-up' // 随访

/** 医疗表单验证规则 */
export interface MedicalValidationRule {
  type: 'required' | 'range' | 'format' | 'custom'
  message: Record<MedicalLocale, string>
  params?: Record<string, any>
}

/** 医疗单位转换 */
export interface MedicalUnit {
  code: string
  name: Record<MedicalLocale, string>
  symbol: string
  category:
    | 'weight'
    | 'height'
    | 'temperature'
    | 'pressure'
    | 'volume'
    | 'time'
    | 'other'
  baseUnit?: string
  conversionFactor?: number
}

// =============================================================================
// 医疗数据格式化类型
// =============================================================================

/** 医疗数值格式化选项 */
export interface MedicalNumberFormatOptions {
  locale: MedicalLocale
  unit?: string
  precision?: number
  showUnit?: boolean
  unitPosition?: 'before' | 'after'
  separator?: string
}

/** 医疗日期格式化选项 */
export interface MedicalDateFormatOptions {
  locale: MedicalLocale
  format?: 'short' | 'medium' | 'long' | 'full' | 'custom'
  customFormat?: string
  timezone?: string
  includeTime?: boolean
}

// =============================================================================
// 医疗翻译配置类型
// =============================================================================

/** 医疗翻译配置 */
export interface MedicalI18nConfig {
  /** 默认语言 */
  defaultLocale: MedicalLocale
  /** 回退语言 */
  fallbackLocale: MedicalLocale
  /** 支持的语言列表 */
  supportedLocales: MedicalLocale[]
  /** 是否启用懒加载 */
  lazy: boolean
  /** 是否启用术语缓存 */
  enableCache: boolean
  /** 缓存过期时间 (毫秒) */
  cacheExpiry: number
  /** 是否启用自动检测 */
  autoDetect: boolean
  /** 术语数据源 */
  termSources: {
    icd10?: string
    snomed?: string
    loinc?: string
    custom?: string[]
  }
}

/** 医疗翻译上下文 */
export interface MedicalTranslationContext {
  /** 医疗专科 */
  specialty?: string
  /** 患者性别 */
  patientGender?: 'male' | 'female' | 'other'
  /** 患者年龄组 */
  ageGroup?: 'pediatric' | 'adult' | 'geriatric'
  /** 临床环境 */
  clinicalContext?: 'inpatient' | 'outpatient' | 'emergency' | 'icu'
  /** 紧急程度 */
  urgency?: 'routine' | 'urgent' | 'emergent'
}

// =============================================================================
// 验证规则接口
// =============================================================================
export interface ValidationRule {
  type: 'required' | 'range' | 'format' | 'custom'
  validator?: (value: any) => boolean
  params?: Record<string, any>
  message: Record<MedicalLocale, string>
}

// =============================================================================
// 导出所有类型
// =============================================================================
export type {
  MedicalLocale,
  MedicalTermCategory,
  MedicalTerm,
  MedicalTermTranslation,
  ICD10Code,
  SNOMEDTerm,
  LOINCTerm,
  MedicalFieldType,
  MedicalValidationRule,
  MedicalUnit,
  MedicalNumberFormatOptions,
  MedicalDateFormatOptions,
  MedicalI18nConfig,
  MedicalTranslationContext,
  ValidationRule,
}
