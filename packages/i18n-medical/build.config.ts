import { defineBuildConfig } from 'unbuild'

export default defineBuildConfig({
  entries: [
    'src/index',
    // 分别导出各个模块
    'src/locales/index',
    'src/medical-terms/index',
    'src/composables/index',
    'src/utils/index',
  ],
  declaration: true,
  clean: true,
  rollup: {
    emitCJS: true,
    inlineDependencies: true,
  },
  externals: [
    'vue',
    '@crf/shared-utils',
    '@crf/type-definitions',
  ],
})
