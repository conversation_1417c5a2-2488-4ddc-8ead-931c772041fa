# @crf/i18n-medical

专业的医疗CRF国际化系统，提供完整的医疗术语翻译和多语言支持。

## 特性

- 🏥 **专业医疗术语库** - 包含常用医疗术语的多语言翻译
- 🌍 **多语言支持** - 支持12种语言，包括中文、英语、日语、韩语等
- 📊 **医疗数据格式化** - 专业的医疗数据本地化格式化
- ✅ **医疗表单验证** - 内置医疗数据验证规则和错误消息
- 🔍 **术语搜索** - 强大的医疗术语搜索和匹配功能
- 💾 **智能缓存** - 高效的翻译缓存机制
- 🎯 **Vue 3 优化** - 基于 Vue 3 Composition API 设计

## 安装

```bash
pnpm add @crf/i18n-medical
```

## 快速开始

### 基础使用

```typescript
import { createMedicalI18n } from '@crf/i18n-medical'

// 创建医疗国际化实例
const medicalI18n = createMedicalI18n({
  defaultLocale: 'zh-CN',
  supportedLocales: ['zh-CN', 'en-US', 'ja-JP']
})

// 在 Vue 应用中使用
app.use(medicalI18n)

// 在组件中使用
import { useMedicalI18n } from '@crf/i18n-medical'

export default {
  setup() {
    const { mt, setLocale } = useMedicalI18n()
    
    // 翻译医疗术语
    const heartRate = mt('medical.vitalSigns.heartRate') // "心率"
    const fever = mt('medical.symptoms.fever') // "发热"
    
    // 切换语言
    await setLocale('en-US')
    
    return { heartRate, fever }
  }
}
```

### 医疗术语翻译

```typescript
import { useMedicalTerms } from '@crf/i18n-medical'

const { getTermTranslation, searchTerms } = useMedicalTerms()

// 获取术语翻译
const translation = getTermTranslation('vital-signs-temperature') // "体温"

// 搜索医疗术语
const results = searchTerms('心率', {
  category: 'vital-signs',
  limit: 10
})
```

### 医疗数据格式化

```typescript
import { useMedicalFormat } from '@crf/i18n-medical'

const { 
  formatWeight, 
  formatTemperature, 
  formatBloodPressure 
} = useMedicalFormat()

// 格式化体重
const weight = formatWeight(70.5, 'zh-CN', 'kg') // "70.5 kg"

// 格式化体温
const temp = formatTemperature(37.2, 'zh-CN', 'celsius') // "37.2 °C"

// 格式化血压
const bp = formatBloodPressure(120, 80, 'zh-CN') // "120/80 mmHg"
```

### 医疗表单验证

```typescript
import { useMedicalValidation } from '@crf/i18n-medical'

const { validateField, validateForm } = useMedicalValidation()

// 验证单个字段
const result = validateField(75.5, ['required', 'weight'])
// { valid: true }

// 验证整个表单
const formData = {
  weight: 70.5,
  height: 175,
  temperature: 37.2
}

const fieldRules = {
  weight: ['required', 'weight'],
  height: ['required', 'height'],
  temperature: ['required', 'temperature']
}

const validation = validateForm(formData, fieldRules)
// { valid: true, errors: {} }
```

## 支持的语言

- 🇨🇳 简体中文 (zh-CN)
- 🇹🇼 繁体中文 (zh-TW)
- 🇺🇸 英语(美国) (en-US)
- 🇬🇧 英语(英国) (en-GB)
- 🇯🇵 日语 (ja-JP)
- 🇰🇷 韩语 (ko-KR)
- 🇩🇪 德语 (de-DE)
- 🇫🇷 法语 (fr-FR)
- 🇪🇸 西班牙语 (es-ES)
- 🇧🇷 葡萄牙语(巴西) (pt-BR)
- 🇷🇺 俄语 (ru-RU)
- 🇸🇦 阿拉伯语 (ar-SA)

## 医疗术语分类

- **解剖学** (anatomy) - 人体结构术语
- **疾病** (disease) - 疾病名称
- **症状** (symptom) - 症状描述
- **药物** (medication) - 药物名称
- **医疗程序** (procedure) - 医疗操作
- **实验室检查** (laboratory) - 检验项目
- **影像学** (imaging) - 影像检查
- **生命体征** (vital-signs) - 生命体征
- **诊断** (diagnosis) - 诊断术语
- **治疗** (treatment) - 治疗方法
- **专科** (specialty) - 医学专科
- **设备** (equipment) - 医疗设备

## API 文档

### useMedicalI18n()

主要的医疗国际化 Composable。

```typescript
const {
  // 翻译函数
  mt,
  
  // 语言管理
  currentLocale,
  setLocale,
  availableLocales,
  
  // 术语功能
  getTermTranslation,
  searchTerms,
  
  // 格式化功能
  formatWeight,
  formatHeight,
  formatTemperature,
  
  // 验证功能
  validateField,
  validateForm,
  
  // 初始化
  init,
  cleanup
} = useMedicalI18n()
```

### 配置选项

```typescript
interface MedicalI18nConfig {
  defaultLocale: MedicalLocale
  fallbackLocale: MedicalLocale
  supportedLocales: MedicalLocale[]
  lazy: boolean
  enableCache: boolean
  cacheExpiry: number
  autoDetect: boolean
  termSources: {
    icd10?: string
    snomed?: string
    loinc?: string
    custom?: string[]
  }
}
```

## 开发

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建
pnpm build

# 测试
pnpm test

# 类型检查
pnpm type-check
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
