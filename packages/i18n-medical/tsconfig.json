{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@crf/shared-utils": ["../shared-utils/src"], "@crf/type-definitions": ["../type-definitions/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "references": [{"path": "../shared-utils"}, {"path": "../type-definitions"}]}