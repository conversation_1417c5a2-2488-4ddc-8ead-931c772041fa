/**
 * 简化的验证工具函数
 * 提供统一的验证逻辑，替代重复的验证实现
 */

// 临时定义类型，直到type-definitions构建问题解决
interface SimpleValidationRule {
  /** 是否必填 */
  required?: boolean
  /** 最小长度 */
  minLength?: number
  /** 最大长度 */
  maxLength?: number
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 正则表达式 */
  pattern?: RegExp
  /** 自定义验证函数 */
  validator?: (value: unknown) => string | null
  /** 错误消息 */
  message?: string
  /** 验证触发时机 */
  trigger?: 'blur' | 'change' | 'input' | 'submit'
}

interface SimpleValidationResult {
  /** 是否验证通过 */
  isValid: boolean
  /** 验证状态 */
  status: 'success' | 'warning' | 'error'
  /** 错误消息列表 */
  errors: string[]
  /** 警告消息列表 */
  warnings: string[]
  /** 主要错误消息 */
  message: string
}

/**
 * 验证单个字段
 */
export function validateField(
  value: unknown,
  rules: SimpleValidationRule | SimpleValidationRule[],
): SimpleValidationResult {
  const ruleArray = Array.isArray(rules) ? rules : [rules]
  const errors: string[] = []
  const warnings: string[] = []

  for (const rule of ruleArray) {
    // 必填验证
    if (rule.required) {
      if (
        value === null ||
        value === undefined ||
        value === '' ||
        (typeof value === 'string' && !value.trim())
      ) {
        errors.push(rule.message || '此字段为必填项')
        continue // 如果必填验证失败，跳过其他验证
      }
    }

    // 如果值为空且不是必填，跳过后续验证
    if (
      (value === null || value === undefined || value === '') &&
      !rule.required
    ) {
      continue
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        errors.push(rule.message || `最少需要输入${rule.minLength}个字符`)
      }
      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        errors.push(rule.message || `最多只能输入${rule.maxLength}个字符`)
      }
    }

    // 数值范围验证
    const numValue = Number(value)
    if (!isNaN(numValue)) {
      if (rule.min !== undefined && numValue < rule.min) {
        errors.push(rule.message || `值不能小于${rule.min}`)
      }
      if (rule.max !== undefined && numValue > rule.max) {
        errors.push(rule.message || `值不能大于${rule.max}`)
      }
    }

    // 正则表达式验证
    if (rule.pattern && typeof value === 'string') {
      const regex =
        typeof rule.pattern === 'string'
          ? new RegExp(rule.pattern)
          : rule.pattern
      if (!regex.test(value)) {
        errors.push(rule.message || '格式不正确')
      }
    }

    // 自定义验证器
    if (rule.validator) {
      const customError = rule.validator(value)
      if (customError) {
        errors.push(customError)
      }
    }
  }

  const isValid = errors.length === 0
  const status =
    errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'success'

  return {
    isValid,
    status,
    errors,
    warnings,
    message: errors[0] || warnings[0] || '',
  }
}

/**
 * 批量验证多个字段
 */
export function validateFields(
  fields: Array<{
    value: unknown
    rules: SimpleValidationRule | SimpleValidationRule[]
    name?: string
  }>,
): {
  isValid: boolean
  errors: Record<string, string[]>
  results: Record<string, SimpleValidationResult>
} {
  const errors: Record<string, string[]> = {}
  const results: Record<string, SimpleValidationResult> = {}
  let hasErrors = false

  fields.forEach((field, index) => {
    const fieldName = field.name || `field_${index}`
    const result = validateField(field.value, field.rules)

    results[fieldName] = result

    if (!result.isValid) {
      errors[fieldName] = result.errors
      hasErrors = true
    }
  })

  return {
    isValid: !hasErrors,
    errors,
    results,
  }
}

/**
 * 创建验证器函数
 */
export function createValidator(
  rules: SimpleValidationRule | SimpleValidationRule[],
) {
  return (value: unknown) => validateField(value, rules)
}

/**
 * 组合多个验证规则
 */
export function combineRules(
  ...rules: SimpleValidationRule[]
): SimpleValidationRule[] {
  return rules
}

/**
 * 条件验证规则
 */
export function conditionalRule(
  condition: (value: unknown) => boolean,
  rule: SimpleValidationRule,
): SimpleValidationRule {
  return {
    ...rule,
    validator: (value: unknown) => {
      if (!condition(value)) {
        return null // 条件不满足时跳过验证
      }
      return rule.validator ? rule.validator(value) : null
    },
  }
}

/**
 * 异步验证器包装器
 */
export function createAsyncValidator(
  asyncValidator: (value: unknown) => Promise<string | null>,
) {
  return {
    validator: (value: unknown) => {
      // 返回一个 Promise 标记，实际验证需要在组件中处理
      return `__ASYNC_VALIDATION__${JSON.stringify(value)}`
    },
    asyncValidator,
  }
}
