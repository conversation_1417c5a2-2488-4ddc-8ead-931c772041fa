// CRF Schema工具函数
import type { CRFFieldSchema, FieldConfig } from './types'

// Schema创建工具类
export class CRFSchemaUtils {
  /**
   * 创建基础字段Schema
   */
  static createField(
    type: CRFFieldSchema['type'],
    fieldConfig: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return {
      type,
      fieldConfig,
      ...options,
    }
  }

  /**
   * 创建文本字段
   */
  static createTextField(
    config: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField('string', config, options)
  }

  /**
   * 创建数字字段
   */
  static createNumberField(
    config: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField('number', config, options)
  }

  /**
   * 创建选择字段
   */
  static createSelectField(
    config: FieldConfig,
    enumValues: unknown[],
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField('string', config, {
      enum: enumValues,
      ...options,
    })
  }

  /**
   * 创建布尔字段
   */
  static createBooleanField(
    config: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField('boolean', config, options)
  }

  /**
   * 创建数组字段
   */
  static createArrayField(
    config: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField('array', config, options)
  }

  /**
   * 创建对象字段
   */
  static createObjectField(
    config: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField('object', config, options)
  }

  /**
   * 创建医疗编码字段（如ICD-10）
   */
  static createMedicalCodeField(
    config: FieldConfig,
    codeSystem:
      | 'icd-10'
      | 'snomed-ct'
      | 'patient-id'
      | 'medical-code' = 'icd-10',
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createTextField(config, {
      format: codeSystem,
      codingSystem: codeSystem.toUpperCase(),
      ...options,
    })
  }

  /**
   * 创建联系方式字段
   */
  static createContactField(
    config: FieldConfig,
    contactType: 'phone-cn' | 'email' | 'id-card-cn' = 'phone-cn',
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createTextField(config, {
      format: contactType,
      ...options,
    })
  }

  /**
   * 创建带范围限制的数字字段
   */
  static createRangeNumberField(
    config: FieldConfig,
    min?: number,
    max?: number,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createNumberField(config, {
      ...(min !== undefined && { minimum: min }),
      ...(max !== undefined && { maximum: max }),
      ...options,
    })
  }

  /**
   * 创建带长度限制的文本字段
   */
  static createLengthTextField(
    config: FieldConfig,
    minLength?: number,
    maxLength?: number,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createTextField(config, {
      ...(minLength !== undefined && { minLength }),
      ...(maxLength !== undefined && { maxLength }),
      ...options,
    })
  }

  /**
   * 创建必填字段
   */
  static createRequiredField(
    type: CRFFieldSchema['type'],
    config: FieldConfig,
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    return this.createField(type, config, {
      required: true,
      ...options,
    })
  }

  /**
   * 创建条件显示字段
   */
  static createConditionalField(
    type: CRFFieldSchema['type'],
    config: FieldConfig,
    dependentField: string,
    dependentValue: unknown,
    operation: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'notIn' = 'eq',
    options: Partial<CRFFieldSchema> = {},
  ): CRFFieldSchema {
    const fieldConfigWithCondition: FieldConfig = {
      ...config,
      showWhen: {
        field: dependentField,
        value: dependentValue,
        operation,
      },
    }

    return this.createField(type, fieldConfigWithCondition, options)
  }

  /**
   * 批量创建字段
   */
  static createFields(
    fieldsConfig: Array<{
      key: string
      type: CRFFieldSchema['type']
      config: FieldConfig
      options?: Partial<CRFFieldSchema>
    }>,
  ): Record<string, CRFFieldSchema> {
    const fields: Record<string, CRFFieldSchema> = {}

    fieldsConfig.forEach(({ key, type, config, options = {} }) => {
      fields[key] = this.createField(type, config, options)
    })

    return fields
  }

  /**
   * 合并字段配置
   */
  static mergeFieldConfigs(
    baseConfig: FieldConfig,
    overrideConfig: Partial<FieldConfig>,
  ): FieldConfig {
    return {
      ...baseConfig,
      ...overrideConfig,
      // 特殊处理选项数组
      options: overrideConfig.options || baseConfig.options,
      // 特殊处理条件显示
      showWhen:
        overrideConfig.showWhen !== undefined
          ? overrideConfig.showWhen
          : baseConfig.showWhen,
    } as FieldConfig
  }

  /**
   * 验证字段配置完整性
   */
  static validateFieldConfig(config: FieldConfig): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!config.code) {
      errors.push('缺少配置组件类型 (code)')
    }

    if (!config.label) {
      errors.push('缺少字段标签 (label)')
    }

    // 验证配置组件类型
    const validCodes = [
      'config-input',
      'config-textarea',
      'config-number',
      'config-select',
      'config-radio',
      'config-checkbox',
      'config-switch',
      'config-slider',
      'config-files',
    ]

    if (config.code && !validCodes.includes(config.code)) {
      errors.push(`无效的配置组件类型: ${config.code}`)
    }

    // 验证分组
    const validGroups = ['basic', 'validation', 'style', 'advanced']
    if (config.configGroup && !validGroups.includes(config.configGroup)) {
      errors.push(`无效的配置分组: ${config.configGroup}`)
    }

    // 验证选择类型的选项
    if (
      ['config-select', 'config-radio', 'config-checkbox'].includes(config.code)
    ) {
      if (!config.options || config.options.length === 0) {
        errors.push('选择类型字段必须提供选项 (options)')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 生成字段唯一ID
   */
  static generateFieldId(prefix = 'field'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 克隆字段Schema
   */
  static cloneFieldSchema(schema: CRFFieldSchema): CRFFieldSchema {
    return JSON.parse(JSON.stringify(schema))
  }

  /**
   * 检查字段是否为必填字段
   */
  static isRequiredField(schema: CRFFieldSchema): boolean {
    return schema.required === true
  }

  /**
   * 检查字段是否在配置中显示
   */
  static isConfigurableField(schema: CRFFieldSchema): boolean {
    return schema.fieldConfig.showInConfig !== false
  }

  /**
   * 获取字段的配置组件类型
   */
  static getFieldConfigType(schema: CRFFieldSchema): string {
    return schema.fieldConfig.code
  }

  /**
   * 获取字段的配置分组
   */
  static getFieldConfigGroup(schema: CRFFieldSchema): string {
    return schema.fieldConfig.configGroup || 'basic'
  }
}

// 导出便捷的全局函数
export const createTextField =
  CRFSchemaUtils.createTextField.bind(CRFSchemaUtils)
export const createNumberField =
  CRFSchemaUtils.createNumberField.bind(CRFSchemaUtils)
export const createSelectField =
  CRFSchemaUtils.createSelectField.bind(CRFSchemaUtils)
export const createBooleanField =
  CRFSchemaUtils.createBooleanField.bind(CRFSchemaUtils)
export const createMedicalCodeField =
  CRFSchemaUtils.createMedicalCodeField.bind(CRFSchemaUtils)
export const createContactField =
  CRFSchemaUtils.createContactField.bind(CRFSchemaUtils)
