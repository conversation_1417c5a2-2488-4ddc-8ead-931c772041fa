// CRF Schema管理器
import type {
  CRFFormSchema,
  FieldConfig,
  ConfigStructure,
  ValidationResult,
} from './types'
import { crfValidator } from './validator'

// Schema管理器类
export class CRFSchemaManager {
  private schemas: Map<string, CRFFormSchema> = new Map()

  /**
   * 注册Schema
   */
  registerSchema(schema: CRFFormSchema): boolean {
    try {
      // 验证Schema本身的格式
      const validationResult = crfValidator.validateSchemaStructure(schema)
      if (!validationResult.isValid) {
        console.error(
          'Schema结构验证失败:',
          schema.$id,
          validationResult.errors,
        )
        return false
      }

      // 编译AJV验证器
      const compileResult = crfValidator.compileSchema(schema)
      if (!compileResult) {
        return false
      }

      // 存储Schema
      this.schemas.set(schema.$id, schema)

      console.log(`Schema注册成功: ${schema.$id}`)
      return true
    } catch (error) {
      console.error(`Schema注册失败: ${schema.$id}`, error)
      return false
    }
  }

  /**
   * 获取Schema
   */
  getSchema(schemaId: string): CRFFormSchema | undefined {
    return this.schemas.get(schemaId)
  }

  /**
   * 移除Schema
   */
  removeSchema(schemaId: string): boolean {
    const removed = this.schemas.delete(schemaId)
    if (removed) {
      crfValidator.removeValidator(schemaId)
    }
    return removed
  }

  /**
   * 获取所有已注册的Schema ID
   */
  getRegisteredSchemas(): string[] {
    return Array.from(this.schemas.keys())
  }

  /**
   * 验证数据
   */
  async validateData(
    schemaId: string,
    data: Record<string, unknown>,
  ): Promise<ValidationResult> {
    return await crfValidator.validateData(schemaId, data)
  }

  /**
   * 生成配置区域结构
   */
  generateConfigStructure(schemaId: string): ConfigStructure {
    const schema = this.schemas.get(schemaId)
    if (!schema) {
      return { groups: {} }
    }

    const groups: ConfigStructure['groups'] = {}

    // 初始化分组
    const configGroups = schema.configGroups || this.getDefaultConfigGroups()
    Object.entries(configGroups).forEach(([key, group]) => {
      groups[key] = {
        label: group.label,
        order: group.order,
        icon: group.icon,
        fields: [],
      }
    })

    // 分配字段到分组
    Object.entries(schema.properties).forEach(([key, fieldSchema]) => {
      const config = fieldSchema.fieldConfig
      if (config.showInConfig !== false) {
        const groupKey = config.configGroup || 'basic'
        if (groups[groupKey]) {
          groups[groupKey].fields.push({
            key,
            config,
            schema: fieldSchema,
          })
        }
      }
    })

    // 排序字段
    Object.values(groups).forEach((group) => {
      group.fields.sort((a, b) => (a.config.order || 0) - (b.config.order || 0))
    })

    return { groups }
  }

  /**
   * 生成表单初始值
   */
  generateInitialValues(schemaId: string): Record<string, unknown> {
    const schema = this.schemas.get(schemaId)
    if (!schema) return {}

    const values: Record<string, unknown> = {}

    Object.entries(schema.properties).forEach(([key, fieldSchema]) => {
      values[key] =
        fieldSchema.default !== undefined
          ? fieldSchema.default
          : this.getDefaultValueByType(fieldSchema.type as string)
    })

    return values
  }

  /**
   * 检查字段是否应该显示
   */
  shouldShowField(
    fieldConfig: FieldConfig,
    formData: Record<string, unknown>,
  ): boolean {
    if (!fieldConfig.showWhen) return true

    const { field, value, operation = 'eq' } = fieldConfig.showWhen
    const fieldValue = formData[field] as unknown

    switch (operation) {
      case 'eq':
        return fieldValue === value
      case 'ne':
        return fieldValue !== value
      case 'gt':
        if (typeof fieldValue === 'number' && typeof value === 'number')
          return fieldValue > value
        return false
      case 'lt':
        if (typeof fieldValue === 'number' && typeof value === 'number')
          return fieldValue < value
        return false
      case 'in':
        return Array.isArray(value) && value.includes(fieldValue as never)
      case 'notIn':
        return Array.isArray(value) && !value.includes(fieldValue as never)
      default:
        return fieldValue === value
    }
  }

  /**
   * 获取字段的默认值
   */
  getFieldDefaultValue(schemaId: string, fieldKey: string): unknown {
    const schema = this.schemas.get(schemaId)
    if (!schema || !schema.properties[fieldKey]) {
      return null
    }

    const fieldSchema = schema.properties[fieldKey]
    return fieldSchema.default !== undefined
      ? fieldSchema.default
      : this.getDefaultValueByType(fieldSchema.type as string)
  }

  /**
   * 更新Schema
   */
  updateSchema(schemaId: string, updates: Partial<CRFFormSchema>): boolean {
    const schema = this.schemas.get(schemaId)
    if (!schema) {
      console.error(`Schema不存在: ${schemaId}`)
      return false
    }

    const updatedSchema = { ...schema, ...updates }

    // 更新时间戳
    if (updatedSchema.metadata) {
      updatedSchema.metadata.updatedAt = new Date().toISOString()
    }

    return this.registerSchema(updatedSchema)
  }

  /**
   * 获取Schema统计信息
   */
  getSchemaStats(schemaId: string): {
    fieldCount: number
    requiredFieldCount: number
    groupCount: number
    configurableFieldCount: number
  } | null {
    const schema = this.schemas.get(schemaId)
    if (!schema) return null

    const fieldCount = Object.keys(schema.properties).length
    const requiredFieldCount = schema.required?.length || 0
    const groupCount = Object.keys(
      schema.configGroups || this.getDefaultConfigGroups(),
    ).length
    const configurableFieldCount = Object.values(schema.properties).filter(
      (field) => field.fieldConfig.showInConfig !== false,
    ).length

    return {
      fieldCount,
      requiredFieldCount,
      groupCount,
      configurableFieldCount,
    }
  }

  /**
   * 获取默认配置分组
   */
  private getDefaultConfigGroups() {
    return {
      basic: { label: '基础配置', order: 1, icon: 'material-symbols:settings' },
      validation: {
        label: '验证配置',
        order: 2,
        icon: 'material-symbols:verified',
      },
      medical: {
        label: '医疗配置',
        order: 3,
        icon: 'material-symbols:medical-services',
      },
      advanced: { label: '高级配置', order: 4, icon: 'material-symbols:tune' },
    }
  }

  /**
   * 根据类型获取默认值
   */
  private getDefaultValueByType(type: string): unknown {
    switch (type) {
      case 'string':
        return ''
      case 'number':
        return 0
      case 'boolean':
        return false
      case 'array':
        return []
      case 'object':
        return {}
      default:
        return null
    }
  }
}

// 全局Schema管理器实例
export const crfSchemaManager = new CRFSchemaManager()
