// CRF Schema系统 - 主入口文件
// 统一导出所有模块，提供简洁的API接口

// === 类型定义 ===
export * from './types'

// === 验证器 ===
export * from './validator'

// === Schema管理器 ===
export * from './manager'

// === 工具函数 ===
export * from './utils'

// === 医疗预设配置 ===
export * from './medical-presets'

// === 主要API简化导出 ===
import { crfSchemaManager } from './manager'
import { crfValidator } from './validator'
import { CRFSchemaUtils } from './utils'
import { MedicalFieldConfigs } from './medical-presets'

// 主要API对象
export const CRFSchema = {
  // Schema管理
  manager: crfSchemaManager,

  // 数据验证
  validator: crfValidator,

  // 工具函数
  utils: CRFSchemaUtils,

  // 医疗预设
  medical: MedicalFieldConfigs,

  // 快捷方法
  register: crfSchemaManager.registerSchema.bind(crfSchemaManager),
  validate: crfSchemaManager.validateData.bind(crfSchemaManager),
  getConfig: crfSchemaManager.generateConfigStructure.bind(crfSchemaManager),
  getInitialValues:
    crfSchemaManager.generateInitialValues.bind(crfSchemaManager),
}

// 为了向后兼容，保留原有的导出名称
export { crfSchemaManager }
export const schemaParser = crfSchemaManager
export const SchemaUtils = {
  generateInitialValues:
    crfSchemaManager.generateInitialValues.bind(crfSchemaManager),
  generateValidationRules: (schemaId: string) => {
    // 从Schema生成验证规则的兼容实现
    const schema = crfSchemaManager.getSchema(schemaId)
    if (!schema) return {}

    const rules: Record<string, unknown[]> = {}
    Object.entries(schema.properties).forEach(([key, fieldSchema]) => {
      const fieldRules = []

      if (fieldSchema.required) {
        fieldRules.push({
          type: 'required',
          message: `${fieldSchema.title || key}为必填项`,
        })
      }

      if (fieldSchema.minLength) {
        fieldRules.push({ type: 'minLength', value: fieldSchema.minLength })
      }

      if (fieldSchema.maxLength) {
        fieldRules.push({ type: 'maxLength', value: fieldSchema.maxLength })
      }

      if (fieldSchema.minimum) {
        fieldRules.push({ type: 'minimum', value: fieldSchema.minimum })
      }

      if (fieldSchema.maximum) {
        fieldRules.push({ type: 'maximum', value: fieldSchema.maximum })
      }

      if (fieldSchema.pattern) {
        fieldRules.push({
          type: 'pattern',
          value: new RegExp(fieldSchema.pattern),
        })
      }

      rules[key] = fieldRules
    })

    return rules
  },
  validateSchema: crfValidator.validateSchemaStructure.bind(crfValidator),
  findField: (schemaId: string, path: string) => {
    const schema = crfSchemaManager.getSchema(schemaId)
    if (!schema) return null
    return schema.properties[path] || null
  },
}

// 默认导出主API对象
export default CRFSchema
