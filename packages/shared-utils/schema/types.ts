// CRF Schema类型定义
export interface FieldConfig {
  // 配置渲染相关
  code: string // 配置组件类型，如 config-input, config-select 等
  label: string // 字段标签
  description?: string // 字段描述
  placeholder?: string // 占位符
  helpIcon?: boolean // 是否显示帮助图标

  // 分组和显示控制
  showInConfig?: boolean // 是否在配置区显示
  configGroup?: 'basic' | 'validation' | 'style' | 'advanced' // 配置分组
  order?: number // 显示顺序

  // 条件显示
  showWhen?: {
    field: string // 依赖字段
    value: unknown // 当依赖字段为此值时显示
    operation?: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'notIn' // 比较操作
  }

  // 选择类型字段选项
  options?: Array<{
    label: string
    value: unknown
    disabled?: boolean
  }>

  // 数值类型限制
  min?: number
  max?: number
  step?: number

  // 字符串类型限制
  minLength?: number
  maxLength?: number
  pattern?: string

  // 文件类型限制
  accept?: string[]
  maxSize?: number
  multiple?: boolean
}

// CRF字段Schema定义
export interface CRFFieldSchema {
  // JSON Schema 标准字段
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  title?: string
  description?: string
  default?: unknown
  enum?: unknown[]
  format?: string
  minimum?: number
  maximum?: number
  minLength?: number
  maxLength?: number
  pattern?: string
  required?: boolean

  // CRF扩展配置
  fieldConfig: FieldConfig

  // 医疗CRF特有属性
  medicalType?:
    | 'patient_info'
    | 'diagnosis'
    | 'treatment'
    | 'lab_result'
    | 'medication'
    | 'adverse_event'
  dataSource?: string // 数据来源
  codingSystem?: string // 编码系统 (ICD-10, SNOMED CT等)
  unit?: string // 单位

  // 数据验证和质量控制
  qualityChecks?: {
    rangeCheck?: { min?: number; max?: number; message?: string }
    crossFieldCheck?: {
      field: string
      operation: string
      value: unknown
      message?: string
    }
    customValidation?: string // 自定义验证函数名
  }
}

// 表单Schema
export interface CRFFormSchema {
  $schema?: string
  $id: string
  title: string
  description?: string
  version: string
  type: 'object'
  properties: Record<string, CRFFieldSchema>
  required?: string[]

  // CRF特有元数据
  metadata: {
    studyId?: string
    protocol?: string
    createdBy: string
    createdAt: string
    updatedAt?: string
    status: 'draft' | 'active' | 'deprecated'
    tags?: string[]
  }

  // 配置分组信息
  configGroups?: Record<
    string,
    {
      label: string
      order: number
      icon?: string
      collapsible?: boolean
      collapsed?: boolean
    }
  >
}

// 验证结果接口
export interface ValidationResult {
  isValid: boolean
  errors: Array<{
    field: string
    message: string
    value?: unknown
  }>
}

// 配置结构接口
export interface ConfigStructure {
  groups: Record<
    string,
    {
      label: string
      order: number
      icon?: string
      fields: Array<{
        key: string
        config: FieldConfig
        schema: CRFFieldSchema
      }>
    }
  >
}
