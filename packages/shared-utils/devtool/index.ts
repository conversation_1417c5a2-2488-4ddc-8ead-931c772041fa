/**
 * 调试工具函数
 */

/**
 * 开发环境警告函数
 * @param scope 作用域/组件名
 * @param message 警告信息
 */
export const debugWarn = (scope: string, message: string): void => {
  if (process.env.NODE_ENV !== 'production') {
    console.warn(`[${scope}] ${message}`)
  }
}

/**
 * 开发环境错误函数
 * @param scope 作用域/组件名
 * @param message 错误信息
 */
export const debugError = (scope: string, message: string): void => {
  if (process.env.NODE_ENV !== 'production') {
    console.error(`[${scope}] ${message}`)
  }
}

/**
 * 开发环境日志函数
 * @param scope 作用域/组件名
 * @param message 日志信息
 * @param data 附加数据
 */
export const debugLog = (
  scope: string,
  message: string,
  data?: unknown,
): void => {
  if (process.env.NODE_ENV !== 'production') {
    if (data !== undefined) {
      console.log(`[${scope}] ${message}`, data)
    } else {
      console.log(`[${scope}] ${message}`)
    }
  }
}
