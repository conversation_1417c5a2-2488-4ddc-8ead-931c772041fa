/**
 * 数据转换工具函数 - 完全类型安全
 */

// 数据转换结果 - 完全类型安全
export interface TransformationResult<T> {
  readonly success: boolean
  readonly data?: T
  readonly error?: string
  readonly warnings?: readonly string[]
}

// 转换选项 - 完全类型安全
export interface TransformationOptions {
  readonly strict?: boolean
  readonly allowUnknown?: boolean
  readonly defaultValue?: unknown
  readonly validateAfterTransform?: boolean
}

// 基础数据转换
export function transformValue(
  value: unknown,
  targetType: 'string' | 'number' | 'boolean' | 'date',
  options: TransformationOptions = {},
): TransformationResult<unknown> {
  try {
    let result: unknown

    switch (targetType) {
      case 'string':
        result = transformToString(value)
        break
      case 'number':
        result = transformToNumber(value)
        break
      case 'boolean':
        result = transformToBoolean(value)
        break
      case 'date':
        result = transformToDate(value)
        break
      default:
        return {
          success: false,
          error: `Unsupported target type: ${targetType}`,
        }
    }

    if (result === null && options.defaultValue !== undefined) {
      result = options.defaultValue
    }

    return { success: true, data: result }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Transformation failed',
    }
  }
}

// 转换为字符串
function transformToString(value: unknown): string | null {
  if (value === null || value === undefined) {
    return null
  }

  if (typeof value === 'string') {
    return value
  }

  if (typeof value === 'number') {
    return String(value)
  }

  if (typeof value === 'boolean') {
    return String(value)
  }

  if (value instanceof Date) {
    return value.toISOString()
  }

  if (Array.isArray(value)) {
    return JSON.stringify(value)
  }

  if (typeof value === 'object') {
    return JSON.stringify(value)
  }

  return String(value)
}

// 转换为数字
function transformToNumber(value: unknown): number | null {
  if (value === null || value === undefined) {
    return null
  }

  if (typeof value === 'number') {
    return isNaN(value) ? null : value
  }

  if (typeof value === 'string') {
    const trimmed = value.trim()
    if (trimmed === '') {
      return null
    }
    const num = parseFloat(trimmed)
    return isNaN(num) ? null : num
  }

  if (typeof value === 'boolean') {
    return value ? 1 : 0
  }

  return null
}

// 转换为布尔值
function transformToBoolean(value: unknown): boolean | null {
  if (value === null || value === undefined) {
    return null
  }

  if (typeof value === 'boolean') {
    return value
  }

  if (typeof value === 'string') {
    const lower = value.toLowerCase().trim()
    if (
      lower === 'true' ||
      lower === '1' ||
      lower === 'yes' ||
      lower === 'on'
    ) {
      return true
    }
    if (
      lower === 'false' ||
      lower === '0' ||
      lower === 'no' ||
      lower === 'off'
    ) {
      return false
    }
    return null
  }

  if (typeof value === 'number') {
    return value !== 0
  }

  return null
}

// 转换为日期
function transformToDate(value: unknown): Date | null {
  if (value === null || value === undefined) {
    return null
  }

  if (value instanceof Date) {
    return isNaN(value.getTime()) ? null : value
  }

  if (typeof value === 'string') {
    const trimmed = value.trim()
    if (trimmed === '') {
      return null
    }
    const date = new Date(trimmed)
    return isNaN(date.getTime()) ? null : date
  }

  if (typeof value === 'number') {
    const date = new Date(value)
    return isNaN(date.getTime()) ? null : date
  }

  return null
}

// 深度转换对象
export function transformObject<T extends Record<string, unknown>>(
  data: unknown,
  schema: Record<string, 'string' | 'number' | 'boolean' | 'date'>,
  options: TransformationOptions = {},
): TransformationResult<T> {
  if (!data || typeof data !== 'object') {
    return { success: false, error: 'Input data must be an object' }
  }

  const result = {} as T
  const warnings: string[] = []

  for (const [key, targetType] of Object.entries(schema)) {
    const value = (data as Record<string, unknown>)[key]
    const transformResult = transformValue(value, targetType, options)

    if (transformResult.success) {
      result[key as keyof T] = transformResult.data as T[keyof T]
    } else {
      if (options.strict) {
        return {
          success: false,
          error: `Failed to transform field ${key}: ${transformResult.error}`,
        }
      } else {
        warnings.push(
          `Failed to transform field ${key}: ${transformResult.error}`,
        )
        if (options.defaultValue !== undefined) {
          result[key as keyof T] = options.defaultValue as T[keyof T]
        }
      }
    }
  }

  // 处理未知字段
  if (!options.allowUnknown) {
    for (const key in data) {
      if (!(key in schema)) {
        warnings.push(`Unknown field: ${key}`)
      }
    }
  }

  return {
    success: true,
    data: result,
    warnings: warnings.length > 0 ? warnings : undefined,
  }
}

// 数组转换
export function transformArray<T>(
  data: unknown,
  itemTransformer: (item: unknown) => TransformationResult<T>,
  options: TransformationOptions = {},
): TransformationResult<T[]> {
  if (!Array.isArray(data)) {
    return { success: false, error: 'Input data must be an array' }
  }

  const result: T[] = []
  const warnings: string[] = []

  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    const transformResult = itemTransformer(item)

    if (transformResult.success) {
      result.push(transformResult.data!)
    } else {
      if (options.strict) {
        return {
          success: false,
          error: `Failed to transform array item at index ${i}: ${transformResult.error}`,
        }
      } else {
        warnings.push(
          `Failed to transform array item at index ${i}: ${transformResult.error}`,
        )
        if (options.defaultValue !== undefined) {
          result.push(options.defaultValue as T)
        }
      }
    }
  }

  return {
    success: true,
    data: result,
    warnings: warnings.length > 0 ? warnings : undefined,
  }
}

// 数据清理和标准化
export function normalizeData(data: unknown): unknown {
  if (data === null || data === undefined) {
    return null
  }

  if (typeof data === 'string') {
    const trimmed = data.trim()
    return trimmed === '' ? null : trimmed
  }

  if (Array.isArray(data)) {
    return data.map(normalizeData).filter((item) => item !== null)
  }

  if (typeof data === 'object') {
    const result: Record<string, unknown> = {}
    for (const [key, value] of Object.entries(data)) {
      const normalized = normalizeData(value)
      if (normalized !== null) {
        result[key] = normalized
      }
    }
    return result
  }

  return data
}

// 数据验证和转换
export function validateAndTransform<T>(
  data: unknown,
  validator: (data: unknown) => boolean,
  transformer: (data: unknown) => TransformationResult<T>,
): TransformationResult<T> {
  if (!validator(data)) {
    return { success: false, error: 'Data validation failed' }
  }

  return transformer(data)
}
