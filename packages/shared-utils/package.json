{"name": "@crf/shared-utils", "version": "1.0.1", "description": "CRF 工具函数库", "private": true, "main": "index.ts", "module": "index.ts", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.ts", "require": "./index.ts"}, "./vue": {"types": "./vue/index.d.ts", "import": "./vue/index.ts", "require": "./vue/index.ts"}, "./core": {"types": "./core/index.d.ts", "import": "./core/index.ts", "require": "./core/index.ts"}, "./schema": {"types": "./schema/index.d.ts", "import": "./schema/index.ts", "require": "./schema/index.ts"}, "./string": {"types": "./string/index.d.ts", "import": "./string/index.ts", "require": "./string/index.ts"}, "./validation": {"types": "./validation/index.d.ts", "import": "./validation/index.ts", "require": "./validation/index.ts"}, "./events": {"types": "./events/index.d.ts", "import": "./events/index.ts", "require": "./events/index.ts"}, "./transformation": {"types": "./transformation/index.d.ts", "import": "./transformation/index.ts", "require": "./transformation/index.ts"}, "./devtool": {"types": "./devtool/index.d.ts", "import": "./devtool/index.ts", "require": "./devtool/index.ts"}, "./types": {"types": "./types/index.d.ts", "import": "./types/index.ts", "require": "./types/index.ts"}}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}, "files": ["dist"], "peerDependencies": {"vue": "^3.5.17"}, "dependencies": {"@vue/shared": "^3.5.13", "nanoid": "^5.1.5"}, "devDependencies": {"@crf/build-config": "workspace:*", "vue": "^3.5.17"}}