import type { App } from '@vue/runtime-core'

// 基础类型工具
export { isObject, isString, isArray } from '@vue/shared'
export { isClient } from '@vueuse/core'

export const isUndefined = (val: unknown): val is undefined => val === undefined
export const isBoolean = (val: unknown): val is boolean =>
  typeof val === 'boolean'
export const isNumber = (val: unknown): val is number => typeof val === 'number'

// 通用类型工具
export type Arrayable<T> = T | T[]

// Vue 相关类型
export type SFCWithInstall<T> = T & { install(app: App): void }

// Props 工具类型
export type Writable<T> = { -readonly [P in keyof T]: T[P] }
export type WritableArray<T> = T extends readonly unknown[] ? Writable<T> : T
export type IfNever<T, Y = true, N = false> = [T] extends [never] ? Y : N
export type IfUnknown<T, Y, N> = [unknown] extends [T] ? Y : N
export type UnknownToNever<T> = IfUnknown<T, never, T>
