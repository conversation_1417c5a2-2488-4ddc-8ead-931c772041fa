import { customAlphabet } from 'nanoid'

/**
 * 字符串工具函数
 */

/**
 * 创建随机ID生成器
 * @param length 长度，默认 8
 * @returns 随机字符串生成函数
 */
export const createNanoId = (length = 8) => {
  return customAlphabet('123456789abcdefghijklmnopqrstuvwxyz', length)
}

/**
 * 生成随机ID
 * @param length 长度，默认 8
 * @returns 随机字符串
 */
export const nanoid = (length = 8) => {
  return createNanoId(length)()
}

/**
 * 首字母大写
 * @param str 字符串
 * @returns 首字母大写的字符串
 */
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 驼峰转短横线
 * @param str 驼峰字符串
 * @returns 短横线字符串
 */
export const kebabCase = (str: string): string => {
  return str
    .replace(/([A-Z])/g, '-$1')
    .toLowerCase()
    .replace(/^-/, '')
}

/**
 * 短横线转驼峰
 * @param str 短横线字符串
 * @returns 驼峰字符串
 */
export const camelCase = (str: string): string => {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 对象工具函数
 */
export { hasOwn } from '@vue/shared'
