{"extends": "@crf/tsconfig/vite.json", "compilerOptions": {"baseUrl": ".", "paths": {"@crf/type-definitions": ["../type-definitions/index.ts"], "@crf/type-definitions/*": ["../type-definitions/*"], "@crf/shared-utils": ["../shared-utils/index.ts"], "@crf/shared-utils/*": ["../shared-utils/*"], "@crf/build-config": ["../build-config/index.ts"], "@crf/build-config/*": ["../build-config/*"], "@crf/ui-components": ["../ui-components/index.ts"], "@crf/ui-components/*": ["../ui-components/*"]}}, "include": ["src/**/*.ts", "src/**/*.vue", "../shared-utils/**/*.ts", "../type-definitions/**/*.ts", "../ui-components/**/*.ts", "../ui-components/**/*.vue"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}