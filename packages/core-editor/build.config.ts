import { defineBuildConfig } from 'unbuild'

export default defineBuildConfig({
  entries: [
    'src/index',
    'src/stores/index',
    'src/composables/index',
    'src/utils/index'
  ],
  declaration: true,
  clean: true,
  rollup: {
    emitCJS: true,
    inlineDependencies: true,
  },
  externals: [
    'vue',
    'pinia',
    'dayjs',
    '@crf/type-definitions',
    '@crf/shared-utils',
    '@crf/app-constants'
  ]
})
