/**
 * CRF 编辑器核心组件注册工具
 * 使用新的组件注册系统
 */

// 暂时注释掉ui-components导入，避免构建时处理Vue文件
// import {
//   componentRegistry,
//   ComponentCategory,
//   type ComponentMeta
// } from '@crf/ui-components'

// 本地类型定义，避免构建问题
export enum ComponentCategory {
  INPUT = 'input',
  DISPLAY = 'display',
  LAYOUT = 'layout',
  ADVANCED = 'advanced',
}

export interface ComponentMeta {
  code: string
  name: string
  label: string
  category: ComponentCategory
  icon?: string
  iconColor?: string
  description?: string
  visible?: boolean
  order?: number
}

// 模拟组件注册器
const mockComponentRegistry = {
  getComponent: (type: string) => null,
  getMeta: (type: string) => null,
  getAllMeta: () => [],
  getComponentsByCategory: (category: ComponentCategory) => [],
  search: ({ query }: { query: string }) => ({ components: [] }),
  isRegistered: (type: string) => false,
}

// 兼容旧版本的组件类型定义
export type ComponentType = string

// 兼容旧版本的组件注册信息
export interface ComponentRegistration {
  meta: ComponentMeta
  component: Record<string, unknown>
  configComponent?: Record<string, unknown>
  enabled: boolean
  order: number
}

/**
 * 获取组件
 */
export function getComponent(
  type: ComponentType,
): Record<string, unknown> | null {
  return mockComponentRegistry.getComponent(type)
}

/**
 * 获取组件名称（用于动态组件渲染）
 */
export function getComponentName(type: ComponentType): string {
  // 使用新的注册系统获取组件元数据
  const meta = mockComponentRegistry.getMeta(type)
  if (meta) {
    return meta.name
  }

  // 如果没有注册，使用传统的映射方式
  const componentNameMap: Record<string, string> = {
    // 标准格式
    text: 'CrfText',
    input: 'CrfInput',
    textarea: 'CrfTextarea',
    number: 'CrfNumber',
    select: 'CrfSelect',
    radio: 'CrfRadio',
    checkbox: 'CrfCheckbox',
    switch: 'CrfSwitch',
    date: 'CrfDate',
    datetime: 'CrfDatetime',
    time: 'CrfTime',
    slider: 'CrfSlider',
    rate: 'CrfRate',
    upload: 'CrfUpload',
    signature: 'CrfSignature',
    divider: 'CrfDivider',
    title: 'CrfTitle',
    description: 'CrfDescription',

    // 带前缀格式的兼容映射
    'crf-text': 'CrfText',
    'crf-input': 'CrfInput',
    'crf-textarea': 'CrfTextarea',
    'crf-number': 'CrfNumber',
    'crf-select': 'CrfSelect',
    'crf-radio': 'CrfRadio',
    'crf-checkbox': 'CrfCheckbox',
    'crf-switch': 'CrfSwitch',
    'crf-date': 'CrfDate',
    'crf-datetime': 'CrfDatetime',
    'crf-time': 'CrfTime',
    'crf-slider': 'CrfSlider',
    'crf-rate': 'CrfRate',
    'crf-upload': 'CrfUpload',
    'crf-signature': 'CrfSignature',
    'crf-divider': 'CrfDivider',
    'crf-title': 'CrfTitle',
    'crf-description': 'CrfDescription',
  }

  // 直接从映射表获取组件名称
  const componentName = componentNameMap[type]
  if (componentName) {
    return componentName
  }

  // 如果映射表中没有，尝试自动转换
  if (typeof type === 'string') {
    const pascalCase = type
      .split('-')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('')

    // 如果已经有 Crf 前缀，直接使用
    if (pascalCase.startsWith('Crf')) {
      return pascalCase
    }

    // 否则添加 Crf 前缀
    return `Crf${pascalCase}`
  }

  // 最后才回退到 div
  console.warn(`[ComponentRegistry] 组件未识别: ${type}，回退到 div`)
  return 'div'
}

/**
 * 获取组件元数据
 */
export function getComponentMeta(type: ComponentType): ComponentMeta | null {
  return componentRegistry.getMeta(type)
}

/**
 * 获取配置组件
 */
export function getConfigComponent(
  type: ComponentType,
): Record<string, unknown> | null {
  return componentRegistry.getConfigComponent(type)
}

/**
 * 获取所有已注册的组件
 */
export function getAllComponents(): ComponentMeta[] {
  return mockComponentRegistry
    .getAllMeta()
    .filter((meta) => meta.visible !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0))
}

/**
 * 按分类获取组件
 */
export function getComponentsByCategory(
  category: ComponentCategory,
): ComponentMeta[] {
  return mockComponentRegistry.getComponentsByCategory(category)
}

/**
 * 获取所有分类
 */
export function getCategories(): ComponentCategory[] {
  return Object.values(ComponentCategory)
}

/**
 * 搜索组件
 */
export function searchComponents(query: string): ComponentMeta[] {
  return mockComponentRegistry.search({ query }).components
}

/**
 * 检查组件是否已注册
 */
export function hasComponent(type: ComponentType): boolean {
  return mockComponentRegistry.isRegistered(type)
}

/**
 * 获取组件统计信息
 */
export function getComponentStats() {
  return { total: 0, byCategory: {} }
}
