{"name": "@crf/network-client", "version": "2.0.0", "description": "Modern network management and API client for CRF applications", "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.mts", "exports": {".": {"types": "./dist/index.d.mts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./client": {"types": "./dist/client/index.d.ts", "import": "./dist/client/index.js"}, "./api": {"types": "./dist/api/index.d.ts", "import": "./dist/api/index.js"}, "./composables": {"types": "./dist/composables/index.d.ts", "import": "./dist/composables/index.js"}, "./config": {"types": "./dist/config/index.d.ts", "import": "./dist/config/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js"}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --watch", "type-check": "vue-tsc --noEmit", "clean": "rm -rf dist"}, "peerDependencies": {"vue": "^3.3.0"}, "dependencies": {"@crf/type-definitions": "workspace:*", "@crf/shared-utils": "workspace:*"}, "devDependencies": {"@crf/build-config": "workspace:*", "@crf/tsconfig": "workspace:*", "typescript": "^5.2.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "keywords": ["network", "http", "api", "client", "vue", "composables", "monorepo"]}