// 核心HTTP客户端

import type {
  HttpConfig,
  RequestOptions,
  HttpResponse,
  HttpError,
  CacheEntry,
  NetworkInfo,
} from '../types/http'
import type { NetworkConfig } from '../config'
import { AuthManager } from './auth-manager'
import { RequestInterceptor, ResponseInterceptor } from './interceptors'

export class HttpClient {
  private config: NetworkConfig
  private authManager: AuthManager
  private requestInterceptor: RequestInterceptor
  private responseInterceptor: ResponseInterceptor
  private cache = new Map<string, CacheEntry>()
  private abortControllers = new Map<string, AbortController>()

  constructor(config: NetworkConfig) {
    this.config = config
    this.authManager = new AuthManager(config.auth)
    this.requestInterceptor = new RequestInterceptor(this.authManager, config)
    this.responseInterceptor = new ResponseInterceptor(this.authManager, config)
  }

  // 生成缓存键
  private getCacheKey(method: string, url: string, params?: any): string {
    const key = `${method}:${url}`
    if (params) {
      const paramStr = JSON.stringify(params)
      return `${key}:${btoa(paramStr).slice(0, 8)}`
    }
    return key
  }

  // 检查缓存
  private getFromCache<T>(cacheKey: string): HttpResponse<T> | null {
    if (!this.config.cache.enabled) return null

    const entry = this.cache.get(cacheKey)
    if (!entry || Date.now() > entry.expires) {
      this.cache.delete(cacheKey)
      return null
    }

    return {
      data: entry.data as T,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
      fromCache: true,
    }
  }

  // 设置缓存
  private setCache<T>(cacheKey: string, data: T, ttl?: number): void {
    if (!this.config.cache.enabled) return

    const expires = Date.now() + (ttl || this.config.cache.defaultTTL)
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      expires,
      etag: Date.now().toString(),
    })

    // 缓存大小控制
    this.cleanupCache()
  }

  // 清理缓存
  private cleanupCache(): void {
    const now = Date.now()

    // 删除过期项
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.cache.delete(key)
      }
    }

    // 如果缓存仍然过大，删除最旧的项
    const entries = Array.from(this.cache.entries())
    if (entries.length > 1000) {
      // 简单的大小控制
      entries
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, entries.length - 800)
        .forEach(([key]) => this.cache.delete(key))
    }
  }

  // 构建URL
  private buildURL(url: string, params?: Record<string, unknown>): string {
    const fullURL = url.startsWith('http')
      ? url
      : `${this.config.baseURL}${url}`

    if (!params || Object.keys(params).length === 0) {
      return fullURL
    }

    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })

    const separator = fullURL.includes('?') ? '&' : '?'
    return `${fullURL}${separator}${searchParams.toString()}`
  }

  // 执行请求
  private async executeRequest<T>(
    method: string,
    url: string,
    data?: unknown,
    options: RequestOptions = {},
  ): Promise<HttpResponse<T>> {
    // 拦截请求
    const requestOptions = await this.requestInterceptor.interceptRequest({
      ...options,
      timeout: options.timeout || this.config.timeout,
    })

    // 检查缓存
    if (method === 'GET' && (options.cache ?? true)) {
      const cacheKey = this.getCacheKey(method, url, options.params)
      const cached = this.getFromCache<T>(cacheKey)
      if (cached) return cached
    }

    // 构建请求
    const requestURL = this.buildURL(url, options.params)
    const abortController = new AbortController()
    const requestId = `${method}:${requestURL}`

    // 取消之前的相同请求
    const existingController = this.abortControllers.get(requestId)
    if (existingController) {
      existingController.abort()
    }
    this.abortControllers.set(requestId, abortController)

    try {
      // 执行请求
      const response = await this.performFetch<T>(
        method,
        requestURL,
        data,
        requestOptions,
        abortController.signal,
      )

      // 缓存响应
      if (method === 'GET' && (options.cache ?? true)) {
        const cacheKey = this.getCacheKey(method, url, options.params)
        this.setCache(cacheKey, response.data, options.cacheTime)
      }

      return this.responseInterceptor.interceptResponse(response)
    } catch (error) {
      return this.responseInterceptor.interceptResponseError(error as HttpError)
    } finally {
      this.abortControllers.delete(requestId)
    }
  }

  // 执行fetch请求
  private async performFetch<T>(
    method: string,
    url: string,
    data: unknown,
    options: RequestOptions,
    signal: AbortSignal,
  ): Promise<HttpResponse<T>> {
    const fetchOptions: RequestInit = {
      method,
      headers: options.headers,
      signal,
      credentials: this.config.withCredentials ? 'include' : 'same-origin',
    }

    // 处理请求体
    if (data !== undefined && method !== 'GET') {
      if (data instanceof FormData) {
        fetchOptions.body = data
        // 删除Content-Type让浏览器自动设置
        delete (fetchOptions.headers as any)['Content-Type']
      } else {
        fetchOptions.body = JSON.stringify(data)
      }
    }

    try {
      const response = await fetch(url, fetchOptions)

      if (!response.ok) {
        throw new HttpError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
        )
      }

      // 解析响应
      const contentType = response.headers.get('Content-Type') || ''
      let responseData: T

      if (contentType.includes('application/json')) {
        responseData = await response.json()
      } else if (contentType.includes('text/')) {
        responseData = (await response.text()) as unknown as T
      } else {
        responseData = (await response.blob()) as unknown as T
      }

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: this.headersToObject(response.headers),
        config: options,
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        throw new HttpError('请求被取消', 0)
      }
      if (error instanceof TypeError) {
        throw new HttpError('网络错误', 0)
      }
      throw error
    }
  }

  // Headers转换为对象
  private headersToObject(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {}
    headers.forEach((value, key) => {
      result[key] = value
    })
    return result
  }

  // GET请求
  async get<T>(
    url: string,
    options?: RequestOptions,
  ): Promise<HttpResponse<T>> {
    return this.executeRequest<T>('GET', url, undefined, options)
  }

  // POST请求
  async post<T>(
    url: string,
    data?: unknown,
    options?: RequestOptions,
  ): Promise<HttpResponse<T>> {
    return this.executeRequest<T>('POST', url, data, {
      ...options,
      cache: false,
    })
  }

  // PUT请求
  async put<T>(
    url: string,
    data?: unknown,
    options?: RequestOptions,
  ): Promise<HttpResponse<T>> {
    return this.executeRequest<T>('PUT', url, data, {
      ...options,
      cache: false,
    })
  }

  // DELETE请求
  async delete<T>(
    url: string,
    options?: RequestOptions,
  ): Promise<HttpResponse<T>> {
    return this.executeRequest<T>('DELETE', url, undefined, {
      ...options,
      cache: false,
    })
  }

  // 取消请求
  cancelRequest(method: string, url: string): void {
    const requestId = `${method}:${this.buildURL(url)}`
    const controller = this.abortControllers.get(requestId)
    if (controller) {
      controller.abort()
      this.abortControllers.delete(requestId)
    }
  }

  // 取消所有请求
  cancelAllRequests(): void {
    this.abortControllers.forEach((controller) => controller.abort())
    this.abortControllers.clear()
  }

  // 清除缓存
  clearCache(): void {
    this.cache.clear()
  }

  // 获取网络信息
  getNetworkInfo(): NetworkInfo {
    if (typeof window === 'undefined') {
      return { online: true }
    }

    const connection = (navigator as any).connection
    return {
      online: navigator.onLine,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt,
      saveData: connection?.saveData,
    }
  }

  // 销毁客户端
  destroy(): void {
    this.cancelAllRequests()
    this.clearCache()
  }
}

// 自定义HTTP错误类
class HttpError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any,
  ) {
    super(message)
    this.name = 'HttpError'
  }
}
