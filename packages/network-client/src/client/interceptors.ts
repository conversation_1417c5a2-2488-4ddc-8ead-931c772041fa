// 请求/响应拦截器

import type { RequestOptions, HttpResponse, HttpError } from '../types/http'
import type { AuthManager } from './auth-manager'
import type { NetworkConfig } from '../config'

export class RequestInterceptor {
  constructor(
    private authManager: AuthManager,
    private config: NetworkConfig,
  ) {}

  // 请求拦截器
  async interceptRequest(options: RequestOptions): Promise<RequestOptions> {
    // 添加认证头
    const authHeaders = this.authManager.getAuthHeader()

    // 检查是否需要刷新令牌
    const token = this.authManager.getAccessToken()
    if (token && this.authManager.shouldRefreshToken(token)) {
      try {
        const newToken = await this.authManager.refreshToken(
          this.config.baseURL + this.config.endpoints.refresh,
        )
        // 使用新令牌更新认证头
        authHeaders.Authorization = `${this.config.auth.tokenPrefix} ${newToken}`
      } catch (error) {
        console.warn('令牌刷新失败:', error)
      }
    }

    return {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders,
        ...options.headers,
      },
    }
  }

  // 请求错误拦截器
  interceptRequestError(error: HttpError): Promise<never> {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
}

export class ResponseInterceptor {
  constructor(
    private authManager: AuthManager,
    private config: NetworkConfig,
  ) {}

  // 响应拦截器
  interceptResponse<T>(response: HttpResponse<T>): HttpResponse<T> {
    return response
  }

  // 响应错误拦截器
  async interceptResponseError(error: HttpError): Promise<never> {
    const { status } = error

    // 处理认证错误
    if (status === 401) {
      this.authManager.handleAuthError()
      return Promise.reject(new Error('认证失败，请重新登录'))
    }

    // 处理权限错误
    if (status === 403) {
      return Promise.reject(new Error('权限不足，无法执行此操作'))
    }

    // 处理服务器错误
    if (status && status >= 500) {
      return Promise.reject(new Error('服务器内部错误，请稍后重试'))
    }

    // 处理网络错误
    if (!status) {
      return Promise.reject(new Error('网络连接失败，请检查网络设置'))
    }

    return Promise.reject(error)
  }
}
