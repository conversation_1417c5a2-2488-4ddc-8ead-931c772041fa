// 认证管理器

import type { NetworkConfig } from '../config'

export class AuthManager {
  private config: NetworkConfig['auth']
  private refreshPromise: Promise<string> | null = null

  constructor(config: NetworkConfig['auth']) {
    this.config = config
  }

  // 获取访问令牌
  getAccessToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.config.tokenKey)
  }

  // 获取刷新令牌
  getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.config.refreshTokenKey)
  }

  // 设置令牌
  setTokens(accessToken: string, refreshToken?: string): void {
    if (typeof window === 'undefined') return

    localStorage.setItem(this.config.tokenKey, accessToken)
    if (refreshToken) {
      localStorage.setItem(this.config.refreshTokenKey, refreshToken)
    }
  }

  // 清除令牌
  clearTokens(): void {
    if (typeof window === 'undefined') return

    localStorage.removeItem(this.config.tokenKey)
    localStorage.removeItem(this.config.refreshTokenKey)
    localStorage.removeItem(this.config.userInfoKey)
  }

  // 检查令牌是否需要刷新
  shouldRefreshToken(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const exp = payload.exp * 1000
      const now = Date.now()
      return exp - now < this.config.refreshThreshold
    } catch {
      return false
    }
  }

  // 获取认证头
  getAuthHeader(): Record<string, string> {
    const token = this.getAccessToken()
    if (!token) return {}

    return {
      Authorization: `${this.config.tokenPrefix} ${token}`,
    }
  }

  // 刷新令牌
  async refreshToken(refreshEndpoint: string): Promise<string> {
    // 避免重复刷新
    if (this.refreshPromise) {
      return this.refreshPromise
    }

    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      throw new Error('无可用的刷新令牌')
    }

    this.refreshPromise = this.performRefresh(refreshEndpoint, refreshToken)

    try {
      const newToken = await this.refreshPromise
      return newToken
    } finally {
      this.refreshPromise = null
    }
  }

  private async performRefresh(
    endpoint: string,
    refreshToken: string,
  ): Promise<string> {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `${this.config.tokenPrefix} ${refreshToken}`,
        },
      })

      if (!response.ok) {
        throw new Error(`刷新令牌失败: ${response.status}`)
      }

      const data = await response.json()
      const { access_token, refresh_token } = data.data || data

      if (!access_token) {
        throw new Error('刷新响应中缺少访问令牌')
      }

      this.setTokens(access_token, refresh_token)
      return access_token
    } catch (error) {
      this.clearTokens()
      // 跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      throw error
    }
  }

  // 处理认证错误
  handleAuthError(): void {
    this.clearTokens()
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
  }
}
