// API相关类型定义

export interface APIResponse<T = unknown> {
  success: boolean
  message: string
  data?: T
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
}

export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
}

export interface User {
  id: string
  username: string
  email?: string
  phone?: string
  roles: string[]
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface CreateUserRequest {
  username: string
  password: string
  email?: string
  phone?: string
  roles?: string[]
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  phone?: string
}

export interface UserRolesRequest {
  roles: string[]
}

export interface BatchUpdateUsersRequest {
  user_ids: string[]
  updates: Partial<UpdateUserRequest>
}

export interface UserStatsResponse {
  total_users: number
  active_users: number
  new_users: number
}

export interface UserPermissionsResponse {
  permissions: string[]
}

export interface Role {
  id: string
  name: string
  description?: string
  permissions: string[]
  type?: string
}

export interface CreateRoleRequest {
  name: string
  description?: string
  permissions?: string[]
}

export interface UpdateRoleRequest {
  name?: string
  description?: string
}

export interface RolePermissionsRequest {
  permissions: string[]
}

export interface Permission {
  id: string
  name: string
  description?: string
}

export interface CheckPermissionRequest {
  permission: string
  resource_id?: string
}

export interface CheckPermissionResponse {
  allowed: boolean
}

export interface LoginRequest {
  username?: string
  password?: string
  phone?: string
  phone_code?: string
  sms_code?: string
  wechat_code?: string
  login_type?: 'password' | 'sms' | 'wechat'
  remember_me?: boolean
}

export interface LoginResponse {
  token: string
  refresh_token: string
  user: User
}

export interface RegisterRequest {
  username: string
  password: string
  email?: string
  phone?: string
}

export interface SmsCodeRequest {
  phone: string
}

export interface RefreshTokenResponse {
  token: string
  refresh_token: string
}

export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

export interface Project {
  id: string
  name: string
}

export interface CreateProjectRequest {
  name: string
}

export interface ProjectsResponse {
  projects: Project[]
}

export interface Template {
  id: string
  name: string
}

export interface CreateTemplateRequest {
  name: string
}

export interface UpdateTemplateRequest {
  name?: string
}

export interface TemplatesResponse {
  templates: Template[]
}

export interface TemplateVersion {
  id: string
  version: number
}

export interface CreateVersionRequest {
  version: number
}

export interface TemplateStatsResponse {
  total: number
}

export interface AccessLinkResponse {
  link: string
}

export interface AutoSaveConfigResponse {
  config: unknown
}

export interface FormInstance {
  id: string
}

export interface CreateInstanceRequest {
  data: unknown
}

export interface UpdateInstanceRequest {
  data: unknown
}

export interface InstancesResponse {
  instances: FormInstance[]
}

export interface ExportInstanceRequest {
  format: string
}

export interface HistoryEntry {
  id: string
}

export interface CreateHistoryRequest {
  entry: string
}

export interface HistoryResponse {
  history: HistoryEntry[]
}

export interface ProfileUpdateRequest {
  email?: string
  phone?: string
}

export interface ResetPasswordRequest {
  new_password: string
}

export interface HealthCheckResponse {
  status: string
}
