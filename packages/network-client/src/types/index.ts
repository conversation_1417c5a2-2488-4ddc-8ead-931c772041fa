// 导出网络相关类型
export type {
  RequestOptions as RequestConfig,
  HttpError as ErrorResponse,
} from './http'

// 导出API相关类型
export type {
  APIResponse,
  PaginationParams,
  PaginationResponse,
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserRolesRequest,
  BatchUpdateUsersRequest,
  UserStatsResponse,
  UserPermissionsResponse,
  Role,
  CreateRoleRequest,
  UpdateRoleRequest,
  RolePermissionsRequest,
  Permission,
  CheckPermissionRequest,
  CheckPermissionResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  SmsCodeRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  Project,
  CreateProjectRequest,
  ProjectsResponse,
  Template,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  TemplatesResponse,
  TemplateVersion,
  CreateVersionRequest,
  TemplateStatsResponse,
  AccessLinkResponse,
  AutoSaveConfigResponse,
  FormInstance,
  CreateInstanceRequest,
  UpdateInstanceRequest,
  InstancesResponse,
  ExportInstanceRequest,
  HistoryEntry,
  CreateHistoryRequest,
  HistoryResponse,
  ProfileUpdateRequest,
  ResetPasswordRequest,
  HealthCheckResponse,
} from './api'

// 保持向后兼容
export type { AxiosRequestConfig } from 'axios'
