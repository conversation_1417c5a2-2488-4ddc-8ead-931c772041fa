// HTTP相关类型定义

export interface HttpConfig {
  baseURL: string
  timeout: number
  withCredentials: boolean
  retries: number
  retryDelay: number
}

export interface RequestOptions {
  timeout?: number
  headers?: Record<string, string>
  params?: Record<string, unknown>
  cache?: boolean
  cacheTime?: number
  retry?: boolean
  retries?: number
  retryDelay?: number
  onProgress?: (progress: number) => void
  signal?: AbortSignal
}

export interface HttpResponse<T = unknown> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
  config: RequestOptions
  fromCache?: boolean
}

export interface HttpError {
  message: string
  status?: number
  code?: string
  response?: {
    data: unknown
    status: number
    headers: Record<string, string>
  }
  request?: unknown
}

export interface CacheEntry<T = unknown> {
  data: T
  timestamp: number
  expires: number
  etag?: string
}

export interface RequestInterceptor {
  onRequest?: (
    config: RequestOptions,
  ) => RequestOptions | Promise<RequestOptions>
  onRequestError?: (error: HttpError) => HttpError | Promise<HttpError>
}

export interface ResponseInterceptor {
  onResponse?: <T>(
    response: HttpResponse<T>,
  ) => HttpResponse<T> | Promise<HttpResponse<T>>
  onResponseError?: (error: HttpError) => HttpError | Promise<HttpError>
}

export interface NetworkInfo {
  online: boolean
  effectiveType?: string
  downlink?: number
  rtt?: number
  saveData?: boolean
}
