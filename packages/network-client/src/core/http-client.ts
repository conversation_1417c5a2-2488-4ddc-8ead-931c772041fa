// 统一 HTTP 客户端

import { TokenManager } from './token-manager'
import {
  API_CONFIG,
  HTTP_STATUS,
  ERROR_MESSAGES,
  getApiUrl,
  buildUrlWithParams,
} from '../config'
import type { APIResponse, RequestConfig, ErrorResponse } from '../types'

// 简单的HTTP客户端实现
const httpClient = {
  async get(url: string, config?: RequestConfig) {
    const response = await fetch(url, {
      method: 'GET',
      headers: config?.headers,
      signal: config?.signal,
    })
    return response
  },

  async post(url: string, data?: unknown, config?: RequestConfig) {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...config?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      signal: config?.signal,
    })
    return response
  },

  async put(url: string, data?: unknown, config?: RequestConfig) {
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...config?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      signal: config?.signal,
    })
    return response
  },

  async delete(url: string, config?: RequestConfig) {
    const response = await fetch(url, {
      method: 'DELETE',
      headers: config?.headers,
      signal: config?.signal,
    })
    return response
  },
}

// 获取HTTP客户端实例
const getHttpClient = () => httpClient

// 响应拦截器 - 统一处理后端API响应格式
const handleResponse = async <T>(response: {
  data: unknown
  fromCache?: boolean
}): Promise<APIResponse<T>> => {
  try {
    // 如果是从缓存返回的
    if (response.fromCache) {
      return response.data as APIResponse<T>
    }

    const data = response.data

    // 后端标准响应格式: { code: number, message: string, data?: {} }
    if (data && typeof data === 'object') {
      const responseData = data as Record<string, unknown>

      // 检查是否是标准的后端响应格式
      if (typeof responseData.code === 'number' && responseData.message) {
        const isSuccess = responseData.code >= 200 && responseData.code < 300
        return {
          success: isSuccess,
          message: responseData.message as string,
          data: responseData.data as T,
        }
      }

      // 兼容旧格式: { message: string, data: {} }
      if (responseData.message && responseData.data !== undefined) {
        return {
          success: true,
          message: responseData.message as string,
          data: responseData.data as T,
        }
      }

      // 直接返回数据的情况（如健康检查等）
      if (
        responseData.status ||
        responseData.user ||
        responseData.templates ||
        responseData.users ||
        responseData.token
      ) {
        return {
          success: true,
          message: 'success',
          data: responseData as T,
        }
      }

      // 其他对象响应直接包装
      return {
        success: true,
        message: 'success',
        data: responseData as T,
      }
    }

    // 非对象响应直接包装
    return {
      success: true,
      message: 'success',
      data: data as T,
    }
  } catch (error) {
    console.error('响应处理失败:', error)
    throw new Error(ERROR_MESSAGES.PARSE_ERROR)
  }
}

// 错误处理器 - 适配后端错误响应格式
const handleError = (error: ErrorResponse): never => {
  console.error('API请求失败:', error)

  let message = '请求失败'

  // 处理后端返回的错误格式
  if (error.response && error.response.data) {
    const errorData = error.response.data

    // 后端标准错误格式: { code: number, message: string }
    if (typeof errorData.code === 'number' && errorData.message) {
      message = errorData.message
    }
    // 兼容格式: { error: "错误描述" }
    else if (errorData.error) {
      message = errorData.error
    }
    // 兼容格式: { message: "错误描述" }
    else if (errorData.message) {
      message = errorData.message
    }
  } else if (error.message) {
    if (error.message.includes('Failed to fetch')) {
      message = ERROR_MESSAGES.NETWORK_ERROR
    } else if (error.message.includes('Network Error')) {
      message = '网络错误，请检查后端服务是否启动'
    } else if (error.message.includes('timeout')) {
      message = ERROR_MESSAGES.TIMEOUT_ERROR
    } else {
      message = error.message
    }
  }

  // 根据HTTP状态码处理不同错误
  if (error.response) {
    const status = error.response.status
    switch (status) {
      case HTTP_STATUS.BAD_REQUEST:
        message = `${ERROR_MESSAGES.BAD_REQUEST}: ${message}`
        break
      case HTTP_STATUS.UNAUTHORIZED:
        message = ERROR_MESSAGES.UNAUTHORIZED
        // 清除本地token和用户信息
        TokenManager.clearTokens()
        // 跳转到登录页
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login'
        }
        break
      case HTTP_STATUS.FORBIDDEN:
        message = ERROR_MESSAGES.FORBIDDEN
        break
      case HTTP_STATUS.NOT_FOUND:
        message = ERROR_MESSAGES.NOT_FOUND
        break
      case HTTP_STATUS.CONFLICT:
        message = `${ERROR_MESSAGES.CONFLICT}: ${message}`
        break
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        message = ERROR_MESSAGES.SERVER_ERROR
        break
      default:
        message = `HTTP ${status}: ${message}`
    }
  }

  throw new Error(message)
}

// 基础HTTP方法
export class HttpClient {
  // GET请求
  static async get<T>(
    url: string,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    try {
      // 处理查询参数
      const fullUrl = config?.params
        ? buildUrlWithParams(url, config.params)
        : url

      // 处理token刷新
      const requestConfig = await TokenManager.handleTokenRefresh({
        timeout: API_CONFIG.TIMEOUT,
        headers: TokenManager.addAuthHeaders(config?.headers),
        ...config,
      })

      const response = await getHttpClient().get(
        getApiUrl(fullUrl),
        requestConfig,
      )
      return handleResponse<T>(response)
    } catch (error) {
      return handleError(error as ErrorResponse)
    }
  }

  // POST请求
  static async post<T>(
    url: string,
    data?: unknown,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    try {
      // 处理token刷新
      const requestConfig = await TokenManager.handleTokenRefresh({
        timeout: API_CONFIG.TIMEOUT,
        headers: TokenManager.addAuthHeaders(config?.headers),
        ...config,
      })

      const response = await getHttpClient().post(
        getApiUrl(url),
        data,
        requestConfig,
      )
      return handleResponse<T>(response)
    } catch (error) {
      return handleError(error as ErrorResponse)
    }
  }

  // PUT请求
  static async put<T>(
    url: string,
    data?: unknown,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    try {
      // 处理token刷新
      const requestConfig = await TokenManager.handleTokenRefresh({
        timeout: API_CONFIG.TIMEOUT,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          ...TokenManager.addAuthHeaders(config?.headers),
        },
        ...config,
      })

      const response = await getHttpClient().put(
        getApiUrl(url),
        data,
        requestConfig,
      )
      return handleResponse<T>(response)
    } catch (error) {
      return handleError(error as ErrorResponse)
    }
  }

  // DELETE请求
  static async delete<T>(
    url: string,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    try {
      // 处理token刷新
      const requestConfig = await TokenManager.handleTokenRefresh({
        timeout: API_CONFIG.TIMEOUT,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          ...TokenManager.addAuthHeaders(config?.headers),
        },
        ...config,
      })

      const response = await getHttpClient().delete(
        getApiUrl(url),
        requestConfig,
      )
      return handleResponse<T>(response)
    } catch (error) {
      return handleError(error as ErrorResponse)
    }
  }
}

// 导出HTTP客户端和Token管理器
export { httpClient, TokenManager }
