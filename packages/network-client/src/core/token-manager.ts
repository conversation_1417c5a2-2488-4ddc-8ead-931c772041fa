// Token 管理器

import { API_CONFIG, ERROR_MESSAGES, STORAGE_KEYS, getApiUrl } from '../config'
import type { RefreshTokenResponse } from '../types'

export class TokenManager {
  private static isRefreshing = false
  private static refreshSubscribers: Array<(token: string) => void> = []

  // 添加刷新订阅者
  private static addRefreshSubscriber(callback: (token: string) => void) {
    this.refreshSubscribers.push(callback)
  }

  // 通知所有订阅者
  private static notifyRefreshSubscribers(token: string) {
    this.refreshSubscribers.forEach((callback) => callback(token))
    this.refreshSubscribers = []
  }

  // 获取token
  static getToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)
  }

  // 获取刷新token
  static getRefreshToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  }

  // 设置token
  static setTokens(accessToken: string, refreshToken?: string) {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, accessToken)
    if (refreshToken) {
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken)
    }
  }

  // 清除token
  static clearTokens() {
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN)
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
    localStorage.removeItem(STORAGE_KEYS.USER_INFO)
  }

  // 解析JWT token
  private static parseToken(token: string): Record<string, unknown> | null {
    try {
      return JSON.parse(atob(token.split('.')[1]))
    } catch (error) {
      console.error('Failed to parse token:', error)
      return null
    }
  }

  // 检查token是否需要刷新
  static shouldRefreshToken(token: string): boolean {
    if (!token) return false

    const payload = this.parseToken(token)
    if (!payload || typeof payload.exp !== 'number') return false

    const exp = payload.exp * 1000 // 转换为毫秒
    const now = Date.now()
    const timeUntilExpiry = exp - now

    // 根据是否为记住我token选择不同的刷新阈值
    const refreshThreshold = payload.remember_me
      ? API_CONFIG.TOKEN_REFRESH.REMEMBER_ME_THRESHOLD
      : API_CONFIG.TOKEN_REFRESH.DEFAULT_THRESHOLD

    return timeUntilExpiry < refreshThreshold
  }

  // 获取token过期时间
  static getTokenExpiryTime(token: string): number | null {
    const payload = this.parseToken(token)
    if (!payload || typeof payload.exp !== 'number') return null
    return payload.exp * 1000 // 转换为毫秒
  }

  // 检查token是否为记住我token
  static isRememberMeToken(token: string): boolean {
    const payload = this.parseToken(token)
    return payload?.remember_me === true
  }

  // 刷新token
  static async refreshToken(): Promise<string | null> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      throw new Error(ERROR_MESSAGES.REFRESH_TOKEN_MISSING)
    }

    try {
      const response = await fetch(getApiUrl('/auth/refresh'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${refreshToken}`,
        },
      })

      if (!response.ok) {
        throw new Error(ERROR_MESSAGES.REFRESH_TOKEN_FAILED)
      }

      const data = await response.json()

      if (data.data && data.data.access_token) {
        this.setTokens(data.data.access_token, data.data.refresh_token)
        return data.data.access_token
      }

      throw new Error('Invalid refresh response')
    } catch (error) {
      console.error('Token refresh failed:', error)
      this.clearTokens()
      // 跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      return null
    }
  }

  // 自动刷新token的请求拦截器
  static async handleTokenRefresh<T extends Record<string, unknown>>(
    config: T,
  ): Promise<T> {
    const token = this.getToken()

    if (token && this.shouldRefreshToken(token)) {
      if (this.isRefreshing) {
        // 如果正在刷新，等待刷新完成
        return new Promise((resolve) => {
          this.addRefreshSubscriber((newToken: string) => {
            const headers = (config.headers as Record<string, string>) || {}
            headers['Authorization'] = `Bearer ${newToken}`
            resolve({ ...config, headers })
          })
        })
      }

      this.isRefreshing = true

      try {
        const newToken = await this.refreshToken()
        if (newToken) {
          const headers = (config.headers as Record<string, string>) || {}
          headers['Authorization'] = `Bearer ${newToken}`
          config = { ...config, headers }
          this.notifyRefreshSubscribers(newToken)
        }
      } catch (error) {
        console.error('Auto token refresh failed:', error)
      } finally {
        this.isRefreshing = false
      }
    }

    return config
  }

  // 添加认证头部
  static addAuthHeaders(
    headers: Record<string, string> = {},
  ): Record<string, string> {
    const token = this.getToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    return headers
  }
}
