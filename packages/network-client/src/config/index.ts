// 网络配置管理

// 后端API基础配置
export const API_CONFIG = {
  // API路径前缀
  API_PREFIX: '/api',

  // 请求超时时间（毫秒）
  TIMEOUT: 30000,

  // 是否携带认证凭据
  WITH_CREDENTIALS: true,

  // 基础URL配置
  BASE_URL: import.meta.env.DEV
    ? ''
    : import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',

  // 刷新token相关配置
  TOKEN_REFRESH: {
    // 默认刷新阈值（5分钟）
    DEFAULT_THRESHOLD: 5 * 60 * 1000,

    // 记住我token刷新阈值（24小时）
    REMEMBER_ME_THRESHOLD: 24 * 60 * 60 * 1000,
  },

  // 缓存配置
  CACHE: {
    // 是否启用缓存
    ENABLED: true,

    // 默认缓存时间（毫秒）
    DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  },

  // 分页配置
  PAGINATION: {
    // 默认每页大小
    DEFAULT_PAGE_SIZE: 20,

    // 最大每页大小
    MAX_PAGE_SIZE: 100,
  },
} as const

// HTTP 状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
} as const

// 错误消息配置
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器内部错误，请联系管理员',
  UNAUTHORIZED: '认证失败，请重新登录',
  FORBIDDEN: '权限不足，无法执行此操作',
  NOT_FOUND: '请求的资源不存在',
  CONFLICT: '资源冲突',
  BAD_REQUEST: '请求参数错误',
  PARSE_ERROR: '响应数据格式错误',
  REFRESH_TOKEN_MISSING: 'No refresh token available',
  REFRESH_TOKEN_FAILED: 'Token refresh failed',
  NETWORK_MANAGER_NOT_INITIALIZED:
    '网络管理器未初始化。请在 Vue 组件中调用 initNetworkManager() 进行初始化。',
} as const

// API 端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    ME: '/auth/me',
    SMS_CODE: '/auth/sms-code',
  },

  // 用户管理
  USERS: {
    BASE: '/users',
    DETAIL: '/users/:id',
    PROFILE: '/users/:id/profile',
    AVATAR: '/users/:id/avatar',
    PASSWORD: '/users/:id/password',
    ROLES: '/users/:id/roles',
    PERMISSIONS: '/users/:id/permissions',
    ACTIVATE: '/users/:id/activate',
    DEACTIVATE: '/users/:id/deactivate',
    RESET_PASSWORD: '/users/:id/reset-password',
    BATCH: '/users/batch',
    STATS: '/users/stats',
    EXPORT: '/users/export',
  },

  // 角色管理
  ROLES: {
    BASE: '/roles',
    DETAIL: '/roles/:id',
    PERMISSIONS: '/roles/:id/permissions',
  },

  // 权限管理
  PERMISSIONS: {
    BASE: '/permissions',
    CHECK: '/check-permission',
  },

  // 项目管理
  PROJECTS: {
    BASE: '/projects',
    DETAIL: '/projects/:id',
  },

  // 模板管理
  TEMPLATES: {
    BASE: '/templates',
    DETAIL: '/templates/:id',
    DELETED: '/templates/deleted',
    VERSIONS: '/templates/:id/versions',
    PUBLISH: '/templates/:id/publish',
    SAVE_DRAFT: '/templates/:id/save-draft',
    ACCESS_LINK: '/templates/:id/access-link',
    STATS: '/templates/:id/stats',
    RESTORE: '/templates/:id/restore',
    ROLLBACK: '/templates/:id/rollback/:versionId',
    AUTO_SAVE_CONFIG: '/templates/auto-save-config',
  },

  // 版本管理
  VERSIONS: {
    PUBLISH: '/versions/:id/publish',
    DELETE: '/versions/:id',
  },

  // 表单实例
  INSTANCES: {
    BASE: '/instances',
    DETAIL: '/instances/:id',
    SUBMIT: '/instances/:id/submit',
    LOCK: '/instances/:id/lock',
    UNLOCK: '/instances/:id/unlock',
    COPY: '/instances/:id/copy',
    EXPORT: '/instances/:id/export',
  },

  // 历史记录
  HISTORY: {
    BASE: '/history',
    BY_RESOURCE: '/history/:type/:id',
  },

  // 系统相关
  SYSTEM: {
    HEALTH: '/health',
  },
} as const

// Token 存储键名
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
} as const

// 请求头配置
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
} as const

// 网络配置接口
export interface NetworkConfig {
  baseURL: string
  timeout: number
  withCredentials: boolean
  auth: {
    tokenRefreshThreshold: number
    rememberMeThreshold: number
  }
  cache: {
    enabled: boolean
    defaultTTL: number
  }
  retry: {
    maxRetries: number
    retryDelay: number
  }
  pagination: {
    defaultPageSize: number
    maxPageSize: number
  }
}

// 默认配置
export const defaultConfig: NetworkConfig = {
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  withCredentials: API_CONFIG.WITH_CREDENTIALS,
  auth: {
    tokenRefreshThreshold: API_CONFIG.TOKEN_REFRESH.DEFAULT_THRESHOLD,
    rememberMeThreshold: API_CONFIG.TOKEN_REFRESH.REMEMBER_ME_THRESHOLD,
  },
  cache: {
    enabled: API_CONFIG.CACHE.ENABLED,
    defaultTTL: API_CONFIG.CACHE.DEFAULT_TTL,
  },
  retry: {
    maxRetries: 3,
    retryDelay: 1000,
  },
  pagination: {
    defaultPageSize: API_CONFIG.PAGINATION.DEFAULT_PAGE_SIZE,
    maxPageSize: API_CONFIG.PAGINATION.MAX_PAGE_SIZE,
  },
}

// 创建配置
export function createConfig(
  userConfig?: Partial<NetworkConfig>,
): NetworkConfig {
  return {
    ...defaultConfig,
    ...userConfig,
    auth: {
      ...defaultConfig.auth,
      ...userConfig?.auth,
    },
    cache: {
      ...defaultConfig.cache,
      ...userConfig?.cache,
    },
    retry: {
      ...defaultConfig.retry,
      ...userConfig?.retry,
    },
    pagination: {
      ...defaultConfig.pagination,
      ...userConfig?.pagination,
    },
  }
}

// 环境配置
export function getEnvironmentConfig(): Partial<NetworkConfig> {
  if (import.meta.env.DEV) {
    return {
      baseURL: '',
      timeout: 60000, // 开发环境增加超时时间
    }
  }

  return {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
    timeout: 30000,
  }
}

// 获取完整的API URL
export const getApiUrl = (endpoint: string): string => {
  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/')
    ? endpoint
    : `/${endpoint}`

  // 开发环境下使用Vite代理，直接返回相对路径
  if (import.meta.env.DEV) {
    // 如果endpoint已经包含/api前缀，直接返回
    if (normalizedEndpoint.startsWith('/api/')) {
      return normalizedEndpoint
    }
    return `${API_CONFIG.API_PREFIX}${normalizedEndpoint}`
  }

  // 生产环境
  if (normalizedEndpoint.startsWith('/api/')) {
    return normalizedEndpoint
  }
  return `${API_CONFIG.API_PREFIX}${normalizedEndpoint}`
}

// 构建带参数的URL
export const buildUrlWithParams = (
  url: string,
  params?: Record<string, unknown>,
): string => {
  if (!params || Object.keys(params).length === 0) {
    return url
  }

  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value))
    }
  })

  const queryString = searchParams.toString()
  return queryString ? `${url}?${queryString}` : url
}

// 替换URL中的路径参数
export const replaceUrlParams = (
  url: string,
  params: Record<string, string>,
): string => {
  let result = url
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`:${key}`, value)
  })
  return result
}
