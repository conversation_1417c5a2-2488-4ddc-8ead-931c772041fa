// Vue HTTP组合式函数

import { ref, computed, onUnmounted, type Ref } from 'vue'
import type { HttpResponse, RequestOptions } from '../types/http'
import { useAPI } from '../api'

export interface UseHttpState<T> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<Error | null>
  isSuccess: Ref<boolean>
  isError: Ref<boolean>
}

export interface UseHttpReturn<T> extends UseHttpState<T> {
  execute: (...args: any[]) => Promise<T>
  reset: () => void
  cancel: () => void
}

// HTTP请求组合式函数
export function useHttp<T = unknown>(
  requestFn: (...args: any[]) => Promise<HttpResponse<T>>,
  options: {
    immediate?: boolean
    initialData?: T
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
  } = {},
): UseHttpReturn<T> {
  const { immediate = false, initialData = null, onSuccess, onError } = options

  // 状态
  const data = ref<T | null>(initialData) as Ref<T | null>
  const loading = ref(false)
  const error = ref<Error | null>(null)

  // 计算属性
  const isSuccess = computed(
    () => !loading.value && !error.value && data.value !== null,
  )
  const isError = computed(() => !loading.value && error.value !== null)

  let abortController: AbortController | null = null

  // 执行请求
  const execute = async (...args: any[]): Promise<T> => {
    // 重置状态
    loading.value = true
    error.value = null

    // 创建新的取消控制器
    abortController = new AbortController()

    try {
      const response = await requestFn(...args)
      data.value = response.data

      if (onSuccess) {
        onSuccess(response.data)
      }

      return response.data
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj

      if (onError) {
        onError(errorObj)
      }

      throw errorObj
    } finally {
      loading.value = false
      abortController = null
    }
  }

  // 重置状态
  const reset = () => {
    data.value = initialData
    loading.value = false
    error.value = null
  }

  // 取消请求
  const cancel = () => {
    if (abortController) {
      abortController.abort()
      loading.value = false
    }
  }

  // 立即执行
  if (immediate) {
    execute()
  }

  // 组件卸载时取消请求
  onUnmounted(() => {
    cancel()
  })

  return {
    data,
    loading,
    error,
    isSuccess,
    isError,
    execute,
    reset,
    cancel,
  }
}

// 异步数据组合式函数
export function useAsyncData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    immediate?: boolean
    cache?: boolean
    cacheTime?: number
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
  } = {},
): UseHttpReturn<T> {
  const api = useAPI()
  const client = api.getHttpClient()

  const wrappedFetcher = async () => {
    return client.get<T>(`/_async/${key}`, {
      cache: options.cache,
      cacheTime: options.cacheTime,
    })
  }

  return useHttp(wrappedFetcher, options)
}
