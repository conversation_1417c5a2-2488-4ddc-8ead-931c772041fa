// Vue API组合式函数

import { ref, computed, type Ref } from 'vue'
import { useAPI } from '../api'
import type {
  User,
  LoginRequest,
  RegisterRequest,
  Template,
  CreateTemplateRequest,
  PaginationParams,
} from '../types'

// 认证组合式函数
export function useAuth() {
  const api = useAPI()

  const user = ref<User | null>(null)
  const loading = ref(false)
  const isAuthenticated = computed(() => user.value !== null)

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await api.auth.login(credentials)
      user.value = response.user
      return response
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    loading.value = true
    try {
      const response = await api.auth.register(userData)
      user.value = response.user
      return response
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    loading.value = true
    try {
      await api.auth.logout()
      user.value = null
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户
  const getCurrentUser = async () => {
    loading.value = true
    try {
      const response = await api.auth.getCurrentUser()
      user.value = response.user
      return response.user
    } finally {
      loading.value = false
    }
  }

  return {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    getCurrentUser,
  }
}

// 用户管理组合式函数
export function useUsers() {
  const api = useAPI()

  const users = ref<User[]>([])
  const total = ref(0)
  const loading = ref(false)

  // 获取用户列表
  const getUsers = async (params?: PaginationParams & { role?: string }) => {
    loading.value = true
    try {
      const response = await api.users.getUsers(params)
      users.value = response.users
      total.value = response.total
      return response
    } finally {
      loading.value = false
    }
  }

  // 创建用户
  const createUser = async (userData: any) => {
    loading.value = true
    try {
      const response = await api.users.createUser(userData)
      // 重新获取用户列表
      await getUsers()
      return response
    } finally {
      loading.value = false
    }
  }

  // 更新用户
  const updateUser = async (userId: string, userData: any) => {
    loading.value = true
    try {
      const response = await api.users.updateUser(userId, userData)
      // 更新本地用户列表
      const index = users.value.findIndex((u) => u.id === userId)
      if (index !== -1) {
        users.value[index] = response.user
      }
      return response
    } finally {
      loading.value = false
    }
  }

  // 删除用户
  const deleteUser = async (userId: string) => {
    loading.value = true
    try {
      const response = await api.users.remove(userId)
      // 从本地列表中移除
      users.value = users.value.filter((u) => u.id !== userId)
      total.value -= 1
      return response
    } finally {
      loading.value = false
    }
  }

  return {
    users,
    total,
    loading,
    getUsers,
    createUser,
    updateUser,
    deleteUser,
  }
}

// 模板管理组合式函数
export function useTemplates() {
  const api = useAPI()

  const templates = ref<Template[]>([])
  const total = ref(0)
  const loading = ref(false)

  // 获取模板列表
  const getTemplates = async (
    params?: PaginationParams & {
      project_id?: string
      status?: string
      template_type?: string
    },
  ) => {
    loading.value = true
    try {
      const response = await api.templates.getTemplates(params)
      templates.value = response.templates
      // 假设响应包含分页信息
      total.value = response.pagination?.total || response.templates.length
      return response
    } finally {
      loading.value = false
    }
  }

  // 创建模板
  const createTemplate = async (templateData: CreateTemplateRequest) => {
    loading.value = true
    try {
      const response = await api.templates.createTemplate(templateData)
      // 重新获取模板列表
      await getTemplates()
      return response
    } finally {
      loading.value = false
    }
  }

  // 更新模板
  const updateTemplate = async (templateId: string, templateData: any) => {
    loading.value = true
    try {
      const response = await api.templates.updateTemplate(
        templateId,
        templateData,
      )
      // 更新本地模板列表
      const index = templates.value.findIndex((t) => t.id === templateId)
      if (index !== -1) {
        templates.value[index] = response.template
      }
      return response
    } finally {
      loading.value = false
    }
  }

  // 删除模板
  const deleteTemplate = async (templateId: string) => {
    loading.value = true
    try {
      const response = await api.templates.deleteTemplate(templateId)
      // 从本地列表中移除
      templates.value = templates.value.filter((t) => t.id !== templateId)
      total.value -= 1
      return response
    } finally {
      loading.value = false
    }
  }

  // 发布模板
  const publishTemplate = async (templateId: string) => {
    loading.value = true
    try {
      const response = await api.templates.publishTemplate(templateId)
      // 更新本地模板状态
      const index = templates.value.findIndex((t) => t.id === templateId)
      if (index !== -1) {
        templates.value[index].status = 'published'
      }
      return response
    } finally {
      loading.value = false
    }
  }

  return {
    templates,
    total,
    loading,
    getTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    publishTemplate,
  }
}
