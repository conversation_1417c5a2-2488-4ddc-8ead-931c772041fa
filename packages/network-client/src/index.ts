// @crf/network-client 主入口文件

// 导出核心HTTP客户端
export { HttpClient } from './client/http-client'
export { AuthManager } from './client/auth-manager'

// 导出API管理器
export { APIManager, createAPI, useAPI, configureAPI } from './api'

// 导出服务类
export {
  AuthService,
  UserService,
  ProfileService,
  TemplateService,
  UnifiedBaseAPI,
  BaseAPI,
  InstanceBaseAPI,
  RestAPI,
} from './api/services'

// 导出配置
export {
  createConfig,
  defaultConfig,
  getEnvironmentConfig,
  type NetworkConfig,
} from './config'

// 导出Vue组合式函数
export {
  useHttp,
  useAsyncData,
  type UseHttpState,
  type UseHttpReturn,
} from './composables/use-http'

export { useAuth, useUsers, useTemplates } from './composables/use-api'

// 导出所有类型
export type * from './types'

// 便捷的初始化函数
export function initNetwork(config?: Partial<NetworkConfig>) {
  configureAPI(config)
  return useAPI()
}
