// REST API基类

import { InstanceBaseAPI } from '../unified-base-api'
import type { PaginationParams, PaginationResponse } from '../../types/api'

export abstract class RestAPI<
  T,
  CreateRequest,
  UpdateRequest,
> extends InstanceBaseAPI {
  protected abstract basePath: string

  // 获取列表
  async findAll(params?: PaginationParams): Promise<{
    data: T[]
    pagination: PaginationResponse<any>
  }> {
    const query = params
      ? this.buildPagination(params.page, params.limit, params.search)
      : {}
    return this.get(this.basePath, { params: query })
  }

  // 根据ID获取单个资源
  async findById(id: string): Promise<T> {
    const url = this.replaceUrlParams(`${this.basePath}/:id`, { id })
    return this.get(url)
  }

  // 创建资源
  async create(data: CreateRequest): Promise<T> {
    return this.post(this.basePath, data)
  }

  // 更新资源
  async update(id: string, data: UpdateRequest): Promise<T> {
    const url = this.replaceUrlParams(`${this.basePath}/:id`, { id })
    return this.put(url, data)
  }

  // 删除资源
  async remove(id: string): Promise<{ success: boolean }> {
    const url = this.replaceUrlParams(`${this.basePath}/:id`, { id })
    return this.delete(url)
  }

  // 批量操作
  async batchOperation(
    operation: string,
    ids: string[],
    data?: unknown,
  ): Promise<{ success: boolean }> {
    return this.post(`${this.basePath}/batch`, {
      operation,
      ids,
      data,
    })
  }
}
