// 用户管理相关 API

import { BaseAPI } from './unified-base-api'
import { API_ENDPOINTS } from '../config'
import type {
  APIResponse,
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserRolesRequest,
  BatchUpdateUsersRequest,
  UserStatsResponse,
  UserPermissionsResponse,
  PaginationParams,
  ProfileUpdateRequest,
  ResetPasswordRequest,
} from '../types'

export class UserAPI extends BaseAPI {
  // 获取用户列表
  static async getUsers(
    params?: PaginationParams & { role?: string },
  ): Promise<APIResponse<{ users: User[]; total: number }>> {
    return this.get<{ users: User[]; total: number }>(
      API_ENDPOINTS.USERS.BASE,
      { params },
    )
  }

  // 获取用户详情
  static async getUserDetail(
    userId: string,
  ): Promise<APIResponse<{ user: User }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.DETAIL, {
      id: userId,
    })
    return this.get<{ user: User }>(url)
  }

  // 创建用户
  static async createUser(
    userData: CreateUserRequest,
  ): Promise<APIResponse<{ user: User }>> {
    return this.post<{ user: User }>(API_ENDPOINTS.USERS.BASE, userData)
  }

  // 更新用户信息
  static async updateUser(
    userId: string,
    userData: UpdateUserRequest,
  ): Promise<APIResponse<{ user: User }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.DETAIL, {
      id: userId,
    })
    return this.put<{ user: User }>(url, userData)
  }

  // 更新用户角色
  static async updateUserRoles(
    userId: string,
    roleData: UserRolesRequest,
  ): Promise<APIResponse<{ message: string }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.ROLES, { id: userId })
    return this.put<{ message: string }>(url, roleData)
  }

  // 激活用户
  static async activateUser(
    userId: string,
  ): Promise<APIResponse<{ message: string }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.ACTIVATE, {
      id: userId,
    })
    return this.post<{ message: string }>(url)
  }

  // 停用用户
  static async deactivateUser(
    userId: string,
  ): Promise<APIResponse<{ message: string }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.DEACTIVATE, {
      id: userId,
    })
    return this.post<{ message: string }>(url)
  }

  // 重置密码
  static async resetPassword(
    userId: string,
    passwordData: ResetPasswordRequest,
  ): Promise<APIResponse<{ message: string }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.RESET_PASSWORD, {
      id: userId,
    })
    return this.post<{ message: string }>(url, passwordData)
  }

  // 批量更新用户
  static async batchUpdateUsers(
    requestData: BatchUpdateUsersRequest,
  ): Promise<APIResponse<{ message: string }>> {
    return this.post<{ message: string }>(
      API_ENDPOINTS.USERS.BATCH,
      requestData,
    )
  }

  // 获取用户统计
  static async getUserStats(): Promise<APIResponse<UserStatsResponse>> {
    return this.get<UserStatsResponse>(API_ENDPOINTS.USERS.STATS)
  }

  // 获取用户权限
  static async getUserPermissions(
    userId: string,
    projectId?: string,
  ): Promise<APIResponse<UserPermissionsResponse>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.PERMISSIONS, {
      id: userId,
    })
    const params = projectId ? { project_id: projectId } : undefined
    return this.get<UserPermissionsResponse>(url, { params })
  }

  // 导出用户
  static async exportUsers(
    format: 'csv' | 'xlsx' = 'csv',
    includeRoles: boolean = true,
  ): Promise<APIResponse<unknown>> {
    const params = { format, include_roles: includeRoles }
    return this.get<unknown>(API_ENDPOINTS.USERS.EXPORT, { params })
  }
}

export class ProfileAPI extends BaseAPI {
  // 获取用户资料
  static async getProfile(
    userId: string,
  ): Promise<APIResponse<{ user: User }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.PROFILE, {
      id: userId,
    })
    return this.get<{ user: User }>(url)
  }

  // 更新用户资料
  static async updateProfile(
    userId: string,
    profileData: ProfileUpdateRequest,
  ): Promise<APIResponse<{ user: User }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.PROFILE, {
      id: userId,
    })
    return this.put<{ user: User }>(url, profileData)
  }

  // 修改密码
  static async changePassword(
    userId: string,
    passwordData: ResetPasswordRequest,
  ): Promise<APIResponse<{ message: string }>> {
    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.PASSWORD, {
      id: userId,
    })
    return this.put<{ message: string }>(url, passwordData)
  }

  // 上传头像
  static async uploadAvatar(
    userId: string,
    file: File,
  ): Promise<APIResponse<{ avatar_url: string }>> {
    const formData = new FormData()
    formData.append('avatar', file)

    const url = this.replaceUrlParams(API_ENDPOINTS.USERS.AVATAR, {
      id: userId,
    })
    return this.post<{ avatar_url: string }>(url, formData, {
      headers: {
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
    })
  }

  // 获取用户统计信息
  static async getUserStats(userId: string): Promise<APIResponse<unknown>> {
    const url =
      this.replaceUrlParams(API_ENDPOINTS.USERS.DETAIL, { id: userId }) +
      '/stats'
    return this.get<unknown>(url)
  }
}
