// 认证相关 API

import { BaseAPI } from './unified-base-api'
import { API_ENDPOINTS } from '../config'
import type {
  APIResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  SmsCodeRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  User,
} from '../types'

export class AuthAPI extends BaseAPI {
  // 用户登录 - 支持多种登录方式
  static async login(
    credentials: LoginRequest,
  ): Promise<APIResponse<LoginResponse>> {
    // 如果已经指定了login_type，直接使用；否则根据凭证类型推断
    let login_type: 'password' | 'sms' | 'wechat'

    if (credentials.login_type) {
      login_type = credentials.login_type
    } else if (credentials.password && credentials.username) {
      login_type = 'password'
    } else if (
      credentials.phone &&
      (credentials.phone_code || credentials.sms_code)
    ) {
      login_type = 'sms'
    } else if (credentials.wechat_code) {
      login_type = 'wechat'
    } else {
      throw new Error('无效的登录凭证')
    }

    // 构建请求数据，确保字段名与后端期望一致
    const requestData: Record<string, unknown> = {
      login_type,
    }

    if (login_type === 'password') {
      requestData.username = credentials.username
      requestData.password = credentials.password
    } else if (login_type === 'sms') {
      requestData.phone = credentials.phone
      // 统一使用phone_code字段名，兼容前端传入的sms_code
      requestData.phone_code = credentials.phone_code || credentials.sms_code
    } else if (login_type === 'wechat') {
      requestData.wechat_code = credentials.wechat_code
    }

    // 添加记住我选项
    if (credentials.remember_me) {
      requestData.remember_me = credentials.remember_me
    }

    return this.post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, requestData)
  }

  // 发送短信验证码
  static async sendSmsCode(
    phoneData: SmsCodeRequest,
  ): Promise<APIResponse<{ message: string }>> {
    return this.post<{ message: string }>(
      API_ENDPOINTS.AUTH.SMS_CODE,
      phoneData,
    )
  }

  // 用户注册
  static async register(
    userData: RegisterRequest,
  ): Promise<APIResponse<{ user: User }>> {
    return this.post<{ user: User }>(API_ENDPOINTS.AUTH.REGISTER, userData)
  }

  // 用户登出
  static async logout(): Promise<APIResponse<{ message: string }>> {
    return this.post<{ message: string }>(API_ENDPOINTS.AUTH.LOGOUT)
  }

  // 获取当前用户信息
  static async getCurrentUser(): Promise<APIResponse<{ user: User }>> {
    return this.get<{ user: User }>(API_ENDPOINTS.AUTH.ME)
  }

  // 刷新Token
  static async refreshToken(): Promise<APIResponse<RefreshTokenResponse>> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    // 直接调用fetch避免循环刷新
    const response = await fetch(API_ENDPOINTS.AUTH.REFRESH, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${refreshToken}`,
      },
    })

    if (!response.ok) {
      throw new Error('Token refresh failed')
    }

    const data = await response.json()
    return {
      success: true,
      message: 'Token refreshed successfully',
      data: data.data,
    }
  }

  // 修改密码
  static async changePassword(
    passwordData: ChangePasswordRequest,
  ): Promise<APIResponse<{ message: string }>> {
    return this.put<{ message: string }>('/user/password', passwordData)
  }
}
