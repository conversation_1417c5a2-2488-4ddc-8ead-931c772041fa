// API模块主入口

import { HttpClient } from '../client/http-client'
import { createConfig } from '../config'
import type { NetworkConfig } from '../config'

// 导入所有服务
import {
  AuthService,
  UserService,
  ProfileService,
  TemplateService,
  RoleService,
} from './services'

// API管理器类
export class APIManager {
  private httpClient: HttpClient

  // 服务实例
  public readonly auth: AuthService
  public readonly users: UserService
  public readonly profile: ProfileService
  public readonly templates: TemplateService
  public readonly roles: RoleService

  constructor(config?: Partial<NetworkConfig>) {
    const finalConfig = createConfig(config)
    this.httpClient = new HttpClient(finalConfig)

    // 初始化服务
    this.auth = new AuthService(this.httpClient)
    this.users = new UserService(this.httpClient)
    this.profile = new ProfileService(this.httpClient)
    this.templates = new TemplateService(this.httpClient)
    this.roles = new RoleService(this.httpClient)
  }

  // 获取HTTP客户端（用于高级操作）
  getHttpClient(): HttpClient {
    return this.httpClient
  }

  // 销毁API管理器
  destroy(): void {
    this.httpClient.destroy()
  }
}

// 创建默认API实例
let defaultAPI: APIManager | null = null

// 创建API实例
export function createAPI(config?: Partial<NetworkConfig>): APIManager {
  return new APIManager(config)
}

// 获取默认API实例
export function useAPI(): APIManager {
  if (!defaultAPI) {
    defaultAPI = new APIManager()
  }
  return defaultAPI
}

// 配置默认API实例
export function configureAPI(config: Partial<NetworkConfig>): void {
  defaultAPI = new APIManager(config)
}

// 导出服务类型
export type {
  AuthService,
  UserService,
  ProfileService,
  TemplateService,
  RoleService,
}
