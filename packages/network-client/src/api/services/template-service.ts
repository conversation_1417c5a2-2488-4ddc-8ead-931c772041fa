// 模板服务

import { RestAPI } from '../base/rest-api'
import type {
  Template,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  TemplatesResponse,
  TemplateVersion,
  CreateVersionRequest,
  TemplateStatsResponse,
  AccessLinkResponse,
  AutoSaveConfigResponse,
  PaginationParams,
} from '../../types/api'

export class TemplateService extends RestAPI<
  Template,
  CreateTemplateRequest,
  UpdateTemplateRequest
> {
  protected basePath = '/templates'

  // 获取模板列表
  async getTemplates(
    params?: PaginationParams & {
      project_id?: string
      status?: string
      template_type?: string
    },
  ): Promise<TemplatesResponse> {
    const query = this.buildQuery({
      ...this.buildPagination(params?.page, params?.limit, params?.search),
      project_id: params?.project_id,
      status: params?.status,
      template_type: params?.template_type,
    })

    return this.get(this.basePath, { params: query })
  }

  // 获取已删除的模板列表
  async getDeletedTemplates(
    params?: PaginationParams & {
      project_id?: string
      template_type?: string
    },
  ): Promise<TemplatesResponse> {
    const query = this.buildQuery({
      ...this.buildPagination(params?.page, params?.limit, params?.search),
      project_id: params?.project_id,
      template_type: params?.template_type,
    })

    return this.get('/templates/deleted', { params: query })
  }

  // 获取单个模板
  async getTemplate(id: string): Promise<{ template: Template }> {
    return this.get(this.replaceUrlParams('/templates/:id', { id }))
  }

  // 创建模板
  async createTemplate(
    templateData: CreateTemplateRequest,
  ): Promise<{ template: Template }> {
    return this.post('/templates', templateData)
  }

  // 更新模板
  async updateTemplate(
    id: string,
    templateData: UpdateTemplateRequest,
  ): Promise<{ template: Template }> {
    return this.put(
      this.replaceUrlParams('/templates/:id', { id }),
      templateData,
    )
  }

  // 删除模板
  async deleteTemplate(id: string): Promise<{ success: boolean }> {
    return this.delete(this.replaceUrlParams('/templates/:id', { id }))
  }

  // 恢复已删除的模板
  async restoreTemplate(id: string): Promise<{ message: string }> {
    return this.post(this.replaceUrlParams('/templates/:id/restore', { id }))
  }

  // 发布模板
  async publishTemplate(id: string): Promise<{ version: TemplateVersion }> {
    return this.post(this.replaceUrlParams('/templates/:id/publish', { id }))
  }

  // 获取模板版本列表
  async getTemplateVersions(
    id: string,
  ): Promise<{ versions: TemplateVersion[] }> {
    return this.get(this.replaceUrlParams('/templates/:id/versions', { id }))
  }

  // 创建版本
  async createVersion(
    versionData: CreateVersionRequest,
  ): Promise<{ version: TemplateVersion }> {
    const url = this.replaceUrlParams('/templates/:id/versions', {
      id: versionData.template_id,
    })
    return this.post(url, versionData)
  }

  // 发布版本
  async publishVersion(
    versionId: string,
  ): Promise<{ version: TemplateVersion }> {
    return this.post(
      this.replaceUrlParams('/versions/:id/publish', { id: versionId }),
    )
  }

  // 删除版本
  async deleteVersion(versionId: string): Promise<{ success: boolean }> {
    return this.delete(
      this.replaceUrlParams('/versions/:id', { id: versionId }),
    )
  }

  // 保存模板草稿
  async saveDraft(
    id: string,
    templateData: Record<string, unknown>,
  ): Promise<{
    template_id: string
    saved_at: string
  }> {
    return this.post(
      this.replaceUrlParams('/templates/:id/save-draft', { id }),
      {
        template_data: templateData,
      },
    )
  }

  // 获取自动保存配置
  async getAutoSaveConfig(): Promise<AutoSaveConfigResponse> {
    return this.get('/templates/auto-save-config')
  }

  // 获取模板访问链接
  async getAccessLink(id: string): Promise<AccessLinkResponse> {
    return this.get(this.replaceUrlParams('/templates/:id/access-link', { id }))
  }

  // 获取模板统计信息
  async getTemplateStats(id: string): Promise<TemplateStatsResponse> {
    return this.get(this.replaceUrlParams('/templates/:id/stats', { id }))
  }

  // 回滚模板到指定版本
  async rollbackToVersion(
    templateId: string,
    versionId: string,
  ): Promise<{ message: string }> {
    return this.post(
      this.replaceUrlParams('/templates/:id/rollback/:versionId', {
        id: templateId,
        versionId,
      }),
    )
  }

  // 获取已发布的模板列表（用于表单填写）
  async getPublishedTemplates(
    params?: PaginationParams & {
      template_type?: string
    },
  ): Promise<TemplatesResponse> {
    const query = this.buildQuery({
      ...this.buildPagination(params?.page, params?.limit, params?.search),
      template_type: params?.template_type,
      status: 'published',
    })

    return this.get('/templates', { params: query })
  }

  // 复制模板
  async duplicateTemplate(
    id: string,
    newName?: string,
  ): Promise<{ template: Template }> {
    return this.post(
      this.replaceUrlParams('/templates/:id/duplicate', { id }),
      {
        name: newName,
      },
    )
  }
}
