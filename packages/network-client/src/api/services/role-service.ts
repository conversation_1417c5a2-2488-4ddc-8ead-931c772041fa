import { RestAPI } from '../base/rest-api'
import type {
  Role,
  CreateRoleRequest,
  UpdateRoleRequest,
  RolePermissionsRequest,
  PaginationParams,
} from '../../types/api'

export class RoleService extends RestAPI<
  Role,
  CreateRoleRequest,
  UpdateRoleRequest
> {
  protected basePath = '/roles'

  // 创建角色
  async createRole(data: CreateRoleRequest): Promise<{ role: Role }> {
    return this.create(data)
  }

  // 更新角色
  async updateRole(
    roleId: string,
    data: UpdateRoleRequest,
  ): Promise<{ role: Role }> {
    return this.update(roleId, data)
  }

  // 删除角色
  async deleteRole(roleId: string): Promise<{ message: string }> {
    return this.remove(roleId)
  }

  // 激活角色
  async activateRole(roleId: string): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/roles/:id/activate', { id: roleId })
    return this.post(url)
  }

  // 停用角色
  async deactivateRole(roleId: string): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/roles/:id/deactivate', { id: roleId })
    return this.post(url)
  }

  // 获取角色列表
  async getRoles(params?: PaginationParams & { type?: string }): Promise<{
    roles: Role[]
    total: number
  }> {
    const query = this.buildQuery({
      ...this.buildPagination(params?.page, params?.limit, params?.search),
      type: params?.type,
    })
    return this.get(this.basePath, { params: query })
  }

  // 获取角色详情
  async getRole(roleId: string): Promise<{ role: Role }> {
    return this.get(this.replaceUrlParams('/roles/:id', { id: roleId }))
  }

  // 更新角色权限
  async updateRolePermissions(
    roleId: string,
    data: RolePermissionsRequest,
  ): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/roles/:id/permissions', { id: roleId })
    return this.put(url, data)
  }

  // 其他方法根据需要添加
}
