// 用户服务

import { RestAPI } from '../base/rest-api'
import type {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserRolesRequest,
  BatchUpdateUsersRequest,
  UserStatsResponse,
  UserPermissionsResponse,
  ProfileUpdateRequest,
  ResetPasswordRequest,
  PaginationParams,
} from '../../types'

export class UserService extends RestAPI<
  User,
  CreateUserRequest,
  UpdateUserRequest
> {
  protected basePath = '/users'

  // 获取用户列表（支持角色过滤）
  async getUsers(params?: PaginationParams & { role?: string }): Promise<{
    users: User[]
    total: number
  }> {
    const query = this.buildQuery({
      ...this.buildPagination(params?.page, params?.limit, params?.search),
      role: params?.role,
    })

    return this.get(this.basePath, { params: query })
  }

  // 获取用户详情
  async getUserDetail(userId: string): Promise<{ user: User }> {
    return this.get(this.replaceUrlParams('/users/:id', { id: userId }))
  }

  // 创建用户
  async createUser(userData: CreateUserRequest): Promise<{ user: User }> {
    return this.post('/users', userData)
  }

  // 更新用户信息
  async updateUser(
    userId: string,
    userData: UpdateUserRequest,
  ): Promise<{ user: User }> {
    return this.put(
      this.replaceUrlParams('/users/:id', { id: userId }),
      userData,
    )
  }

  // 更新用户角色
  async updateUserRoles(
    userId: string,
    roleData: UserRolesRequest,
  ): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/users/:id/roles', { id: userId })
    return this.put(url, roleData)
  }

  // 激活用户
  async activateUser(userId: string): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/users/:id/activate', { id: userId })
    return this.post(url)
  }

  // 停用用户
  async deactivateUser(userId: string): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/users/:id/deactivate', { id: userId })
    return this.post(url)
  }

  // 重置密码
  async resetPassword(
    userId: string,
    passwordData: ResetPasswordRequest,
  ): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/users/:id/reset-password', {
      id: userId,
    })
    return this.post(url, passwordData)
  }

  // 批量更新用户
  async batchUpdateUsers(
    requestData: BatchUpdateUsersRequest,
  ): Promise<{ message: string }> {
    return this.post('/users/batch', requestData)
  }

  // 获取用户统计
  async getUserStats(): Promise<UserStatsResponse> {
    return this.get('/users/stats')
  }

  // 获取用户权限
  async getUserPermissions(
    userId: string,
    projectId?: string,
  ): Promise<UserPermissionsResponse> {
    const url = this.replaceUrlParams('/users/:id/permissions', { id: userId })
    const params = projectId ? { project_id: projectId } : undefined
    return this.get(url, { params })
  }

  // 导出用户
  async exportUsers(
    format: 'csv' | 'xlsx' = 'csv',
    includeRoles: boolean = true,
  ): Promise<unknown> {
    const params = { format, include_roles: includeRoles }
    return this.get('/users/export', { params })
  }
}

// 个人资料服务
export class ProfileService extends BaseAPI {
  // 获取用户资料
  async getProfile(userId: string): Promise<{ user: User }> {
    const url = this.replaceUrlParams('/users/:id/profile', { id: userId })
    return this.get(url)
  }

  // 更新用户资料
  async updateProfile(
    userId: string,
    profileData: ProfileUpdateRequest,
  ): Promise<{ user: User }> {
    const url = this.replaceUrlParams('/users/:id/profile', { id: userId })
    return this.put(url, profileData)
  }

  // 修改密码
  async changePassword(
    userId: string,
    passwordData: ResetPasswordRequest,
  ): Promise<{ message: string }> {
    const url = this.replaceUrlParams('/users/:id/password', { id: userId })
    return this.put(url, passwordData)
  }

  // 上传头像
  async uploadAvatar(
    userId: string,
    file: File,
  ): Promise<{ avatar_url: string }> {
    const formData = new FormData()
    formData.append('avatar', file)

    const url = this.replaceUrlParams('/users/:id/avatar', { id: userId })
    return this.post(url, formData)
  }

  // 获取用户统计信息
  async getUserStats(userId: string): Promise<unknown> {
    const url = this.replaceUrlParams('/users/:id/stats', { id: userId })
    return this.get(url)
  }
}
