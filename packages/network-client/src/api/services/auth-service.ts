// 认证服务

import { InstanceBaseAPI } from '../unified-base-api'
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  SmsCodeRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
} from '../../types'

export class AuthService extends InstanceBaseAPI {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // 推断登录类型
    let loginType: 'password' | 'sms' | 'wechat' = 'password'

    if (credentials.login_type) {
      loginType = credentials.login_type
    } else if (
      credentials.phone &&
      (credentials.phone_code || credentials.sms_code)
    ) {
      loginType = 'sms'
    } else if (credentials.wechat_code) {
      loginType = 'wechat'
    }

    // 构建请求数据
    const requestData: any = { login_type: loginType }

    if (loginType === 'password') {
      requestData.username = credentials.username
      requestData.password = credentials.password
    } else if (loginType === 'sms') {
      requestData.phone = credentials.phone
      requestData.phone_code = credentials.phone_code || credentials.sms_code
    } else if (loginType === 'wechat') {
      requestData.wechat_code = credentials.wechat_code
    }

    if (credentials.remember_me) {
      requestData.remember_me = credentials.remember_me
    }

    return this.post('/auth/login', requestData)
  }

  // 用户注册
  async register(userData: RegisterRequest): Promise<{ user: User }> {
    return this.post('/auth/register', userData)
  }

  // 用户登出
  async logout(): Promise<{ message: string }> {
    return this.post('/auth/logout')
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<{ user: User }> {
    return this.get('/auth/me')
  }

  // 发送短信验证码
  async sendSmsCode(phoneData: SmsCodeRequest): Promise<{ message: string }> {
    return this.post('/auth/sms-code', phoneData)
  }

  // 刷新Token
  async refreshToken(): Promise<RefreshTokenResponse> {
    return this.post('/auth/refresh')
  }

  // 修改密码
  async changePassword(
    passwordData: ChangePasswordRequest,
  ): Promise<{ message: string }> {
    return this.put('/auth/password', passwordData)
  }
}
