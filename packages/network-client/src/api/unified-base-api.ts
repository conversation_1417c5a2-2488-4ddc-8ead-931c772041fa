/**
 * 统一的BaseAPI实现
 * 支持静态方法和实例方法两种使用方式
 */

import { HttpClient } from '../core/http-client'
import { replaceUrlParams } from '../config'
import type { APIResponse, RequestConfig } from '../types'
import type { HttpResponse, RequestOptions } from '../types/http'

/**
 * 统一的BaseAPI基类
 * 同时支持静态方法和实例方法调用
 */
export abstract class UnifiedBaseAPI {
  protected client?: HttpClient

  constructor(client?: HttpClient) {
    this.client = client
  }

  // ==================== 静态方法 ====================

  /**
   * 静态GET请求
   */
  protected static async get<T>(
    url: string,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    return HttpClient.get<T>(url, config)
  }

  /**
   * 静态POST请求
   */
  protected static async post<T>(
    url: string,
    data?: unknown,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    return HttpClient.post<T>(url, data, config)
  }

  /**
   * 静态PUT请求
   */
  protected static async put<T>(
    url: string,
    data?: unknown,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    return HttpClient.put<T>(url, data, config)
  }

  /**
   * 静态DELETE请求
   */
  protected static async delete<T>(
    url: string,
    config?: RequestConfig,
  ): Promise<APIResponse<T>> {
    return HttpClient.delete<T>(url, config)
  }

  /**
   * 静态URL参数替换
   */
  protected static replaceUrlParams(
    url: string,
    params: Record<string, string>,
  ): string {
    return replaceUrlParams(url, params)
  }

  // ==================== 实例方法 ====================

  /**
   * 实例GET请求
   */
  protected async get<T>(
    endpoint: string,
    options?: RequestOptions,
  ): Promise<T> {
    if (!this.client) {
      throw new Error('HttpClient instance is required for instance methods')
    }
    const response = await this.client.get<{ data: T }>(endpoint, options)
    return this.extractData(response)
  }

  /**
   * 实例POST请求
   */
  protected async post<T>(
    endpoint: string,
    data?: unknown,
    options?: RequestOptions,
  ): Promise<T> {
    if (!this.client) {
      throw new Error('HttpClient instance is required for instance methods')
    }
    const response = await this.client.post<{ data: T }>(
      endpoint,
      data,
      options,
    )
    return this.extractData(response)
  }

  /**
   * 实例PUT请求
   */
  protected async put<T>(
    endpoint: string,
    data?: unknown,
    options?: RequestOptions,
  ): Promise<T> {
    if (!this.client) {
      throw new Error('HttpClient instance is required for instance methods')
    }
    const response = await this.client.put<{ data: T }>(endpoint, data, options)
    return this.extractData(response)
  }

  /**
   * 实例DELETE请求
   */
  protected async delete<T>(
    endpoint: string,
    options?: RequestOptions,
  ): Promise<T> {
    if (!this.client) {
      throw new Error('HttpClient instance is required for instance methods')
    }
    const response = await this.client.delete<{ data: T }>(endpoint, options)
    return this.extractData(response)
  }

  /**
   * 提取响应数据
   */
  private extractData<T>(response: HttpResponse<{ data: T }>): T {
    const { data } = response

    // 标准格式: { success: boolean, data: T, message: string }
    if (typeof data === 'object' && data !== null && 'data' in data) {
      return (data as any).data
    }

    // 直接返回数据
    return data as unknown as T
  }

  /**
   * 构建查询参数
   */
  protected buildQuery(
    params: Record<string, unknown>,
  ): Record<string, unknown> {
    const query: Record<string, unknown> = {}

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        query[key] = value
      }
    })

    return query
  }

  /**
   * 构建分页参数
   */
  protected buildPagination(
    page?: number,
    limit?: number,
    search?: string,
  ): Record<string, unknown> {
    return this.buildQuery({
      page,
      limit,
      search,
    })
  }

  /**
   * 实例URL参数替换
   */
  protected replaceUrlParams(
    url: string,
    params: Record<string, string>,
  ): string {
    let result = url
    Object.entries(params).forEach(([key, value]) => {
      result = result.replace(`:${key}`, encodeURIComponent(value))
    })
    return result
  }
}

/**
 * 向后兼容的BaseAPI类（静态方法）
 */
export abstract class BaseAPI extends UnifiedBaseAPI {
  constructor() {
    super()
  }
}

/**
 * 向后兼容的BaseAPI类（实例方法）
 */
export abstract class InstanceBaseAPI extends UnifiedBaseAPI {
  constructor(client: HttpClient) {
    super(client)
  }
}
