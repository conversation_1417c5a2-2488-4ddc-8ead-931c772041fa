# Iconify Icon 组件

一个功能强大的 Vue 3 Iconify 图标组件，支持在线和离线两种模式，提供超过 200,000 个图标的访问能力。

## 🎯 简化命名

现在使用最简洁的组件名称：

| 组件名 | 说明 | 推荐度 |
|--------|------|--------|
| `<Icon>` | 最简洁的组件名 | ⭐⭐⭐⭐⭐ |
| `<IconifyIcon>` | 完整名称（向后兼容） | ⭐⭐ |

## 特性

- 🌐 **在线模式**：直接从 Iconify API 加载图标，无需本地安装
- 📦 **离线模式**：支持本地图标包，适合私有化部署
- 🎨 **丰富的图标库**：支持 150+ 图标集，超过 200,000 个图标
- ⚡ **按需加载**：只加载使用到的图标，优化性能
- 🎯 **TypeScript 支持**：完整的类型定义
- 🔧 **灵活配置**：支持大小、颜色、旋转、翻转等属性
- 🚀 **现代化**：基于 Vue 3 Composition API

## 安装

### 基础依赖

```bash
# 在线模式（必需）
npm install @iconify/vue

# Vue 3 和相关依赖
npm install vue@^3.0.0
```

### 离线模式依赖

```bash
# 离线模式插件
npm install -D unplugin-icons unplugin-vue-components unplugin-auto-import

# 安装所有图标集（约 300MB）
npm install -D @iconify/json

# 或者安装特定图标集
npm install -D @iconify-json/mdi
npm install -D @iconify-json/carbon
npm install -D @iconify-json/ant-design
npm install -D @iconify-json/heroicons
```

## 配置

### Vite 配置（离线模式）

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import Components from 'unplugin-vue-components/vite'

export default defineConfig({
  plugins: [
    vue(),
    Components({
      resolvers: [
        IconsResolver({
          prefix: 'icon',
          enabledCollections: ['mdi', 'carbon', 'ant-design', 'heroicons']
        })
      ]
    }),
    Icons({
      compiler: 'vue3',
      autoInstall: true
    })
  ]
})
```

## 基础用法

### 组件注册

```typescript
// 全局注册（自动注册多个简化别名）
import { createApp } from 'vue'
import IconifyIconPlugin from './path/to/iconify'

const app = createApp(App)
app.use(IconifyIconPlugin)
// 现在可以使用: <Icon>, <IconifyIcon>

// 或按需导入
import { Icon, IconifyIcon } from './path/to/iconify'
```

### ✨ 简化命名（推荐）

```vue
<template>
  <!-- 最简洁的用法 -->
  <Icon icon="mdi:home" />
  <Icon icon="mdi:heart" color="red" :size="24" />
  
  <!-- 统一使用 Icon -->
  <Icon icon="mdi:arrow-right" :rotate="90" />
  <Icon icon="mdi:thumb-up" :horizontal-flip="true" />
</template>
```

### 在线模式（默认）

```vue
<template>
  <!-- 基础用法 -->
  <Icon icon="mdi:home" />
  
  <!-- 设置大小和颜色 -->
  <Icon icon="mdi:heart" color="red" :size="24" />
  
  <!-- 旋转和翻转 -->
  <Icon icon="mdi:arrow-right" :rotate="90" />
  <Icon icon="mdi:thumb-up" :horizontal-flip="true" />
</template>
```

### 离线模式

```vue
<template>
  <!-- 离线模式 -->
  <Icon icon="mdi:home" :offline="true" />
  <Icon icon="carbon/user" :offline="true" />
  
  <!-- 或者直接使用自动导入的组件 -->
  <icon-mdi-home />
  <icon-carbon-user />
</template>
```

## Props API

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `icon` | `string` | - | 图标名称 **必填** |
| `size` | `string \| number` | `'1em'` | 图标大小 |
| `color` | `string` | - | 图标颜色 |
| `opacity` | `string \| number` | `1` | 透明度 |
| `inline` | `boolean` | `false` | 是否内联显示 |
| `rotate` | `number \| string` | - | 旋转角度 |
| `horizontalFlip` | `boolean` | `false` | 水平翻转 |
| `verticalFlip` | `boolean` | `false` | 垂直翻转 |
| `offline` | `boolean` | `false` | 是否使用离线模式 |
| `prefix` | `string` | `'icon'` | 离线模式下的前缀 |
| `tip` | `string` | - | 点击时显示的提示文本 |
| `tipPlacement` | `'top' \| 'bottom' \| 'left' \| 'right'` | `'top'` | 提示显示位置 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `load` | `iconName: string` | 图标加载完成时触发 |
| `error` | `error: any` | 图标加载失败时触发 |
| `click` | `event: MouseEvent` | 图标被点击时触发 |

## 图标命名规则

### 在线模式

图标名称格式：`{prefix}:{icon-name}`

- `mdi:home` - Material Design Icons 的 home 图标
- `ant-design:star-filled` - Ant Design 的 star-filled 图标
- `heroicons:user-circle` - Heroicons 的 user-circle 图标

### 离线模式

图标名称格式：`{collection}/{icon-name}` 或 `{collection}:{icon-name}`

- `mdi/home` 或 `mdi:home`
- `carbon/user` 或 `carbon:user`

## 使用示例

### 不同尺寸

```vue
<template>
  <Icon icon="mdi:star" :size="16" />
  <Icon icon="mdi:star" :size="24" />
  <Icon icon="mdi:star" :size="32" />
  <Icon icon="mdi:star" size="2em" />
</template>
```

### 颜色控制

```vue
<template>
  <Icon icon="mdi:heart" color="red" />
  <Icon icon="mdi:star" color="#ffc107" />
  <Icon icon="mdi:check" color="green" />
</template>
```

### 🆕 Tip 提示功能

```vue
<template>
  <!-- 基础 tip 提示（默认显示在上方） -->
  <Icon 
    icon="mdi:home" 
    tip="这是首页图标"
    @click="handleClick"
  />
  
  <!-- 不同位置的提示 -->
  <Icon icon="mdi:user" tip="用户中心" tip-placement="top" />
  <Icon icon="mdi:settings" tip="系统设置" tip-placement="bottom" />
  <Icon icon="mdi:bell" tip="消息通知" tip-placement="left" />
  <Icon icon="mdi:search" tip="搜索功能" tip-placement="right" />
</template>

<script setup>
const handleClick = (event) => {
  console.log('图标被点击了', event)
}
</script>
```

**Tip 功能特性：**
- 🖱️ 鼠标悬浮时显示提示文本
- 📍 默认显示在图标上方
- ✅ 支持 4 个方向：top、bottom、left、right
- ✨ 带有淡入淡出动画效果
- ⚡ 鼠标离开时立即隐藏
- 🔍 鼠标悬停放大效果
- 👆 点击缩小反馈效果
- 🏆 最高层级 (z-index: 99999)，不会被其他元素遮挡
  <Icon icon="mdi:heart" color="#ff6b35" />
  <Icon icon="mdi:heart" color="rgb(255, 107, 53)" />
</template>
```

### 变换效果

```vue
<template>
  <!-- 旋转 -->
  <Icon icon="mdi:arrow-right" :rotate="90" />
  <Icon icon="mdi:arrow-right" :rotate="180" />
  
  <!-- 翻转 -->
  <Icon icon="mdi:thumb-up" :horizontal-flip="true" />
  <Icon icon="mdi:thumb-up" :vertical-flip="true" />
</template>
```

### 内联使用

```vue
<template>
  <p>
    这是一段文字 
    <Icon icon="mdi:star" :inline="true" color="gold" /> 
    中间包含图标的示例。
  </p>
</template>
```

### 事件处理

```vue
<template>
  <Icon 
    icon="mdi:information" 
    @load="handleLoad"
    @error="handleError"
  />
</template>

<script setup>
const handleLoad = (iconName) => {
  console.log(`图标 ${iconName} 加载成功`)
}

const handleError = (error) => {
  console.error('图标加载失败:', error)
}
</script>
```

## 图标资源

### 热门图标集

- **Material Design Icons (mdi)**: 7000+ 图标
- **Carbon Design System (carbon)**: 2000+ 图标  
- **Ant Design Icons (ant-design)**: 800+ 图标
- **Heroicons (heroicons)**: 300+ 图标
- **Feather Icons (feather)**: 280+ 图标
- **Font Awesome (fa)**: 2000+ 图标

### 图标浏览

- [Iconify 官方图标浏览器](https://icon-sets.iconify.design/)
- [Icônes 在线图标库](https://icones.js.org/)

## 最佳实践

### 性能优化

1. **按需安装图标集**：只安装项目中使用的图标集
2. **使用离线模式**：对于私有化部署，推荐使用离线模式
3. **合理设置缓存**：在线模式下合理设置 API 缓存

### 项目组织

```
src/
├── components/
│   └── icons/          # 图标相关组件
│       ├── IconifyIcon.vue
│       └── index.ts
├── types/
│   └── iconify.d.ts    # 类型声明
└── utils/
    └── iconify.ts      # 图标工具函数
```

### 代码规范

```vue
<!-- 推荐：使用语义化的图标名称 -->
<iconify-icon icon="mdi:home" />
<iconify-icon icon="mdi:user-account" />

<!-- 避免：使用过于复杂的变换 -->
<iconify-icon 
  icon="mdi:star" 
  :rotate="45" 
  :horizontal-flip="true" 
  :vertical-flip="true" 
/>
```

## 故障排除

### 常见问题

1. **图标不显示**
   - 检查网络连接（在线模式）
   - 确认图标名称正确
   - 检查图标集是否已安装（离线模式）

2. **TypeScript 错误**
   - 确保已导入类型声明文件
   - 检查 `unplugin-icons` 的类型声明

3. **离线模式配置问题**
   - 确认 Vite 插件配置正确
   - 检查图标集安装是否完整

### 调试技巧

```vue
<template>
  <iconify-icon 
    icon="mdi:debug"
    @load="(name) => console.log('Loaded:', name)"
    @error="(err) => console.error('Error:', err)"
  />
</template>
```

## 升级指南

### 从其他图标库迁移

```vue
<!-- Element Plus Icons -->
<el-icon><House /></el-icon>
<!-- 迁移到 -->
<Icon icon="ep:house" />

<!-- Ant Design Vue Icons -->
<HomeOutlined />
<!-- 迁移到 -->
<Icon icon="ant-design:home-outlined" />

<!-- 原来的 IconifyIcon -->
<iconify-icon icon="mdi:home" />
<!-- 现在可以简化为 -->
<Icon icon="mdi:home" />
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个组件。

## 许可证

MIT License 