/**
 * CrfTextarea 组件 Schema 定义
 *
 * 使用 TypeBox 定义组件的配置 Schema，支持运行时验证和类型推导
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

/**
 * CrfTextarea 组件配置 Schema
 */
export const CrfTextareaConfigSchema = createComponentSchema({
  // =============================================================================
  // 基础设置
  // =============================================================================
  title: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '组件标题',
      default: '多行文本',
      placeholder: '请输入组件标题',
      description: '显示在组件上方的标题文本',
      showInConfig: true,
      configGroup: 'basic',
      helpIcon: true,
      validation: {
        required: true,
        message: '组件标题不能为空',
      },
    }),
  ),

  description: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '组件描述',
      default: '请输入多行文本内容',
      placeholder: '请输入组件描述',
      description: '显示在组件下方的描述文本',
      showInConfig: true,
      configGroup: 'basic',
    }),
  ),

  placeholder: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '占位符文本',
      default: '请输入内容',
      placeholder: '请输入内容',
      description: '当输入框为空时显示的提示文本',
      showInConfig: true,
      configGroup: 'basic',
      helpIcon: true,
    }),
  ),

  // =============================================================================
  // 格式和验证设置
  // =============================================================================
  required: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '必填项',
      default: false,
      description: '是否为必填字段',
      showInConfig: true,
      configGroup: 'validation',
    }),
  ),

  minLength: Type.Optional(
    Type.Number({
      code: 'config-number',
      label: '最少字符数',
      default: 0,
      placeholder: '请输入',
      minimum: 0,
      maximum: 1000,
      description: '输入内容的最少字符数',
      showInConfig: true,
      configGroup: 'validation',
    }),
  ),

  maxLength: Type.Optional(
    Type.Number({
      code: 'config-number',
      label: '最多字符数',
      default: 1000,
      placeholder: '请输入',
      minimum: 1,
      maximum: 10000,
      description: '输入内容的最多字符数',
      showInConfig: true,
      configGroup: 'validation',
    }),
  ),

  // =============================================================================
  // 外观设置
  // =============================================================================
  size: Type.Optional(
    Type.Union(
      [Type.Literal('small'), Type.Literal('medium'), Type.Literal('large')],
      {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        description: '组件的显示大小',
        enumNames: ['小', '中', '大'],
        showInConfig: true,
        configGroup: 'appearance',
      },
    ),
  ),

  rows: Type.Optional(
    Type.Number({
      code: 'config-number',
      label: '显示行数',
      default: 4,
      placeholder: '请输入',
      minimum: 1,
      maximum: 20,
      description: '文本域显示的行数',
      showInConfig: true,
      configGroup: 'appearance',
    }),
  ),

  autosize: Type.Optional(
    Type.Union(
      [
        Type.Boolean(),
        Type.Object({
          minRows: Type.Optional(
            Type.Number({
              minimum: 1,
              maximum: 20,
            }),
          ),
          maxRows: Type.Optional(
            Type.Number({
              minimum: 1,
              maximum: 50,
            }),
          ),
        }),
      ],
      {
        code: 'config-switch',
        label: '自动调整高度',
        default: false,
        description: '是否根据内容自动调整文本域高度',
        showInConfig: true,
        configGroup: 'appearance',
      },
    ),
  ),

  resize: Type.Optional(
    Type.Union(
      [
        Type.Literal('none'),
        Type.Literal('both'),
        Type.Literal('horizontal'),
        Type.Literal('vertical'),
      ],
      {
        code: 'config-select',
        label: '调整大小',
        default: 'vertical',
        description: '用户是否可以调整文本域大小',
        enumNames: ['不可调整', '可调整', '水平调整', '垂直调整'],
        showInConfig: true,
        configGroup: 'appearance',
      },
    ),
  ),

  // =============================================================================
  // 功能设置
  // =============================================================================
  showCount: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '显示字符计数',
      default: false,
      description: '是否显示当前字符数和最大字符数',
      showInConfig: true,
      configGroup: 'feature',
    }),
  ),

  autofocus: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '自动获取焦点',
      default: false,
      description: '页面加载时是否自动获取焦点',
      showInConfig: true,
      configGroup: 'feature',
    }),
  ),

  // =============================================================================
  // 自定义错误设置
  // =============================================================================
  enableCustomError: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '自定义错误提示',
      default: false,
      description: '是否启用自定义错误提示信息',
      showInConfig: true,
      configGroup: 'validation',
      helpIcon: true,
    }),
  ),

  customErrorMessage: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '错误提示内容',
      default: '',
      placeholder: '请输入自定义错误信息',
      description: '当验证失败时显示的自定义错误信息',
      showInConfig: true,
      configGroup: 'validation',
      showWhen: {
        field: 'enableCustomError',
        value: true,
      },
    }),
  ),

  // =============================================================================
  // 医疗相关字段
  // =============================================================================
  medicalType: Type.Optional(
    Type.Union(
      [
        Type.Literal('general'),
        Type.Literal('vital'),
        Type.Literal('lab'),
        Type.Literal('medication'),
        Type.Literal('diagnosis'),
        Type.Literal('procedure'),
        Type.Literal('allergy'),
        Type.Literal('history'),
        Type.Literal('symptom'),
        Type.Literal('assessment'),
        Type.Literal('plan'),
        Type.Literal('progress'),
      ],
      {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        description: '医疗数据的类型分类',
        enumNames: [
          '通用',
          '生命体征',
          '检验',
          '用药',
          '诊断',
          '操作',
          '过敏',
          '病史',
          '症状',
          '评估',
          '计划',
          '进展',
        ],
        showInConfig: true,
        configGroup: 'medical',
      },
    ),
  ),

  fieldCode: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '字段编码',
      default: '',
      placeholder: '请输入字段编码',
      description: '用于数据收集和导出的字段编码',
      showInConfig: true,
      configGroup: 'medical',
    }),
  ),

  // =============================================================================
  // 数据收集设置
  // =============================================================================
  dataCollection: Type.Optional(
    Type.Union(
      [
        Type.Literal('required'),
        Type.Literal('optional'),
        Type.Literal('conditional'),
      ],
      {
        code: 'config-select',
        label: '数据收集要求',
        default: 'optional',
        description: '该字段在数据收集中的要求级别',
        enumNames: ['必填', '可选', '条件性'],
        showInConfig: true,
        configGroup: 'medical',
      },
    ),
  ),

  // =============================================================================
  // 编码系统设置
  // =============================================================================
  codingSystem: Type.Optional(
    Type.String({
      code: 'config-select',
      label: '编码系统',
      default: '',
      description: '使用的医疗编码系统（如ICD-10、SNOMED CT等）',
      showInConfig: true,
      configGroup: 'coding',
      options: [
        { label: '无', value: '' },
        { label: 'ICD-10', value: 'ICD-10' },
        { label: 'ICD-11', value: 'ICD-11' },
        { label: 'SNOMED CT', value: 'SNOMED-CT' },
        { label: 'LOINC', value: 'LOINC' },
        { label: 'RxNorm', value: 'RxNorm' },
        { label: 'CPT', value: 'CPT' },
        { label: '自定义', value: 'custom' },
      ],
    }),
  ),

  // =============================================================================
  // 格式化设置
  // =============================================================================
  textFormat: Type.Optional(
    Type.Union(
      [
        Type.Literal('plain'),
        Type.Literal('structured'),
        Type.Literal('soap'),
        Type.Literal('narrative'),
      ],
      {
        code: 'config-select',
        label: '文本格式',
        default: 'plain',
        description: '文本内容的格式类型',
        enumNames: ['纯文本', '结构化', 'SOAP格式', '叙述性'],
        showInConfig: true,
        configGroup: 'format',
      },
    ),
  ),

  // =============================================================================
  // 模板设置
  // =============================================================================
  template: Type.Optional(
    Type.String({
      code: 'config-textarea',
      label: '文本模板',
      default: '',
      placeholder: '请输入文本模板',
      description: '预设的文本模板，用户可以基于此模板填写',
      showInConfig: true,
      configGroup: 'template',
    }),
  ),

  useTemplate: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '启用模板',
      default: false,
      description: '是否启用文本模板功能',
      showInConfig: true,
      configGroup: 'template',
    }),
  ),
})

/**
 * 从 Schema 推导的类型
 */
export type CrfTextareaSchema = Static<typeof CrfTextareaConfigSchema>

/**
 * 组件配置类型别名
 */
export type CrfTextareaConfig = CrfTextareaSchema

/**
 * 默认导出
 */
export default CrfTextareaConfigSchema
