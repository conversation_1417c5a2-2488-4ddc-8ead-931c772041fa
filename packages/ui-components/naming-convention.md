# 组件命名规范

## 概述

本文档定义了 CRF Frontend 项目中组件的统一命名规范，旨在提高代码的一致性和可维护性。

## 命名规范

### 组件前缀

- **标准前缀**: `Crf` (推荐)
- **向后兼容前缀**: `Jz` (已弃用，仅用于向后兼容)

### 组件命名模式

#### 1. 组件类名

```typescript
// ✅ 推荐
export const CrfText = withInstallByCode(Text, 'CrfText')
export const CrfInput = withInstallByCode(Input, 'CrfInput')
export const CrfRadio = withInstallByCode(Radio, 'CrfRadio')

// ⚠️ 向后兼容（已弃用）
export const JzText = CrfText
export const JzCrfInput = CrfInput
export const JzRadio = CrfRadio
```

#### 2. Schema 类型名

```typescript
// ✅ 推荐
export type CrfTextSchema = Static<typeof CrfTextConfigSchema>
export type CrfInputSchema = Static<typeof CrfInputConfigSchema>
export type CrfRadioSchema = Static<typeof CrfRadioConfigSchema>

// ⚠️ 向后兼容（已弃用）
export type JzTextSchema = CrfTextSchema
export type JzCrfInputSchema = CrfInputSchema
export type JzRadioSchema = CrfRadioSchema
```

#### 3. 配置对象名

```typescript
// ✅ 推荐
export const defaultCrfTextConfig: CrfTextSchema = { ... }
export const defaultCrfInputConfig: CrfInputSchema = { ... }
export const defaultCrfRadioConfig: CrfRadioSchema = { ... }
```

#### 4. Schema 定义名

```typescript
// ✅ 推荐
export const CrfTextConfigSchema = createComponentSchema('text', { ... })
export const CrfInputConfigSchema = createComponentSchema('input', { ... })
export const CrfRadioConfigSchema = createComponentSchema('radio', { ... })
```

### 文件命名

#### 1. 组件目录结构

```
packages/components/
├── text/
│   ├── index.ts          # 组件导出
│   ├── schema.ts         # Schema 定义
│   ├── types.ts          # 类型定义
│   └── Text.vue          # 组件实现
├── input/
│   ├── index.ts
│   ├── schema.ts
│   ├── types.ts
│   └── Input.vue
└── radio/
    ├── index.ts
    ├── schema.ts
    ├── types.ts
    └── Radio.vue
```

#### 2. 类型文件命名

```
packages/types/
├── components/
│   ├── text.ts           # CrfTextProps, CrfTextSchema 等
│   ├── input.ts          # CrfInputProps, CrfInputSchema 等
│   └── radio.ts          # CrfRadioProps, CrfRadioSchema 等
```

## 迁移指南

### 从 Jz 前缀迁移到 Crf 前缀

1. **组件使用**

```typescript
// 旧方式（已弃用）
import { JzText, JzCrfInput } from '@crf/components'

// 新方式（推荐）
import { CrfText, CrfInput } from '@crf/components'
```

2. **类型引用**

```typescript
// 旧方式（已弃用）
import type { JzTextSchema, JzCrfInputSchema } from '@crf/components'

// 新方式（推荐）
import type { CrfTextSchema, CrfInputSchema } from '@crf/components'
```

3. **组件注册**

```typescript
// 旧方式（已弃用）
app.component('JzText', JzText)
app.component('JzCrfInput', JzCrfInput)

// 新方式（推荐）
app.component('CrfText', CrfText)
app.component('CrfInput', CrfInput)
```

## 向后兼容性

为确保平滑迁移，我们将保持以下向后兼容性：

1. **别名导出**: 所有 `Jz` 前缀的组件和类型将作为 `Crf` 前缀的别名导出
2. **渐进式迁移**: 允许项目逐步从 `Jz` 前缀迁移到 `Crf` 前缀
3. **弃用警告**: 在开发环境中使用 `Jz` 前缀时会显示弃用警告

## 最佳实践

1. **新组件**: 所有新组件必须使用 `Crf` 前缀
2. **现有组件**: 逐步迁移现有组件到 `Crf` 前缀
3. **文档更新**: 所有文档和示例应使用 `Crf` 前缀
4. **测试**: 确保新旧命名方式都有相应的测试覆盖

## 检查清单

在创建或更新组件时，请确保：

- [ ] 组件使用 `Crf` 前缀命名
- [ ] 提供 `Jz` 前缀的向后兼容别名
- [ ] Schema 类型使用 `Crf` 前缀
- [ ] 配置对象使用 `Crf` 前缀
- [ ] 文件结构符合规范
- [ ] 更新相关文档和示例
- [ ] 添加或更新测试用例
