{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "baseUrl": ".", "rootDir": ".", "outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUncheckedIndexedAccess": true, "lib": ["dom", "dom.iterable", "esnext"], "jsx": "preserve", "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "useDefineForClassFields": true, "paths": {"vue": ["../../node_modules/vue"], "@vue/*": ["../../node_modules/@vue/*"]}}, "references": [{"path": "../vue-hooks"}, {"path": "../type-definitions"}, {"path": "../shared-utils"}], "include": ["*.ts", "*.vue", "*.d.ts", "*/**/*.ts", "*/**/*.vue", "*/**/*.d.ts", "vue-shims.d.ts"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts", "**/node_modules/**"]}