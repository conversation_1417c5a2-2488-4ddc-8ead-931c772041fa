<template>
  <CrfBaseContainer
    :title="props.title || '多选'"
    :description="props.description"
    :required="props.required"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :error="validationState.status === 'error' ? validationState.message : ''"
    :direction="props.direction"
    :medical-type="props.medicalType"
    :validation-status="validationState.status"
  >
    <!-- 全选按钮 -->
    <div
      v-if="props.checkAll"
      class="crf-pb-2 crf-border-b crf-border-gray-200"
    >
      <n-checkbox
        v-model:checked="checkAllStatus"
        :indeterminate="isIndeterminate"
        :disabled="shouldDisableInEditMode"
        @update:checked="handleCheckAllChange"
      >
        全选
      </n-checkbox>
    </div>

    <!-- 选项列表 -->
    <n-checkbox-group
      v-model:value="internalValue"
      :disabled="shouldDisableInEditMode"
      @update:value="handleChange"
    >
      <!-- 可拖拽的选项列表 -->
      <draggable
        v-model="localOptions"
        :disabled="!isInEditorEditMode"
        item-key="value"
        ghost-class="crf-drag-ghost"
        chosen-class="crf-drag-chosen"
        drag-class="crf-drag-active"
        animation="200"
        @start="onDragStart"
        @end="onDragEnd"
        @change="onDragChange"
        class="crf-flex crf-flex-col crf-gap-2"
        :class="{
          'crf-flex-row crf-flex-wrap crf-gap-4':
            props.direction === 'horizontal',
        }"
      >
        <template #item="{ element, index }">
          <div
            class="crf-option-wrapper crf-transition-colors"
            :class="{
              'crf-state-draggable': isInEditorEditMode,
              'crf-state-editing': optionEditStates[index],
            }"
          >
            <!-- 复选框 -->
            <n-checkbox
              :value="element.value"
              :disabled="element.disabled"
              class="crf-flex-1"
            >
              <span
                v-if="!optionEditStates[index]"
                class="crf-text-sm crf-text-primary"
                >{{ element.label }}</span
              >
              <n-input
                v-else-if="editingOptions[index]"
                v-model:value="editingOptions[index].label"
                size="small"
                class="crf-max-w-xs crf-ml-2"
                @blur="saveOptionEdit(index)"
                @keyup.enter="saveOptionEdit(index)"
                @keyup.esc="cancelOptionEdit(index)"
                @click.stop
              />
            </n-checkbox>

            <!-- 编辑操作按钮 -->
            <div v-if="isInEditorEditMode" class="crf-option-actions">
              <button
                v-if="!optionEditStates[index]"
                class="crf-option-button crf-transition-colors"
                @click.stop="startOptionEdit(index)"
                title="编辑选项"
              >
                <n-icon><PencilOutline /></n-icon>
              </button>
              <button
                class="crf-option-button crf-option-button--danger crf-transition-colors"
                @click.stop="deleteOption(index)"
                title="删除选项"
                :disabled="!canDeleteOption"
              >
                <n-icon><TrashOutline /></n-icon>
              </button>
              <button
                class="crf-option-button crf-cursor-move crf-transition-colors"
                title="拖拽排序"
              >
                <n-icon><SwapVerticalOutline /></n-icon>
              </button>
            </div>
          </div>
        </template>
      </draggable>
    </n-checkbox-group>

    <!-- 添加选项按钮 -->
    <template #footer v-if="isInEditorEditMode">
      <button
        class="crf-add-option-button crf-transition-colors"
        @click="addOption"
        title="添加选项"
        :disabled="!canAddOption"
      >
        <n-icon><AddOutline /></n-icon>
        <span>添加选项</span>
      </button>
    </template>
  </CrfBaseContainer>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { NCheckbox, NCheckboxGroup, NInput, NIcon } from 'naive-ui'
import {
  AddOutline,
  PencilOutline,
  TrashOutline,
  SwapVerticalOutline,
} from '@vicons/ionicons5'
import draggable from 'vuedraggable'
import CrfBaseContainer from '../base/CrfBaseContainer.vue'
import { useFormComponent } from '@crf/vue-composables/useFormComponent'
import { useOptionManager } from '@crf/vue-composables/useOptionManager'
import type { Option } from '@crf/vue-composables/useOptionManager'

// 定义组件属性
export interface CrfCheckboxProps {
  /** 组件值 */
  modelValue?: (string | number)[]
  /** 组件标题 */
  title?: string
  /** 组件描述 */
  description?: string
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 选项列表 */
  options?: Option[]
  /** 布局方向 */
  direction?: 'horizontal' | 'vertical'
  /** 是否显示全选按钮 */
  checkAll?: boolean
  /** 医疗数据类型 */
  medicalType?: 'vital-signs' | 'medication' | 'diagnosis' | 'procedure'
  /** 是否在编辑器编辑模式 */
  isInEditorEditMode?: boolean
  /** 验证规则 */
  validation?: Array<{
    required?: boolean
    message?: string
  }>
}

// 定义组件事件
export interface CrfCheckboxEmits {
  'update:modelValue': [value: (string | number)[] | undefined]
  'update:options': [options: Option[]]
  change: [value: (string | number)[] | undefined]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  validate: [
    result: { status: 'success' | 'warning' | 'error'; message: string },
  ]
}

const props = withDefaults(defineProps<CrfCheckboxProps>(), {
  direction: 'vertical',
  checkAll: false,
  options: () => [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
  ],
})

const emit = defineEmits<CrfCheckboxEmits>()

// 使用表单组件通用逻辑
const {
  internalValue,
  validationState,
  shouldDisableInEditMode,
  handleChange,
  // handleFocus,
  // handleBlur,
  validate,
  clearValidation,
} = useFormComponent(props, emit as any, {
  defaultValue: [],
  validateOnChange: true,
  customValidator: (value: any) => {
    // 检查值是否在允许的选项中
    if (Array.isArray(value) && value.length > 0) {
      const validValues = localOptions.value.map((option: any) => option.value)
      const invalidValues = value.filter((v: any) => !validValues.includes(v))
      if (invalidValues.length > 0) {
        return {
          isValid: false,
          status: 'error',
          errors: ['选择的值无效'],
          warnings: [],
          message: '选择的值无效',
        }
      }
    }

    return {
      isValid: true,
      status: 'success',
      errors: [],
      warnings: [],
      message: '',
    }
  },
})

// 使用选项管理逻辑
const {
  localOptions,
  optionEditStates,
  editingOptions,
  canAddOption,
  canDeleteOption,
  addOption,
  startOptionEdit,
  saveOptionEdit,
  cancelOptionEdit,
  deleteOption,
  onDragStart,
  onDragEnd,
  onDragChange,
} = useOptionManager(
  {
    options: props.options,
    allowEdit: true,
    allowDelete: true,
    allowAdd: true,
    allowSort: true,
    minOptions: 1,
    maxOptions: 20,
    isInEditorEditMode: props.isInEditorEditMode,
  },
  emit as any,
)

// 全选功能
const checkAllStatus = computed({
  get: () => {
    if (!Array.isArray(internalValue.value)) return false
    return internalValue.value.length === localOptions.value.length
  },
  set: (checked: boolean) => {
    if (checked) {
      internalValue.value = localOptions.value.map(
        (option: any) => option.value,
      ) as any
    } else {
      internalValue.value = []
    }
    handleChange(internalValue.value)
  },
})

const isIndeterminate = computed(() => {
  if (!Array.isArray(internalValue.value)) return false
  return (
    internalValue.value.length > 0 &&
    internalValue.value.length < localOptions.value.length
  )
})

const handleCheckAllChange = (checked: boolean) => {
  checkAllStatus.value = checked
}

// 对外暴露的API
defineExpose({
  validate,
  clearValidation,
  internalValue,
  validationState,
})
</script>

<style lang="scss" scoped>
/* 复选框组件的特殊样式覆盖 */
:deep(.n-checkbox) {
  margin: 0;
  display: flex;
  align-items: center;
  min-height: 32px;

  .n-checkbox__label {
    font-size: var(--crf-font-size-sm);
    color: var(--crf-color-text-primary);
    line-height: var(--crf-line-height-normal);
  }

  .n-checkbox__box {
    border-color: var(--crf-color-border-primary);
  }

  &:hover .n-checkbox__box {
    border-color: var(--crf-color-primary);
  }

  &.n-checkbox--checked .n-checkbox__box {
    border-color: var(--crf-color-primary);
    background-color: var(--crf-color-primary);
  }

  &.n-checkbox--disabled {
    opacity: 0.6;

    .n-checkbox__label {
      color: var(--crf-color-text-disabled);
    }
  }
}

/* 复选框组样式 */
:deep(.n-checkbox-group) {
  width: 100%;
}
</style>
