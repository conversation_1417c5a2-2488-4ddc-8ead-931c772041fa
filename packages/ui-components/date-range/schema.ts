/**
 * CrfDateRange 组件 Schema 定义
 *
 * 使用 TypeBox 定义组件的配置 Schema，支持运行时验证和类型推导
 */

import { Type, type Static } from '@sinclair/typebox'
import { createComponentSchema } from '../base/base-schema'

/**
 * 快捷选项 Schema
 */
const ShortcutSchema = Type.Object({
  text: Type.String(),
  value: Type.Array(Type.String()),
})

/**
 * CrfDateRange 组件配置 Schema
 */
export const CrfDateRangeConfigSchema = createComponentSchema({
  // =============================================================================
  // 基础设置
  // =============================================================================
  title: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '组件标题',
      default: '日期范围',
      placeholder: '请输入组件标题',
      description: '显示在组件上方的标题文本',
      showInConfig: true,
      configGroup: 'basic',
      helpIcon: true,
      validation: {
        required: true,
        message: '组件标题不能为空',
      },
    }),
  ),

  description: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '组件描述',
      default: '请选择日期范围',
      placeholder: '请输入组件描述',
      description: '显示在组件下方的描述文本',
      showInConfig: true,
      configGroup: 'basic',
    }),
  ),

  startPlaceholder: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '开始日期占位符',
      default: '开始日期',
      placeholder: '请输入开始日期占位符',
      description: '开始日期输入框的占位符文本',
      showInConfig: true,
      configGroup: 'basic',
    }),
  ),

  endPlaceholder: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '结束日期占位符',
      default: '结束日期',
      placeholder: '请输入结束日期占位符',
      description: '结束日期输入框的占位符文本',
      showInConfig: true,
      configGroup: 'basic',
    }),
  ),

  // =============================================================================
  // 格式设置
  // =============================================================================
  type: Type.Optional(
    Type.Union(
      [
        Type.Literal('date'),
        Type.Literal('datetime'),
        Type.Literal('month'),
        Type.Literal('year'),
        Type.Literal('week'),
      ],
      {
        code: 'config-select',
        label: '日期类型',
        default: 'date',
        description: '日期选择器的类型',
        enumNames: ['日期', '日期时间', '月份', '年份', '周'],
        showInConfig: true,
        configGroup: 'format',
      },
    ),
  ),

  format: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '显示格式',
      default: 'YYYY-MM-DD',
      placeholder: '如：YYYY-MM-DD',
      description: '日期在输入框中的显示格式',
      showInConfig: true,
      configGroup: 'format',
    }),
  ),

  valueFormat: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '数据格式',
      default: 'YYYY-MM-DD',
      placeholder: '如：YYYY-MM-DD',
      description: '绑定值的格式，不指定则与显示格式一致',
      showInConfig: true,
      configGroup: 'format',
    }),
  ),

  separator: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '分隔符',
      default: '至',
      placeholder: '如：至、-、~',
      description: '日期范围的分隔符',
      showInConfig: true,
      configGroup: 'format',
    }),
  ),

  // =============================================================================
  // 功能设置
  // =============================================================================
  editable: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '允许手动输入',
      default: true,
      description: '是否允许手动输入日期',
      showInConfig: true,
      configGroup: 'feature',
    }),
  ),

  clearable: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '显示清空按钮',
      default: true,
      description: '是否显示清空按钮',
      showInConfig: true,
      configGroup: 'feature',
    }),
  ),

  unlinkPanels: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '独立面板',
      default: false,
      description: '左右面板是否联动，设为true后两个面板可以选择不同年份',
      showInConfig: true,
      configGroup: 'feature',
    }),
  ),

  // =============================================================================
  // 快捷选项设置
  // =============================================================================
  shortcuts: Type.Optional(
    Type.Array(ShortcutSchema, {
      code: 'config-shortcuts',
      label: '快捷选项',
      default: [
        { text: '今天', value: ['', ''] },
        { text: '最近一周', value: ['', ''] },
        { text: '最近一月', value: ['', ''] },
        { text: '最近三月', value: ['', ''] },
      ],
      description: '预设的快捷日期选项',
      showInConfig: true,
      configGroup: 'feature',
    }),
  ),

  // =============================================================================
  // 外观设置
  // =============================================================================
  size: Type.Optional(
    Type.Union(
      [Type.Literal('small'), Type.Literal('medium'), Type.Literal('large')],
      {
        code: 'config-select',
        label: '组件大小',
        default: 'medium',
        description: '组件的显示大小',
        enumNames: ['小', '中', '大'],
        showInConfig: true,
        configGroup: 'appearance',
      },
    ),
  ),

  // =============================================================================
  // 验证设置
  // =============================================================================
  required: Type.Optional(
    Type.Boolean({
      code: 'config-switch',
      label: '必填项',
      default: false,
      description: '是否为必填字段',
      showInConfig: true,
      configGroup: 'validation',
    }),
  ),

  // =============================================================================
  // 医疗相关字段
  // =============================================================================
  medicalType: Type.Optional(
    Type.Union(
      [
        Type.Literal('general'),
        Type.Literal('admission'),
        Type.Literal('discharge'),
        Type.Literal('treatment'),
        Type.Literal('followup'),
        Type.Literal('study'),
      ],
      {
        code: 'config-select',
        label: '医疗数据类型',
        default: 'general',
        description: '医疗数据的类型分类',
        enumNames: [
          '通用',
          '入院时间',
          '出院时间',
          '治疗周期',
          '随访期间',
          '研究期间',
        ],
        showInConfig: true,
        configGroup: 'medical',
      },
    ),
  ),

  fieldCode: Type.Optional(
    Type.String({
      code: 'config-input',
      label: '字段编码',
      default: '',
      placeholder: '请输入字段编码',
      description: '用于数据收集和导出的字段编码',
      showInConfig: true,
      configGroup: 'medical',
    }),
  ),
})

/**
 * 从 Schema 推导的类型
 */
export type CrfDateRangeSchema = Static<typeof CrfDateRangeConfigSchema>

/**
 * 组件配置类型别名
 */
export type CrfDateRangeConfig = CrfDateRangeSchema

/**
 * 默认导出
 */
export default CrfDateRangeConfigSchema
