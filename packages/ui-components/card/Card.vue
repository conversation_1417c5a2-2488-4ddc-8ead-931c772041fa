<script lang="ts" setup>
import { computed } from 'vue'
import { CrfIcon } from '../icon'

defineOptions({
  name: 'CrfCard',
})

const props = defineProps({
  icon: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  info: {
    type: String,
    default: '',
  },
  iconColor: {
    type: String,
    default: '#000',
  },
  tag: {
    type: String,
    default: '',
  },
  compact: Boolean,
})

const compactClass = computed(() =>
  props.compact ? 'component-item-compact' : '',
)
</script>

<template>
  <div :class="['component-item', compactClass]">
    <div class="component-icon">
      <crf-icon :color="props.iconColor" :icon="props.icon" />
    </div>
    <div class="component-info">
      <div class="component-name">
        {{ props.title }}
        <span v-if="props.tag" class="component-tag medical">{{
          props.tag
        }}</span>
      </div>
      <div class="component-description">{{ props.info }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.component-item {
  display: flex;
  align-items: center;
  gap: 0.375rem; // 减小间距
  padding: 0.375rem; // 减小内边距
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;

  // 增强拖拽提示
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--border-radius);
    border: 2px dashed transparent;
    transition: all 0.2s ease;
    pointer-events: none;
  }
}

.component-item:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.component-item.dragging {
  opacity: 0.6;
  transform: rotate(5deg);
}

.component-icon {
  width: 32px; // 减小图标容器尺寸
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  font-size: 1.25rem; // 减小图标尺寸
  color: var(--white);
  flex-shrink: 0;
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-name {
  font-weight: 500; // 减小字重
  color: var(--gray-800);
  margin-bottom: 0.125rem; // 减小间距
  font-size: 0.875rem; // 减小字号
  display: flex;
  align-items: center;
  gap: 0.375rem; // 减小间距
}

.component-description {
  font-size: 0.75rem; // 减小字号
  color: var(--gray-600);
  line-height: 1.3; // 减小行高
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.component-tag {
  font-size: 0.625rem;
  padding: 0.125rem 0.25rem; // 减小内边距
  border-radius: 0.25rem;
  font-weight: 500; // 减小字重
  white-space: nowrap;
  flex-shrink: 0;

  &.medical {
    background: #e1f5fe;
    color: #0277bd;
    border: 1px solid #b3e5fc;
  }
}

// 紧凑模式样式调整
.component-item-compact {
  padding: 0.25rem 0.375rem; // 进一步减小内边距
  gap: 0.25rem; // 减小间距

  .component-icon {
    width: 28px; // 更小的图标容器
    height: 28px;
    font-size: 1.125rem; // 更小的图标
  }

  .component-name {
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
  }

  .component-description {
    font-size: 0.75rem;
  }

  .drag-indicator {
    width: 14px;
    height: 14px;
    right: 4px;
  }
}

// 添加拖拽动画
@keyframes dragPulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.component-item:hover .drag-indicator {
  animation: dragPulse 2s ease-in-out infinite;
}
</style>
