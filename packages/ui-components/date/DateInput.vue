<template>
  <div
    class="crf-base-container"
    :class="{
      'crf-state-editing': editing,
      'crf-state-disabled': shouldDisableInEditMode,
      'crf-state-readonly': readonly,
      'crf-state-required': required,
    }"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{
          props.title || '日期选择'
        }}</span>
        <span v-if="props.required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="props.description" class="crf-base-description">
        {{ props.description }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="crf-base-content">
      <n-date-picker
        ref="dateRef"
        v-model:value="vModelCompatibleValue"
        :type="nativeDateType"
        :placeholder="props.placeholder || '请选择日期'"
        :format="props.format"
        :value-format="props.valueFormat"
        :size="nativeSize"
        :clearable="props.clearable"
        :disabled="shouldDisableInEditMode"
        class="u-w-full"
        @update:value="handleChange"
        @blur="handleDateBlur"
        @focus="handleDateFocus"
      />
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject } from 'vue'
import dayjs from 'dayjs'
import type {
  CrfDateProps,
  DateValue,
  SingleDateValue,
  RangeDateValue,
} from '@crf/type-definitions'
import { NDatePicker } from 'naive-ui'
import { EditorMode } from '@crf/type-definitions/core'

// 定义组件名称
defineOptions({
  name: 'CrfDate',
})

const props = withDefaults(defineProps<CrfDateProps>(), {
  modelValue: null,
  title: '日期',
  description: '请选择日期',
  placeholder: '请选择日期',
  type: 'date' as const,
  format: 'YYYY-MM-DD',
  valueFormat: 'YYYY-MM-DD',
  size: 'medium' as const,
  editable: true,
  clearable: true,
  validateEvent: true,
  medicalType: 'general' as const,
  fieldCode: undefined,
  required: false,
  disabled: false,
  readonly: false,
  visible: true,
  theme: 'default' as const,
})

const emit = defineEmits<{
  'update:modelValue': [value: DateValue]
  change: [value: DateValue]
  'calendar-change': [value: DateValue]
  'panel-change': [value: DateValue, mode: string, view: string]
  'visible-change': [visible: boolean]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  click: [event: MouseEvent]
  validate: [result: { isValid: boolean; errors: string[] }]
}>()

// ===== 验证状态 =====
const validationState = ref({
  status: '',
  message: '',
})

// 验证函数
const validate = async (value: DateValue) => {
  const errors: string[] = []

  // 必填验证
  if (props.required && !value) {
    errors.push('请选择日期')
  }

  // 更新验证状态
  if (errors.length > 0) {
    validationState.value = {
      status: 'error',
      message: errors[0] || '',
    }
    return { isValid: false, errors }
  } else {
    validationState.value = {
      status: 'success',
      message: '',
    }
    return { isValid: true, errors: [] }
  }
}

// 清除验证
const clearValidation = () => {
  validationState.value = {
    status: '',
    message: '',
  }
}

// 映射size到Naive UI期望的类型
const nativeSize = computed((): 'small' | 'medium' | 'large' => {
  if (props.size === 'medium' || !props.size) {
    return 'medium'
  }
  return props.size as 'small' | 'medium' | 'large'
})

// 映射date type到Naive UI的类型
const nativeDateType = computed(() => {
  // Naive UI 的日期选择器类型映射
  const typeMap: Record<
    string,
    | 'date'
    | 'datetime'
    | 'year'
    | 'month'
    | 'daterange'
    | 'datetimerange'
    | 'monthrange'
    | 'yearrange'
  > = {
    date: 'date',
    datetime: 'datetime',
    year: 'year',
    month: 'month',
    week: 'date', // Naive UI 不支持 week，使用 date 代替
    weekrange: 'daterange', // Naive UI 不支持 weekrange，使用 daterange 代替
    dates: 'date', // 多选日期
    daterange: 'daterange',
    datetimerange: 'datetimerange',
    monthrange: 'monthrange',
    yearrange: 'yearrange',
  }
  return typeMap[props.type as string] || 'date'
})

// ===== 编辑器状态管理 =====
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const dateRef = ref<InstanceType<typeof NDatePicker> | null>(null)
const originalValue = ref<DateValue>(null)

// 计算是否在编辑器编辑模式下且组件被禁用

const shouldDisableInEditMode = computed(() => {
  const editorMode =
    typeof editStore?.mode === 'object' && editStore?.mode !== null
      ? (editStore.mode as { value?: string }).value
      : editStore?.mode
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () => {
    return typeof editStore?.mode === 'object' && editStore?.mode !== null
      ? (editStore.mode as { value?: string }).value
      : editStore?.mode
  },
  (newMode: any, oldMode: any) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      internalValue.value = null // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  },
)

const isComponentEditable = computed(() => {
  const mode =
    typeof editStore?.mode === 'object' && editStore?.mode !== null
      ? (editStore.mode as { value?: string }).value
      : editStore?.mode
  if (mode === 'preview') return true
  if (mode === 'publish') return true
  if (mode === EditorMode.EDIT) return false
  return true
})

// 内部值管理
const internalValue = ref<DateValue>(null)

// v-model 兼容层：处理 Naive UI 不接受 null 的问题
const vModelCompatibleValue = computed({
  get() {
    if (internalValue.value == null) return null
    const isRange = nativeDateType.value.endsWith('range')
    const convert = (v: SingleDateValue): number | null => {
      if (v == null) return null
      if (typeof v === 'string') return dayjs(v).valueOf()
      if (typeof v === 'number') return v
      if (v instanceof Date) return v.getTime()
      return null
    }
    if (isRange) {
      if (
        Array.isArray(internalValue.value) &&
        internalValue.value.length === 2
      ) {
        const [start, end] = (internalValue.value as RangeDateValue).map(
          convert,
        )
        if (start !== null && end !== null)
          return [start, end] as [number, number]
      }
      return null
    }
    return convert(internalValue.value as SingleDateValue)
  },
  set(val: number | [number, number] | null) {
    if (val == null) {
      internalValue.value = null
      return
    }
    const isRange = nativeDateType.value.endsWith('range')
    const convert = (ts: number): SingleDateValue =>
      props.valueFormat ? dayjs(ts).format(props.valueFormat) : new Date(ts)
    if (isRange && Array.isArray(val) && val.length === 2) {
      internalValue.value = val.map(convert) as RangeDateValue
    } else if (!isRange && typeof val === 'number') {
      internalValue.value = convert(val)
    } else {
      internalValue.value = null
    }
  },
})

// 同步 props.modelValue 到 internalValue
watch(
  () => props.modelValue,
  (newValue: any) => {
    // 处理数据清空的情况
    if (newValue === undefined || newValue === null) {
      internalValue.value = null
    } else {
      internalValue.value = newValue
    }
  },
  { immediate: true },
)

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue: any) => {
  if (isComponentEditable.value) {
    emit('update:modelValue', newValue)
  }
})

// ===== 编辑功能 =====
const startEdit = () => {
  if (props.disabled || props.readonly || !isComponentEditable.value) return

  editing.value = true
  originalValue.value = internalValue.value

  // ElDatePicker 组件不支持 focus 方法
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)

  if (validationResult.isValid) {
    editing.value = false
  } else {
    console.log('验证失败，保持编辑状态:', validationResult.errors)
  }

  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
  editing.value = false
  clearValidation()
}

// ===== 事件处理 =====
const handleChange = async (val: number | [number, number] | null) => {
  const isRange = nativeDateType.value.endsWith('range')
  let value: DateValue = null
  if (val !== null) {
    const convert = (ts: number): SingleDateValue =>
      props.valueFormat ? dayjs(ts).format(props.valueFormat) : new Date(ts)
    if (isRange && Array.isArray(val) && val.length === 2) {
      value = val.map(convert) as RangeDateValue
    } else if (!isRange && typeof val === 'number') {
      value = convert(val)
    }
  }
  internalValue.value = value
  await validate(value)
  emit('change', value)
}

const handleDateBlur = async (event: FocusEvent) => {
  await validate(internalValue.value)
  emit('blur', event)

  // 延迟关闭编辑状态
  setTimeout(() => {
    finishEdit()
  }, 100)
}

const handleDateFocus = (event: FocusEvent) => {
  emit('focus', event)
}

// ===== 监听器 =====
watch(
  () => props.modelValue,
  async (newValue: any) => {
    if (newValue) {
      await validate(newValue)
    }
  },
)

// ===== 对外暴露的API =====
defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation,
})
</script>

<style scoped>
@import '../shared/index.scss';

/* Naive UI 组件样式覆盖 */
:deep(.n-date-picker) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border);
  --n-border-radius: var(--crf-border-radius);
  --n-line-height: var(--crf-line-height-base);
  --n-padding: var(--crf-spacing-2) var(--crf-spacing-3);
  --n-height: var(--crf-size-control-base);
}
</style>
