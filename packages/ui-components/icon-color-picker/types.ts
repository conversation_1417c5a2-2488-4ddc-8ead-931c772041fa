export interface IconColorPickerProps {
  /** 当前选中的图标名称 */
  modelValue?: string
  /** 当前选中的颜色 */
  color?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 占位符文本 */
  placeholder?: string
  /** 弹窗的最大宽度 */
  popoverMaxWidth?: number
  /** 弹窗的最大高度 */
  popoverMaxHeight?: number
}

export interface IconColorPickerEmits {
  /** 图标变化事件 */
  (e: 'update:modelValue', value: string): void
  /** 颜色变化事件 */
  (e: 'update:color', value: string): void
  /** 图标选择事件 */
  (e: 'icon-change', value: string): void
  /** 颜色选择事件 */
  (e: 'color-change', value: string): void
}

export interface ColorOption {
  name: string
  value: string
}

export interface ColorRow {
  colors: ColorOption[]
}
