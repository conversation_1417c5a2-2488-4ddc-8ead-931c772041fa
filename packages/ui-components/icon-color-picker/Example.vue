<template>
  <div class="icon-color-picker-example">
    <h2>图标颜色选择器示例</h2>

    <div class="example-section">
      <h3>基本用法</h3>
      <div class="example-item">
        <label>选择图标和颜色：</label>
        <CrfIconColorPicker v-model="basicIcon" v-model:color="basicColor" />
        <div class="result">
          <span>选中的图标：{{ basicIcon }}</span>
          <span>选中的颜色：{{ basicColor }}</span>
          <div class="preview" :style="{ color: basicColor }">
            <n-icon :size="24">
              <component :is="basicIcon" />
            </n-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>自定义占位符</h3>
      <div class="example-item">
        <label>自定义占位符文本：</label>
        <CrfIconColorPicker
          v-model="customIcon"
          v-model:color="customColor"
          placeholder="请选择一个图标"
        />
      </div>
    </div>

    <div class="example-section">
      <h3>禁用状态</h3>
      <div class="example-item">
        <label>禁用的选择器：</label>
        <CrfIconColorPicker
          v-model="disabledIcon"
          v-model:color="disabledColor"
          disabled
        />
      </div>
    </div>

    <div class="example-section">
      <h3>自定义尺寸</h3>
      <div class="example-item">
        <label>更大的弹窗：</label>
        <CrfIconColorPicker
          v-model="largeIcon"
          v-model:color="largeColor"
          :popover-max-width="400"
          :popover-max-height="500"
        />
      </div>
    </div>

    <div class="example-section">
      <h3>事件监听</h3>
      <div class="example-item">
        <label>带事件监听的选择器：</label>
        <CrfIconColorPicker
          v-model="eventIcon"
          v-model:color="eventColor"
          @icon-change="handleIconChange"
          @color-change="handleColorChange"
        />
        <div class="event-log">
          <h4>事件日志：</h4>
          <ul>
            <li v-for="(log, index) in eventLogs" :key="index">
              {{ log }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CrfIconColorPicker } from '../index'
import {
  DocumentOutline as Document,
  PersonOutline as User,
  StarOutline as Star,
  NotificationsOutline as Bell,
} from '@vicons/ionicons5'

defineOptions({
  components: {
    // 使用 Vicons 图标替代 Element Plus 图标
    Document,
    User,
    Star,
    Bell,
  },
})

// 基本用法
const basicIcon = ref('Document')
const basicColor = ref('#3b82f6')

// 自定义占位符
const customIcon = ref('')
const customColor = ref('#10b981')

// 禁用状态
const disabledIcon = ref('User')
const disabledColor = ref('#ef4444')

// 自定义尺寸
const largeIcon = ref('Star')
const largeColor = ref('#f59e0b')

// 事件监听
const eventIcon = ref('Bell')
const eventColor = ref('#8b5cf6')
const eventLogs = ref<string[]>([])

const handleIconChange = (icon: string) => {
  eventLogs.value.unshift(
    `图标变更为: ${icon} - ${new Date().toLocaleTimeString()}`,
  )
  if (eventLogs.value.length > 5) {
    eventLogs.value.pop()
  }
}

const handleColorChange = (color: string) => {
  eventLogs.value.unshift(
    `颜色变更为: ${color} - ${new Date().toLocaleTimeString()}`,
  )
  if (eventLogs.value.length > 5) {
    eventLogs.value.pop()
  }
}
</script>

<style lang="scss" scoped>
.icon-color-picker-example {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #1f2937;
    margin-bottom: 24px;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
  }

  .example-section {
    margin-bottom: 32px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #ffffff;

    h3 {
      color: #374151;
      margin-bottom: 16px;
      font-size: 18px;
    }

    .example-item {
      display: flex;
      flex-direction: column;
      gap: 12px;

      label {
        font-weight: 500;
        color: #4b5563;
      }

      .result {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 12px;
        background: #f9fafb;
        border-radius: 6px;
        font-size: 14px;
        color: #6b7280;

        .preview {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #e5e7eb;
        }
      }

      .event-log {
        margin-top: 16px;
        padding: 12px;
        background: #f3f4f6;
        border-radius: 6px;

        h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
          color: #374151;
        }

        ul {
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            padding: 4px 0;
            font-size: 12px;
            color: #6b7280;
            border-bottom: 1px solid #e5e7eb;

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}
</style>
