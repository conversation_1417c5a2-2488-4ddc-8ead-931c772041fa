/**
 * 组件注册系统 - 主入口
 * 简化版本，采用手动注册方式
 */

// 导出类型定义
export {
  ComponentCategory,
  type ComponentMeta,
  type ComponentRegistration,
  type ComponentSearchOptions,
  type ComponentSearchResult,
  type ComponentRegistrationOptions,
} from './types'

// 导出注册器
export {
  ComponentRegistry,
  componentRegistry,
  registerComponent,
  getComponent,
  getComponentMeta,
  searchComponents,
  initializeRegistry,
} from './registry'

// 导出安装器
export { withInstall, withInstallByCode } from './install'
