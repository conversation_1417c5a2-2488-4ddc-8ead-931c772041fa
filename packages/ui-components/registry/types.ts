/**
 * 统一的组件类型定义
 */
import type { Component } from 'vue'

// 组件分类
export enum ComponentCategory {
  BASIC = 'basic', // 基础组件
  INPUT = 'input', // 输入组件
  DISPLAY = 'display', // 展示组件
  LAYOUT = 'layout', // 布局组件
  NAVIGATION = 'navigation', // 导航组件
  FEEDBACK = 'feedback', // 反馈组件
  DATA = 'data', // 数据组件
  ADVANCED = 'advanced', // 高级组件
  MEDICAL = 'medical', // 医疗专用组件
  CUSTOM = 'custom', // 自定义组件
}

// 组件元数据
export interface ComponentMeta {
  code: string
  name: string
  label: string
  category: ComponentCategory
  icon: string
  iconColor?: string
  description: string
  version: string
  author?: string
  keywords?: string[]
  tags?: string[]
  order?: number
  visible?: boolean
  deprecated?: boolean
  experimental?: boolean
  // 属性定义
  props?: Record<
    string,
    {
      type: string
      default?: unknown
      required?: boolean
      description?: string
      options?: Record<string, unknown>[]
    }
  >
  // 事件定义
  events?: Record<
    string,
    {
      description?: string
      payload?: unknown
    }
  >
  // 插槽定义
  slots?: Record<
    string,
    {
      description?: string
      props?: Record<string, unknown>
    }
  >
}

// 组件注册配置
export interface ComponentRegistration {
  code: string
  component: Component
  configComponent?: Component
  meta: ComponentMeta
  schema?: Record<string, unknown>
}

// 组件搜索选项
export interface ComponentSearchOptions {
  query?: string
  category?: ComponentCategory
  tags?: string[]
  keywords?: string[]
  includeDeprecated?: boolean
  includeExperimental?: boolean
  limit?: number
  offset?: number
}

// 组件搜索结果
export interface ComponentSearchResult {
  components: ComponentMeta[]
  total: number
  hasMore: boolean
}

// 组件注册选项
export interface ComponentRegistrationOptions {
  configComponent?: Component
  schema?: Record<string, unknown>
  autoInstall?: boolean
  order?: number
}
