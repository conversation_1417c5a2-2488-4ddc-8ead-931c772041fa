/**
 * 简化的组件注册系统
 * 采用手动注册方式，提供更好的性能和可维护性
 */

import type { App, Component } from 'vue'
import {
  ComponentCategory,
  type ComponentMeta,
  type ComponentRegistration,
  type ComponentSearchOptions,
  type ComponentSearchResult,
  type ComponentRegistrationOptions,
} from './types'

/**
 * 组件注册器
 */
export class ComponentRegistry {
  private static instance: ComponentRegistry
  private components = new Map<string, ComponentRegistration>()
  private metadata = new Map<string, ComponentMeta>()
  private nameMap = new Map<string, string>() // name -> code
  private categoryMap = new Map<ComponentCategory, Set<string>>()
  private initialized = false

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ComponentRegistry {
    if (!ComponentRegistry.instance) {
      ComponentRegistry.instance = new ComponentRegistry()
    }
    return ComponentRegistry.instance
  }

  /**
   * 初始化注册器
   */
  initialize(): void {
    if (this.initialized) return

    // 初始化分类映射
    Object.values(ComponentCategory).forEach((category) => {
      this.categoryMap.set(category, new Set())
    })

    this.initialized = true
  }

  /**
   * 注册单个组件
   */
  register(
    code: string,
    component: Component,
    meta: ComponentMeta,
    options: ComponentRegistrationOptions = {},
  ): ComponentRegistration {
    // 验证参数
    this.validateRegistration(code, component, meta)

    // 创建注册信息
    const registration: ComponentRegistration = {
      code,
      component,
      configComponent: options.configComponent,
      meta: { ...meta, code },
      schema: options.schema,
    }

    // 保存注册信息
    this.components.set(code, registration)
    this.metadata.set(code, meta)
    this.nameMap.set(meta.name, code)

    // 更新分类映射
    if (!this.categoryMap.has(meta.category)) {
      this.categoryMap.set(meta.category, new Set())
    }
    this.categoryMap.get(meta.category)!.add(code)

    return registration
  }

  /**
   * 批量注册组件
   */
  registerBatch(
    components: Array<{
      code: string
      component: Record<string, unknown>
      meta: ComponentMeta
      options?: ComponentRegistrationOptions
    }>,
  ): ComponentRegistration[] {
    return components.map(({ code, component, meta, options = {} }) =>
      this.register(code, component, meta, options),
    )
  }

  /**
   * 注册所有组件到Vue应用
   */
  installAll(app: App): void {
    this.components.forEach((registration, _code) => {
      app.component(registration.meta.name, registration.component)
    })

    console.log(`🎉 已注册 ${this.components.size} 个组件到Vue应用`)
  }

  /**
   * 获取组件
   */
  getComponent(code: string): Component | null {
    const registration = this.components.get(code)
    return registration?.component as Component | null
  }

  /**
   * 获取组件配置组件
   */
  getConfigComponent(code: string): Component | null {
    const registration = this.components.get(code)
    return registration?.configComponent as Component | null
  }

  /**
   * 获取组件元数据
   */
  getMeta(code: string): ComponentMeta | null {
    return this.metadata.get(code) || null
  }

  /**
   * 获取组件Schema
   */
  getSchema(code: string): Record<string, unknown> | null {
    const registration = this.components.get(code)
    return registration?.schema || null
  }

  /**
   * 根据名称获取组件代码
   */
  getCodeByName(name: string): string | null {
    return this.nameMap.get(name) || null
  }

  /**
   * 获取所有组件代码
   */
  getAllCodes(): string[] {
    return Array.from(this.components.keys())
  }

  /**
   * 获取所有组件元数据
   */
  getAllMeta(): ComponentMeta[] {
    return Array.from(this.metadata.values())
  }

  /**
   * 检查组件是否已注册
   */
  isRegistered(code: string): boolean {
    return this.components.has(code)
  }

  /**
   * 按分类获取组件
   */
  getComponentsByCategory(category: ComponentCategory): ComponentMeta[] {
    const codes = this.categoryMap.get(category) || new Set()
    return Array.from(codes)
      .map((code) => this.metadata.get(code)!)
      .filter(Boolean)
      .sort((a, b) => (a.order || 0) - (b.order || 0))
  }

  /**
   * 搜索组件
   */
  search(options: ComponentSearchOptions = {}): ComponentSearchResult {
    const {
      query = '',
      category,
      tags = [],
      keywords = [],
      includeDeprecated = false,
      includeExperimental = true,
      limit = 50,
      offset = 0,
    } = options

    let components = this.getAllMeta()

    // 按分类过滤
    if (category) {
      components = components.filter((meta) => meta.category === category)
    }

    // 按弃用状态过滤
    if (!includeDeprecated) {
      components = components.filter((meta) => !meta.deprecated)
    }

    // 按实验状态过滤
    if (!includeExperimental) {
      components = components.filter((meta) => !meta.experimental)
    }

    // 按查询字符串过滤
    if (query) {
      const lowerQuery = query.toLowerCase()
      components = components.filter(
        (meta) =>
          meta.name.toLowerCase().includes(lowerQuery) ||
          meta.label.toLowerCase().includes(lowerQuery) ||
          meta.description.toLowerCase().includes(lowerQuery) ||
          (meta.keywords || []).some((keyword) =>
            keyword.toLowerCase().includes(lowerQuery),
          ),
      )
    }

    // 按标签过滤
    if (tags.length > 0) {
      components = components.filter((meta) =>
        tags.some((tag) => (meta.tags || []).includes(tag)),
      )
    }

    // 按关键字过滤
    if (keywords.length > 0) {
      components = components.filter((meta) =>
        keywords.some((keyword) => (meta.keywords || []).includes(keyword)),
      )
    }

    // 排序
    components.sort((a, b) => {
      // 首先按order排序
      if (a.order !== b.order) {
        return (a.order || 0) - (b.order || 0)
      }
      // 然后按名称排序
      return a.name.localeCompare(b.name)
    })

    // 分页
    const total = components.length
    const paginatedComponents = components.slice(offset, offset + limit)
    const hasMore = offset + limit < total

    return {
      components: paginatedComponents,
      total,
      hasMore,
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const categories = new Map<ComponentCategory, number>()
    let deprecatedCount = 0
    let experimentalCount = 0

    this.metadata.forEach((meta) => {
      categories.set(meta.category, (categories.get(meta.category) || 0) + 1)
      if (meta.deprecated) deprecatedCount++
      if (meta.experimental) experimentalCount++
    })

    return {
      totalComponents: this.components.size,
      categories: Object.fromEntries(categories),
      deprecatedCount,
      experimentalCount,
    }
  }

  /**
   * 注销组件
   */
  unregister(code: string): boolean {
    const registration = this.components.get(code)
    if (!registration) return false

    // 从各个映射中删除
    this.components.delete(code)
    this.metadata.delete(code)
    this.nameMap.delete(registration.meta.name)

    // 从分类映射中删除
    const categorySet = this.categoryMap.get(registration.meta.category)
    if (categorySet) {
      categorySet.delete(code)
    }

    return true
  }

  /**
   * 清除所有组件
   */
  clear(): void {
    this.components.clear()
    this.metadata.clear()
    this.nameMap.clear()
    this.categoryMap.clear()
    this.initialized = false
  }

  /**
   * 验证注册参数
   */
  private validateRegistration(
    code: string,
    component: Component,
    meta: ComponentMeta,
  ): void {
    if (!code || typeof code !== 'string') {
      throw new Error('组件代码必须是非空字符串')
    }

    if (!component) {
      throw new Error('组件不能为空')
    }

    if (!meta || typeof meta !== 'object') {
      throw new Error('组件元数据必须是对象')
    }

    if (!meta.name || typeof meta.name !== 'string') {
      throw new Error('组件名称必须是非空字符串')
    }

    if (this.components.has(code)) {
      throw new Error(`组件 ${code} 已经注册`)
    }

    if (this.nameMap.has(meta.name)) {
      throw new Error(`组件名称 ${meta.name} 已经被使用`)
    }
  }
}

// 创建全局实例
export const componentRegistry = ComponentRegistry.getInstance()

// 便捷函数导出
export const registerComponent = (
  code: string,
  component: Component,
  meta: ComponentMeta,
  options?: ComponentRegistrationOptions,
) => {
  return componentRegistry.register(code, component, meta, options)
}

export const getComponent = (code: string): Component | null => {
  return componentRegistry.getComponent(code) as Component | null
}

export const getComponentMeta = (code: string) => {
  return componentRegistry.getMeta(code)
}

export const searchComponents = (options: ComponentSearchOptions = {}) => {
  return componentRegistry.search(options)
}

export const initializeRegistry = () => {
  return componentRegistry.initialize()
}
