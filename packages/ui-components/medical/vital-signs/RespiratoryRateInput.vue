<template>
  <div class="respiratory-rate-input w-full">
    <!-- 标签 -->
    <div v-if="showLabel" class="mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
        <span v-if="medicalCode" class="text-xs text-gray-500 ml-2"
          >({{ medicalCode }})</span
        >
      </label>
    </div>

    <!-- 呼吸频率输入区域 -->
    <div
      class="respiratory-container p-3 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600"
    >
      <div class="flex items-center gap-3">
        <!-- 呼吸频率数值输入 -->
        <div class="respiratory-value flex-1">
          <div class="relative">
            <input
              v-model.number="respiratoryValue"
              type="number"
              :placeholder="placeholder"
              :readonly="readonly"
              :disabled="disabled"
              :class="[
                'w-full px-3 py-2 text-center text-lg font-medium border rounded-md',
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
                {
                  'border-red-500 focus:ring-red-500 focus:border-red-500':
                    hasError,
                  'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500':
                    hasWarning,
                  'border-green-500 focus:ring-green-500 focus:border-green-500':
                    isNormal,
                  'bg-gray-50 dark:bg-gray-600': readonly,
                  'opacity-50 cursor-not-allowed': disabled,
                },
              ]"
              @blur="validateRespiratoryRate"
              @input="handleInput"
            />

            <!-- 呼吸图标 -->
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
              <svg
                :class="[
                  'w-5 h-5',
                  respiratoryValue && isNormal
                    ? 'text-blue-500 animate-breathing'
                    : 'text-gray-400',
                ]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- 单位显示 -->
        <div class="unit-display">
          <span class="text-sm text-gray-600 dark:text-gray-400 px-2">{{
            unit
          }}</span>
        </div>

        <!-- 呼吸模式选择 -->
        <div class="breathing-pattern">
          <select
            v-model="breathingPattern"
            :disabled="disabled || readonly"
            class="px-3 py-2 border rounded-md text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @change="handlePatternChange"
          >
            <option value="normal">正常</option>
            <option value="shallow">浅快</option>
            <option value="deep">深慢</option>
            <option value="irregular">不规律</option>
            <option value="labored">费力</option>
            <option value="cheyne-stokes">潮式呼吸</option>
            <option value="kussmaul">库斯毛呼吸</option>
            <option value="other">其他</option>
          </select>
        </div>
      </div>

      <!-- 呼吸频率评估显示 -->
      <div v-if="respiratoryAssessment" class="respiratory-assessment mt-3">
        <div
          :class="[
            'flex items-center justify-between p-2 rounded-md text-sm',
            getAssessmentClass(respiratoryAssessment.level),
          ]"
        >
          <div class="flex items-center gap-2">
            <span class="font-medium">{{
              getRespiratoryIcon(respiratoryAssessment.level)
            }}</span>
            <span>{{ respiratoryAssessment.interpretation }}</span>
          </div>
          <span class="text-xs">{{
            respiratoryAssessment.level.toUpperCase()
          }}</span>
        </div>
        <div
          v-if="respiratoryAssessment.recommendation"
          class="text-xs text-gray-600 dark:text-gray-400 mt-1 px-2"
        >
          {{ respiratoryAssessment.recommendation }}
        </div>
      </div>

      <!-- 呼吸频率分类 -->
      <div v-if="respiratoryCategory" class="respiratory-category mt-2">
        <div
          :class="[
            'text-xs px-2 py-1 rounded',
            getCategoryClass(respiratoryCategory.type),
          ]"
        >
          {{ respiratoryCategory.description }}
          <span v-if="respiratoryCategory.ageGroup" class="ml-1"
            >({{ respiratoryCategory.ageGroup }})</span
          >
        </div>
      </div>

      <!-- 呼吸模式异常警告 -->
      <div v-if="breathingPattern !== 'normal'" class="pattern-warning mt-2">
        <div
          class="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md"
        >
          <svg
            class="w-4 h-4 text-yellow-600 dark:text-yellow-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-sm text-yellow-800 dark:text-yellow-200">
            检测到呼吸异常: {{ getPatternDisplayName(breathingPattern) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 参考值范围 -->
    <div
      v-if="showReferenceRange && referenceRange"
      class="reference-range mt-2"
    >
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <span class="font-medium">正常范围:</span>
        <span class="ml-1">{{ formatReferenceRange(referenceRange) }}</span>
      </div>
    </div>

    <!-- 年龄相关的呼吸频率范围 -->
    <div v-if="showAgeRanges" class="age-ranges mt-2">
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <div class="grid grid-cols-2 gap-2">
          <div>
            <span class="font-medium">成人:</span>
            <span class="ml-1">12-20 次/分</span>
          </div>
          <div>
            <span class="font-medium">儿童:</span>
            <span class="ml-1">20-30 次/分</span>
          </div>
          <div>
            <span class="font-medium">婴儿:</span>
            <span class="ml-1">30-60 次/分</span>
          </div>
          <div>
            <span class="font-medium">新生儿:</span>
            <span class="ml-1">40-60 次/分</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误和警告信息 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <p class="text-xs text-red-600 dark:text-red-400">{{ errorMessage }}</p>
    </div>
    <div v-if="warningMessage" class="warning-message mt-2">
      <p class="text-xs text-yellow-600 dark:text-yellow-400">
        {{ warningMessage }}
      </p>
    </div>

    <!-- 测量时间 -->
    <div v-if="showTimestamp && timestamp" class="timestamp mt-2">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        测量时间: {{ formatTimestamp(timestamp) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMedicalValidation } from '../composables/useMedicalValidation'
import { useMedicalAssessment } from '../composables/useMedicalAssessment'
import type {
  ReferenceRange,
  ValueAssessment,
  AssessmentLevel,
} from '../types/medical'

// 呼吸模式类型
type BreathingPattern =
  | 'normal'
  | 'shallow'
  | 'deep'
  | 'irregular'
  | 'labored'
  | 'cheyne-stokes'
  | 'kussmaul'
  | 'other'

// Props定义
interface Props {
  modelValue?: {
    value?: number
    unit?: string
    pattern?: BreathingPattern
    timestamp?: Date
  }
  label?: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  disabled?: boolean
  showLabel?: boolean
  showReferenceRange?: boolean
  showAgeRanges?: boolean
  showTimestamp?: boolean
  unit?: string
  medicalCode?: string
  autoAssess?: boolean
  patientAge?: number
}

const props = withDefaults(defineProps<Props>(), {
  label: '呼吸频率',
  placeholder: '请输入呼吸频率',
  showLabel: true,
  showReferenceRange: true,
  showAgeRanges: false,
  showTimestamp: false,
  unit: '次/分',
  medicalCode: 'vital-signs:respiratory-rate',
  autoAssess: true,
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: typeof props.modelValue]
  change: [value: typeof props.modelValue]
  'assessment-change': [assessment: ValueAssessment | null]
  'pattern-change': [pattern: BreathingPattern]
}>()

// 响应式状态
const respiratoryValue = ref<number | undefined>(props.modelValue?.value)
const breathingPattern = ref<BreathingPattern>(
  props.modelValue?.pattern || 'normal',
)
const timestamp = ref(props.modelValue?.timestamp || new Date())

// 使用组合式API
const {
  validate,
  addError,
  addWarning,
  clearValidation,
  hasError,
  hasWarning,
  errorMessage,
  warningMessage,
} = useMedicalValidation()
const { assessValue, getReferenceRange, formatReferenceRange } =
  useMedicalAssessment()

// 评估状态
const respiratoryAssessment = ref<ValueAssessment | null>(null)
const referenceRange = ref<ReferenceRange | null>(null)

// 计算属性
const isNormal = computed(() => {
  return respiratoryAssessment.value?.level === 'normal'
})

const respiratoryCategory = computed(() => {
  if (!respiratoryValue.value) return null

  const rr = respiratoryValue.value
  const age = props.patientAge

  // 根据年龄分组判断呼吸频率分类
  if (age && age < 1) {
    // 新生儿 (0-1岁)
    if (rr < 30)
      return { type: 'bradypnea', description: '呼吸过缓', ageGroup: '新生儿' }
    if (rr > 60)
      return { type: 'tachypnea', description: '呼吸过速', ageGroup: '新生儿' }
    return { type: 'normal', description: '正常呼吸', ageGroup: '新生儿' }
  } else if (age && age < 12) {
    // 儿童 (1-12岁)
    if (rr < 15)
      return { type: 'bradypnea', description: '呼吸过缓', ageGroup: '儿童' }
    if (rr > 30)
      return { type: 'tachypnea', description: '呼吸过速', ageGroup: '儿童' }
    return { type: 'normal', description: '正常呼吸', ageGroup: '儿童' }
  } else {
    // 成人 (>12岁)
    if (rr < 12)
      return { type: 'bradypnea', description: '呼吸过缓', ageGroup: '成人' }
    if (rr > 20)
      return { type: 'tachypnea', description: '呼吸过速', ageGroup: '成人' }
    return { type: 'normal', description: '正常呼吸', ageGroup: '成人' }
  }
})

// 方法
const updateModelValue = () => {
  const value = {
    value: respiratoryValue.value,
    unit: props.unit,
    pattern: breathingPattern.value,
    timestamp: timestamp.value,
  }
  emit('update:modelValue', value)
  emit('change', value)
}

const handleInput = () => {
  clearValidation()
  updateModelValue()
  if (props.autoAssess) {
    assessRespiratoryRate()
  }
}

const handlePatternChange = () => {
  updateModelValue()
  emit('pattern-change', breathingPattern.value)
}

const validateRespiratoryRate = () => {
  clearValidation()

  if (props.required && !respiratoryValue.value) {
    addError('呼吸频率为必填项')
    return false
  }

  if (respiratoryValue.value) {
    if (respiratoryValue.value < 1 || respiratoryValue.value > 100) {
      addError('呼吸频率数值超出合理范围')
      return false
    }

    if (respiratoryValue.value < 8 || respiratoryValue.value > 40) {
      addWarning('呼吸频率数值异常，请确认')
    }
  }

  return true
}

const assessRespiratoryRate = async () => {
  if (respiratoryValue.value) {
    try {
      const assessment = await assessValue({
        testCode: 'respiratory_rate',
        value: respiratoryValue.value,
        unit: props.unit,
        age: props.patientAge,
      })
      respiratoryAssessment.value = assessment
      emit('assessment-change', assessment)
    } catch (error) {
      console.error('Respiratory rate assessment failed:', error)
    }
  }
}

const loadReferenceRange = async () => {
  if (props.showReferenceRange) {
    try {
      const range = await getReferenceRange('respiratory_rate', {
        age: props.patientAge,
      })
      referenceRange.value = range
    } catch (error) {
      console.error('Failed to load reference range:', error)
    }
  }
}

// 显示相关函数
const getPatternDisplayName = (pattern: BreathingPattern): string => {
  const patternNames = {
    normal: '正常',
    shallow: '浅快',
    deep: '深慢',
    irregular: '不规律',
    labored: '费力',
    'cheyne-stokes': '潮式呼吸',
    kussmaul: '库斯毛呼吸',
    other: '其他',
  }
  return patternNames[pattern]
}

const getRespiratoryIcon = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return '🫁'
    case 'low':
      return '😴'
    case 'high':
      return '😤'
    case 'critical':
      return '🚨'
    default:
      return '🫁'
  }
}

const getAssessmentClass = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'low':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'high':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getCategoryClass = (type: string): string => {
  switch (type) {
    case 'normal':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
    case 'bradypnea':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
    case 'tachypnea':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const formatTimestamp = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

// 监听器
watch([respiratoryValue, breathingPattern], () => {
  updateModelValue()
})

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      respiratoryValue.value = newValue.value
      breathingPattern.value = newValue.pattern || 'normal'
      timestamp.value = newValue.timestamp || new Date()
    }
  },
  { deep: true },
)

watch(
  () => props.patientAge,
  () => {
    loadReferenceRange()
    if (props.autoAssess) {
      assessRespiratoryRate()
    }
  },
)

// 生命周期
onMounted(() => {
  loadReferenceRange()
})
</script>

<style scoped>
.respiratory-rate-input {
  @apply relative;
}

.respiratory-container {
  @apply transition-all duration-200;
}

.respiratory-container:focus-within {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

@keyframes breathing {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.animate-breathing {
  animation: breathing 3s ease-in-out infinite;
}
</style>
