<template>
  <div class="temperature-input w-full">
    <!-- 标签 -->
    <div v-if="showLabel" class="mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
        <span v-if="medicalCode" class="text-xs text-gray-500 ml-2"
          >({{ medicalCode }})</span
        >
      </label>
    </div>

    <!-- 体温输入区域 -->
    <div
      class="temperature-container p-3 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600"
    >
      <div class="flex items-center gap-3">
        <!-- 体温数值输入 -->
        <div class="temperature-value flex-1">
          <div class="relative">
            <input
              v-model.number="temperatureValue"
              type="number"
              step="0.1"
              :placeholder="placeholder"
              :readonly="readonly"
              :disabled="disabled"
              :class="[
                'w-full px-3 py-2 text-center text-lg font-medium border rounded-md',
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
                {
                  'border-red-500 focus:ring-red-500 focus:border-red-500':
                    hasError,
                  'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500':
                    hasWarning,
                  'border-green-500 focus:ring-green-500 focus:border-green-500':
                    isNormal,
                  'bg-gray-50 dark:bg-gray-600': readonly,
                  'opacity-50 cursor-not-allowed': disabled,
                },
              ]"
              @blur="validateTemperature"
              @input="handleInput"
            />

            <!-- 温度计图标 -->
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
              <svg
                class="w-5 h-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M10 2a2 2 0 00-2 2v7.586l-1.707 1.707a1 1 0 101.414 1.414L10 12.414l2.293 2.293a1 1 0 001.414-1.414L12 11.586V4a2 2 0 00-2-2z"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- 单位选择 -->
        <div class="unit-selector">
          <select
            v-model="selectedUnit"
            :disabled="disabled || readonly"
            class="px-3 py-2 border rounded-md text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @change="handleUnitChange"
          >
            <option value="celsius">°C</option>
            <option value="fahrenheit">°F</option>
            <option value="kelvin">K</option>
          </select>
        </div>

        <!-- 测量部位选择 -->
        <div class="measurement-site">
          <select
            v-model="measurementSite"
            :disabled="disabled || readonly"
            class="px-3 py-2 border rounded-md text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @change="handleSiteChange"
          >
            <option value="oral">口腔</option>
            <option value="rectal">直肠</option>
            <option value="axillary">腋下</option>
            <option value="tympanic">耳温</option>
            <option value="temporal">额温</option>
          </select>
        </div>
      </div>

      <!-- 体温评估显示 -->
      <div v-if="temperatureAssessment" class="temperature-assessment mt-3">
        <div
          :class="[
            'flex items-center justify-between p-2 rounded-md text-sm',
            getAssessmentClass(temperatureAssessment.level),
          ]"
        >
          <div class="flex items-center gap-2">
            <span class="font-medium">{{
              getTemperatureIcon(temperatureAssessment.level)
            }}</span>
            <span>{{ temperatureAssessment.interpretation }}</span>
          </div>
          <span class="text-xs">{{
            temperatureAssessment.level.toUpperCase()
          }}</span>
        </div>
        <div
          v-if="temperatureAssessment.recommendation"
          class="text-xs text-gray-600 dark:text-gray-400 mt-1 px-2"
        >
          {{ temperatureAssessment.recommendation }}
        </div>
      </div>

      <!-- 体温分类 -->
      <div v-if="temperatureCategory" class="temperature-category mt-2">
        <div
          :class="[
            'text-xs px-2 py-1 rounded',
            getCategoryClass(temperatureCategory.type),
          ]"
        >
          {{ temperatureCategory.description }}
        </div>
      </div>
    </div>

    <!-- 参考值范围 -->
    <div
      v-if="showReferenceRange && referenceRange"
      class="reference-range mt-2"
    >
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <span class="font-medium"
          >正常范围 ({{ getSiteDisplayName(measurementSite) }}):</span
        >
        <span class="ml-1">{{ formatReferenceRange(referenceRange) }}</span>
      </div>
    </div>

    <!-- 单位转换显示 -->
    <div
      v-if="showConversions && temperatureValue"
      class="unit-conversions mt-2"
    >
      <div
        class="text-xs text-gray-600 dark:text-gray-400 grid grid-cols-3 gap-2"
      >
        <div v-if="selectedUnit !== 'celsius'">
          <span class="font-medium">摄氏度:</span>
          <span class="ml-1"
            >{{
              convertToCelsius(temperatureValue, selectedUnit).toFixed(1)
            }}°C</span
          >
        </div>
        <div v-if="selectedUnit !== 'fahrenheit'">
          <span class="font-medium">华氏度:</span>
          <span class="ml-1"
            >{{
              convertToFahrenheit(temperatureValue, selectedUnit).toFixed(1)
            }}°F</span
          >
        </div>
        <div v-if="selectedUnit !== 'kelvin'">
          <span class="font-medium">开尔文:</span>
          <span class="ml-1"
            >{{
              convertToKelvin(temperatureValue, selectedUnit).toFixed(1)
            }}K</span
          >
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <p class="text-xs text-red-600 dark:text-red-400">{{ errorMessage }}</p>
    </div>

    <!-- 警告信息 -->
    <div v-if="warningMessage" class="warning-message mt-2">
      <p class="text-xs text-yellow-600 dark:text-yellow-400">
        {{ warningMessage }}
      </p>
    </div>

    <!-- 测量时间 -->
    <div v-if="showTimestamp && timestamp" class="timestamp mt-2">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        测量时间: {{ formatTimestamp(timestamp) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMedicalValidation } from '../composables/useMedicalValidation'
import { useMedicalAssessment } from '../composables/useMedicalAssessment'
import type {
  ReferenceRange,
  ValueAssessment,
  AssessmentLevel,
} from '../types/medical'

// 测量部位类型
type MeasurementSite = 'oral' | 'rectal' | 'axillary' | 'tympanic' | 'temporal'
type TemperatureUnit = 'celsius' | 'fahrenheit' | 'kelvin'

// Props定义
interface Props {
  modelValue?: {
    value?: number
    unit?: TemperatureUnit
    site?: MeasurementSite
    timestamp?: Date
  }
  label?: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  disabled?: boolean
  showLabel?: boolean
  showReferenceRange?: boolean
  showConversions?: boolean
  showTimestamp?: boolean
  medicalCode?: string
  autoAssess?: boolean
  defaultUnit?: TemperatureUnit
  defaultSite?: MeasurementSite
}

const props = withDefaults(defineProps<Props>(), {
  label: '体温',
  placeholder: '请输入体温',
  showLabel: true,
  showReferenceRange: true,
  showConversions: false,
  showTimestamp: false,
  medicalCode: 'vital-signs:temperature',
  autoAssess: true,
  defaultUnit: 'celsius',
  defaultSite: 'oral',
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: typeof props.modelValue]
  change: [value: typeof props.modelValue]
  'assessment-change': [assessment: ValueAssessment | null]
}>()

// 响应式状态
const temperatureValue = ref<number | undefined>(props.modelValue?.value)
const selectedUnit = ref<TemperatureUnit>(
  props.modelValue?.unit || props.defaultUnit,
)
const measurementSite = ref<MeasurementSite>(
  props.modelValue?.site || props.defaultSite,
)
const timestamp = ref(props.modelValue?.timestamp || new Date())

// 使用组合式API
const {
  validate,
  addError,
  addWarning,
  clearValidation,
  hasError,
  hasWarning,
  errorMessage,
  warningMessage,
} = useMedicalValidation()
const { assessValue, getReferenceRange, formatReferenceRange } =
  useMedicalAssessment()

// 评估状态
const temperatureAssessment = ref<ValueAssessment | null>(null)
const referenceRange = ref<ReferenceRange | null>(null)

// 计算属性
const isNormal = computed(() => {
  return temperatureAssessment.value?.level === 'normal'
})

const temperatureCategory = computed(() => {
  if (!temperatureValue.value) return null

  // 转换为摄氏度进行分类
  const celsiusValue = convertToCelsius(
    temperatureValue.value,
    selectedUnit.value,
  )

  if (celsiusValue < 35.0) {
    return { type: 'hypothermia', description: '体温过低' }
  } else if (celsiusValue >= 35.0 && celsiusValue <= 37.2) {
    return { type: 'normal', description: '正常体温' }
  } else if (celsiusValue > 37.2 && celsiusValue <= 38.0) {
    return { type: 'low-fever', description: '低热' }
  } else if (celsiusValue > 38.0 && celsiusValue <= 39.0) {
    return { type: 'moderate-fever', description: '中等发热' }
  } else if (celsiusValue > 39.0 && celsiusValue <= 41.0) {
    return { type: 'high-fever', description: '高热' }
  } else {
    return { type: 'hyperthermia', description: '超高热' }
  }
})

// 方法
const updateModelValue = () => {
  const value = {
    value: temperatureValue.value,
    unit: selectedUnit.value,
    site: measurementSite.value,
    timestamp: timestamp.value,
  }
  emit('update:modelValue', value)
  emit('change', value)
}

const handleInput = () => {
  clearValidation()
  updateModelValue()
  if (props.autoAssess) {
    assessTemperature()
  }
}

const handleUnitChange = () => {
  updateModelValue()
  if (props.autoAssess) {
    assessTemperature()
  }
}

const handleSiteChange = () => {
  updateModelValue()
  loadReferenceRange()
  if (props.autoAssess) {
    assessTemperature()
  }
}

const validateTemperature = () => {
  clearValidation()

  if (props.required && !temperatureValue.value) {
    addError('体温为必填项')
    return false
  }

  if (temperatureValue.value) {
    const celsiusValue = convertToCelsius(
      temperatureValue.value,
      selectedUnit.value,
    )

    if (celsiusValue < 25 || celsiusValue > 50) {
      addError('体温数值超出合理范围')
      return false
    }

    if (celsiusValue < 30 || celsiusValue > 45) {
      addWarning('体温数值异常，请确认')
    }
  }

  return true
}

const assessTemperature = async () => {
  if (temperatureValue.value) {
    try {
      // 转换为摄氏度进行评估
      const celsiusValue = convertToCelsius(
        temperatureValue.value,
        selectedUnit.value,
      )
      const assessment = await assessValue({
        testCode: 'temperature',
        value: celsiusValue,
        unit: 'celsius',
      })
      temperatureAssessment.value = assessment
      emit('assessment-change', assessment)
    } catch (error) {
      console.error('Temperature assessment failed:', error)
    }
  }
}

const loadReferenceRange = async () => {
  if (props.showReferenceRange) {
    try {
      const range = await getReferenceRange('temperature', {
        // 可以根据测量部位调整参考值
      })
      referenceRange.value = range
    } catch (error) {
      console.error('Failed to load reference range:', error)
    }
  }
}

// 温度转换函数
const convertToCelsius = (value: number, fromUnit: TemperatureUnit): number => {
  switch (fromUnit) {
    case 'celsius':
      return value
    case 'fahrenheit':
      return ((value - 32) * 5) / 9
    case 'kelvin':
      return value - 273.15
    default:
      return value
  }
}

const convertToFahrenheit = (
  value: number,
  fromUnit: TemperatureUnit,
): number => {
  const celsius = convertToCelsius(value, fromUnit)
  return (celsius * 9) / 5 + 32
}

const convertToKelvin = (value: number, fromUnit: TemperatureUnit): number => {
  const celsius = convertToCelsius(value, fromUnit)
  return celsius + 273.15
}

// 显示相关函数
const getSiteDisplayName = (site: MeasurementSite): string => {
  const siteNames = {
    oral: '口腔',
    rectal: '直肠',
    axillary: '腋下',
    tympanic: '耳温',
    temporal: '额温',
  }
  return siteNames[site]
}

const getTemperatureIcon = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return '🌡️'
    case 'low':
      return '❄️'
    case 'high':
      return '🔥'
    case 'critical':
      return '🚨'
    default:
      return '🌡️'
  }
}

const getAssessmentClass = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'low':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'high':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getCategoryClass = (type: string): string => {
  switch (type) {
    case 'normal':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
    case 'hypothermia':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
    case 'low-fever':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'moderate-fever':
      return 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400'
    case 'high-fever':
    case 'hyperthermia':
      return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const formatTimestamp = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

// 监听器
watch([temperatureValue, selectedUnit, measurementSite], () => {
  updateModelValue()
})

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      temperatureValue.value = newValue.value
      selectedUnit.value = newValue.unit || props.defaultUnit
      measurementSite.value = newValue.site || props.defaultSite
      timestamp.value = newValue.timestamp || new Date()
    }
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  loadReferenceRange()
})
</script>

<style scoped>
.temperature-input {
  @apply relative;
}

.temperature-container {
  @apply transition-all duration-200;
}

.temperature-container:focus-within {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.temperature-value input {
  @apply transition-all duration-200;
}

.temperature-assessment {
  @apply transition-all duration-200;
}
</style>
