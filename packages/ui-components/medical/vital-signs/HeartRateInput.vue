<template>
  <div class="heart-rate-input w-full">
    <!-- 标签 -->
    <div v-if="showLabel" class="mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
        <span v-if="medicalCode" class="text-xs text-gray-500 ml-2"
          >({{ medicalCode }})</span
        >
      </label>
    </div>

    <!-- 心率输入区域 -->
    <div
      class="heart-rate-container p-3 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600"
    >
      <div class="flex items-center gap-3">
        <!-- 心率数值输入 -->
        <div class="heart-rate-value flex-1">
          <div class="relative">
            <input
              v-model.number="heartRateValue"
              type="number"
              :placeholder="placeholder"
              :readonly="readonly"
              :disabled="disabled"
              :class="[
                'w-full px-3 py-2 text-center text-lg font-medium border rounded-md',
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
                {
                  'border-red-500 focus:ring-red-500 focus:border-red-500':
                    hasError,
                  'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500':
                    hasWarning,
                  'border-green-500 focus:ring-green-500 focus:border-green-500':
                    isNormal,
                  'bg-gray-50 dark:bg-gray-600': readonly,
                  'opacity-50 cursor-not-allowed': disabled,
                },
              ]"
              @blur="validateHeartRate"
              @input="handleInput"
            />

            <!-- 心跳图标 -->
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
              <svg
                :class="[
                  'w-5 h-5',
                  heartRateValue && isNormal
                    ? 'text-red-500 animate-pulse'
                    : 'text-gray-400',
                ]"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- 单位显示 -->
        <div class="unit-display">
          <span class="text-sm text-gray-600 dark:text-gray-400 px-2">{{
            unit
          }}</span>
        </div>

        <!-- 心律选择 -->
        <div class="rhythm-selector">
          <select
            v-model="rhythm"
            :disabled="disabled || readonly"
            class="px-3 py-2 border rounded-md text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @change="handleRhythmChange"
          >
            <option value="regular">规律</option>
            <option value="irregular">不规律</option>
            <option value="atrial-fibrillation">房颤</option>
            <option value="atrial-flutter">房扑</option>
            <option value="sinus-tachycardia">窦性心动过速</option>
            <option value="sinus-bradycardia">窦性心动过缓</option>
            <option value="other">其他</option>
          </select>
        </div>
      </div>

      <!-- 心率评估显示 -->
      <div v-if="heartRateAssessment" class="heart-rate-assessment mt-3">
        <div
          :class="[
            'flex items-center justify-between p-2 rounded-md text-sm',
            getAssessmentClass(heartRateAssessment.level),
          ]"
        >
          <div class="flex items-center gap-2">
            <span class="font-medium">{{
              getHeartRateIcon(heartRateAssessment.level)
            }}</span>
            <span>{{ heartRateAssessment.interpretation }}</span>
          </div>
          <span class="text-xs">{{
            heartRateAssessment.level.toUpperCase()
          }}</span>
        </div>
        <div
          v-if="heartRateAssessment.recommendation"
          class="text-xs text-gray-600 dark:text-gray-400 mt-1 px-2"
        >
          {{ heartRateAssessment.recommendation }}
        </div>
      </div>

      <!-- 心率分类 -->
      <div v-if="heartRateCategory" class="heart-rate-category mt-2">
        <div
          :class="[
            'text-xs px-2 py-1 rounded',
            getCategoryClass(heartRateCategory.type),
          ]"
        >
          {{ heartRateCategory.description }}
          <span v-if="heartRateCategory.ageGroup" class="ml-1"
            >({{ heartRateCategory.ageGroup }})</span
          >
        </div>
      </div>

      <!-- 心律异常警告 -->
      <div v-if="rhythm !== 'regular'" class="rhythm-warning mt-2">
        <div
          class="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md"
        >
          <svg
            class="w-4 h-4 text-yellow-600 dark:text-yellow-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-sm text-yellow-800 dark:text-yellow-200">
            检测到心律异常: {{ getRhythmDisplayName(rhythm) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 参考值范围 -->
    <div
      v-if="showReferenceRange && referenceRange"
      class="reference-range mt-2"
    >
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <span class="font-medium">正常范围:</span>
        <span class="ml-1">{{ formatReferenceRange(referenceRange) }}</span>
      </div>
    </div>

    <!-- 年龄相关的心率范围 -->
    <div v-if="showAgeRanges" class="age-ranges mt-2">
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <div class="grid grid-cols-2 gap-2">
          <div>
            <span class="font-medium">成人:</span>
            <span class="ml-1">60-100 次/分</span>
          </div>
          <div>
            <span class="font-medium">儿童:</span>
            <span class="ml-1">70-120 次/分</span>
          </div>
          <div>
            <span class="font-medium">婴儿:</span>
            <span class="ml-1">100-160 次/分</span>
          </div>
          <div>
            <span class="font-medium">新生儿:</span>
            <span class="ml-1">120-160 次/分</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <p class="text-xs text-red-600 dark:text-red-400">{{ errorMessage }}</p>
    </div>

    <!-- 警告信息 -->
    <div v-if="warningMessage" class="warning-message mt-2">
      <p class="text-xs text-yellow-600 dark:text-yellow-400">
        {{ warningMessage }}
      </p>
    </div>

    <!-- 测量时间 -->
    <div v-if="showTimestamp && timestamp" class="timestamp mt-2">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        测量时间: {{ formatTimestamp(timestamp) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMedicalValidation } from '../composables/useMedicalValidation'
import { useMedicalAssessment } from '../composables/useMedicalAssessment'
import type {
  ReferenceRange,
  ValueAssessment,
  AssessmentLevel,
} from '../types/medical'

// 心律类型
type HeartRhythm =
  | 'regular'
  | 'irregular'
  | 'atrial-fibrillation'
  | 'atrial-flutter'
  | 'sinus-tachycardia'
  | 'sinus-bradycardia'
  | 'other'

// Props定义
interface Props {
  modelValue?: {
    value?: number
    unit?: string
    rhythm?: HeartRhythm
    timestamp?: Date
  }
  label?: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  disabled?: boolean
  showLabel?: boolean
  showReferenceRange?: boolean
  showAgeRanges?: boolean
  showTimestamp?: boolean
  unit?: string
  medicalCode?: string
  autoAssess?: boolean
  patientAge?: number
}

const props = withDefaults(defineProps<Props>(), {
  label: '心率',
  placeholder: '请输入心率',
  showLabel: true,
  showReferenceRange: true,
  showAgeRanges: false,
  showTimestamp: false,
  unit: '次/分',
  medicalCode: 'vital-signs:heart-rate',
  autoAssess: true,
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: typeof props.modelValue]
  change: [value: typeof props.modelValue]
  'assessment-change': [assessment: ValueAssessment | null]
  'rhythm-change': [rhythm: HeartRhythm]
}>()

// 响应式状态
const heartRateValue = ref<number | undefined>(props.modelValue?.value)
const rhythm = ref<HeartRhythm>(props.modelValue?.rhythm || 'regular')
const timestamp = ref(props.modelValue?.timestamp || new Date())

// 使用组合式API
const {
  validate,
  addError,
  addWarning,
  clearValidation,
  hasError,
  hasWarning,
  errorMessage,
  warningMessage,
} = useMedicalValidation()
const { assessValue, getReferenceRange, formatReferenceRange } =
  useMedicalAssessment()

// 评估状态
const heartRateAssessment = ref<ValueAssessment | null>(null)
const referenceRange = ref<ReferenceRange | null>(null)

// 计算属性
const isNormal = computed(() => {
  return heartRateAssessment.value?.level === 'normal'
})

const heartRateCategory = computed(() => {
  if (!heartRateValue.value) return null

  const hr = heartRateValue.value
  const age = props.patientAge

  // 根据年龄分组判断心率分类
  if (age && age < 1) {
    // 新生儿 (0-1岁)
    if (hr < 100)
      return {
        type: 'bradycardia',
        description: '心动过缓',
        ageGroup: '新生儿',
      }
    if (hr > 180)
      return {
        type: 'tachycardia',
        description: '心动过速',
        ageGroup: '新生儿',
      }
    return { type: 'normal', description: '正常心率', ageGroup: '新生儿' }
  } else if (age && age < 12) {
    // 儿童 (1-12岁)
    if (hr < 70)
      return { type: 'bradycardia', description: '心动过缓', ageGroup: '儿童' }
    if (hr > 130)
      return { type: 'tachycardia', description: '心动过速', ageGroup: '儿童' }
    return { type: 'normal', description: '正常心率', ageGroup: '儿童' }
  } else {
    // 成人 (>12岁)
    if (hr < 60)
      return { type: 'bradycardia', description: '心动过缓', ageGroup: '成人' }
    if (hr > 100)
      return { type: 'tachycardia', description: '心动过速', ageGroup: '成人' }
    return { type: 'normal', description: '正常心率', ageGroup: '成人' }
  }
})

// 方法
const updateModelValue = () => {
  const value = {
    value: heartRateValue.value,
    unit: props.unit,
    rhythm: rhythm.value,
    timestamp: timestamp.value,
  }
  emit('update:modelValue', value)
  emit('change', value)
}

const handleInput = () => {
  clearValidation()
  updateModelValue()
  if (props.autoAssess) {
    assessHeartRate()
  }
}

const handleRhythmChange = () => {
  updateModelValue()
  emit('rhythm-change', rhythm.value)
  if (props.autoAssess) {
    assessHeartRate()
  }
}

const validateHeartRate = () => {
  clearValidation()

  if (props.required && !heartRateValue.value) {
    addError('心率为必填项')
    return false
  }

  if (heartRateValue.value) {
    if (heartRateValue.value < 20 || heartRateValue.value > 300) {
      addError('心率数值超出合理范围')
      return false
    }

    if (heartRateValue.value < 40 || heartRateValue.value > 150) {
      addWarning('心率数值异常，请确认')
    }
  }

  return true
}

const assessHeartRate = async () => {
  if (heartRateValue.value) {
    try {
      const assessment = await assessValue({
        testCode: 'heart_rate',
        value: heartRateValue.value,
        unit: props.unit,
        age: props.patientAge,
      })
      heartRateAssessment.value = assessment
      emit('assessment-change', assessment)
    } catch (error) {
      console.error('Heart rate assessment failed:', error)
    }
  }
}

const loadReferenceRange = async () => {
  if (props.showReferenceRange) {
    try {
      const range = await getReferenceRange('heart_rate', {
        age: props.patientAge,
      })
      referenceRange.value = range
    } catch (error) {
      console.error('Failed to load reference range:', error)
    }
  }
}

// 显示相关函数
const getRhythmDisplayName = (rhythmType: HeartRhythm): string => {
  const rhythmNames = {
    regular: '规律',
    irregular: '不规律',
    'atrial-fibrillation': '房颤',
    'atrial-flutter': '房扑',
    'sinus-tachycardia': '窦性心动过速',
    'sinus-bradycardia': '窦性心动过缓',
    other: '其他',
  }
  return rhythmNames[rhythmType]
}

const getHeartRateIcon = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return '💓'
    case 'low':
      return '🐌'
    case 'high':
      return '🏃'
    case 'critical':
      return '🚨'
    default:
      return '💓'
  }
}

const getAssessmentClass = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'low':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'high':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getCategoryClass = (type: string): string => {
  switch (type) {
    case 'normal':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
    case 'bradycardia':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
    case 'tachycardia':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const formatTimestamp = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

// 监听器
watch([heartRateValue, rhythm], () => {
  updateModelValue()
})

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      heartRateValue.value = newValue.value
      rhythm.value = newValue.rhythm || 'regular'
      timestamp.value = newValue.timestamp || new Date()
    }
  },
  { deep: true },
)

watch(
  () => props.patientAge,
  () => {
    loadReferenceRange()
    if (props.autoAssess) {
      assessHeartRate()
    }
  },
)

// 生命周期
onMounted(() => {
  loadReferenceRange()
})
</script>

<style scoped>
.heart-rate-input {
  @apply relative;
}

.heart-rate-container {
  @apply transition-all duration-200;
}

.heart-rate-container:focus-within {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.heart-rate-value input {
  @apply transition-all duration-200;
}

.heart-rate-assessment {
  @apply transition-all duration-200;
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.animate-pulse {
  animation: heartbeat 1s ease-in-out infinite;
}
</style>
