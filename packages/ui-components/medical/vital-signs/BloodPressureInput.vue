<template>
  <div class="blood-pressure-input w-full">
    <!-- 标签 -->
    <div v-if="showLabel" class="mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
        <span v-if="medicalCode" class="text-xs text-gray-500 ml-2"
          >({{ medicalCode }})</span
        >
      </label>
    </div>

    <!-- 血压输入区域 -->
    <div
      class="blood-pressure-inputs flex items-center gap-2 p-3 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600"
    >
      <!-- 收缩压输入 -->
      <div class="systolic-input flex-1">
        <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1"
          >收缩压</label
        >
        <input
          v-model.number="systolicValue"
          type="number"
          :placeholder="systolicPlaceholder"
          :readonly="readonly"
          :disabled="disabled"
          :class="[
            'w-full px-3 py-2 text-center border rounded-md',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
            {
              'border-red-500 focus:ring-red-500 focus:border-red-500':
                systolicError,
              'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500':
                systolicWarning,
              'bg-gray-50 dark:bg-gray-600': readonly,
              'opacity-50 cursor-not-allowed': disabled,
            },
          ]"
          @blur="validateSystolic"
          @input="handleSystolicInput"
        />
        <div v-if="systolicAssessment" class="mt-1">
          <span
            :class="[
              'text-xs px-2 py-1 rounded',
              getAssessmentClass(systolicAssessment.level),
            ]"
          >
            {{ systolicAssessment.interpretation }}
          </span>
        </div>
      </div>

      <!-- 分隔符 -->
      <div
        class="separator text-2xl font-bold text-gray-400 dark:text-gray-500 px-2"
      >
        /
      </div>

      <!-- 舒张压输入 -->
      <div class="diastolic-input flex-1">
        <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1"
          >舒张压</label
        >
        <input
          v-model.number="diastolicValue"
          type="number"
          :placeholder="diastolicPlaceholder"
          :readonly="readonly"
          :disabled="disabled"
          :class="[
            'w-full px-3 py-2 text-center border rounded-md',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
            {
              'border-red-500 focus:ring-red-500 focus:border-red-500':
                diastolicError,
              'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500':
                diastolicWarning,
              'bg-gray-50 dark:bg-gray-600': readonly,
              'opacity-50 cursor-not-allowed': disabled,
            },
          ]"
          @blur="validateDiastolic"
          @input="handleDiastolicInput"
        />
        <div v-if="diastolicAssessment" class="mt-1">
          <span
            :class="[
              'text-xs px-2 py-1 rounded',
              getAssessmentClass(diastolicAssessment.level),
            ]"
          >
            {{ diastolicAssessment.interpretation }}
          </span>
        </div>
      </div>

      <!-- 单位显示 -->
      <div class="unit-display flex flex-col items-center justify-center px-2">
        <span class="text-sm text-gray-600 dark:text-gray-400">{{ unit }}</span>
        <button
          v-if="allowUnitConversion && availableUnits.length > 1"
          type="button"
          class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 mt-1"
          @click="showUnitSelector = !showUnitSelector"
        >
          转换
        </button>
      </div>
    </div>

    <!-- 单位选择器 -->
    <div
      v-if="showUnitSelector"
      class="unit-selector mt-2 p-2 border rounded bg-white dark:bg-gray-800 dark:border-gray-600 shadow-lg"
    >
      <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1"
        >选择单位:</label
      >
      <select
        v-model="selectedUnit"
        class="w-full text-sm border rounded px-2 py-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        @change="handleUnitChange"
      >
        <option
          v-for="unitOption in availableUnits"
          :key="unitOption.code"
          :value="unitOption.code"
        >
          {{ unitOption.name }} ({{ unitOption.symbol }})
        </option>
      </select>
    </div>

    <!-- 血压分类显示 -->
    <div v-if="bloodPressureCategory" class="bp-category mt-3 p-2 rounded-lg">
      <div
        :class="[
          'text-sm font-medium',
          getCategoryClass(bloodPressureCategory.level),
        ]"
      >
        <div class="flex items-center justify-between">
          <span>{{ bloodPressureCategory.category }}</span>
          <span class="text-xs">{{ bloodPressureCategory.level }}</span>
        </div>
        <div
          v-if="bloodPressureCategory.recommendation"
          class="text-xs mt-1 opacity-90"
        >
          {{ bloodPressureCategory.recommendation }}
        </div>
      </div>
    </div>

    <!-- 参考值范围 -->
    <div
      v-if="showReferenceRange && referenceRanges"
      class="reference-ranges mt-2"
    >
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <div class="grid grid-cols-2 gap-2">
          <div v-if="referenceRanges.systolic">
            <span class="font-medium">收缩压正常范围:</span>
            <span class="ml-1">{{
              formatReferenceRange(referenceRanges.systolic)
            }}</span>
          </div>
          <div v-if="referenceRanges.diastolic">
            <span class="font-medium">舒张压正常范围:</span>
            <span class="ml-1">{{
              formatReferenceRange(referenceRanges.diastolic)
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessages.length > 0" class="error-messages mt-2">
      <div
        v-for="error in errorMessages"
        :key="error"
        class="text-xs text-red-600 dark:text-red-400"
      >
        {{ error }}
      </div>
    </div>

    <!-- 警告信息 -->
    <div v-if="warningMessages.length > 0" class="warning-messages mt-2">
      <div
        v-for="warning in warningMessages"
        :key="warning"
        class="text-xs text-yellow-600 dark:text-yellow-400"
      >
        {{ warning }}
      </div>
    </div>

    <!-- 测量时间 -->
    <div v-if="showTimestamp && timestamp" class="timestamp mt-2">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        测量时间: {{ formatTimestamp(timestamp) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMedicalValidation } from '../composables/useMedicalValidation'
import { useMedicalUnits } from '../composables/useMedicalUnits'
import { useMedicalAssessment } from '../composables/useMedicalAssessment'
import type {
  MedicalUnit,
  ReferenceRange,
  ValueAssessment,
  AssessmentLevel,
} from '../types/medical'

// Props定义
interface Props {
  modelValue?: {
    systolic?: number
    diastolic?: number
    unit?: string
    timestamp?: Date
  }
  label?: string
  required?: boolean
  readonly?: boolean
  disabled?: boolean
  showLabel?: boolean
  showReferenceRange?: boolean
  showTimestamp?: boolean
  unit?: string
  allowUnitConversion?: boolean
  availableUnits?: MedicalUnit[]
  medicalCode?: string
  systolicPlaceholder?: string
  diastolicPlaceholder?: string
  autoAssess?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  label: '血压',
  showLabel: true,
  showReferenceRange: true,
  showTimestamp: false,
  unit: 'mmHg',
  allowUnitConversion: false,
  availableUnits: () => [],
  medicalCode: 'vital-signs:blood-pressure',
  systolicPlaceholder: '收缩压',
  diastolicPlaceholder: '舒张压',
  autoAssess: true,
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: typeof props.modelValue]
  change: [value: typeof props.modelValue]
  'assessment-change': [
    assessment: { systolic?: ValueAssessment; diastolic?: ValueAssessment },
  ]
}>()

// 响应式状态
const systolicValue = ref<number | undefined>(props.modelValue?.systolic)
const diastolicValue = ref<number | undefined>(props.modelValue?.diastolic)
const selectedUnit = ref(props.unit)
const showUnitSelector = ref(false)
const timestamp = ref(props.modelValue?.timestamp || new Date())

// 使用组合式API
const { validate, addError, addWarning, clearValidation } =
  useMedicalValidation()
const { convertUnit } = useMedicalUnits()
const { assessValue, getReferenceRange, formatReferenceRange } =
  useMedicalAssessment()

// 验证和评估状态
const systolicError = ref(false)
const diastolicError = ref(false)
const systolicWarning = ref(false)
const diastolicWarning = ref(false)
const systolicAssessment = ref<ValueAssessment | null>(null)
const diastolicAssessment = ref<ValueAssessment | null>(null)
const referenceRanges = ref<{
  systolic?: ReferenceRange
  diastolic?: ReferenceRange
} | null>(null)
const errorMessages = ref<string[]>([])
const warningMessages = ref<string[]>([])

// 计算属性
const bloodPressureCategory = computed(() => {
  if (!systolicValue.value || !diastolicValue.value) return null

  const systolic = systolicValue.value
  const diastolic = diastolicValue.value

  // 根据AHA/ACC指南分类血压
  if (systolic < 120 && diastolic < 80) {
    return {
      category: '正常血压',
      level: 'normal' as AssessmentLevel,
      recommendation: '保持健康的生活方式',
    }
  } else if (systolic >= 120 && systolic <= 129 && diastolic < 80) {
    return {
      category: '血压升高',
      level: 'low' as AssessmentLevel,
      recommendation: '改善生活方式，定期监测',
    }
  } else if (
    (systolic >= 130 && systolic <= 139) ||
    (diastolic >= 80 && diastolic <= 89)
  ) {
    return {
      category: '1级高血压',
      level: 'high' as AssessmentLevel,
      recommendation: '建议咨询医生，考虑药物治疗',
    }
  } else if (systolic >= 140 || diastolic >= 90) {
    return {
      category: '2级高血压',
      level: 'critical' as AssessmentLevel,
      recommendation: '需要立即医疗干预',
    }
  } else if (systolic >= 180 || diastolic >= 120) {
    return {
      category: '高血压危象',
      level: 'critical' as AssessmentLevel,
      recommendation: '立即就医！',
    }
  }

  return null
})

// 方法
const updateModelValue = () => {
  const value = {
    systolic: systolicValue.value,
    diastolic: diastolicValue.value,
    unit: selectedUnit.value,
    timestamp: timestamp.value,
  }
  emit('update:modelValue', value)
  emit('change', value)
}

const handleSystolicInput = () => {
  clearValidation()
  updateModelValue()
  if (props.autoAssess) {
    assessSystolic()
  }
}

const handleDiastolicInput = () => {
  clearValidation()
  updateModelValue()
  if (props.autoAssess) {
    assessDiastolic()
  }
}

const validateSystolic = () => {
  systolicError.value = false
  systolicWarning.value = false

  if (props.required && !systolicValue.value) {
    systolicError.value = true
    addError('收缩压为必填项')
    return false
  }

  if (systolicValue.value) {
    if (systolicValue.value < 50 || systolicValue.value > 300) {
      systolicWarning.value = true
      addWarning('收缩压数值异常，请确认')
    }
    if (systolicValue.value < 30 || systolicValue.value > 400) {
      systolicError.value = true
      addError('收缩压数值超出合理范围')
      return false
    }
  }

  return true
}

const validateDiastolic = () => {
  diastolicError.value = false
  diastolicWarning.value = false

  if (props.required && !diastolicValue.value) {
    diastolicError.value = true
    addError('舒张压为必填项')
    return false
  }

  if (diastolicValue.value) {
    if (diastolicValue.value < 30 || diastolicValue.value > 200) {
      diastolicWarning.value = true
      addWarning('舒张压数值异常，请确认')
    }
    if (diastolicValue.value < 10 || diastolicValue.value > 250) {
      diastolicError.value = true
      addError('舒张压数值超出合理范围')
      return false
    }
  }

  return true
}

const assessSystolic = async () => {
  if (systolicValue.value && selectedUnit.value) {
    try {
      const assessment = await assessValue({
        testCode: 'systolic_bp',
        value: systolicValue.value,
        unit: selectedUnit.value,
      })
      systolicAssessment.value = assessment
    } catch (error) {
      console.error('Systolic assessment failed:', error)
    }
  }
}

const assessDiastolic = async () => {
  if (diastolicValue.value && selectedUnit.value) {
    try {
      const assessment = await assessValue({
        testCode: 'diastolic_bp',
        value: diastolicValue.value,
        unit: selectedUnit.value,
      })
      diastolicAssessment.value = assessment
    } catch (error) {
      console.error('Diastolic assessment failed:', error)
    }
  }
}

const handleUnitChange = async () => {
  if (selectedUnit.value !== props.unit) {
    try {
      if (systolicValue.value) {
        const systolicConverted = await convertUnit(
          systolicValue.value,
          props.unit,
          selectedUnit.value,
        )
        systolicValue.value = systolicConverted.convertedValue
      }
      if (diastolicValue.value) {
        const diastolicConverted = await convertUnit(
          diastolicValue.value,
          props.unit,
          selectedUnit.value,
        )
        diastolicValue.value = diastolicConverted.convertedValue
      }
      updateModelValue()
      showUnitSelector.value = false
    } catch (error) {
      console.error('Unit conversion failed:', error)
    }
  }
}

const getAssessmentClass = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'low':
    case 'high':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getCategoryClass = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return 'text-green-700 bg-green-50 dark:text-green-400 dark:bg-green-900/20'
    case 'low':
    case 'high':
      return 'text-yellow-700 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/20'
    case 'critical':
      return 'text-red-700 bg-red-50 dark:text-red-400 dark:bg-red-900/20'
    default:
      return 'text-gray-700 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20'
  }
}

const formatTimestamp = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

// 监听器
watch([systolicValue, diastolicValue], () => {
  updateModelValue()
})

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      systolicValue.value = newValue.systolic
      diastolicValue.value = newValue.diastolic
      selectedUnit.value = newValue.unit || props.unit
      timestamp.value = newValue.timestamp || new Date()
    }
  },
  { deep: true },
)

// 生命周期
onMounted(async () => {
  // 加载参考值范围
  if (props.showReferenceRange) {
    try {
      const [systolicRange, diastolicRange] = await Promise.all([
        getReferenceRange('systolic_bp'),
        getReferenceRange('diastolic_bp'),
      ])
      referenceRanges.value = {
        systolic: systolicRange || undefined,
        diastolic: diastolicRange || undefined,
      }
    } catch (error) {
      console.error('Failed to load reference ranges:', error)
    }
  }
})
</script>

<style scoped>
.blood-pressure-input {
  @apply relative;
}

.blood-pressure-inputs {
  @apply transition-all duration-200;
}

.blood-pressure-inputs:focus-within {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.unit-selector {
  @apply absolute top-full left-0 right-0 z-10;
}

.bp-category {
  @apply transition-all duration-200;
}
</style>
