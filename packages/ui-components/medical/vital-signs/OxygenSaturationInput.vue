<template>
  <div class="oxygen-saturation-input w-full">
    <!-- 标签 -->
    <div v-if="showLabel" class="mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
        <span v-if="medicalCode" class="text-xs text-gray-500 ml-2"
          >({{ medicalCode }})</span
        >
      </label>
    </div>

    <!-- 血氧饱和度输入区域 -->
    <div
      class="oxygen-container p-3 border rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600"
    >
      <div class="flex items-center gap-3">
        <!-- 血氧数值输入 -->
        <div class="oxygen-value flex-1">
          <div class="relative">
            <input
              v-model.number="oxygenValue"
              type="number"
              step="0.1"
              min="0"
              max="100"
              :placeholder="placeholder"
              :readonly="readonly"
              :disabled="disabled"
              :class="[
                'w-full px-3 py-2 text-center text-lg font-medium border rounded-md',
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
                {
                  'border-red-500 focus:ring-red-500 focus:border-red-500':
                    hasError,
                  'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500':
                    hasWarning,
                  'border-green-500 focus:ring-green-500 focus:border-green-500':
                    isNormal,
                  'bg-gray-50 dark:bg-gray-600': readonly,
                  'opacity-50 cursor-not-allowed': disabled,
                },
              ]"
              @blur="validateOxygenSaturation"
              @input="handleInput"
            />

            <!-- 血氧图标 -->
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
              <svg
                :class="['w-5 h-5', getOxygenIconClass()]"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path
                  fill-rule="evenodd"
                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- 单位显示 -->
        <div class="unit-display">
          <span class="text-sm text-gray-600 dark:text-gray-400 px-2">{{
            unit
          }}</span>
        </div>

        <!-- 氧疗状态 -->
        <div class="oxygen-therapy">
          <label class="flex items-center gap-2">
            <input
              v-model="oxygenTherapy"
              type="checkbox"
              :disabled="disabled || readonly"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              @change="handleTherapyChange"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">氧疗</span>
          </label>
        </div>

        <!-- 氧流量输入 -->
        <div v-if="oxygenTherapy" class="oxygen-flow">
          <div class="flex items-center gap-1">
            <input
              v-model.number="oxygenFlow"
              type="number"
              step="0.1"
              min="0"
              placeholder="流量"
              :disabled="disabled || readonly"
              class="w-16 px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              @input="handleFlowChange"
            />
            <span class="text-xs text-gray-600 dark:text-gray-400">L/min</span>
          </div>
        </div>
      </div>

      <!-- 血氧饱和度评估显示 -->
      <div v-if="oxygenAssessment" class="oxygen-assessment mt-3">
        <div
          :class="[
            'flex items-center justify-between p-2 rounded-md text-sm',
            getAssessmentClass(oxygenAssessment.level),
          ]"
        >
          <div class="flex items-center gap-2">
            <span class="font-medium">{{
              getOxygenIcon(oxygenAssessment.level)
            }}</span>
            <span>{{ oxygenAssessment.interpretation }}</span>
          </div>
          <span class="text-xs">{{
            oxygenAssessment.level.toUpperCase()
          }}</span>
        </div>
        <div
          v-if="oxygenAssessment.recommendation"
          class="text-xs text-gray-600 dark:text-gray-400 mt-1 px-2"
        >
          {{ oxygenAssessment.recommendation }}
        </div>
      </div>

      <!-- 血氧分类 -->
      <div v-if="oxygenCategory" class="oxygen-category mt-2">
        <div
          :class="[
            'text-xs px-2 py-1 rounded',
            getCategoryClass(oxygenCategory.type),
          ]"
        >
          {{ oxygenCategory.description }}
        </div>
      </div>

      <!-- 氧疗信息显示 -->
      <div v-if="oxygenTherapy" class="oxygen-therapy-info mt-2">
        <div
          class="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md"
        >
          <svg
            class="w-4 h-4 text-blue-600 dark:text-blue-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-sm text-blue-800 dark:text-blue-200">
            患者正在接受氧疗
            <span v-if="oxygenFlow" class="ml-1"
              >- 氧流量: {{ oxygenFlow }} L/min</span
            >
          </span>
        </div>
      </div>
    </div>

    <!-- 参考值范围 -->
    <div
      v-if="showReferenceRange && referenceRange"
      class="reference-range mt-2"
    >
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <span class="font-medium">正常范围:</span>
        <span class="ml-1">{{ formatReferenceRange(referenceRange) }}</span>
      </div>
    </div>

    <!-- 血氧分级指导 -->
    <div v-if="showGuidelines" class="oxygen-guidelines mt-2">
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <div class="grid grid-cols-2 gap-2">
          <div class="flex items-center gap-1">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>正常: ≥95%</span>
          </div>
          <div class="flex items-center gap-1">
            <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>轻度缺氧: 90-94%</span>
          </div>
          <div class="flex items-center gap-1">
            <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
            <span>中度缺氧: 85-89%</span>
          </div>
          <div class="flex items-center gap-1">
            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>重度缺氧: <85%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误和警告信息 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <p class="text-xs text-red-600 dark:text-red-400">{{ errorMessage }}</p>
    </div>
    <div v-if="warningMessage" class="warning-message mt-2">
      <p class="text-xs text-yellow-600 dark:text-yellow-400">
        {{ warningMessage }}
      </p>
    </div>

    <!-- 测量时间 -->
    <div v-if="showTimestamp && timestamp" class="timestamp mt-2">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        测量时间: {{ formatTimestamp(timestamp) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useMedicalValidation } from '../composables/useMedicalValidation'
import { useMedicalAssessment } from '../composables/useMedicalAssessment'
import type {
  ReferenceRange,
  ValueAssessment,
  AssessmentLevel,
} from '../types/medical'

// Props定义
interface Props {
  modelValue?: {
    value?: number
    unit?: string
    oxygenTherapy?: boolean
    oxygenFlow?: number
    timestamp?: Date
  }
  label?: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  disabled?: boolean
  showLabel?: boolean
  showReferenceRange?: boolean
  showGuidelines?: boolean
  showTimestamp?: boolean
  unit?: string
  medicalCode?: string
  autoAssess?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  label: '血氧饱和度',
  placeholder: '请输入血氧饱和度',
  showLabel: true,
  showReferenceRange: true,
  showGuidelines: false,
  showTimestamp: false,
  unit: '%',
  medicalCode: 'vital-signs:oxygen-saturation',
  autoAssess: true,
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: typeof props.modelValue]
  change: [value: typeof props.modelValue]
  'assessment-change': [assessment: ValueAssessment | null]
  'therapy-change': [therapy: boolean, flow?: number]
}>()

// 响应式状态
const oxygenValue = ref<number | undefined>(props.modelValue?.value)
const oxygenTherapy = ref<boolean>(props.modelValue?.oxygenTherapy || false)
const oxygenFlow = ref<number | undefined>(props.modelValue?.oxygenFlow)
const timestamp = ref(props.modelValue?.timestamp || new Date())

// 使用组合式API
const {
  validate,
  addError,
  addWarning,
  clearValidation,
  hasError,
  hasWarning,
  errorMessage,
  warningMessage,
} = useMedicalValidation()
const { assessValue, getReferenceRange, formatReferenceRange } =
  useMedicalAssessment()

// 评估状态
const oxygenAssessment = ref<ValueAssessment | null>(null)
const referenceRange = ref<ReferenceRange | null>(null)

// 计算属性
const isNormal = computed(() => {
  return oxygenAssessment.value?.level === 'normal'
})

const oxygenCategory = computed(() => {
  if (!oxygenValue.value) return null

  const spo2 = oxygenValue.value

  if (spo2 >= 95) {
    return { type: 'normal', description: '正常血氧' }
  } else if (spo2 >= 90) {
    return { type: 'mild-hypoxia', description: '轻度缺氧' }
  } else if (spo2 >= 85) {
    return { type: 'moderate-hypoxia', description: '中度缺氧' }
  } else {
    return { type: 'severe-hypoxia', description: '重度缺氧' }
  }
})

// 方法
const updateModelValue = () => {
  const value = {
    value: oxygenValue.value,
    unit: props.unit,
    oxygenTherapy: oxygenTherapy.value,
    oxygenFlow: oxygenFlow.value,
    timestamp: timestamp.value,
  }
  emit('update:modelValue', value)
  emit('change', value)
}

const handleInput = () => {
  clearValidation()
  updateModelValue()
  if (props.autoAssess) {
    assessOxygenSaturation()
  }
}

const handleTherapyChange = () => {
  if (!oxygenTherapy.value) {
    oxygenFlow.value = undefined
  }
  updateModelValue()
  emit('therapy-change', oxygenTherapy.value, oxygenFlow.value)
}

const handleFlowChange = () => {
  updateModelValue()
  emit('therapy-change', oxygenTherapy.value, oxygenFlow.value)
}

const validateOxygenSaturation = () => {
  clearValidation()

  if (props.required && !oxygenValue.value) {
    addError('血氧饱和度为必填项')
    return false
  }

  if (oxygenValue.value) {
    if (oxygenValue.value < 50 || oxygenValue.value > 100) {
      addError('血氧饱和度数值超出合理范围')
      return false
    }

    if (oxygenValue.value < 90) {
      addWarning('血氧饱和度偏低，请注意')
    }
  }

  return true
}

const assessOxygenSaturation = async () => {
  if (oxygenValue.value) {
    try {
      const assessment = await assessValue({
        testCode: 'oxygen_saturation',
        value: oxygenValue.value,
        unit: props.unit,
      })
      oxygenAssessment.value = assessment
      emit('assessment-change', assessment)
    } catch (error) {
      console.error('Oxygen saturation assessment failed:', error)
    }
  }
}

const loadReferenceRange = async () => {
  if (props.showReferenceRange) {
    try {
      const range = await getReferenceRange('oxygen_saturation')
      referenceRange.value = range
    } catch (error) {
      console.error('Failed to load reference range:', error)
    }
  }
}

// 显示相关函数
const getOxygenIcon = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return '🫁'
    case 'low':
      return '😮‍💨'
    case 'high':
      return '💨'
    case 'critical':
      return '🚨'
    default:
      return '🫁'
  }
}

const getOxygenIconClass = (): string => {
  if (!oxygenValue.value) return 'text-gray-400'

  if (oxygenValue.value >= 95) return 'text-green-500'
  if (oxygenValue.value >= 90) return 'text-yellow-500'
  if (oxygenValue.value >= 85) return 'text-orange-500'
  return 'text-red-500'
}

const getAssessmentClass = (level: AssessmentLevel): string => {
  switch (level) {
    case 'normal':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'low':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'high':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getCategoryClass = (type: string): string => {
  switch (type) {
    case 'normal':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
    case 'mild-hypoxia':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'moderate-hypoxia':
      return 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400'
    case 'severe-hypoxia':
      return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const formatTimestamp = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

// 监听器
watch([oxygenValue, oxygenTherapy, oxygenFlow], () => {
  updateModelValue()
})

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      oxygenValue.value = newValue.value
      oxygenTherapy.value = newValue.oxygenTherapy || false
      oxygenFlow.value = newValue.oxygenFlow
      timestamp.value = newValue.timestamp || new Date()
    }
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  loadReferenceRange()
})
</script>

<style scoped>
.oxygen-saturation-input {
  @apply relative;
}

.oxygen-container {
  @apply transition-all duration-200;
}

.oxygen-container:focus-within {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.oxygen-value input {
  @apply transition-all duration-200;
}

.oxygen-assessment {
  @apply transition-all duration-200;
}
</style>
