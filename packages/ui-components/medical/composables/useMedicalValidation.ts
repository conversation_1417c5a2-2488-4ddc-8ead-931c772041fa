import { ref, computed } from 'vue'
import type { MedicalValidationRule } from '../types/medical'

export function useMedicalValidation() {
  const errors = ref<string[]>([])
  const warnings = ref<string[]>([])
  const isValidating = ref(false)

  // 计算属性
  const hasError = computed(() => errors.value.length > 0)
  const hasWarning = computed(() => warnings.value.length > 0)
  const isValid = computed(() => !hasError.value)
  const errorMessage = computed(() => errors.value.join('; '))
  const warningMessage = computed(() => warnings.value.join('; '))

  // 清除验证结果
  const clearValidation = () => {
    errors.value = []
    warnings.value = []
  }

  // 添加错误
  const addError = (message: string) => {
    if (!errors.value.includes(message)) {
      errors.value.push(message)
    }
  }

  // 添加警告
  const addWarning = (message: string) => {
    if (!warnings.value.includes(message)) {
      warnings.value.push(message)
    }
  }

  // 验证必填字段
  const validateRequired = (value: any, fieldName: string): boolean => {
    if (value === null || value === undefined || value === '') {
      addError(`${fieldName}为必填项`)
      return false
    }
    return true
  }

  // 验证数值范围
  const validateNumericRange = (
    value: number,
    minValue?: number,
    maxValue?: number,
    fieldName: string = '数值',
  ): boolean => {
    let isValid = true

    if (minValue !== undefined && value < minValue) {
      addError(`${fieldName}不能小于${minValue}`)
      isValid = false
    }

    if (maxValue !== undefined && value > maxValue) {
      addError(`${fieldName}不能大于${maxValue}`)
      isValid = false
    }

    return isValid
  }

  // 验证数值类型
  const validateNumeric = (value: any, fieldName: string = '数值'): boolean => {
    const numValue = parseFloat(value)
    if (isNaN(numValue)) {
      addError(`${fieldName}必须是有效的数字`)
      return false
    }
    return true
  }

  // 验证字符串长度
  const validateStringLength = (
    value: string,
    minLength?: number,
    maxLength?: number,
    fieldName: string = '文本',
  ): boolean => {
    let isValid = true

    if (minLength !== undefined && value.length < minLength) {
      addError(`${fieldName}长度不能少于${minLength}个字符`)
      isValid = false
    }

    if (maxLength !== undefined && value.length > maxLength) {
      addError(`${fieldName}长度不能超过${maxLength}个字符`)
      isValid = false
    }

    return isValid
  }

  // 验证正则表达式模式
  const validatePattern = (
    value: string,
    pattern: string,
    fieldName: string = '格式',
  ): boolean => {
    try {
      const regex = new RegExp(pattern)
      if (!regex.test(value)) {
        addError(`${fieldName}格式不正确`)
        return false
      }
    } catch (error) {
      console.error('Invalid regex pattern:', pattern, error)
      addError(`${fieldName}验证规则错误`)
      return false
    }
    return true
  }

  // 验证医疗编码格式
  const validateMedicalCode = (
    value: string,
    fieldName: string = '医疗编码',
  ): boolean => {
    // 医疗编码格式: system:code (例如: icd10:E11.9)
    const codePattern = /^[a-zA-Z0-9]+:[a-zA-Z0-9.-]+$/
    if (!codePattern.test(value)) {
      addError(`${fieldName}格式应为"编码系统:编码"，如"icd10:E11.9"`)
      return false
    }
    return true
  }

  // 验证医疗单位
  const validateMedicalUnit = (
    value: string,
    allowedUnits?: string[],
    fieldName: string = '单位',
  ): boolean => {
    if (allowedUnits && allowedUnits.length > 0) {
      if (!allowedUnits.includes(value)) {
        addError(`${fieldName}必须是以下之一: ${allowedUnits.join(', ')}`)
        return false
      }
    }
    return true
  }

  // 验证生命体征数值
  const validateVitalSigns = (value: number, type: string): boolean => {
    let isValid = true
    const numValue = parseFloat(value.toString())

    if (isNaN(numValue)) {
      addError(`${type}必须是有效的数字`)
      return false
    }

    switch (type) {
      case 'systolic_bp':
        if (numValue < 50 || numValue > 300) {
          addWarning('收缩压数值异常，请确认')
        }
        if (numValue < 30 || numValue > 400) {
          addError('收缩压数值超出合理范围')
          isValid = false
        }
        break

      case 'diastolic_bp':
        if (numValue < 30 || numValue > 200) {
          addWarning('舒张压数值异常，请确认')
        }
        if (numValue < 10 || numValue > 250) {
          addError('舒张压数值超出合理范围')
          isValid = false
        }
        break

      case 'heart_rate':
        if (numValue < 40 || numValue > 150) {
          addWarning('心率数值异常，请确认')
        }
        if (numValue < 20 || numValue > 300) {
          addError('心率数值超出合理范围')
          isValid = false
        }
        break

      case 'temperature':
        if (numValue < 35 || numValue > 42) {
          addWarning('体温数值异常，请确认')
        }
        if (numValue < 25 || numValue > 50) {
          addError('体温数值超出合理范围')
          isValid = false
        }
        break

      case 'respiratory_rate':
        if (numValue < 8 || numValue > 40) {
          addWarning('呼吸频率异常，请确认')
        }
        if (numValue < 1 || numValue > 100) {
          addError('呼吸频率超出合理范围')
          isValid = false
        }
        break

      case 'oxygen_saturation':
        if (numValue < 90 || numValue > 100) {
          addWarning('血氧饱和度异常，请确认')
        }
        if (numValue < 50 || numValue > 100) {
          addError('血氧饱和度超出合理范围')
          isValid = false
        }
        break

      default:
        // 通用数值验证
        if (numValue < 0) {
          addWarning('数值为负数，请确认')
        }
    }

    return isValid
  }

  // 主验证函数
  const validate = (
    value: any,
    rules: MedicalValidationRule,
    fieldName?: string,
  ): boolean => {
    isValidating.value = true
    clearValidation()

    const name = fieldName || rules.fieldType || '字段'

    try {
      // 必填验证
      if (rules.required && !validateRequired(value, name)) {
        return false
      }

      // 如果值为空且非必填，跳过其他验证
      if (
        !rules.required &&
        (value === null || value === undefined || value === '')
      ) {
        return true
      }

      // 根据数据类型进行验证
      switch (rules.dataType) {
        case 'number':
          if (!validateNumeric(value, name)) return false
          const numValue = parseFloat(value)
          if (
            !validateNumericRange(
              numValue,
              rules.minValue,
              rules.maxValue,
              name,
            )
          ) {
            return false
          }
          break

        case 'string':
          const strValue = String(value)
          if (
            rules.pattern &&
            !validatePattern(strValue, rules.pattern, name)
          ) {
            return false
          }
          break

        case 'medical_code':
          if (!validateMedicalCode(String(value), name)) return false
          break

        case 'medical_unit':
          if (!validateMedicalUnit(String(value), rules.allowedUnits, name)) {
            return false
          }
          break
      }

      // 自定义验证规则
      if (rules.customRules) {
        for (const [ruleKey, ruleValue] of Object.entries(rules.customRules)) {
          switch (ruleKey) {
            case 'vital_signs_type':
              if (!validateVitalSigns(parseFloat(value), ruleValue as string)) {
                return false
              }
              break
            // 可以添加更多自定义规则
          }
        }
      }

      return true
    } finally {
      isValidating.value = false
    }
  }

  // 批量验证
  const validateMultiple = (
    values: Record<string, any>,
    rules: Record<string, MedicalValidationRule>,
  ): boolean => {
    clearValidation()
    let allValid = true

    for (const [fieldName, fieldValue] of Object.entries(values)) {
      const fieldRules = rules[fieldName]
      if (fieldRules) {
        const fieldValid = validate(fieldValue, fieldRules, fieldName)
        if (!fieldValid) {
          allValid = false
        }
      }
    }

    return allValid
  }

  return {
    // 状态
    errors,
    warnings,
    isValidating,
    hasError,
    hasWarning,
    isValid,
    errorMessage,
    warningMessage,

    // 方法
    validate,
    validateMultiple,
    validateRequired,
    validateNumeric,
    validateNumericRange,
    validateStringLength,
    validatePattern,
    validateMedicalCode,
    validateMedicalUnit,
    validateVitalSigns,
    clearValidation,
    addError,
    addWarning,
  }
}
