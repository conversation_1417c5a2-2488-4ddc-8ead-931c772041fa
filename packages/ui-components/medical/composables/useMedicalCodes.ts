import { ref, computed } from 'vue'
import type { MedicalCode } from '../types/medical'

// 搜索参数接口
interface SearchCodesParams {
  query: string
  codeSystem?: string
  category?: string
  limit?: number
  offset?: number
}

// 分类搜索参数接口
interface GetCodesByCategoryParams {
  category: string
  codeSystem?: string
  limit?: number
  offset?: number
}

// 代码验证参数接口
interface ValidateCodeParams {
  code: string
  codeSystem: string
}

// 代码详情参数接口
interface GetCodeDetailsParams {
  code: string
  codeSystem: string
}

// API响应接口
interface SearchResponse {
  codes: MedicalCode[]
  total: number
  hasMore: boolean
}

// 使用医疗编码的组合式函数
export function useMedicalCodes() {
  // 响应式状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const cache = ref<Map<string, MedicalCode[]>>(new Map())

  // 计算属性
  const hasError = computed(() => !!error.value)

  // API基础URL
  const API_BASE = '/api/medical'

  // 生成缓存键
  const getCacheKey = (params: any): string => {
    return JSON.stringify(params)
  }

  // 搜索医疗代码
  const searchCodes = async (
    params: SearchCodesParams,
  ): Promise<MedicalCode[]> => {
    const cacheKey = getCacheKey(params)

    // 检查缓存
    if (cache.value.has(cacheKey)) {
      return cache.value.get(cacheKey)!
    }

    isLoading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams({
        q: params.query,
        code_system: params.codeSystem || 'ICD-10',
        limit: (params.limit || 20).toString(),
        offset: (params.offset || 0).toString(),
      })

      if (params.category) {
        queryParams.append('category', params.category)
      }

      const response = await fetch(`${API_BASE}/codes/search?${queryParams}`)

      if (!response.ok) {
        throw new Error(`搜索失败: ${response.statusText}`)
      }

      const data: SearchResponse = await response.json()

      // 缓存结果
      cache.value.set(cacheKey, data.codes)

      return data.codes
    } catch (err) {
      error.value = err instanceof Error ? err.message : '搜索医疗代码失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 根据分类获取代码
  const getCodesByCategory = async (
    params: GetCodesByCategoryParams,
  ): Promise<MedicalCode[]> => {
    const cacheKey = getCacheKey(params)

    if (cache.value.has(cacheKey)) {
      return cache.value.get(cacheKey)!
    }

    isLoading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams({
        category: params.category,
        code_system: params.codeSystem || 'ICD-10',
        limit: (params.limit || 20).toString(),
        offset: (params.offset || 0).toString(),
      })

      const response = await fetch(`${API_BASE}/codes/category?${queryParams}`)

      if (!response.ok) {
        throw new Error(`获取分类代码失败: ${response.statusText}`)
      }

      const data: SearchResponse = await response.json()

      cache.value.set(cacheKey, data.codes)

      return data.codes
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取分类代码失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 验证代码
  const validateCode = async (params: ValidateCodeParams): Promise<boolean> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/codes/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      if (!response.ok) {
        throw new Error(`验证失败: ${response.statusText}`)
      }

      const data = await response.json()
      return data.valid
    } catch (err) {
      error.value = err instanceof Error ? err.message : '验证代码失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取代码详情
  const getCodeDetails = async (
    params: GetCodeDetailsParams,
  ): Promise<MedicalCode | null> => {
    const cacheKey = getCacheKey(params)

    if (cache.value.has(cacheKey)) {
      const cached = cache.value.get(cacheKey)!
      return cached[0] || null
    }

    isLoading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams({
        code: params.code,
        code_system: params.codeSystem,
      })

      const response = await fetch(`${API_BASE}/codes/details?${queryParams}`)

      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`获取代码详情失败: ${response.statusText}`)
      }

      const code: MedicalCode = await response.json()

      // 缓存结果
      cache.value.set(cacheKey, [code])

      return code
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取代码详情失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取代码层次结构
  const getCodeHierarchy = async (
    code: string,
    codeSystem: string,
  ): Promise<MedicalCode[]> => {
    isLoading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams({
        code,
        code_system: codeSystem,
      })

      const response = await fetch(`${API_BASE}/codes/hierarchy?${queryParams}`)

      if (!response.ok) {
        throw new Error(`获取代码层次失败: ${response.statusText}`)
      }

      const data = await response.json()
      return data.hierarchy || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取代码层次失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取相关代码
  const getRelatedCodes = async (
    code: string,
    codeSystem: string,
  ): Promise<MedicalCode[]> => {
    isLoading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams({
        code,
        code_system: codeSystem,
      })

      const response = await fetch(`${API_BASE}/codes/related?${queryParams}`)

      if (!response.ok) {
        throw new Error(`获取相关代码失败: ${response.statusText}`)
      }

      const data = await response.json()
      return data.related || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取相关代码失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 批量验证代码
  const validateCodes = async (
    codes: { code: string; codeSystem: string }[],
  ): Promise<{ [key: string]: boolean }> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/codes/validate-batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codes }),
      })

      if (!response.ok) {
        throw new Error(`批量验证失败: ${response.statusText}`)
      }

      const data = await response.json()
      return data.results || {}
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量验证代码失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取代码统计信息
  const getCodeStats = async (codeSystem: string): Promise<any> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(
        `${API_BASE}/codes/stats?code_system=${codeSystem}`,
      )

      if (!response.ok) {
        throw new Error(`获取统计信息失败: ${response.statusText}`)
      }

      return await response.json()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取统计信息失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 清除缓存
  const clearCache = () => {
    cache.value.clear()
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  // 预定义的常用ICD分类
  const commonICDCategories = [
    { code: 'A00-B99', name: '某些传染病和寄生虫病' },
    { code: 'C00-D48', name: '肿瘤' },
    { code: 'D50-D89', name: '血液及造血器官疾病' },
    { code: 'E00-E90', name: '内分泌、营养和代谢疾病' },
    { code: 'F00-F99', name: '精神和行为障碍' },
    { code: 'G00-G99', name: '神经系统疾病' },
    { code: 'H00-H59', name: '眼和附器疾病' },
    { code: 'H60-H95', name: '耳和乳突疾病' },
    { code: 'I00-I99', name: '循环系统疾病' },
    { code: 'J00-J99', name: '呼吸系统疾病' },
    { code: 'K00-K93', name: '消化系统疾病' },
    { code: 'L00-L99', name: '皮肤和皮下组织疾病' },
    { code: 'M00-M99', name: '肌肉骨骼系统疾病' },
    { code: 'N00-N99', name: '泌尿生殖系统疾病' },
    { code: 'O00-O99', name: '妊娠、分娩和产褥期' },
    { code: 'P00-P96', name: '围产期起源的某些情况' },
    { code: 'Q00-Q99', name: '先天性畸形' },
    { code: 'R00-R99', name: '症状、体征和异常临床检验结果' },
    { code: 'S00-T98', name: '损伤、中毒和外因的某些其他后果' },
    { code: 'V01-Y98', name: '疾病和死亡的外因' },
    { code: 'Z00-Z99', name: '影响健康状态和与保健机构接触的因素' },
  ]

  return {
    // 状态
    isLoading,
    error,
    hasError,

    // 方法
    searchCodes,
    getCodesByCategory,
    validateCode,
    getCodeDetails,
    getCodeHierarchy,
    getRelatedCodes,
    validateCodes,
    getCodeStats,
    clearCache,
    clearError,

    // 常量
    commonICDCategories,
  }
}
