import { ref, computed } from 'vue'
import type {
  MedicalUnit,
  UnitConversionRequest,
  UnitConversionResponse,
  MedicalUnitSearchRequest,
  APIResponse,
  PaginatedResponse,
} from '../types/medical'

export function useMedicalUnits() {
  const units = ref<MedicalUnit[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const unitsByCategory = computed(() => {
    const grouped: Record<string, MedicalUnit[]> = {}
    units.value.forEach((unit) => {
      const category = unit.category || 'other'
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(unit)
    })
    return grouped
  })

  // 获取API基础URL
  const getApiBaseUrl = () => {
    // 从环境变量或配置中获取
    return (import.meta as any).env?.VITE_API_BASE_URL || '/api'
  }

  // 获取医疗单位列表
  const fetchUnits = async (
    searchParams?: MedicalUnitSearchRequest,
  ): Promise<MedicalUnit[]> => {
    isLoading.value = true
    error.value = null

    try {
      const params = new URLSearchParams()
      if (searchParams?.category)
        params.append('category', searchParams.category)
      if (searchParams?.search) params.append('search', searchParams.search)
      if (searchParams?.limit)
        params.append('limit', searchParams.limit.toString())
      if (searchParams?.offset)
        params.append('offset', searchParams.offset.toString())

      const response = await fetch(`${getApiBaseUrl()}/medical/units?${params}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: APIResponse<PaginatedResponse<MedicalUnit>> =
        await response.json()
      if (result.code === 200 && result.data) {
        units.value = result.data.items
        return result.data.items
      } else {
        throw new Error(result.message || '获取医疗单位失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取医疗单位失败'
      console.error('Failed to fetch medical units:', err)
      return []
    } finally {
      isLoading.value = false
    }
  }

  // 根据分类获取单位
  const getUnitsByCategory = async (
    category: string,
  ): Promise<MedicalUnit[]> => {
    return await fetchUnits({ category })
  }

  // 搜索单位
  const searchUnits = async (searchTerm: string): Promise<MedicalUnit[]> => {
    return await fetchUnits({ search: searchTerm })
  }

  // 获取可用单位（用于转换）
  const getAvailableUnits = (currentUnit: string): MedicalUnit[] => {
    const current = units.value.find((u) => u.code === currentUnit)
    if (!current || !current.baseUnit) {
      return []
    }

    // 返回具有相同基础单位的所有单位
    return units.value.filter((u) => u.baseUnit === current.baseUnit)
  }

  // 单位转换
  const convertUnit = async (
    value: number,
    fromUnit: string,
    toUnit: string,
  ): Promise<UnitConversionResponse> => {
    if (fromUnit === toUnit) {
      return {
        originalValue: value,
        originalUnit: fromUnit,
        convertedValue: value,
        convertedUnit: toUnit,
        conversionRate: 1,
      }
    }

    try {
      const request: UnitConversionRequest = {
        value,
        fromUnit,
        toUnit,
      }

      const response = await fetch(`${getApiBaseUrl()}/medical/convert-unit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: APIResponse<{ conversion: UnitConversionResponse }> =
        await response.json()
      if (result.code === 200 && result.data) {
        return result.data.conversion
      } else {
        throw new Error(result.message || '单位转换失败')
      }
    } catch (err) {
      console.error('Unit conversion failed:', err)
      throw new Error(err instanceof Error ? err.message : '单位转换失败')
    }
  }

  // 本地单位转换（基于预定义规则）
  const convertUnitLocally = (
    value: number,
    fromUnit: string,
    toUnit: string,
  ): UnitConversionResponse | null => {
    const fromUnitData = units.value.find((u) => u.code === fromUnit)
    const toUnitData = units.value.find((u) => u.code === toUnit)

    if (!fromUnitData || !toUnitData) {
      return null
    }

    // 检查是否可以转换（必须有相同的基础单位）
    if (fromUnitData.baseUnit !== toUnitData.baseUnit) {
      return null
    }

    const fromFactor = fromUnitData.conversionFactor || 1
    const toFactor = toUnitData.conversionFactor || 1
    const fromOffset = fromUnitData.conversionOffset || 0
    const toOffset = toUnitData.conversionOffset || 0

    let convertedValue: number
    let conversionRate: number

    // 处理有偏移量的转换（如温度）
    if (fromOffset !== 0 || toOffset !== 0) {
      // 先转换到基础单位，再转换到目标单位
      const baseValue = (value + fromOffset) * fromFactor
      convertedValue = baseValue / toFactor - toOffset
      conversionRate = fromFactor / toFactor
    } else {
      // 普通单位转换
      convertedValue = (value * fromFactor) / toFactor
      conversionRate = fromFactor / toFactor
    }

    // 保留合理的精度
    convertedValue = Math.round(convertedValue * 1000000) / 1000000

    return {
      originalValue: value,
      originalUnit: fromUnit,
      convertedValue,
      convertedUnit: toUnit,
      conversionRate,
    }
  }

  // 格式化单位显示
  const formatUnit = (unit: MedicalUnit): string => {
    return `${unit.name} (${unit.symbol})`
  }

  // 获取单位信息
  const getUnitInfo = (unitCode: string): MedicalUnit | null => {
    return units.value.find((u) => u.code === unitCode) || null
  }

  // 验证单位是否有效
  const isValidUnit = (unitCode: string): boolean => {
    return units.value.some((u) => u.code === unitCode)
  }

  // 获取单位分类列表
  const getCategories = (): string[] => {
    const categories = new Set<string>()
    units.value.forEach((unit) => {
      if (unit.category) {
        categories.add(unit.category)
      }
    })
    return Array.from(categories).sort()
  }

  // 预定义的常用单位转换
  const commonConversions = {
    // 温度转换
    temperature: {
      celsius_to_fahrenheit: (c: number) => (c * 9) / 5 + 32,
      fahrenheit_to_celsius: (f: number) => ((f - 32) * 5) / 9,
      celsius_to_kelvin: (c: number) => c + 273.15,
      kelvin_to_celsius: (k: number) => k - 273.15,
    },

    // 重量转换
    weight: {
      kg_to_lb: (kg: number) => kg * 2.20462,
      lb_to_kg: (lb: number) => lb / 2.20462,
      kg_to_g: (kg: number) => kg * 1000,
      g_to_kg: (g: number) => g / 1000,
    },

    // 长度转换
    length: {
      cm_to_inch: (cm: number) => cm / 2.54,
      inch_to_cm: (inch: number) => inch * 2.54,
      m_to_ft: (m: number) => m * 3.28084,
      ft_to_m: (ft: number) => ft / 3.28084,
    },

    // 压力转换
    pressure: {
      mmhg_to_kpa: (mmhg: number) => mmhg * 0.133322,
      kpa_to_mmhg: (kpa: number) => kpa / 0.133322,
      mmhg_to_psi: (mmhg: number) => mmhg * 0.0193368,
      psi_to_mmhg: (psi: number) => psi / 0.0193368,
    },
  }

  // 快速转换函数
  const quickConvert = (
    value: number,
    conversionType: keyof typeof commonConversions,
    conversionMethod: string,
  ): number | null => {
    const conversions = commonConversions[conversionType]
    if (
      conversions &&
      typeof conversions[conversionMethod as keyof typeof conversions] ===
        'function'
    ) {
      return (
        conversions[conversionMethod as keyof typeof conversions] as Function
      )(value)
    }
    return null
  }

  return {
    // 状态
    units,
    isLoading,
    error,
    unitsByCategory,

    // 方法
    fetchUnits,
    getUnitsByCategory,
    searchUnits,
    getAvailableUnits,
    convertUnit,
    convertUnitLocally,
    formatUnit,
    getUnitInfo,
    isValidUnit,
    getCategories,
    quickConvert,
    commonConversions,
  }
}
