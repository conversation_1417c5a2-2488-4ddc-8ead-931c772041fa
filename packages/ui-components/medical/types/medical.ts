// 医疗数据类型枚举
export type MedicalDataType =
  | 'general'
  | 'vital-signs'
  | 'laboratory'
  | 'medication'
  | 'diagnosis'
  | 'procedure'
  | 'allergy'
  | 'immunization'
  | 'family-history'
  | 'social-history'
  | 'physical-exam'
  | 'assessment'
  | 'plan'

// 医疗编码接口
export interface MedicalCode {
  system: string // 编码系统 (icd10, icd11, snomed, loinc, atc)
  code: string
  display: string
  description?: string
  version?: string
}

// 医疗单位接口
export interface MedicalUnit {
  code: string
  name: string
  symbol: string
  category?: string
  baseUnit?: string
  conversionFactor?: number
  conversionOffset?: number
  isSIUnit?: boolean
}

// 参考值范围接口
export interface ReferenceRange {
  testCode: string
  testName: string
  minValue: number | null
  maxValue: number | null
  unit?: string
  ageMin?: number
  ageMax?: number
  gender?: 'male' | 'female' | 'all'
  population?: 'adult' | 'pediatric' | 'elderly' | 'pregnant'
  conditionNotes?: string
}

// 数值评估级别
export type AssessmentLevel = 'normal' | 'low' | 'high' | 'critical'

// 数值评估结果接口
export interface ValueAssessment {
  value: number
  unit: string
  level: AssessmentLevel
  interpretation: string
  recommendation?: string
  criticalAlert: boolean
  referenceRange?: ReferenceRange
}

// 医疗验证规则接口
export interface MedicalValidationRule {
  fieldType: string
  dataType: 'number' | 'string' | 'medical_code' | 'medical_unit'
  required: boolean
  minValue?: number
  maxValue?: number
  allowedUnits?: string[]
  pattern?: string
  customRules?: Record<string, any>
}

// 医疗组件基础属性接口
export interface MedicalComponentProps {
  modelValue?: any
  componentType?: string
  medicalDataType?: MedicalDataType
  label?: string
  placeholder?: string
  required?: boolean
  readonly?: boolean
  disabled?: boolean
  helpText?: string
  showLabel?: boolean
  unit?: string
  allowUnitConversion?: boolean
  availableUnits?: MedicalUnit[]
  showReferenceRange?: boolean
  showMedicalCodes?: boolean
  medicalCode?: string
  medicalCodes?: MedicalCode[]
  fieldId?: string
  validationRules?: MedicalValidationRule
}

// 生命体征数据接口
export interface VitalSigns {
  bloodPressure?: {
    systolic: number
    diastolic: number
    unit: string
    timestamp: Date
  }
  heartRate?: {
    value: number
    unit: string
    rhythm?: string
    timestamp: Date
  }
  temperature?: {
    value: number
    unit: string
    site?: 'oral' | 'rectal' | 'axillary' | 'tympanic' | 'temporal'
    timestamp: Date
  }
  respiratoryRate?: {
    value: number
    unit: string
    timestamp: Date
  }
  oxygenSaturation?: {
    value: number
    unit: string
    oxygenTherapy?: boolean
    timestamp: Date
  }
  height?: {
    value: number
    unit: string
    timestamp: Date
  }
  weight?: {
    value: number
    unit: string
    timestamp: Date
  }
  bmi?: {
    value: number
    category?: 'underweight' | 'normal' | 'overweight' | 'obese'
    timestamp: Date
  }
}

// 实验室检查数据接口
export interface LabResult {
  testCode: string
  testName: string
  value: number | string
  unit?: string
  referenceRange?: ReferenceRange
  assessment?: ValueAssessment
  specimen?: {
    type: string
    collectionTime: Date
    collectionSite?: string
  }
  performer?: {
    name: string
    organization: string
  }
  timestamp: Date
  status: 'preliminary' | 'final' | 'corrected' | 'cancelled'
  notes?: string
}

// 药物信息接口
export interface MedicationInfo {
  drugCode: string
  genericName: string
  brandNames?: string[]
  drugClass?: string
  atcCode?: string
  dosageForms?: string[]
  strengths?: string[]
  routes?: string[]
  contraindications?: string
  interactions?: string
  sideEffects?: string[]
  pregnancyCategory?: string
  isPrescription: boolean
}

// 药物处方接口
export interface MedicationPrescription {
  medication: MedicationInfo
  dosage: {
    amount: number
    unit: string
    frequency: string
    route: string
    duration?: string
  }
  instructions?: string
  prescriber: {
    name: string
    license: string
  }
  prescriptionDate: Date
  startDate?: Date
  endDate?: Date
  refills?: number
  status: 'active' | 'completed' | 'cancelled' | 'suspended'
}

// 诊断信息接口
export interface DiagnosisInfo {
  code: MedicalCode
  description: string
  type: 'primary' | 'secondary' | 'differential'
  severity?: 'mild' | 'moderate' | 'severe'
  onset?: Date
  status: 'active' | 'resolved' | 'chronic' | 'recurrent'
  evidence?: {
    type: 'clinical' | 'laboratory' | 'imaging' | 'pathology'
    description: string
    date: Date
  }[]
  notes?: string
}

// 过敏信息接口
export interface AllergyInfo {
  allergen: {
    code?: MedicalCode
    name: string
    category: 'medication' | 'food' | 'environmental' | 'other'
  }
  reaction: {
    manifestation: string[]
    severity: 'mild' | 'moderate' | 'severe' | 'life-threatening'
    onset?: 'immediate' | 'delayed'
  }
  verificationStatus: 'confirmed' | 'unconfirmed' | 'refuted'
  criticality: 'low' | 'high' | 'unable-to-assess'
  onsetDate?: Date
  lastOccurrence?: Date
  notes?: string
}

// 医疗组件事件接口
export interface MedicalComponentEvents {
  'update:modelValue': (value: any) => void
  focus: (event: FocusEvent) => void
  blur: (event: FocusEvent) => void
  change: (value: any) => void
  'unit-change': (unit: string, convertedValue: any) => void
  'assessment-change': (assessment: ValueAssessment | null) => void
  'validation-change': (isValid: boolean, errors: string[]) => void
  'medical-code-select': (code: MedicalCode) => void
  'reference-range-load': (range: ReferenceRange | null) => void
}

// API请求接口
export interface MedicalCodeSearchRequest {
  codeSystem?: string
  search?: string
  language?: string
  parentCode?: string
  level?: number
  limit?: number
  offset?: number
}

export interface MedicalUnitSearchRequest {
  category?: string
  search?: string
  limit?: number
  offset?: number
}

export interface UnitConversionRequest {
  value: number
  fromUnit: string
  toUnit: string
}

export interface UnitConversionResponse {
  originalValue: number
  originalUnit: string
  convertedValue: number
  convertedUnit: string
  conversionRate: number
  conversionNotes?: string
}

export interface ReferenceRangeSearchRequest {
  testCode: string
  age?: number
  gender?: string
  population?: string
}

export interface ValueAssessmentRequest {
  testCode: string
  value: number
  unit: string
  age?: number
  gender?: string
}

// API响应接口
export interface APIResponse<T = any> {
  code: number
  message: string
  data?: T
}

export interface PaginatedResponse<T = any> {
  items: T[]
  pagination: {
    total: number
    limit: number
    offset: number
  }
}

// 医疗组件配置接口
export interface MedicalComponentConfig {
  apiBaseUrl: string
  defaultLanguage: string
  enableUnitConversion: boolean
  enableReferenceRanges: boolean
  enableValueAssessment: boolean
  enableMedicalCodes: boolean
  strictValidation: boolean
  autoSave: boolean
  autoSaveInterval: number
}
