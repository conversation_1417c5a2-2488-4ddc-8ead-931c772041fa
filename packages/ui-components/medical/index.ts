// 医疗组件库入口文件
import type { App } from 'vue'

// 基础组件
import MedicalComponent from './base/MedicalComponent.vue'

// 生命体征组件
import BloodPressureInput from './vital-signs/BloodPressureInput.vue'
import TemperatureInput from './vital-signs/TemperatureInput.vue'
import HeartRateInput from './vital-signs/HeartRateInput.vue'
import RespiratoryRateInput from './vital-signs/RespiratoryRateInput.vue'
import OxygenSaturationInput from './vital-signs/OxygenSaturationInput.vue'

// 医疗编码组件
import ICDSelector from './coding/ICDSelector.vue'

// 类型定义
export * from './types/medical'

// 组合式API
export { useMedicalValidation } from './composables/useMedicalValidation'
export { useMedicalUnits } from './composables/useMedicalUnits'
export { useMedicalAssessment } from './composables/useMedicalAssessment'
export { useMedicalCodes } from './composables/useMedicalCodes'

// 组件映射
const components = {
  // 基础组件
  MedicalComponent,

  // 生命体征组件
  BloodPressureInput,
  TemperatureInput,
  HeartRateInput,
  RespiratoryRateInput,
  OxygenSaturationInput,

  // 医疗编码组件
  ICDSelector,
}

// 单独导出组件
export {
  // 基础组件
  MedicalComponent,

  // 生命体征组件
  BloodPressureInput,
  TemperatureInput,
  HeartRateInput,
  RespiratoryRateInput,
  OxygenSaturationInput,

  // 医疗编码组件
  ICDSelector,
}

// 安装函数
export function install(app: App) {
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component)
  })
}

// 默认导出 - 简化为只导出安装函数
export default {
  install,
}

// 版本信息
export const version = '1.0.0'

// 配置选项
export interface MedicalComponentsConfig {
  apiBaseUrl?: string
  defaultLanguage?: string
  enableUnitConversion?: boolean
  enableReferenceRanges?: boolean
  enableValueAssessment?: boolean
  enableMedicalCodes?: boolean
  strictValidation?: boolean
  theme?: 'light' | 'dark' | 'auto'
}

// 全局配置
let globalConfig: MedicalComponentsConfig = {
  apiBaseUrl: '/api',
  defaultLanguage: 'zh-CN',
  enableUnitConversion: true,
  enableReferenceRanges: true,
  enableValueAssessment: true,
  enableMedicalCodes: true,
  strictValidation: true,
  theme: 'auto',
}

// 配置函数
export function setConfig(config: Partial<MedicalComponentsConfig>) {
  globalConfig = { ...globalConfig, ...config }
}

export function getConfig(): MedicalComponentsConfig {
  return { ...globalConfig }
}

// 工具函数
export function createMedicalComponentsPlugin(
  config?: Partial<MedicalComponentsConfig>,
) {
  return {
    install(app: App) {
      if (config) {
        setConfig(config)
      }
      install(app)

      // 提供全局配置
      app.provide('medicalComponentsConfig', getConfig())
    },
  }
}
