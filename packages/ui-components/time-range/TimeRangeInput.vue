<template>
  <div
    class="crf-base-container"
    :class="{
      'crf-state-disabled': disabled,
      'crf-state-readonly': readonly,
      'crf-state-required': required,
    }"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div class="crf-base-header">
      <div class="crf-base-title">
        <span class="u-text-sm u-font-medium u-text-primary">{{
          title || '时间范围'
        }}</span>
        <span v-if="required" class="u-text-error u-font-bold">*</span>
      </div>
      <div v-if="description" class="crf-base-description">
        {{ description }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="crf-base-content">
      <div class="u-flex u-items-center u-gap-2 u-w-full">
        <n-time-picker
          ref="startTimeRef"
          v-model:value="startTime"
          :placeholder="startPlaceholder || '开始时间'"
          :format="format || 'HH:mm:ss'"
          :size="nativeSize"
          :clearable="clearable"
          :disabled="shouldDisableInEditMode"
          class="u-flex-1"
          @update:value="handleStartTimeChange"
          @blur="handleTimeRangeBlur"
          @focus="handleTimeRangeFocus"
        />
        <span class="u-text-secondary u-text-sm u-whitespace-nowrap">{{
          separator || '至'
        }}</span>
        <n-time-picker
          ref="endTimeRef"
          v-model:value="endTime"
          :placeholder="endPlaceholder || '结束时间'"
          :format="format || 'HH:mm:ss'"
          :size="nativeSize"
          :clearable="clearable"
          :disabled="shouldDisableInEditMode"
          class="u-flex-1"
          @update:value="handleEndTimeChange"
          @blur="handleTimeRangeBlur"
          @focus="handleTimeRangeFocus"
        />
      </div>
    </div>

    <!-- 验证错误信息 -->
    <div v-if="validationState.message" class="crf-form-error u-mt-2">
      {{ validationState.message }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, readonly, inject, nextTick } from 'vue'
import type { TimeRangeValue } from '@crf/type-definitions'
import { NTimePicker } from 'naive-ui'
import { EditorMode } from '@crf/type-definitions/core'

defineOptions({ name: 'CrfTimeRange' })

const props = withDefaults(
  defineProps<{
    modelValue?: TimeRangeValue
    title?: string
    description?: string
    placeholder?: string | [string, string]
    startPlaceholder?: string
    endPlaceholder?: string
    format?: string
    size?: 'small' | 'default' | 'large'
    editable?: boolean
    clearable?: boolean
    separator?: string
    medicalType?:
      | 'general'
      | 'treatment'
      | 'medication'
      | 'procedure'
      | 'monitoring'
    fieldCode?: string
    required?: boolean
    disabled?: boolean
    readonly?: boolean
    visible?: boolean
    theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
  }>(),
  {
    modelValue: null,
    title: '时间范围',
    description: '请选择时间范围',
    startPlaceholder: '开始时间',
    endPlaceholder: '结束时间',
    format: 'HH:mm:ss',
    size: 'default' as const,
    editable: true,
    clearable: true,
    separator: '至',
    medicalType: 'general' as const,
    required: false,
    disabled: false,
    readonly: false,
    visible: true,
    theme: 'default' as const,
  },
)

const emit = defineEmits<{
  'update:modelValue': [value: TimeRangeValue]
  change: [value: TimeRangeValue]
  'visible-change': [visible: boolean]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  click: [event: MouseEvent]
  validate: [result: { isValid: boolean; errors: string[] }]
}>()

const validationState = ref({ status: '', message: '' })
const editStore = inject('editStore', null) as Record<string, unknown> | null
const editing = ref(false)
const startTimeRef = ref<any>(null)
const endTimeRef = ref<any>(null)
const originalValue = ref<TimeRangeValue | undefined>(undefined)

// 内部时间值
const startTime = ref<number | null>(null)
const endTime = ref<number | null>(null)

const validate = async (value: [number, number] | null) => {
  const errors: string[] = []
  if (
    props.required &&
    (value == null || value[0] == null || value[1] == null)
  ) {
    errors.push('请选择时间范围')
  }
  const isValid = errors.length === 0
  validationState.value = {
    status: isValid ? 'success' : 'error',
    message: errors[0] || '',
  }
  return { isValid, errors }
}

const clearValidation = () => {
  validationState.value = { status: '', message: '' }
}

// 映射size到Naive UI期望的类型
const nativeSize = computed(() => {
  if (props.size === 'default') {
    return 'medium'
  }
  return props.size
})

const shouldDisableInEditMode = computed(() => {
  const editorMode =
    (editStore?.mode as { value: string } | undefined)?.value ??
    (editStore?.mode as string | undefined)
  // 只有在编辑模式下才禁用，预览和发布模式下不禁用
  return editorMode === EditorMode.EDIT && !editing.value
})

// 监听模式切换，从预览切换到编辑时重置数据区的值
watch(
  () =>
    (editStore?.mode as { value: string } | undefined)?.value ??
    (editStore?.mode as string | undefined),
  (newMode: any, oldMode: any) => {
    if (oldMode === 'preview' && newMode === 'edit') {
      startTime.value = null
      endTime.value = null
      internalValue.value = null // 重置为空数据
      console.log('🔄 从预览模式切换到编辑模式，已重置数据为空')
    }
  },
)

const internalValue = ref<[number, number] | null>(null)

// 计算属性：处理两个独立时间选择器的值

// 同步 props.modelValue 到 internalValue
watch(
  () => props.modelValue,
  (newValue: any) => {
    if (newValue == null) {
      internalValue.value = null
      startTime.value = null
      endTime.value = null
    } else {
      internalValue.value = newValue.map((str: any) => {
        if (!str) return 0
        const [h, m, s] = str.split(':').map(Number)
        return new Date(1970, 0, 1, h ?? 0, m ?? 0, s ?? 0).getTime()
      }) as [number, number]
      startTime.value = internalValue.value[0]
      endTime.value = internalValue.value[1]
    }
  },
  { immediate: true },
)

// 监听 internalValue 变化并发送更新事件
watch(internalValue, (newValue: any) => {
  if (newValue != null) {
    const stringValue = newValue.map((ts: any) => {
      const date = new Date(ts)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }) as [string, string]
    emit('update:modelValue', stringValue)
  } else {
    emit('update:modelValue', null)
  }
})

const startEdit = () => {
  if (props.disabled || props.readonly) return
  editing.value = true
  if (internalValue.value != null) {
    originalValue.value = internalValue.value
      ? (internalValue.value.map((ts) => {
          const date = new Date(ts)
          return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
        }) as [string, string])
      : undefined
  }
  nextTick(() => {
    if (startTimeRef.value && typeof startTimeRef.value.focus === 'function') {
      startTimeRef.value.focus()
    }
  })
}

const finishEdit = async () => {
  const validationResult = await validate(internalValue.value)
  if (validationResult.isValid) {
    editing.value = false
  }
  emit('validate', validationResult)
  return validationResult.isValid
}

const cancelEdit = () => {
  internalValue.value = originalValue.value
    ? (originalValue.value.map((str) => {
        const [h, m, s] = str.split(':').map(Number)
        return new Date(1970, 0, 1, h ?? 0, m ?? 0, s ?? 0).getTime()
      }) as [number, number])
    : null
  editing.value = false
  clearValidation()
}

const handleChange = async (value: [number, number] | null) => {
  await validate(value)
  if (value != null) {
    const stringValue = value.map((ts) => {
      const date = new Date(ts)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }) as [string, string]
    emit('change', stringValue)
  } else {
    emit('change', null)
  }
}

const handleStartTimeChange = async (value: number | null) => {
  startTime.value = value
  const rangeValue =
    startTime.value != null && endTime.value != null
      ? ([startTime.value, endTime.value] as [number, number])
      : null
  internalValue.value = rangeValue
  await handleChange(rangeValue)
}

const handleEndTimeChange = async (value: number | null) => {
  endTime.value = value
  const rangeValue =
    startTime.value != null && endTime.value != null
      ? ([startTime.value, endTime.value] as [number, number])
      : null
  internalValue.value = rangeValue
  await handleChange(rangeValue)
}

const handleTimeRangeBlur = async (event: FocusEvent) => {
  await validate(internalValue.value)
  emit('blur', event)
}

const handleTimeRangeFocus = (event: FocusEvent) => emit('focus', event)

watch(
  () => props.modelValue,
  async (newValue: any) => {
    await validate(internalValue.value)
  },
)

defineExpose({
  isEditing: readonly(editing),
  modelValue: readonly(internalValue),
  startEdit,
  endEdit: finishEdit,
  cancelEdit,
  getValidationState: () => validationState.value,
  validate,
  clearValidation,
})
</script>

<style scoped>
@import '../shared/index.scss';

/* Naive UI 时间选择器样式覆盖 */
:deep(.n-time-picker) {
  --n-font-size: var(--crf-font-size-sm);
  --n-color: var(--crf-color-bg-primary);
  --n-text-color: var(--crf-color-text-primary);
  --n-border: 1px solid var(--crf-color-border-primary);
  --n-border-radius: var(--crf-border-radius-sm);
  --n-height: var(--crf-input-height-md);
  --n-padding-left: var(--crf-spacing-sm);
  --n-padding-right: var(--crf-spacing-sm);

  width: 100%;
}

:deep(.n-time-picker:hover) {
  --n-border-hover: 1px solid var(--crf-color-border-hover);
}

:deep(.n-time-picker:focus-within) {
  --n-border-focus: 1px solid var(--crf-color-primary);
  --n-box-shadow-focus: 0 0 0 2px var(--crf-color-primary-light);
}

:deep(.n-time-picker.n-time-picker--disabled) {
  --n-color-disabled: var(--crf-color-bg-disabled);
  --n-text-color-disabled: var(--crf-color-text-disabled);
  --n-border-disabled: 1px solid var(--crf-color-border-disabled);
}
</style>
