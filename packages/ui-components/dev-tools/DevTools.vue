<template>
  <div
    v-if="isDev"
    class="crf-dev-tools"
    :class="{ 'crf-dev-tools--collapsed': isCollapsed }"
  >
    <!-- 工具栏头部 -->
    <div class="crf-dev-tools__header" @click="toggleCollapse">
      <div class="crf-dev-tools__title">
        <span class="crf-dev-tools__icon">🛠️</span>
        <span>CRF 开发工具</span>
      </div>
      <button class="crf-dev-tools__toggle">
        {{ isCollapsed ? '展开' : '收起' }}
      </button>
    </div>

    <!-- 工具内容 -->
    <div v-if="!isCollapsed" class="crf-dev-tools__content">
      <!-- 标签页 -->
      <div class="crf-dev-tools__tabs">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          class="crf-dev-tools__tab"
          :class="{ 'crf-dev-tools__tab--active': activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          {{ tab.label }}
        </button>
      </div>

      <!-- 组件信息面板 -->
      <div v-if="activeTab === 'components'" class="crf-dev-tools__panel">
        <div class="crf-dev-tools__section">
          <h4>组件树</h4>
          <div class="crf-component-tree">
            <div
              v-for="component in componentTree"
              :key="component.id"
              class="crf-component-item"
            >
              <div class="crf-component-name">{{ component.name }}</div>
              <div class="crf-component-props">
                <span
                  v-for="(value, key) in component.props"
                  :key="key"
                  class="crf-prop"
                >
                  {{ key }}: {{ formatValue(value) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 性能监控面板 -->
      <div v-if="activeTab === 'performance'" class="crf-dev-tools__panel">
        <div class="crf-dev-tools__section">
          <h4>性能指标</h4>
          <div class="crf-metrics">
            <div class="crf-metric">
              <span class="crf-metric__label">FPS:</span>
              <span class="crf-metric__value">{{
                performanceMetrics.fps
              }}</span>
            </div>
            <div class="crf-metric">
              <span class="crf-metric__label">内存使用:</span>
              <span class="crf-metric__value"
                >{{ performanceMetrics.memory }}MB</span
              >
            </div>
            <div class="crf-metric">
              <span class="crf-metric__label">渲染时间:</span>
              <span class="crf-metric__value"
                >{{ (performanceMetrics as any).renderTime || 0 }}ms</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 样式调试面板 -->
      <div v-if="activeTab === 'styles'" class="crf-dev-tools__panel">
        <div class="crf-dev-tools__section">
          <h4>CSS 变量</h4>
          <div class="crf-css-variables">
            <div
              v-for="(value, name) in cssVariables"
              :key="name"
              class="crf-css-variable"
            >
              <span class="crf-css-variable__name">{{ name }}:</span>
              <span class="crf-css-variable__value">{{ value }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态管理面板 -->
      <div v-if="activeTab === 'state'" class="crf-dev-tools__panel">
        <div class="crf-dev-tools__section">
          <h4>应用状态</h4>
          <pre class="crf-state-viewer">{{
            JSON.stringify(appState, null, 2)
          }}</pre>
        </div>
      </div>

      <!-- 网络请求面板 -->
      <div v-if="activeTab === 'network'" class="crf-dev-tools__panel">
        <div class="crf-dev-tools__section">
          <h4>网络请求</h4>
          <div class="crf-network-requests">
            <div
              v-for="request in networkRequests"
              :key="request.id"
              class="crf-network-request"
            >
              <div class="crf-request-method">{{ request.method }}</div>
              <div class="crf-request-url">{{ request.url }}</div>
              <div
                class="crf-request-status"
                :class="`crf-status-${request.status}`"
              >
                {{ request.status }}
              </div>
              <div class="crf-request-time">{{ request.duration }}ms</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { usePerformanceMonitor } from '@crf/vue-composables/usePerformance'

// 开发工具属性
export interface DevToolsProps {
  /** 是否显示开发工具 */
  show?: boolean
  /** 默认激活的标签页 */
  defaultTab?: string
}

const props = withDefaults(defineProps<DevToolsProps>(), {
  show: true,
  defaultTab: 'components',
})

// 响应式数据
const isCollapsed = ref(false)
const activeTab = ref(props.defaultTab)

// 检查是否为开发环境
const isDev = computed(() => {
  return (import.meta as any).env?.DEV && props.show
})

// 标签页配置
const tabs = [
  { key: 'components', label: '组件' },
  { key: 'performance', label: '性能' },
  { key: 'styles', label: '样式' },
  { key: 'state', label: '状态' },
  { key: 'network', label: '网络' },
]

// 性能监控
const {
  metrics: performanceMetrics,
  startMonitoring,
  stopMonitoring,
} = usePerformanceMonitor()

// 组件树信息
const componentTree = ref([
  {
    id: 'app',
    name: 'App',
    props: { theme: 'medical', locale: 'zh-CN' },
  },
  {
    id: 'editor',
    name: 'CrfEditor',
    props: { mode: 'edit', autosave: true },
  },
])

// CSS变量
const cssVariables = ref({})

// 应用状态
const appState = ref({})

// 网络请求
const networkRequests = ref([
  {
    id: 1,
    method: 'GET',
    url: '/api/forms',
    status: 200,
    duration: 245,
  },
  {
    id: 2,
    method: 'POST',
    url: '/api/forms/save',
    status: 201,
    duration: 156,
  },
])

// 工具方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

const formatValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

// 获取CSS变量
const getCSSVariables = () => {
  const root = document.documentElement
  const styles = getComputedStyle(root)
  const variables: Record<string, string> = {}

  for (let i = 0; i < styles.length; i++) {
    const name = styles[i]
    if (name && name.startsWith('--crf-')) {
      variables[name] = styles.getPropertyValue(name).trim()
    }
  }

  cssVariables.value = variables
}

// 生命周期
onMounted(() => {
  if (isDev.value) {
    startMonitoring()
    getCSSVariables()

    // 监听CSS变量变化
    const observer = new MutationObserver(() => {
      getCSSVariables()
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style', 'class'],
    })
  }
})

onUnmounted(() => {
  if (isDev.value) {
    stopMonitoring()
  }
})
</script>

<style lang="scss" scoped>
.crf-dev-tools {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  max-height: 600px;
  background: var(--crf-color-bg-primary);
  border: 1px solid var(--crf-color-border-primary);
  border-radius: var(--crf-border-radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;

  &--collapsed {
    height: auto;
    max-height: none;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--crf-spacing-3);
    background: var(--crf-color-primary);
    color: white;
    cursor: pointer;
    border-radius: var(--crf-border-radius-md) var(--crf-border-radius-md) 0 0;
  }

  &__title {
    display: flex;
    align-items: center;
    gap: var(--crf-spacing-2);
    font-weight: 600;
  }

  &__icon {
    font-size: 16px;
  }

  &__toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      opacity: 0.8;
    }
  }

  &__content {
    max-height: 500px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__tabs {
    display: flex;
    border-bottom: 1px solid var(--crf-color-border-primary);
  }

  &__tab {
    flex: 1;
    padding: var(--crf-spacing-2);
    background: var(--crf-color-bg-secondary);
    border: none;
    cursor: pointer;
    font-size: 11px;

    &--active {
      background: var(--crf-color-bg-primary);
      color: var(--crf-color-primary);
      font-weight: 600;
    }

    &:hover:not(&--active) {
      background: var(--crf-color-bg-hover);
    }
  }

  &__panel {
    flex: 1;
    overflow-y: auto;
    padding: var(--crf-spacing-3);
  }

  &__section {
    margin-bottom: var(--crf-spacing-4);

    h4 {
      margin: 0 0 var(--crf-spacing-2) 0;
      color: var(--crf-color-text-primary);
      font-size: 13px;
      font-weight: 600;
    }
  }
}

.crf-component-tree {
  .crf-component-item {
    margin-bottom: var(--crf-spacing-2);
    padding: var(--crf-spacing-2);
    background: var(--crf-color-bg-secondary);
    border-radius: var(--crf-border-radius-sm);
  }

  .crf-component-name {
    font-weight: 600;
    color: var(--crf-color-primary);
    margin-bottom: var(--crf-spacing-1);
  }

  .crf-component-props {
    display: flex;
    flex-wrap: wrap;
    gap: var(--crf-spacing-1);
  }

  .crf-prop {
    background: var(--crf-color-bg-hover);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
  }
}

.crf-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--crf-spacing-2);

  .crf-metric {
    display: flex;
    justify-content: space-between;
    padding: var(--crf-spacing-2);
    background: var(--crf-color-bg-secondary);
    border-radius: var(--crf-border-radius-sm);

    &__label {
      color: var(--crf-color-text-secondary);
    }

    &__value {
      font-weight: 600;
      color: var(--crf-color-primary);
    }
  }
}

.crf-css-variables {
  max-height: 200px;
  overflow-y: auto;

  .crf-css-variable {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
    border-bottom: 1px solid var(--crf-color-border-secondary);

    &__name {
      color: var(--crf-color-text-secondary);
      font-weight: 500;
    }

    &__value {
      color: var(--crf-color-primary);
      font-family: monospace;
    }
  }
}

.crf-state-viewer {
  background: var(--crf-color-bg-secondary);
  padding: var(--crf-spacing-3);
  border-radius: var(--crf-border-radius-sm);
  font-size: 10px;
  max-height: 200px;
  overflow: auto;
  margin: 0;
}

.crf-network-requests {
  .crf-network-request {
    display: grid;
    grid-template-columns: 60px 1fr 60px 60px;
    gap: var(--crf-spacing-2);
    padding: var(--crf-spacing-2);
    border-bottom: 1px solid var(--crf-color-border-secondary);
    align-items: center;

    .crf-request-method {
      font-weight: 600;
      color: var(--crf-color-primary);
    }

    .crf-request-url {
      font-family: monospace;
      color: var(--crf-color-text-primary);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .crf-request-status {
      text-align: center;
      font-weight: 600;

      &.crf-status-200 {
        color: #10b981;
      }
      &.crf-status-201 {
        color: #10b981;
      }
      &.crf-status-400 {
        color: #f59e0b;
      }
      &.crf-status-404 {
        color: #ef4444;
      }
      &.crf-status-500 {
        color: #ef4444;
      }
    }

    .crf-request-time {
      text-align: right;
      color: var(--crf-color-text-secondary);
    }
  }
}
</style>
