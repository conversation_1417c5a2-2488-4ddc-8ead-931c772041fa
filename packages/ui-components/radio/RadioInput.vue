<template>
  <CrfBaseContainer
    :title="props.title || '单选'"
    :description="props.description"
    :required="props.required"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :error="validationState.status === 'error' ? validationState.message : ''"
    :direction="props.direction"
    :medical-type="props.medicalType"
    :validation-status="validationState.status"
  >
    <!-- 选项列表 -->
    <n-radio-group
      v-model:value="internalValue"
      :size="nativeSize as 'small' | 'medium' | 'large'"
      :disabled="shouldDisableInEditMode"
      :name="`radio-group-${Math.random()}`"
      @update:value="handleChange"
    >
      <!-- 可拖拽的选项列表 -->
      <draggable
        v-model="localOptions"
        :disabled="!isInEditorEditMode"
        item-key="value"
        ghost-class="crf-drag-ghost"
        chosen-class="crf-drag-chosen"
        drag-class="crf-drag-active"
        animation="200"
        @start="onDragStart"
        @end="onDragEnd"
        @change="onDragChange"
        class="crf-flex crf-flex-col crf-gap-2"
        :class="{
          'crf-flex-row crf-flex-wrap crf-gap-4':
            props.direction === 'horizontal',
        }"
      >
        <template #item="{ element, index }">
          <div
            class="crf-option-wrapper crf-transition-colors"
            :class="{
              'crf-state-draggable': isInEditorEditMode,
              'crf-state-editing': optionEditStates[index],
            }"
          >
            <!-- 单选框 -->
            <n-radio
              :value="element.value"
              :disabled="element.disabled"
              class="crf-flex-1"
            >
              <span
                v-if="!optionEditStates[index]"
                class="crf-text-sm crf-text-primary"
              >
                {{ element.label }}
              </span>
              <n-input
                v-else-if="editingOptions[index]"
                v-model:value="editingOptions[index].label"
                size="small"
                class="crf-max-w-xs crf-ml-2"
                @blur="saveOptionEdit(index)"
                @keyup.enter="saveOptionEdit(index)"
                @keyup.esc="cancelOptionEdit(index)"
                @click.stop
              />
            </n-radio>

            <!-- 编辑操作按钮 -->
            <div v-if="isInEditorEditMode" class="crf-option-actions">
              <button
                v-if="!optionEditStates[index]"
                class="crf-option-button crf-transition-colors"
                @click.stop="startOptionEdit(index)"
                title="编辑选项"
              >
                <n-icon><PencilOutline /></n-icon>
              </button>
              <button
                class="crf-option-button crf-option-button--danger crf-transition-colors"
                @click.stop="deleteOption(index)"
                title="删除选项"
                :disabled="!canDeleteOption"
              >
                <n-icon><TrashOutline /></n-icon>
              </button>
              <button
                class="crf-option-button crf-cursor-move crf-transition-colors"
                title="拖拽排序"
              >
                <n-icon><SwapVerticalOutline /></n-icon>
              </button>
            </div>
          </div>
        </template>
      </draggable>
    </n-radio-group>

    <!-- 添加选项按钮 -->
    <template #footer>
      <div v-if="isInEditorEditMode" class="crf-w-full">
        <button
          class="crf-button crf-button--primary crf-w-full"
          @click="addOption"
          :disabled="!canAddOption"
        >
          <n-icon><AddOutline /></n-icon>
          添加选项
        </button>
      </div>
    </template>
  </CrfBaseContainer>
</template>

<script lang="ts" setup>
import { NRadio, NRadioGroup, NInput, NIcon } from 'naive-ui'
import {
  AddOutline,
  PencilOutline,
  TrashOutline,
  SwapVerticalOutline,
} from '@vicons/ionicons5'
import draggable from 'vuedraggable'
import CrfBaseContainer from '../base/CrfBaseContainer.vue'
import { useFormComponent } from '@crf/vue-composables/useFormComponent'
import { useOptionManager } from '@crf/vue-composables/useOptionManager'
import type { Option } from '@crf/vue-composables/useOptionManager'

// 定义组件属性
export interface CrfRadioProps {
  /** 组件值 */
  modelValue?: string | number
  /** 组件标题 */
  title?: string
  /** 组件描述 */
  description?: string
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 选项列表 */
  options?: Option[]
  /** 布局方向 */
  direction?: 'horizontal' | 'vertical'
  /** 医疗数据类型 */
  medicalType?: 'vital-signs' | 'medication' | 'diagnosis' | 'procedure'
  /** 是否在编辑器编辑模式 */
  isInEditorEditMode?: boolean
  /** 验证规则 */
  validation?: Array<{
    required?: boolean
    message?: string
  }>
}

// 定义组件事件
export interface CrfRadioEmits {
  'update:modelValue': [value: string | number | undefined]
  'update:options': [options: Option[]]
  change: [value: string | number | undefined]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  validate: [
    result: { status: 'success' | 'warning' | 'error'; message: string },
  ]
}

const props = withDefaults(defineProps<CrfRadioProps>(), {
  direction: 'vertical',
  options: () => [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
  ],
})

const emit = defineEmits<CrfRadioEmits>()

// 使用表单组件通用逻辑
const {
  internalValue,
  validationState,
  shouldDisableInEditMode,
  nativeSize,
  handleChange,
  // handleFocus,
  // handleBlur,
  validate,
  clearValidation,
} = useFormComponent(props, emit as any, {
  defaultValue: '',
  validateOnChange: true,
  customValidator: (value: any) => {
    // 检查值是否在允许的选项中
    if (value !== undefined && value !== null && value !== '') {
      const validValues = localOptions.value.map((option: any) => option.value)
      if (!validValues.includes(value)) {
        return {
          isValid: false,
          status: 'error',
          errors: ['选择的值无效'],
          warnings: [],
          message: '选择的值无效',
        }
      }
    }

    return {
      isValid: true,
      status: 'success',
      errors: [],
      warnings: [],
      message: '',
    }
  },
})

// 使用选项管理逻辑
const {
  localOptions,
  optionEditStates,
  editingOptions,
  canAddOption,
  canDeleteOption,
  addOption,
  startOptionEdit,
  saveOptionEdit,
  cancelOptionEdit,
  deleteOption,
  onDragStart,
  onDragEnd,
  onDragChange,
} = useOptionManager(
  {
    options: props.options,
    allowEdit: true,
    allowDelete: true,
    allowAdd: true,
    allowSort: true,
    minOptions: 1,
    maxOptions: 20,
    isInEditorEditMode: props.isInEditorEditMode,
  },
  emit as any,
)

// 对外暴露的API
defineExpose({
  validate,
  clearValidation,
  internalValue,
  validationState,
})
</script>

<style lang="scss" scoped>
/* 单选框组件的特殊样式覆盖 */
:deep(.n-radio) {
  margin: 0;
  display: flex;
  align-items: center;
  min-height: 32px;

  .n-radio__label {
    font-size: var(--crf-font-size-sm);
    color: var(--crf-color-text-primary);
    line-height: var(--crf-line-height-normal);
  }

  .n-radio__dot {
    border-color: var(--crf-color-border-primary);
  }

  &:hover .n-radio__dot {
    border-color: var(--crf-color-primary);
  }

  &.n-radio--checked .n-radio__dot {
    border-color: var(--crf-color-primary);
    background-color: var(--crf-color-primary);
  }

  &.n-radio--disabled {
    opacity: 0.6;

    .n-radio__label {
      color: var(--crf-color-text-disabled);
    }
  }
}

/* 单选框组样式 */
:deep(.n-radio-group) {
  width: 100%;
}

/* 选项描述样式 */
.option-description {
  font-size: var(--crf-font-size-xs);
  color: var(--crf-color-text-tertiary);
  margin-left: var(--crf-spacing-1);
  line-height: var(--crf-line-height-relaxed);
}
</style>
