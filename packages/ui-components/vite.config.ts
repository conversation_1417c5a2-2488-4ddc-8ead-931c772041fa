import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  build: {
    lib: {
      entry: resolve(__dirname, 'index.ts'),
      name: 'CrfComponents',
      fileName: 'index'
    },
    rollupOptions: {
      external: [
        'vue',
        'naive-ui',
        '@crf/type-definitions',
        '@crf/shared-utils',
        '@crf/vue-hooks',
        '@crf/app-constants'
      ],
      output: {
        exports: 'named',
        globals: {
          vue: 'Vue',
          'naive-ui': 'NaiveUI',
          '@crf/type-definitions': 'CrfTypes',
          '@crf/shared-utils': 'CrfUtils',
          '@crf/vue-hooks': 'CrfHooks',
          '@crf/app-constants': 'CrfConstants'
        }
      }
    }
  }
})