/**
 * CRF 统一样式系统入口文件
 * 整合所有样式模块，提供一致的设计系统
 * 
 * 使用方式：
 * 1. 在 main.ts 中导入：import '@/packages/components/base/styles/index.scss'
 * 2. 在组件中使用统一的 CSS 变量和工具类
 * 3. 逐步替换硬编码样式
 */

/* ===== 基础样式系统 ===== */
/* 统一颜色变量 - 替换所有硬编码颜色 */
@use './unified-colors.scss';

/* 统一间距和尺寸系统 - 替换所有硬编码尺寸 */
@use './unified-spacing.scss';

/* 公共组件样式库 - 提供可复用的组件样式 */
@use './common-components.scss';

/* 工具类库 - 提供原子级样式类 */
@use './utility-classes.scss';

/* ===== 全局重置和基础样式 ===== */
/* 现代化的 CSS 重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移除默认的 focus outline，使用自定义样式 */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

a:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* 按钮基础样式重置 */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
}

input::placeholder,
textarea::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 列表样式 */
ul,
ol {
  list-style: none;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

th,
td {
  padding: 0;
  text-align: left;
}

/* ===== 滚动条样式 ===== */
/* Webkit 浏览器滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-secondary);
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-focus);
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-secondary) var(--color-bg-secondary);
}

/* ===== 选择文本样式 ===== */
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

::-moz-selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

/* ===== 打印样式 ===== */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  abbr[title]::after {
    content: " (" attr(title) ")";
  }

  a[href^="#"]::after,
  a[href^="javascript:"]::after {
    content: "";
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}

/* ===== 深色主题支持 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    /* 在深色模式下，可以覆盖颜色变量 */
    /* 这里可以根据需要添加深色主题的颜色覆盖 */
  }
}

/* ===== 减少动画偏好支持 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
  :root {
    --color-border-primary: #000;
    --color-text-primary: #000;
    --color-bg-primary: #fff;
  }
}

/* ===== 兼容性样式 ===== */
/* 兼容旧版本的 JZ 组件系统 */
.jz-component {
  /* 保持向后兼容 */
  font-family: var(--font-family-base);
  color: var(--color-text-primary);
}

/* 兼容 Naive UI 组件 */
.n-button,
.n-input,
.n-select {
  /* 确保 Naive UI 组件使用统一的颜色系统 */
  --n-color-primary: var(--color-primary);
  --n-color-primary-hover: var(--color-primary-500);
  --n-color-primary-pressed: var(--color-primary-700);
  --n-border-radius: var(--radius-base);
}

/* ===== 组件库特定样式 ===== */
/* CRF 组件前缀 */
[class*="crf-"] {
  box-sizing: border-box;
}

/* 医疗表单特定样式 */
.crf-medical-form {
  /* 医疗表单的特殊样式 */
  font-family: var(--font-family-base);
  line-height: var(--line-height-relaxed);
}

.crf-medical-form .required {
  color: var(--color-error);
}

.crf-medical-form .validation-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

/* ===== 动画定义 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* 动画工具类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* ===== 调试和开发辅助 ===== */
/* 开发模式下的调试样式 */
.debug-mode {
  * {
    outline: 1px solid rgba(255, 0, 0, 0.3) !important;
  }
  
  *:hover {
    outline: 2px solid rgba(255, 0, 0, 0.6) !important;
  }
}

/* 网格调试 */
.debug-grid {
  background-image: 
    linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
  background-size: var(--spacing-4) var(--spacing-4);
}

/* ===== 使用说明注释 ===== */
/*
使用指南：

1. 导入方式：
   在 main.ts 中添加：
   import '@/packages/components/base/styles/index.scss'

2. 颜色使用：
   // 替换硬编码颜色
   color: #333; → color: var(--color-text-primary);
   background: #fff; → background: var(--color-bg-primary);
   border: 1px solid #e0e0e0; → border: 1px solid var(--color-border-primary);

3. 间距使用：
   // 替换硬编码间距
   padding: 16px; → padding: var(--spacing-4);
   margin: 8px; → margin: var(--spacing-2);
   gap: 12px; → gap: var(--spacing-3);

4. 工具类使用：
   // 使用原子级工具类
   <div class="u-flex u-items-center u-gap-2 u-p-4 u-bg-primary u-rounded-lg">
     <span class="u-text-lg u-font-semibold u-text-white">内容</span>
   </div>

5. 组件样式使用：
   // 使用预定义的组件样式
   <div class="crf-base-container">
     <div class="crf-base-header">
       <div class="crf-base-title">
         <span class="title-text">标题</span>
       </div>
     </div>
     <div class="crf-base-content crf-base-content--vertical">
       <!-- 内容 -->
     </div>
   </div>

6. 迁移策略：
   - 优先使用 CSS 变量替换硬编码值
   - 使用工具类替换简单的内联样式
   - 使用组件样式库替换重复的样式定义
   - 逐步重构，保持向后兼容

7. 主题支持：
   - 所有颜色都支持主题切换
   - 支持深色模式
   - 支持高对比度模式
   - 支持减少动画偏好

8. 响应式设计：
   - 使用响应式工具类
   - 移动端优先的设计原则
   - 灵活的断点系统

9. 可访问性：
   - 支持键盘导航
   - 支持屏幕阅读器
   - 符合 WCAG 2.1 标准
   - 提供高对比度支持

10. 性能优化：
    - CSS 变量减少重复代码
    - 工具类提高缓存效率
    - 按需加载样式模块
    - 优化的动画性能
*/