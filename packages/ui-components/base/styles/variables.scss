// 基础组件主题变量系统
// 遵循 CSS Custom Properties 规范，支持主题切换

// ===== 颜色系统 =====

// 主要颜色
:root {
    // 品牌色
    --jz-color-primary: #409eff;
    --jz-color-primary-light-1: #53a8ff;
    --jz-color-primary-light-2: #66b1ff;
    --jz-color-primary-light-3: #79bbff;
    --jz-color-primary-light-4: #8cc5ff;
    --jz-color-primary-light-5: #a0cfff;
    --jz-color-primary-light-6: #b3d8ff;
    --jz-color-primary-light-7: #c6e2ff;
    --jz-color-primary-light-8: #d9ecff;
    --jz-color-primary-light-9: #ecf5ff;
    --jz-color-primary-dark-1: #337ecc;
    --jz-color-primary-dark-2: #2d70b3;

    // 功能色
    --jz-color-success: #67c23a;
    --jz-color-success-light: #e1f3d8;
    --jz-color-success-dark: #529b2e;

    --jz-color-warning: #e6a23c;
    --jz-color-warning-light: #fdf6ec;
    --jz-color-warning-dark: #b88230;

    --jz-color-danger: #f56c6c;
    --jz-color-danger-light: #fef0f0;
    --jz-color-danger-dark: #c45656;

    --jz-color-info: #909399;
    --jz-color-info-light: #f4f4f5;
    --jz-color-info-dark: #73767a;

    // 中性色
    --jz-color-text-primary: #303133;
    --jz-color-text-regular: #606266;
    --jz-color-text-secondary: #909399;
    --jz-color-text-placeholder: #c0c4cc;
    --jz-color-text-disabled: #c0c4cc;

    // 边框色
    --jz-border-color-base: #dcdfe6;
    --jz-border-color-light: #e4e7ed;
    --jz-border-color-lighter: #ebeef5;
    --jz-border-color-extra-light: #f2f6fc;
    --jz-border-color-dark: #d4d7de;
    --jz-border-color-darker: #cdd0d6;

    // 背景色
    --jz-background-color-base: #f5f7fa;
    --jz-background-color-page: #ffffff;
    --jz-background-color-overlay: #ffffff;
}

// ===== 尺寸系统 =====

:root {
    // 间距
    --jz-spacing-xs: 4px;
    --jz-spacing-sm: 8px;
    --jz-spacing-md: 16px;
    --jz-spacing-lg: 24px;
    --jz-spacing-xl: 32px;
    --jz-spacing-xxl: 48px;

    // 字体大小
    --jz-font-size-xs: 12px;
    --jz-font-size-sm: 13px;
    --jz-font-size-base: 14px;
    --jz-font-size-md: 15px;
    --jz-font-size-lg: 16px;
    --jz-font-size-xl: 18px;
    --jz-font-size-xxl: 20px;

    // 行高
    --jz-line-height-xs: 1.2;
    --jz-line-height-sm: 1.4;
    --jz-line-height-base: 1.5;
    --jz-line-height-lg: 1.6;
    --jz-line-height-xl: 1.8;

    // 字重
    --jz-font-weight-light: 300;
    --jz-font-weight-normal: 400;
    --jz-font-weight-medium: 500;
    --jz-font-weight-semibold: 600;
    --jz-font-weight-bold: 700;

    // 圆角
    --jz-border-radius-xs: 2px;
    --jz-border-radius-sm: 4px;
    --jz-border-radius-base: 6px;
    --jz-border-radius-lg: 8px;
    --jz-border-radius-xl: 12px;
    --jz-border-radius-round: 50%;

    // 边框宽度
    --jz-border-width-base: 1px;
    --jz-border-width-medium: 2px;
    --jz-border-width-thick: 3px;

    // 阴影
    --jz-box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    --jz-box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    --jz-box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);

    // 过渡动画
    --jz-transition-duration-fast: 0.2s;
    --jz-transition-duration-base: 0.3s;
    --jz-transition-duration-slow: 0.5s;
    --jz-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

// ===== 组件特定变量 =====

:root {
    // 基础组件变量
    --jz-base-padding-vertical: var(--jz-spacing-lg);
    --jz-base-padding-horizontal: var(--jz-spacing-lg);
    --jz-base-margin-bottom: var(--jz-spacing-md);
    --jz-base-border-radius: var(--jz-border-radius-base);
    --jz-base-border-color: var(--jz-border-color-light);
    --jz-base-background: transparent;  // 移除默认背景色，让组件更简洁
    --jz-base-box-shadow: var(--jz-box-shadow-light);

    // 头部区域
    --jz-base-header-margin-bottom: var(--jz-spacing-xs);
    --jz-base-header-font-size: var(--jz-font-size-lg);
    --jz-base-header-font-weight: var(--jz-font-weight-medium);
    --jz-base-header-color: var(--jz-color-text-primary);

    // 标题区域
    --jz-base-title-font-size: var(--jz-font-size-lg);
    --jz-base-title-font-weight: var(--jz-font-weight-medium);
    --jz-base-title-color: var(--jz-color-text-primary);
    --jz-base-title-line-height: var(--jz-line-height-base);

    // 必填标识
    --jz-base-required-color: var(--jz-color-danger);
    --jz-base-required-font-size: var(--jz-font-size-xl);
    --jz-base-required-margin-right: var(--jz-spacing-xs);

    // 描述区域
    --jz-base-description-margin-bottom: var(--jz-spacing-sm);
    --jz-base-description-font-size: var(--jz-font-size-sm);
    --jz-base-description-color: var(--jz-color-text-secondary);
    --jz-base-description-line-height: var(--jz-line-height-sm);
    --jz-base-description-min-height: 20px;

    // 内容区域
    --jz-base-content-margin-bottom: var(--jz-spacing-sm);

    // 帮助文本区域
    --jz-base-help-font-size: var(--jz-font-size-xs);
    --jz-base-help-color: var(--jz-color-text-placeholder);
    --jz-base-help-margin-top: var(--jz-spacing-xs);

    // 错误信息区域
    --jz-base-error-color: var(--jz-color-danger);
    --jz-base-error-font-size: var(--jz-font-size-xs);
    --jz-base-error-margin-top: var(--jz-spacing-xs);

    // 底部区域
    --jz-base-footer-margin-top: var(--jz-spacing-xs);
    --jz-base-footer-font-size: var(--jz-font-size-sm);
}

// ===== 尺寸变体 =====

// 小尺寸
.jz-base--small {
    --jz-base-padding-vertical: var(--jz-spacing-md);
    --jz-base-padding-horizontal: var(--jz-spacing-md);
    --jz-base-header-font-size: var(--jz-font-size-base);
    --jz-base-title-font-size: var(--jz-font-size-base);
}

// 大尺寸
.jz-base--large {
    --jz-base-padding-vertical: var(--jz-spacing-xl);
    --jz-base-padding-horizontal: var(--jz-spacing-xl);
    --jz-base-header-font-size: var(--jz-font-size-xl);
    --jz-base-title-font-size: var(--jz-font-size-xl);
}

// ===== 主题变体 =====

// 主要主题
.jz-base--primary {
    --jz-base-border-color: var(--jz-color-primary-light-7);
    --jz-base-background: var(--jz-color-primary-light-9);
}

// 成功主题
.jz-base--success {
    --jz-base-border-color: var(--jz-color-success-light);
    --jz-base-background: var(--jz-color-success-light);
}

// 警告主题
.jz-base--warning {
    --jz-base-border-color: var(--jz-color-warning-light);
    --jz-base-background: var(--jz-color-warning-light);
}

// 危险主题
.jz-base--danger {
    --jz-base-border-color: var(--jz-color-danger-light);
    --jz-base-background: var(--jz-color-danger-light);
}

// ===== 状态变体 =====

// 禁用状态
.jz-base--disabled {
    --jz-base-background: var(--jz-color-info-light);
    --jz-base-title-color: var(--jz-color-text-disabled);
    --jz-base-description-color: var(--jz-color-text-disabled);
    opacity: 0.6;
    cursor: not-allowed;
}

// 只读状态
.jz-base--readonly {
    --jz-base-background: var(--jz-background-color-base);
    --jz-base-border-color: var(--jz-border-color-lighter);
}

// 错误状态
.jz-base--error {
    --jz-base-border-color: var(--jz-color-danger);
    --jz-base-background: var(--jz-color-danger-light);
}

// 成功状态
.jz-base--success-status {
    --jz-base-border-color: var(--jz-color-success);
}

// 警告状态
.jz-base--warning-status {
    --jz-base-border-color: var(--jz-color-warning);
}