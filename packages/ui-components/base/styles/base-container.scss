/* 基础容器样式 - 统一所有组件的容器样式 */
.crf-base-container {
  border: 1px solid var(--crf-color-border);
  border-radius: var(--crf-radius);
  padding: var(--crf-spacing);
  background-color: var(--crf-color-bg-primary);
  min-height: var(--crf-min-height-md);
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-sm);
  width: 100%;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    border-color: var(--crf-color-primary-light);
  }
  
  &:focus-within {
    border-color: var(--crf-color-primary);
    box-shadow: 0 0 0 2px var(--crf-color-primary-light);
  }
  
  &.crf-state-disabled {
    opacity: 0.6;
    pointer-events: none;
    background-color: var(--crf-color-bg-disabled);
  }
  
  &.crf-state-error {
    border-color: var(--crf-color-error);
    
    &:focus-within {
      box-shadow: 0 0 0 2px var(--crf-color-error-light);
    }
  }
}

.crf-base-header {
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-xs);
  margin-bottom: var(--crf-spacing-sm);
}

.crf-base-title {
  font-size: var(--crf-font-size-base);
  font-weight: var(--crf-font-weight-medium);
  color: var(--crf-color-text-primary);
  line-height: var(--crf-line-height-base);
  
  .required-mark {
    color: var(--crf-color-error);
    margin-left: 2px;
  }
}

.crf-base-description {
  font-size: var(--crf-font-size-sm);
  color: var(--crf-color-text-secondary);
  line-height: var(--crf-line-height-relaxed);
}

.crf-base-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-sm);
}

.crf-form-error {
  font-size: var(--crf-font-size-xs);
  color: var(--crf-color-error);
  margin-top: var(--crf-spacing-xs);
  line-height: var(--crf-line-height-base);
  
  &:empty {
    display: none;
  }
}

/* 选项容器样式 */
.crf-options-container {
  display: flex;
  flex-direction: column;
  gap: var(--crf-spacing-xs);
}

.crf-option-item {
  display: flex;
  align-items: center;
  gap: var(--crf-spacing-sm);
  padding: var(--crf-spacing-xs);
  border-radius: var(--crf-radius-sm);
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: var(--crf-color-bg-secondary);
  }
  
  &.dragging {
    background-color: var(--crf-color-primary-light);
    border: 1px dashed var(--crf-color-primary);
  }
}

/* 操作按钮样式 */
.crf-action-buttons {
  display: flex;
  gap: var(--crf-spacing-xs);
  margin-top: var(--crf-spacing-sm);
}

.crf-action-button {
  padding: var(--crf-spacing-xs) var(--crf-spacing-sm);
  border: 1px solid var(--crf-color-border);
  border-radius: var(--crf-radius-sm);
  background-color: var(--crf-color-bg-primary);
  color: var(--crf-color-text-primary);
  font-size: var(--crf-font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: var(--crf-color-primary);
    color: var(--crf-color-primary);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
