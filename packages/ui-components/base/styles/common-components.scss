/**
 * 公共组件样式库
 * 提取重复的样式定义，提供可复用的基础组件样式
 * 减少代码重复，提高样式一致性
 */

/* 导入统一的颜色和间距系统 */
@use './unified-colors.scss';
@use './unified-spacing.scss';

/* ===== 基础容器样式 ===== */
.crf-base-container {
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-card);
  padding: var(--container-padding-md);
  background-color: var(--color-bg-primary);
  min-height: var(--min-height-md);
  display: flex;
  flex-direction: column;
  gap: var(--component-gap-sm);
  width: 100%;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    border-color: var(--color-border-focus);
  }
  
  &:focus-within {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
}

/* 紧凑型容器 */
.crf-base-container--compact {
  padding: var(--container-padding-sm);
  min-height: var(--min-height-sm);
  gap: var(--component-gap-xs);
}

/* 宽松型容器 */
.crf-base-container--spacious {
  padding: var(--container-padding-lg);
  min-height: var(--min-height-lg);
  gap: var(--component-gap-md);
}

/* ===== 基础头部样式 ===== */
.crf-base-header {
  border-bottom: 1px solid var(--color-border-primary);
  padding-bottom: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.crf-base-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-1);
  
  .title-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    line-height: var(--line-height-normal);
  }
  
  .required-mark {
    color: var(--color-error);
    font-weight: var(--font-weight-bold);
    margin-left: var(--spacing-1);
  }
}

.crf-base-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-2);
}

/* ===== 基础内容区域样式 ===== */
.crf-base-content {
  flex: 1;
  padding: var(--spacing-2) 0;
  
  /* 水平排列 */
  &--horizontal {
    display: flex;
    flex-wrap: wrap;
    gap: var(--component-gap-md);
  }
  
  /* 垂直排列 */
  &--vertical {
    display: flex;
    flex-direction: column;
    gap: var(--component-gap-sm);
  }
  
  /* 网格排列 */
  &--grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--component-gap-md);
  }
}

/* ===== 基础操作区域样式 ===== */
.crf-base-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-2);
  border-top: 1px solid var(--color-border-primary);
  margin-top: auto;
}

.crf-action-buttons {
  display: flex;
  gap: var(--component-gap-md);
  
  .crf-action-button {
    padding: var(--spacing-1) var(--spacing-2);
    height: auto;
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    background: transparent;
    border: none;
    border-radius: var(--radius-button);
    cursor: pointer;
    transition: color 0.2s ease, background-color 0.2s ease;
    
    &:hover {
      color: var(--color-primary);
      background-color: var(--color-bg-secondary);
    }
    
    &:disabled {
      color: var(--color-text-disabled);
      cursor: not-allowed;
    }
  }
}

.crf-status-indicators {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  
  .crf-status-tag {
    font-size: 11px;
    height: 20px;
    line-height: 18px;
    padding: 0 var(--spacing-2);
    border-radius: var(--radius-sm);
    background-color: var(--color-bg-tertiary);
    color: var(--color-text-secondary);
  }
}

/* ===== 基础表单样式 ===== */
.crf-form-item {
  margin-bottom: var(--form-item-margin);
  
  .crf-form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: var(--form-label-margin);
    line-height: var(--line-height-normal);
  }
  
  .crf-form-help {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
    margin-top: var(--form-help-margin);
    line-height: var(--line-height-relaxed);
  }
  
  .crf-form-error {
    color: var(--color-error);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-relaxed);
    margin-top: var(--form-error-margin);
  }
}

/* ===== 基础选项样式 ===== */
.crf-option-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  margin-bottom: var(--spacing-2);
  
  &.draggable {
    cursor: move;
    border: 1px solid transparent;
    background-color: var(--color-bg-secondary);
    
    &:hover {
      background-color: var(--color-bg-tertiary);
      border: 1px dashed var(--color-primary);
    }
  }
  
  &.editing {
    background-color: var(--color-primary-50);
    border: 1px solid var(--color-primary);
  }
}

.crf-option-actions {
  display: flex;
  gap: var(--spacing-1);
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: auto;
  
  .crf-option-button {
    padding: var(--spacing-1);
    font-size: 11px;
    color: var(--color-text-secondary);
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    min-width: unset;
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease, background-color 0.2s ease;
    
    &:hover {
      color: var(--color-primary);
      background-color: var(--color-bg-tertiary);
    }
  }
}

.crf-option-wrapper:hover .crf-option-actions {
  opacity: 1;
}

/* ===== 拖拽状态样式 ===== */
.crf-drag-ghost {
  opacity: 0.5;
  background-color: var(--color-drag-ghost) !important;
  border: 2px dashed var(--color-drag-border) !important;
}

.crf-drag-chosen {
  background-color: var(--color-drag-chosen) !important;
  border: 2px solid var(--color-warning) !important;
}

.crf-drag-active {
  opacity: 0.8;
  transform: rotate(2deg);
}

/* ===== 医疗主题样式 ===== */
.crf-medical-theme {
  &[data-medical-type="symptom"] {
    border-left: 3px solid var(--color-medical-symptom);
  }
  
  &[data-medical-type="comorbidity"] {
    border-left: 3px solid var(--color-medical-comorbidity);
  }
  
  &[data-medical-type="medication"] {
    border-left: 3px solid var(--color-medical-medication);
  }
  
  &[data-medical-type="procedure"] {
    border-left: 3px solid var(--color-medical-procedure);
  }
  
  &[data-medical-type="allergy"] {
    border-left: 3px solid var(--color-medical-allergy);
  }
}

/* ===== 状态样式 ===== */
.crf-state-success {
  border-color: var(--color-success);
  background-color: var(--color-success-50);
  
  .crf-base-title .title-text {
    color: var(--color-success-700);
  }
}

.crf-state-warning {
  border-color: var(--color-warning);
  background-color: var(--color-warning-50);
  
  .crf-base-title .title-text {
    color: var(--color-warning-700);
  }
}

.crf-state-error {
  border-color: var(--color-error);
  background-color: var(--color-error-50);
  
  .crf-base-title .title-text {
    color: var(--color-error-700);
  }
}

.crf-state-disabled {
  opacity: 0.6;
  pointer-events: none;
  background-color: var(--color-bg-tertiary);
  
  .crf-base-title .title-text {
    color: var(--color-text-disabled);
  }
}

/* ===== 尺寸变体 ===== */
.crf-size-small {
  .crf-base-container {
    padding: var(--container-padding-sm);
    min-height: var(--min-height-sm);
  }
  
  .crf-base-title .title-text {
    font-size: var(--font-size-xs);
  }
  
  .crf-base-description {
    font-size: 11px;
  }
}

.crf-size-large {
  .crf-base-container {
    padding: var(--container-padding-lg);
    min-height: var(--min-height-lg);
  }
  
  .crf-base-title .title-text {
    font-size: var(--font-size-lg);
  }
  
  .crf-base-description {
    font-size: var(--font-size-sm);
  }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .crf-base-container {
    padding: var(--container-padding-sm);
  }
  
  .crf-base-content--horizontal {
    flex-direction: column;
  }
  
  .crf-base-content--grid {
    grid-template-columns: 1fr;
  }
  
  .crf-base-actions {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: stretch;
  }
  
  .crf-action-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .crf-base-container {
    padding: var(--container-padding-xs);
    border-radius: var(--radius-sm);
  }
  
  .crf-base-title .title-text {
    font-size: var(--font-size-xs);
  }
  
  .crf-option-wrapper {
    padding: var(--spacing-1);
  }
}

/* ===== 工具类组合 ===== */
/* 快速布局类 */
.crf-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.crf-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.crf-flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.crf-flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 快速间距类 */
.crf-space-y-1 > * + * {
  margin-top: var(--spacing-1);
}

.crf-space-y-2 > * + * {
  margin-top: var(--spacing-2);
}

.crf-space-y-4 > * + * {
  margin-top: var(--spacing-4);
}

.crf-space-x-1 > * + * {
  margin-left: var(--spacing-1);
}

.crf-space-x-2 > * + * {
  margin-left: var(--spacing-2);
}

.crf-space-x-4 > * + * {
  margin-left: var(--spacing-4);
}

/* 快速文本类 */
.crf-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.crf-text-wrap {
  word-wrap: break-word;
  word-break: break-word;
}

/* 快速边框类 */
.crf-border-dashed {
  border-style: dashed;
}

.crf-border-dotted {
  border-style: dotted;
}

/* 快速过渡类 */
.crf-transition {
  transition: all 0.2s ease;
}

.crf-transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.crf-transition-transform {
  transition: transform 0.2s ease;
}

/* ===== 使用示例注释 ===== */
/*
使用示例：

<!-- 基础容器 -->
<div class="crf-base-container">
  <div class="crf-base-header">
    <div class="crf-base-title">
      <span class="title-text">标题</span>
      <span class="required-mark">*</span>
    </div>
    <div class="crf-base-description">描述信息</div>
  </div>
  
  <div class="crf-base-content crf-base-content--vertical">
    <!-- 内容区域 -->
  </div>
  
  <div class="crf-base-actions">
    <div class="crf-action-buttons">
      <button class="crf-action-button">编辑</button>
      <button class="crf-action-button">删除</button>
    </div>
    <div class="crf-status-indicators">
      <span class="crf-status-tag">已保存</span>
    </div>
  </div>
</div>

<!-- 紧凑型容器 -->
<div class="crf-base-container crf-base-container--compact">
  <!-- 内容 -->
</div>

<!-- 带状态的容器 -->
<div class="crf-base-container crf-state-success">
  <!-- 内容 -->
</div>

<!-- 医疗主题容器 -->
<div class="crf-base-container crf-medical-theme" data-medical-type="symptom">
  <!-- 内容 -->
</div>
*/