/**
 * 统一间距和尺寸系统
 * 基于 8px 网格系统，与 UnoCSS 兼容
 * 用于替换项目中所有硬编码的间距和尺寸值
 */

:root {
  /* ===== 基础间距系统 (8px 网格) ===== */
  --spacing-0: 0px;      /* 0 */
  --spacing-1: 4px;      /* 0.25rem */
  --spacing-2: 8px;      /* 0.5rem */
  --spacing-3: 12px;     /* 0.75rem */
  --spacing-4: 16px;     /* 1rem */
  --spacing-5: 20px;     /* 1.25rem */
  --spacing-6: 24px;     /* 1.5rem */
  --spacing-7: 28px;     /* 1.75rem */
  --spacing-8: 32px;     /* 2rem */
  --spacing-9: 36px;     /* 2.25rem */
  --spacing-10: 40px;    /* 2.5rem */
  --spacing-11: 44px;    /* 2.75rem */
  --spacing-12: 48px;    /* 3rem */
  --spacing-14: 56px;    /* 3.5rem */
  --spacing-16: 64px;    /* 4rem */
  --spacing-20: 80px;    /* 5rem */
  --spacing-24: 96px;    /* 6rem */
  --spacing-28: 112px;   /* 7rem */
  --spacing-32: 128px;   /* 8rem */
  --spacing-36: 144px;   /* 9rem */
  --spacing-40: 160px;   /* 10rem */
  --spacing-44: 176px;   /* 11rem */
  --spacing-48: 192px;   /* 12rem */
  --spacing-52: 208px;   /* 13rem */
  --spacing-56: 224px;   /* 14rem */
  --spacing-60: 240px;   /* 15rem */
  --spacing-64: 256px;   /* 16rem */
  --spacing-72: 288px;   /* 18rem */
  --spacing-80: 320px;   /* 20rem */
  --spacing-96: 384px;   /* 24rem */
  
  /* ===== 语义化间距别名 ===== */
  --spacing-xs: var(--spacing-1);    /* 4px - 极小间距 */
  --spacing-sm: var(--spacing-2);    /* 8px - 小间距 */
  --spacing-md: var(--spacing-4);    /* 16px - 中等间距 */
  --spacing-lg: var(--spacing-6);    /* 24px - 大间距 */
  --spacing-xl: var(--spacing-8);    /* 32px - 超大间距 */
  --spacing-2xl: var(--spacing-12);  /* 48px - 特大间距 */
  --spacing-3xl: var(--spacing-16);  /* 64px - 巨大间距 */
  
  /* ===== 组件专用间距 ===== */
  /* 容器内边距 */
  --container-padding-xs: var(--spacing-2);   /* 8px */
  --container-padding-sm: var(--spacing-3);   /* 12px */
  --container-padding-md: var(--spacing-4);   /* 16px */
  --container-padding-lg: var(--spacing-6);   /* 24px */
  --container-padding-xl: var(--spacing-8);   /* 32px */
  
  /* 组件间距 */
  --component-gap-xs: var(--spacing-1);       /* 4px */
  --component-gap-sm: var(--spacing-2);       /* 8px */
  --component-gap-md: var(--spacing-4);       /* 16px */
  --component-gap-lg: var(--spacing-6);       /* 24px */
  --component-gap-xl: var(--spacing-8);       /* 32px */
  
  /* 表单间距 */
  --form-item-margin: var(--spacing-4);       /* 16px */
  --form-label-margin: var(--spacing-1);      /* 4px */
  --form-help-margin: var(--spacing-1);       /* 4px */
  --form-error-margin: var(--spacing-1);      /* 4px */
  
  /* ===== 尺寸系统 ===== */
  /* 宽度 */
  --width-xs: 120px;     /* 极小宽度 */
  --width-sm: 200px;     /* 小宽度 */
  --width-md: 300px;     /* 中等宽度 */
  --width-lg: 400px;     /* 大宽度 */
  --width-xl: 500px;     /* 超大宽度 */
  --width-2xl: 600px;    /* 特大宽度 */
  --width-3xl: 800px;    /* 巨大宽度 */
  --width-4xl: 1000px;   /* 超巨大宽度 */
  --width-5xl: 1200px;   /* 最大宽度 */
  
  /* 高度 */
  --height-xs: 24px;     /* 极小高度 */
  --height-sm: 32px;     /* 小高度 */
  --height-md: 40px;     /* 中等高度 */
  --height-lg: 48px;     /* 大高度 */
  --height-xl: 56px;     /* 超大高度 */
  --height-2xl: 64px;    /* 特大高度 */
  
  /* 最小高度 */
  --min-height-xs: 60px;   /* 极小最小高度 */
  --min-height-sm: 80px;   /* 小最小高度 */
  --min-height-md: 120px;  /* 中等最小高度 */
  --min-height-lg: 160px;  /* 大最小高度 */
  --min-height-xl: 200px;  /* 超大最小高度 */
  
  /* ===== 圆角系统 ===== */
  --radius-none: 0px;
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;      /* 默认圆角 */
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-full: 50%;
  
  /* 语义化圆角 */
  --radius-button: var(--radius-md);     /* 按钮圆角 */
  --radius-card: var(--radius-lg);       /* 卡片圆角 */
  --radius-input: var(--radius-md);      /* 输入框圆角 */
  --radius-modal: var(--radius-xl);      /* 模态框圆角 */
  
  /* ===== 阴影系统 ===== */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: 0 0 #0000;
  
  /* 语义化阴影 */
  --shadow-card: var(--shadow-sm);       /* 卡片阴影 */
  --shadow-dropdown: var(--shadow-lg);   /* 下拉菜单阴影 */
  --shadow-modal: var(--shadow-2xl);     /* 模态框阴影 */
  --shadow-tooltip: var(--shadow-md);    /* 提示框阴影 */
  
  /* ===== 字体大小系统 ===== */
  --font-size-xs: 12px;    /* 0.75rem */
  --font-size-sm: 14px;    /* 0.875rem */
  --font-size-base: 16px;  /* 1rem */
  --font-size-lg: 18px;    /* 1.125rem */
  --font-size-xl: 20px;    /* 1.25rem */
  --font-size-2xl: 24px;   /* 1.5rem */
  --font-size-3xl: 30px;   /* 1.875rem */
  --font-size-4xl: 36px;   /* 2.25rem */
  --font-size-5xl: 48px;   /* 3rem */
  --font-size-6xl: 60px;   /* 3.75rem */
  --font-size-7xl: 72px;   /* 4.5rem */
  --font-size-8xl: 96px;   /* 6rem */
  --font-size-9xl: 128px;  /* 8rem */
  
  /* ===== 行高系统 ===== */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* ===== 字体粗细 ===== */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* ===== 兼容性映射 ===== */
  /* JZ 系统兼容 */
  --jz-spacing-xs: var(--spacing-1);
  --jz-spacing-sm: var(--spacing-2);
  --jz-spacing-md: var(--spacing-4);
  --jz-spacing-lg: var(--spacing-6);
  --jz-spacing-xl: var(--spacing-8);
  --jz-spacing-xxl: var(--spacing-12);
  
  --jz-font-size-xs: var(--font-size-xs);
  --jz-font-size-sm: 13px;
  --jz-font-size-base: var(--font-size-sm);
  --jz-font-size-md: 15px;
  --jz-font-size-lg: var(--font-size-base);
  --jz-font-size-xl: var(--font-size-lg);
  --jz-font-size-xxl: var(--font-size-xl);
  
  --jz-border-radius-xs: var(--radius-xs);
  --jz-border-radius-sm: var(--radius-sm);
  --jz-border-radius-base: var(--radius-md);
  --jz-border-radius-lg: var(--radius-lg);
  --jz-border-radius-xl: var(--radius-xl);
  
  /* CRF 编辑器布局 */
  --edit-header-height: 4rem;
  --edit-block-width: 25rem;
  --edit-block-toolbox-width: 5rem;
  --edit-config-width: 23rem;
}

/* ===== 工具类 ===== */
/* 内边距工具类 */
.p-0 { padding: var(--spacing-0) !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }

/* 外边距工具类 */
.m-0 { margin: var(--spacing-0) !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }

/* 间隙工具类 */
.gap-1 { gap: var(--spacing-1) !important; }
.gap-2 { gap: var(--spacing-2) !important; }
.gap-3 { gap: var(--spacing-3) !important; }
.gap-4 { gap: var(--spacing-4) !important; }
.gap-6 { gap: var(--spacing-6) !important; }
.gap-8 { gap: var(--spacing-8) !important; }

/* 圆角工具类 */
.rounded-none { border-radius: var(--radius-none) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* 阴影工具类 */
.shadow-none { box-shadow: var(--shadow-none) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* 字体大小工具类 */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }

/* 字体粗细工具类 */
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* ===== 常用硬编码值映射 ===== */
/*
硬编码间距替换映射表：

padding: 4px → padding: var(--spacing-1) 或 class="p-1"
padding: 8px → padding: var(--spacing-2) 或 class="p-2"
padding: 12px → padding: var(--spacing-3) 或 class="p-3"
padding: 16px → padding: var(--spacing-4) 或 class="p-4"
padding: 24px → padding: var(--spacing-6) 或 class="p-6"
padding: 32px → padding: var(--spacing-8) 或 class="p-8"

margin: 4px → margin: var(--spacing-1) 或 class="m-1"
margin: 8px → margin: var(--spacing-2) 或 class="m-2"
margin: 16px → margin: var(--spacing-4) 或 class="m-4"

gap: 4px → gap: var(--spacing-1) 或 class="gap-1"
gap: 8px → gap: var(--spacing-2) 或 class="gap-2"
gap: 12px → gap: var(--spacing-3) 或 class="gap-3"
gap: 16px → gap: var(--spacing-4) 或 class="gap-4"

border-radius: 4px → border-radius: var(--radius-sm) 或 class="rounded-sm"
border-radius: 6px → border-radius: var(--radius-md) 或 class="rounded"
border-radius: 8px → border-radius: var(--radius-lg) 或 class="rounded-lg"

font-size: 11px → font-size: var(--font-size-xs) 或 class="text-xs"
font-size: 12px → font-size: var(--font-size-xs) 或 class="text-xs"
font-size: 14px → font-size: var(--font-size-sm) 或 class="text-sm"
font-size: 16px → font-size: var(--font-size-base) 或 class="text-base"
font-size: 18px → font-size: var(--font-size-lg) 或 class="text-lg"

min-height: 120px → min-height: var(--min-height-md)
height: 24px → height: var(--height-xs)
height: 32px → height: var(--height-sm)
height: 40px → height: var(--height-md)
*/