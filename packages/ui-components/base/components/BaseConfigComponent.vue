<template>
  <div class="base-config-component" :class="configClasses">
    <!-- 配置字段标题 -->
    <div v-if="field.label" class="config-field-header">
      <label :for="fieldId" class="config-field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-mark">*</span>
      </label>

      <div v-if="field.description || field.tooltip" class="config-field-help">
        <n-tooltip
          v-if="field.tooltip"
          :content="field.tooltip"
          placement="top"
        >
          <crf-icon
            icon="material-symbols:help-outline"
            size="14"
            color="#6B7280"
          />
        </n-tooltip>
        <span v-if="field.description" class="field-description">{{
          field.description
        }}</span>
      </div>
    </div>

    <!-- 动态组件渲染 -->
    <div class="config-field-content">
      <component
        :is="componentType"
        :id="fieldId"
        v-model="configValue"
        v-bind="componentProps"
        :class="inputClasses"
        @input="handleInput"
        @change="handleChange"
        @blur="handleBlur"
        @focus="handleFocus"
      >
        <!-- 选择器选项 -->
        <template v-if="isSelectType && field.options">
          <n-option
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </template>

        <!-- 单选框组选项 -->
        <template v-if="isRadioGroupType && field.options">
          <n-radio
            v-for="option in field.options"
            :key="String(option.value)"
            :value="option.value as any"
            :disabled="option.disabled"
          >
            {{ option.label }}
          </n-radio>
        </template>

        <!-- 复选框组选项 -->
        <template v-if="isCheckboxGroupType && field.options">
          <n-checkbox
            v-for="option in field.options"
            :key="String(option.value)"
            :value="option.value as any"
            :disabled="option.disabled"
          >
            {{ option.label }}
          </n-checkbox>
        </template>
      </component>

      <!-- 验证错误信息 -->
      <div v-if="hasError" class="config-field-error">
        <crf-icon icon="material-symbols:error" size="14" color="#F56C6C" />
        <span>{{ validationState.message }}</span>
      </div>

      <!-- 帮助文本 -->
      <div v-if="field.helpText && !hasError" class="config-field-help-text">
        {{ field.helpText }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { NTooltip, NRadio, NCheckbox } from 'naive-ui'
import { useFormConfig } from '../composables/useFormConfig'
import { generateId } from '@crf/shared-utils/core'

// 定义CrfIcon组件类型
interface CrfIconComponent {
  icon: string
  size?: string | number
  color?: string
}

// 声明全局组件
declare global {
  interface GlobalComponents {
    'crf-icon': CrfIconComponent
  }
}

/**
 * 通用配置字段组件
 * 根据字段类型动态渲染不同的表单控件
 */

export interface ConfigFieldOption {
  label: string
  value: unknown
  disabled?: boolean
  icon?: string
}

export interface ConfigField {
  key: string
  type: string
  label: string
  description?: string
  tooltip?: string
  helpText?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  placeholder?: string
  options?: ConfigFieldOption[]
  validation?: Record<string, unknown>[]
  props?: Record<string, unknown>
  // 分组信息
  group?: string
  order?: number
  // 显示控制
  visible?: boolean
  dependsOn?: {
    field: string
    value: unknown
    action: 'show' | 'hide' | 'enable' | 'disable'
  }[]
}

interface Props {
  field: ConfigField
  value: unknown
  disabled?: boolean
}

interface Emits {
  (e: 'update:value', value: unknown): void
  (e: 'change', value: unknown): void
  (e: 'validate', result: { isValid: boolean; errors: string[] }): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits<Emits>()

// 生成唯一ID
const fieldId = ref(`config-field-${generateId()}`)

// 内部值状态
const configValue = ref<unknown>(props.value)

// 使用通用配置逻辑
const configData = ref({ ...props.field, value: props.value })
const {
  validationState,
  hasError,
  hasWarning,
  isValid,
  handleInput: baseHandleInput,
  handleChange: baseHandleChange,
  handleBlur,
  handleFocus,
  validate,
  clearValidation,
} = useFormConfig(configData, {
  validationRules: (props.field.validation || []) as any[],
  validationTrigger: 'change',
})

// 组件类型映射
const componentTypeMap: Record<string, string> = {
  input: 'n-input',
  textarea: 'n-input',
  number: 'n-input-number',
  select: 'n-select',
  switch: 'n-switch',
  'radio-group': 'n-radio-group',
  'checkbox-group': 'n-checkbox-group',
  slider: 'n-slider',
  date: 'n-date-picker',
  time: 'n-time-picker',
  color: 'n-color-picker',
}

// 计算属性
const componentType = computed(() => {
  return componentTypeMap[props.field.type] || 'el-input'
})

const isSelectType = computed(() => props.field.type === 'select')
const isRadioGroupType = computed(() => props.field.type === 'radio-group')
const isCheckboxGroupType = computed(
  () => props.field.type === 'checkbox-group',
)

const componentProps = computed(() => {
  const baseProps = {
    placeholder: props.field.placeholder,
    disabled: props.disabled || props.field.disabled,
    readonly: props.field.readonly,
    size: 'medium',
    ...props.field.props,
  }

  // 根据不同组件类型添加特定属性
  switch (props.field.type) {
    case 'input':
      return {
        ...baseProps,
        maxlength: props.field.props?.maxLength,
        'show-count': props.field.props?.showWordLimit,
        clearable: true,
        type: props.field.props?.inputType || 'text',
      }

    case 'textarea':
      return {
        ...baseProps,
        rows: props.field.props?.rows || 3,
        autosize: props.field.props?.autosize || { minRows: 2, maxRows: 6 },
        maxlength: props.field.props?.maxLength,
        'show-count': props.field.props?.showWordLimit,
        type: 'textarea',
      }

    case 'number':
      return {
        ...baseProps,
        min: props.field.props?.min,
        max: props.field.props?.max,
        step: props.field.props?.step || 1,
        precision: props.field.props?.precision,
        'show-button': true,
      }

    case 'select':
      return {
        ...baseProps,
        clearable: true,
        filterable: props.field.props?.filterable !== false,
        multiple: props.field.props?.multiple || false,
        options: props.field.options || [],
      }

    case 'date':
      return {
        ...baseProps,
        type: props.field.props?.dateType || 'date',
        format: props.field.props?.format || 'yyyy-MM-dd',
        'value-format': props.field.props?.valueFormat || 'yyyy-MM-dd',
      }

    case 'slider':
      return {
        ...baseProps,
        min: props.field.props?.min || 0,
        max: props.field.props?.max || 100,
        step: props.field.props?.step || 1,
        'show-stops': props.field.props?.showStops || false,
        'show-tooltip': props.field.props?.showTooltip !== false,
      }

    default:
      return baseProps
  }
})

const configClasses = computed(() => [
  'base-config-component',
  `config-type-${props.field.type}`,
  {
    'has-error': hasError.value,
    'has-warning': hasWarning.value,
    'is-valid': isValid.value,
    'is-required': props.field.required,
    'is-disabled': props.disabled || props.field.disabled,
  },
])

const inputClasses = computed(() => [
  'config-input',
  {
    'is-error': hasError.value,
    'is-warning': hasWarning.value,
  },
])

// 事件处理
const handleInput = async (value: unknown) => {
  configValue.value = value
  await baseHandleInput(value)
  emit('update:value', value)
}

const handleChange = async (value: unknown) => {
  configValue.value = value
  await baseHandleChange(value)
  emit('update:value', value)
  emit('change', value)
}

// 监听外部值变化
watch(
  () => props.value,
  (newValue: any) => {
    if (newValue !== configValue.value) {
      configValue.value = newValue
    }
  },
  { immediate: true },
)

// 监听验证状态变化
watch([hasError, hasWarning, isValid], () => {
  emit('validate', {
    isValid: isValid.value,
    errors: hasError.value ? [validationState.value.message] : [],
  })
})

// 暴露方法
defineExpose({
  validate,
  clearValidation,
  focus: () => {
    nextTick(() => {
      const input = document.getElementById(fieldId.value)
      input?.focus()
    })
  },
})
</script>

<style lang="scss" scoped>
.base-config-component {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.config-field-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.config-field-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  line-height: 1.4;

  .required-mark {
    color: #f56c6c;
    margin-left: 2px;
  }
}

.config-field-help {
  display: flex;
  align-items: center;
  gap: 6px;

  .field-description {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
  }
}

.config-field-content {
  position: relative;
}

.config-input {
  width: 100%;

  &.is-error {
    :deep(.n-input),
    :deep(.n-select),
    :deep(.n-input-number) {
      border-color: #f56c6c;
      box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.2);
    }
  }

  &.is-warning {
    :deep(.n-input),
    :deep(.n-select),
    :deep(.n-input-number) {
      border-color: #e6a23c;
      box-shadow: 0 0 0 1px rgba(230, 162, 60, 0.2);
    }
  }
}

.config-field-error {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.4;
}

.config-field-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

// 不同类型的特殊样式
.config-type-switch {
  .config-field-content {
    display: flex;
    align-items: center;
  }
}

.config-type-slider {
  .config-input {
    padding: 8px 0;
  }
}

.config-type-color {
  .config-input {
    width: auto;
  }
}

// 状态样式
.base-config-component {
  &.has-error {
    .config-field-label {
      color: #f56c6c;
    }
  }

  &.has-warning {
    .config-field-label {
      color: #e6a23c;
    }
  }

  &.is-disabled {
    .config-field-label {
      color: #c0c4cc;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-field-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .config-field-help {
    align-self: flex-start;
  }
}
</style>
