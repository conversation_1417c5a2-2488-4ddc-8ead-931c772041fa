<template>
  <div
    class="crf-base-container crf-medical-theme"
    :class="containerClasses"
    :data-medical-type="medicalType"
  >
    <!-- 组件头部 -->
    <div v-if="$slots.header || title || description" class="crf-base-header">
      <slot name="header">
        <div v-if="title" class="crf-base-title">
          <span class="title-text">{{ title }}</span>
          <span v-if="required" class="required-mark">*</span>
        </div>
        <div v-if="description" class="crf-base-description">
          {{ description }}
        </div>
      </slot>
    </div>

    <!-- 组件内容区域 -->
    <div class="crf-base-content" :class="contentClasses">
      <slot />
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="crf-base-error">
      {{ error }}
    </div>

    <!-- 底部插槽 -->
    <div v-if="$slots.footer" class="crf-base-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface CrfBaseContainerProps {
  /** 组件标题 */
  title?: string
  /** 组件描述 */
  description?: string
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 错误信息 */
  error?: string
  /** 内容布局方向 */
  direction?: 'horizontal' | 'vertical' | 'grid'
  /** 医疗数据类型 */
  medicalType?: 'vital-signs' | 'medication' | 'diagnosis' | 'procedure'
  /** 验证状态 */
  validationStatus?: 'success' | 'warning' | 'error'
  /** 自定义CSS类 */
  customClass?: string | string[]
}

const props = withDefaults(defineProps<CrfBaseContainerProps>(), {
  direction: 'vertical',
  validationStatus: 'success',
})

// 容器样式类
const containerClasses = computed(() => {
  const classes: string[] = []

  if (props.disabled) {
    classes.push('crf-state-disabled')
  }

  if (props.readonly) {
    classes.push('crf-state-readonly')
  }

  if (props.required) {
    classes.push('crf-state-required')
  }

  if (props.validationStatus === 'error' || props.error) {
    classes.push('crf-state-error')
  }

  if (props.validationStatus === 'warning') {
    classes.push('crf-state-warning')
  }

  if (props.customClass) {
    if (Array.isArray(props.customClass)) {
      classes.push(...props.customClass)
    } else {
      classes.push(props.customClass)
    }
  }

  return classes
})

// 内容区域样式类
const contentClasses = computed(() => {
  const classes: string[] = []

  switch (props.direction) {
    case 'horizontal':
      classes.push('crf-base-content--horizontal')
      break
    case 'grid':
      classes.push('crf-base-content--grid')
      break
    default:
      classes.push('crf-base-content--vertical')
  }

  return classes
})
</script>

<style scoped>
/* 组件特定的样式增强 */
.crf-base-container {
  /* 基础样式已在全局样式中定义 */
}

/* 状态变体增强 */
.crf-state-warning {
  border-color: var(--crf-color-warning);

  &:focus-within {
    box-shadow: 0 0 0 2px var(--crf-color-warning-200);
  }
}

/* 底部区域样式 */
.crf-base-footer {
  margin-top: var(--crf-spacing-3);
  padding-top: var(--crf-spacing-3);
  border-top: 1px solid var(--crf-color-border-primary);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--crf-spacing-2);
}

/* 响应式优化 */
@media (max-width: 640px) {
  .crf-base-footer {
    flex-direction: column;
    align-items: stretch;
    gap: var(--crf-spacing-2);
  }
}
</style>
