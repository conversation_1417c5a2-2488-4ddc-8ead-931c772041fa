<template>
  <div class="data-list w-full">
    <!-- 列表头部工具栏 -->
    <div
      v-if="showToolbar"
      class="list-toolbar mb-4 flex items-center justify-between"
    >
      <div class="toolbar-left flex items-center gap-3">
        <!-- 标题 -->
        <h3
          v-if="title"
          class="text-lg font-medium text-gray-900 dark:text-gray-100"
        >
          {{ title }}
        </h3>

        <!-- 批量操作 -->
        <div
          v-if="selectedItems.length > 0"
          class="batch-actions flex items-center gap-2"
        >
          <span class="text-sm text-gray-600 dark:text-gray-400">
            已选择 {{ selectedItems.length }} 项
          </span>
          <button
            v-for="action in batchActions"
            :key="action.key"
            type="button"
            :class="[
              'px-3 py-1 text-sm rounded border',
              action.type === 'danger'
                ? 'border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
            ]"
            @click="handleBatchAction(action)"
          >
            {{ action.label }}
          </button>
        </div>
      </div>

      <div class="toolbar-right flex items-center gap-3">
        <!-- 搜索框 -->
        <div v-if="searchable" class="search-box relative">
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="searchPlaceholder"
            class="w-64 px-3 py-2 pl-9 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @input="handleSearch"
          />
          <svg
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <!-- 视图切换 -->
        <div
          v-if="viewSwitchable"
          class="view-switch flex items-center border border-gray-300 dark:border-gray-600 rounded-md"
        >
          <button
            type="button"
            :class="[
              'px-3 py-2 text-sm',
              viewMode === 'list'
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200',
            ]"
            @click="viewMode = 'list'"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <button
            type="button"
            :class="[
              'px-3 py-2 text-sm',
              viewMode === 'grid'
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200',
            ]"
            @click="viewMode = 'grid'"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
              />
            </svg>
          </button>
        </div>

        <!-- 排序选择 -->
        <div v-if="sortable" class="sort-select">
          <select
            v-model="sortField"
            class="px-3 py-2 text-sm border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @change="handleSort"
          >
            <option value="">默认排序</option>
            <option
              v-for="field in sortFields"
              :key="field.key"
              :value="field.key"
            >
              {{ field.label }}
            </option>
          </select>
        </div>

        <!-- 刷新按钮 -->
        <button
          v-if="refreshable"
          type="button"
          class="p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          @click="handleRefresh"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-overlay flex items-center justify-center py-12"
    >
      <div class="flex items-center gap-3">
        <svg
          class="animate-spin w-5 h-5 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          />
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        <span class="text-gray-600 dark:text-gray-400">{{ loadingText }}</span>
      </div>
    </div>

    <!-- 列表内容 -->
    <div v-else-if="filteredData.length > 0" class="list-content">
      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="list-view space-y-2">
        <div
          v-for="(item, index) in paginatedData"
          :key="getItemKey(item, index)"
          :class="[
            'list-item p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800',
            'hover:shadow-md transition-all duration-200',
            {
              'ring-2 ring-blue-500 ring-opacity-50': isItemSelected(item),
              'cursor-pointer': itemClickable,
            },
          ]"
          @click="itemClickable && handleItemClick(item, index)"
        >
          <div class="flex items-center gap-4">
            <!-- 选择框 -->
            <div v-if="selectable" class="flex-shrink-0">
              <input
                type="checkbox"
                :checked="isItemSelected(item)"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                @change="toggleItemSelection(item)"
                @click.stop
              />
            </div>

            <!-- 图标/头像 -->
            <div v-if="showIcon" class="flex-shrink-0">
              <slot name="icon" :item="item" :index="index">
                <div
                  class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-gray-500 dark:text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
              </slot>
            </div>

            <!-- 主要内容 -->
            <div class="flex-1 min-w-0">
              <slot name="content" :item="item" :index="index">
                <div class="flex items-center justify-between">
                  <div>
                    <h4
                      class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate"
                    >
                      {{ getItemTitle(item) }}
                    </h4>
                    <p
                      v-if="getItemDescription(item)"
                      class="text-sm text-gray-500 dark:text-gray-400 truncate"
                    >
                      {{ getItemDescription(item) }}
                    </p>
                  </div>
                  <div
                    v-if="getItemMeta(item)"
                    class="text-xs text-gray-400 dark:text-gray-500"
                  >
                    {{ getItemMeta(item) }}
                  </div>
                </div>
              </slot>
            </div>

            <!-- 操作按钮 -->
            <div v-if="actions.length > 0" class="flex-shrink-0">
              <div class="flex items-center gap-2">
                <button
                  v-for="action in getItemActions(item)"
                  :key="action.key"
                  type="button"
                  :class="[
                    'px-2 py-1 text-xs rounded border',
                    action.type === 'danger'
                      ? 'border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20'
                      : action.type === 'primary'
                        ? 'border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20'
                        : 'border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
                  ]"
                  :disabled="action.disabled && action.disabled(item)"
                  @click.stop="handleAction(action, item, index)"
                >
                  {{ action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-else class="grid-view">
        <div :class="['grid gap-4', `grid-cols-${gridCols}`]">
          <div
            v-for="(item, index) in paginatedData"
            :key="getItemKey(item, index)"
            :class="[
              'grid-item p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800',
              'hover:shadow-md transition-all duration-200',
              {
                'ring-2 ring-blue-500 ring-opacity-50': isItemSelected(item),
                'cursor-pointer': itemClickable,
              },
            ]"
            @click="itemClickable && handleItemClick(item, index)"
          >
            <!-- 选择框 -->
            <div v-if="selectable" class="flex justify-end mb-2">
              <input
                type="checkbox"
                :checked="isItemSelected(item)"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                @change="toggleItemSelection(item)"
                @click.stop
              />
            </div>

            <!-- 网格内容 -->
            <slot name="grid-content" :item="item" :index="index">
              <div class="text-center">
                <!-- 图标/头像 -->
                <div v-if="showIcon" class="mb-3 flex justify-center">
                  <slot name="icon" :item="item" :index="index">
                    <div
                      class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="w-8 h-8 text-gray-500 dark:text-gray-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                  </slot>
                </div>

                <!-- 标题和描述 -->
                <h4
                  class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1"
                >
                  {{ getItemTitle(item) }}
                </h4>
                <p
                  v-if="getItemDescription(item)"
                  class="text-xs text-gray-500 dark:text-gray-400 mb-2"
                >
                  {{ getItemDescription(item) }}
                </p>
                <p
                  v-if="getItemMeta(item)"
                  class="text-xs text-gray-400 dark:text-gray-500 mb-3"
                >
                  {{ getItemMeta(item) }}
                </p>

                <!-- 操作按钮 -->
                <div
                  v-if="actions.length > 0"
                  class="flex justify-center gap-1"
                >
                  <button
                    v-for="action in getItemActions(item)"
                    :key="action.key"
                    type="button"
                    :class="[
                      'px-2 py-1 text-xs rounded border',
                      action.type === 'danger'
                        ? 'border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20'
                        : action.type === 'primary'
                          ? 'border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
                    ]"
                    :disabled="action.disabled && action.disabled(item)"
                    @click.stop="handleAction(action, item, index)"
                  >
                    {{ action.label }}
                  </button>
                </div>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state py-12 text-center">
      <slot name="empty">
        <svg
          class="mx-auto w-12 h-12 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        <p class="mt-4 text-gray-500 dark:text-gray-400">{{ emptyText }}</p>
      </slot>
    </div>

    <!-- 分页器 -->
    <div
      v-if="paginated && filteredData.length > 0"
      class="pagination mt-6 flex items-center justify-between"
    >
      <div class="pagination-info text-sm text-gray-600 dark:text-gray-400">
        显示第 {{ (currentPage - 1) * pageSize + 1 }} -
        {{ Math.min(currentPage * pageSize, filteredData.length) }} 条， 共
        {{ filteredData.length }} 条记录
      </div>

      <div class="pagination-controls flex items-center gap-2">
        <button
          type="button"
          :disabled="currentPage === 1"
          class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700"
          @click="goToPage(currentPage - 1)"
        >
          上一页
        </button>

        <div class="page-numbers flex items-center gap-1">
          <button
            v-for="page in visiblePages"
            :key="page"
            type="button"
            :class="[
              'px-3 py-1 text-sm border rounded',
              page === currentPage
                ? 'bg-blue-600 text-white border-blue-600'
                : 'border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700',
            ]"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>

        <button
          type="button"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700"
          @click="goToPage(currentPage + 1)"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 操作定义接口
interface ListAction {
  key: string
  label: string
  type?: 'default' | 'primary' | 'danger'
  disabled?: (item: any) => boolean
  visible?: (item: any) => boolean
}

// 排序字段接口
interface SortField {
  key: string
  label: string
}

// Props定义
interface Props {
  data?: any[]
  actions?: ListAction[]
  batchActions?: ListAction[]
  sortFields?: SortField[]
  title?: string
  loading?: boolean
  loadingText?: string
  emptyText?: string
  searchable?: boolean
  searchPlaceholder?: string
  selectable?: boolean
  paginated?: boolean
  pageSize?: number
  showToolbar?: boolean
  viewSwitchable?: boolean
  sortable?: boolean
  refreshable?: boolean
  itemClickable?: boolean
  showIcon?: boolean
  gridCols?: number
  itemKey?: string | ((item: any) => string)
  titleField?: string
  descriptionField?: string
  metaField?: string
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  actions: () => [],
  batchActions: () => [],
  sortFields: () => [],
  loadingText: '加载中...',
  emptyText: '暂无数据',
  searchPlaceholder: '搜索...',
  pageSize: 12,
  showToolbar: true,
  showIcon: true,
  gridCols: 3,
  itemKey: 'id',
  titleField: 'title',
  descriptionField: 'description',
  metaField: 'meta',
})

// Emits定义
const emit = defineEmits<{
  'item-click': [item: any, index: number]
  action: [action: ListAction, item: any, index: number]
  'batch-action': [action: ListAction, items: any[]]
  sort: [field: string]
  search: [query: string]
  refresh: []
  'selection-change': [items: any[]]
}>()

// 响应式状态
const searchQuery = ref('')
const selectedItems = ref<any[]>([])
const sortField = ref<string>('')
const currentPage = ref(1)
const viewMode = ref<'list' | 'grid'>('list')

// 计算属性
const filteredData = computed(() => {
  let result = [...props.data]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter((item) => {
      const title = getItemTitle(item).toLowerCase()
      const description = getItemDescription(item).toLowerCase()
      return title.includes(query) || description.includes(query)
    })
  }

  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      const aVal = a[sortField.value]
      const bVal = b[sortField.value]

      if (aVal < bVal) return -1
      if (aVal > bVal) return 1
      return 0
    })
  }

  return result
})

const paginatedData = computed(() => {
  if (!props.paginated) return filteredData.value

  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredData.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / props.pageSize)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// 方法
const getItemKey = (item: any, index: number): string => {
  if (typeof props.itemKey === 'function') {
    return props.itemKey(item)
  }
  return item[props.itemKey] || index.toString()
}

const getItemTitle = (item: any): string => {
  return item[props.titleField] || ''
}

const getItemDescription = (item: any): string => {
  return item[props.descriptionField] || ''
}

const getItemMeta = (item: any): string => {
  return item[props.metaField] || ''
}

const getItemActions = (item: any): ListAction[] => {
  return props.actions.filter(
    (action) => !action.visible || action.visible(item),
  )
}

const isItemSelected = (item: any): boolean => {
  const key = getItemKey(item, 0)
  return selectedItems.value.some(
    (selectedItem) => getItemKey(selectedItem, 0) === key,
  )
}

const toggleItemSelection = (item: any) => {
  const key = getItemKey(item, 0)
  const index = selectedItems.value.findIndex(
    (selectedItem) => getItemKey(selectedItem, 0) === key,
  )

  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(item)
  }

  emit('selection-change', [...selectedItems.value])
}

const handleSearch = () => {
  currentPage.value = 1
  emit('search', searchQuery.value)
}

const handleSort = () => {
  emit('sort', sortField.value)
}

const handleItemClick = (item: any, index: number) => {
  emit('item-click', item, index)
}

const handleAction = (action: ListAction, item: any, index: number) => {
  emit('action', action, item, index)
}

const handleBatchAction = (action: ListAction) => {
  emit('batch-action', action, [...selectedItems.value])
}

const handleRefresh = () => {
  emit('refresh')
}

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// 监听器
watch(
  () => props.data,
  () => {
    selectedItems.value = []
    currentPage.value = 1
  },
)

// 暴露方法给父组件
defineExpose({
  clearSelection: () => {
    selectedItems.value = []
    emit('selection-change', [])
  },
  selectAll: () => {
    selectedItems.value = [...paginatedData.value]
    emit('selection-change', [...selectedItems.value])
  },
  getSelectedItems: () => [...selectedItems.value],
  refresh: handleRefresh,
  switchView: (mode: 'list' | 'grid') => {
    viewMode.value = mode
  },
})
</script>

<style scoped>
.data-list {
  @apply w-full;
}

.list-item {
  @apply transition-all duration-200;
}

.list-item:hover {
  @apply transform translate-y-0.5;
}

.grid-item {
  @apply transition-all duration-200;
}

.grid-item:hover {
  @apply transform -translate-y-1;
}

.view-switch button {
  @apply transition-all duration-200;
}

.view-switch button:first-child {
  @apply rounded-l-md border-r-0;
}

.view-switch button:last-child {
  @apply rounded-r-md;
}
</style>
