<template>
  <div class="cascader-select w-full" ref="cascaderRef">
    <!-- 标签 -->
    <div v-if="showLabel" class="mb-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ label }}
        <span v-if="required" class="text-red-500 ml-1">*</span>
      </label>
    </div>

    <!-- 选择器触发器 -->
    <div class="cascader-trigger relative" @click="toggleDropdown">
      <div
        :class="[
          'w-full px-3 py-2 border rounded-md cursor-pointer',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
          {
            'border-red-500 focus:ring-red-500 focus:border-red-500': hasError,
            'bg-gray-50 dark:bg-gray-600 opacity-50 cursor-not-allowed':
              disabled,
            'ring-2 ring-blue-500 ring-opacity-50': isOpen,
          },
        ]"
        :disabled="disabled"
      >
        <div class="flex items-center justify-between">
          <!-- 显示选中的值 -->
          <div class="flex-1 min-w-0">
            <div v-if="displayValue" class="flex items-center gap-1 flex-wrap">
              <span
                v-for="(item, index) in displayValue"
                :key="index"
                class="inline-flex items-center gap-1 text-sm"
              >
                {{ item }}
                <svg
                  v-if="index < displayValue.length - 1"
                  class="w-3 h-3 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </span>
            </div>
            <span v-else class="text-gray-500 dark:text-gray-400">{{
              placeholder
            }}</span>
          </div>

          <!-- 清除按钮 -->
          <button
            v-if="clearable && displayValue && !disabled"
            type="button"
            class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            @click.stop="clearSelection"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          <!-- 下拉箭头 -->
          <svg
            :class="[
              'w-4 h-4 ml-2 transition-transform duration-200',
              { 'transform rotate-180': isOpen },
            ]"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>
    </div>

    <!-- 下拉面板 -->
    <Teleport to="body">
      <div
        v-if="isOpen"
        :style="dropdownStyle"
        class="cascader-dropdown fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg"
        @click.stop
      >
        <div class="flex min-h-48 max-h-64">
          <!-- 级联面板 -->
          <div
            v-for="(level, levelIndex) in cascaderLevels"
            :key="levelIndex"
            class="cascader-level border-r border-gray-200 dark:border-gray-600 last:border-r-0"
            :style="{ minWidth: `${levelWidth}px` }"
          >
            <!-- 级别标题 -->
            <div
              v-if="showLevelTitles && levelTitles[levelIndex]"
              class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"
            >
              <span
                class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {{ levelTitles[levelIndex] }}
              </span>
            </div>

            <!-- 选项列表 -->
            <div class="cascader-options max-h-48 overflow-y-auto">
              <div
                v-for="option in level"
                :key="option[valueKey]"
                :class="[
                  'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                  'hover:bg-gray-100 dark:hover:bg-gray-700',
                  {
                    'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400':
                      isSelected(option, levelIndex),
                    'text-gray-900 dark:text-gray-100': !isSelected(
                      option,
                      levelIndex,
                    ),
                  },
                ]"
                @click="selectOption(option, levelIndex)"
              >
                <div class="flex items-center justify-between">
                  <span>{{ option[labelKey] }}</span>
                  <div class="flex items-center gap-1">
                    <!-- 选中标记 -->
                    <svg
                      v-if="isSelected(option, levelIndex)"
                      class="w-4 h-4 text-blue-600 dark:text-blue-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <!-- 有子级标记 -->
                    <svg
                      v-if="hasChildren(option)"
                      class="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div
                v-if="level.length === 0"
                class="px-3 py-4 text-center text-gray-500 dark:text-gray-400 text-sm"
              >
                {{ emptyText }}
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作栏 -->
        <div
          v-if="showFooter"
          class="cascader-footer px-3 py-2 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700"
        >
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ getSelectionInfo() }}
            </span>
            <div class="flex items-center gap-2">
              <button
                type="button"
                class="px-2 py-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                @click="clearSelection"
              >
                清空
              </button>
              <button
                type="button"
                class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                @click="confirmSelection"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message mt-1">
      <p class="text-xs text-red-600 dark:text-red-400">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'

// 选项接口
interface CascaderOption {
  [key: string]: any
  children?: CascaderOption[]
}

// Props定义
interface Props {
  modelValue?: any[]
  options?: CascaderOption[]
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  clearable?: boolean
  showLabel?: boolean
  showFooter?: boolean
  showLevelTitles?: boolean
  levelTitles?: string[]
  valueKey?: string
  labelKey?: string
  childrenKey?: string
  levelWidth?: number
  emptyText?: string
  changeOnSelect?: boolean
  expandTrigger?: 'click' | 'hover'
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
  clearable: true,
  showLabel: true,
  showFooter: false,
  showLevelTitles: false,
  levelTitles: () => [],
  valueKey: 'value',
  labelKey: 'label',
  childrenKey: 'children',
  levelWidth: 160,
  emptyText: '暂无数据',
  changeOnSelect: false,
  expandTrigger: 'click',
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: any[]]
  change: [value: any[], selectedOptions: CascaderOption[]]
  expand: [value: any[], level: number]
}>()

// 响应式状态
const cascaderRef = ref<HTMLElement>()
const isOpen = ref(false)
const selectedPath = ref<any[]>(props.modelValue || [])
const selectedOptions = ref<CascaderOption[]>([])
const dropdownStyle = ref<Record<string, string>>({})
const hasError = ref(false)
const errorMessage = ref('')

// 计算属性
const cascaderLevels = computed(() => {
  const levels: CascaderOption[][] = []
  let currentOptions = props.options || []

  // 第一级
  levels.push(currentOptions)

  // 后续级别
  for (let i = 0; i < selectedPath.value.length; i++) {
    const selectedValue = selectedPath.value[i]
    const selectedOption = currentOptions.find(
      (opt) => opt[props.valueKey] === selectedValue,
    )

    if (selectedOption && selectedOption[props.childrenKey]) {
      currentOptions = selectedOption[props.childrenKey]
      levels.push(currentOptions)
    } else {
      break
    }
  }

  return levels
})

const displayValue = computed(() => {
  if (selectedOptions.value.length === 0) return null
  return selectedOptions.value.map((opt) => opt[props.labelKey])
})

// 方法
const toggleDropdown = () => {
  if (props.disabled) return

  if (isOpen.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

const openDropdown = async () => {
  isOpen.value = true
  await nextTick()
  updateDropdownPosition()
}

const closeDropdown = () => {
  isOpen.value = false
}

const updateDropdownPosition = () => {
  if (!cascaderRef.value) return

  const rect = cascaderRef.value.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  const dropdownHeight = 300 // 估算高度

  let top = rect.bottom + 4
  if (top + dropdownHeight > viewportHeight) {
    top = rect.top - dropdownHeight - 4
  }

  dropdownStyle.value = {
    top: `${top}px`,
    left: `${rect.left}px`,
    minWidth: `${rect.width}px`,
  }
}

const selectOption = (option: CascaderOption, levelIndex: number) => {
  // 更新选中路径
  const newPath = [
    ...selectedPath.value.slice(0, levelIndex),
    option[props.valueKey],
  ]
  selectedPath.value = newPath

  // 更新选中选项
  updateSelectedOptions()

  // 触发展开事件
  emit('expand', newPath, levelIndex)

  // 如果允许中间选择或没有子级，触发change事件
  if (props.changeOnSelect || !hasChildren(option)) {
    emitChange()

    // 如果没有子级，关闭下拉框
    if (!hasChildren(option)) {
      closeDropdown()
    }
  }
}

const updateSelectedOptions = () => {
  const options: CascaderOption[] = []
  let currentOptions = props.options || []

  for (const value of selectedPath.value) {
    const option = currentOptions.find((opt) => opt[props.valueKey] === value)
    if (option) {
      options.push(option)
      currentOptions = option[props.childrenKey] || []
    } else {
      break
    }
  }

  selectedOptions.value = options
}

const emitChange = () => {
  emit('update:modelValue', [...selectedPath.value])
  emit('change', [...selectedPath.value], [...selectedOptions.value])
}

const clearSelection = () => {
  selectedPath.value = []
  selectedOptions.value = []
  emitChange()
}

const confirmSelection = () => {
  emitChange()
  closeDropdown()
}

const isSelected = (option: CascaderOption, levelIndex: number): boolean => {
  return selectedPath.value[levelIndex] === option[props.valueKey]
}

const hasChildren = (option: CascaderOption): boolean => {
  return (
    Array.isArray(option[props.childrenKey]) &&
    option[props.childrenKey].length > 0
  )
}

const getSelectionInfo = (): string => {
  if (selectedOptions.value.length === 0) return '未选择'
  return `已选择 ${selectedOptions.value.length} 级`
}

const handleClickOutside = (event: Event) => {
  if (!cascaderRef.value?.contains(event.target as Node)) {
    closeDropdown()
  }
}

// 监听器
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      selectedPath.value = [...newValue]
      updateSelectedOptions()
    } else {
      selectedPath.value = []
      selectedOptions.value = []
    }
  },
  { deep: true },
)

watch(
  () => props.options,
  () => {
    updateSelectedOptions()
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', updateDropdownPosition)
  window.addEventListener('scroll', updateDropdownPosition)

  // 初始化选中选项
  updateSelectedOptions()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updateDropdownPosition)
  window.removeEventListener('scroll', updateDropdownPosition)
})
</script>

<style scoped>
.cascader-select {
  @apply relative;
}

.cascader-trigger {
  @apply transition-all duration-200;
}

.cascader-dropdown {
  @apply transition-all duration-200;
  animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cascader-level {
  @apply flex-shrink-0;
}

.cascader-options {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.cascader-options::-webkit-scrollbar {
  width: 6px;
}

.cascader-options::-webkit-scrollbar-track {
  background: transparent;
}

.cascader-options::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

.cascader-options::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}
</style>
