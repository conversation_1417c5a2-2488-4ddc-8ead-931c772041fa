import { withInstallByCode } from '@crf/shared-utils'
import BaseComponent from './BaseComponent.vue'
import CrfBaseContainer from './CrfBaseContainer.vue'
import CascaderSelect from './CascaderSelect.vue'
import DataTable from './DataTable.vue'
import DataList from './DataList.vue'

// 导出类型
export type {
  BaseComponentProps,
  BaseComponentSlots,
  BaseComponentEmits,
  BaseComponentConfig,
} from './types'

// 导出 Schema
export * from './base-schema'

// 主要导出（简化版本避免类型错误）
export { default as CrfBaseComponent } from './BaseComponent.vue'
export { default as CrfCascaderSelect } from './CascaderSelect.vue'
export { default as CrfDataTable } from './DataTable.vue'
export { default as CrfDataList } from './DataList.vue'

export { CrfBaseContainer }

// 新增基础组件导出
export { CascaderSelect, DataTable, DataList }

// 默认导出
export default BaseComponent

// 组件实例类型
export type BaseComponentInstance = InstanceType<typeof BaseComponent>
export type CascaderSelectInstance = InstanceType<typeof CascaderSelect>
export type DataTableInstance = InstanceType<typeof DataTable>
export type DataListInstance = InstanceType<typeof DataList>

// 单独导出原始组件
export { BaseComponent }
