import { Type } from '@sinclair/typebox'

/**
 * 医疗字段通用配置混入
 * 用于CRF组件的医疗相关配置，避免重复定义
 */
export const medicalFieldsMixin = {
  // 医疗字段类型
  medicalType: Type.Optional(
    Type.Union(
      [
        Type.Literal('general'), // 通用
        Type.Literal('patient-info'), // 患者信息
        Type.Literal('diagnosis'), // 诊断
        Type.Literal('symptom'), // 症状
        Type.Literal('treatment'), // 治疗方案
        Type.Literal('assessment'), // 评估量表
        Type.Literal('risk-factor'), // 危险因素
        Type.Literal('medication'), // 用药
        Type.Literal('vital-signs'), // 生命体征
        Type.Literal('laboratory'), // 实验室检查
        Type.Literal('imaging'), // 影像学检查
        Type.Literal('procedure'), // 操作程序
        Type.Literal('adverse-event'), // 不良事件
      ],
      {
        description: '医疗字段类型',
        default: 'general',
      },
    ),
  ),

  // 医疗字段编码
  fieldCode: Type.Optional(
    Type.String({
      description: '医疗字段编码，用于数据标准化和互操作性',
      pattern: '^[A-Z0-9_]{2,20}$',
    }),
  ),

  // 数据收集要求
  dataCollection: Type.Optional(
    Type.Union(
      [
        Type.Literal('required'), // 必填
        Type.Literal('optional'), // 可选
        Type.Literal('conditional'), // 条件性
        Type.Literal('derived'), // 衍生字段
      ],
      {
        description: '数据收集要求',
        default: 'optional',
      },
    ),
  ),

  // 启用医疗数据验证
  enableMedicalValidation: Type.Optional(
    Type.Boolean({
      description: '是否启用医疗数据验证规则',
      default: false,
    }),
  ),

  // 医疗编码系统
  codingSystem: Type.Optional(
    Type.Union(
      [
        Type.Literal('ICD-10'), // 国际疾病分类
        Type.Literal('ICD-11'), // 国际疾病分类第11版
        Type.Literal('SNOMED-CT'), // 系统化医学术语
        Type.Literal('LOINC'), // 实验室检验编码
        Type.Literal('RxNorm'), // 药物编码
        Type.Literal('CPT'), // 操作术语编码
        Type.Literal('CUSTOM'), // 自定义编码
      ],
      {
        description: '使用的医疗编码系统',
        default: 'CUSTOM',
      },
    ),
  ),

  // 数据质量要求
  dataQuality: Type.Optional(
    Type.Object(
      {
        // 完整性要求
        completeness: Type.Optional(
          Type.Number({
            description: '数据完整性要求百分比',
            minimum: 0,
            maximum: 100,
            default: 95,
          }),
        ),

        // 一致性检查
        consistencyCheck: Type.Optional(
          Type.Boolean({
            description: '是否需要一致性检查',
            default: false,
          }),
        ),

        // 时效性要求
        timeliness: Type.Optional(
          Type.Number({
            description: '数据时效性要求（小时）',
            minimum: 0,
            default: 24,
          }),
        ),
      },
      {
        description: '数据质量要求配置',
      },
    ),
  ),

  // 临床意义
  clinicalSignificance: Type.Optional(
    Type.Object(
      {
        // 临床重要性级别
        importance: Type.Optional(
          Type.Union(
            [
              Type.Literal('critical'), // 关键
              Type.Literal('major'), // 主要
              Type.Literal('minor'), // 次要
              Type.Literal('informational'), // 信息性
            ],
            {
              description: '临床重要性级别',
              default: 'minor',
            },
          ),
        ),

        // 影响决策
        impactsDecision: Type.Optional(
          Type.Boolean({
            description: '是否影响临床决策',
            default: false,
          }),
        ),

        // 安全相关
        safetyRelated: Type.Optional(
          Type.Boolean({
            description: '是否与患者安全相关',
            default: false,
          }),
        ),
      },
      {
        description: '临床意义配置',
      },
    ),
  ),
}

/**
 * 验证字段通用配置混入
 * 用于所有表单组件的验证相关配置
 */
export const validationFieldsMixin = {
  // 最小长度
  minLength: Type.Optional(
    Type.Number({
      description: '最小字符长度',
      minimum: 0,
    }),
  ),

  // 最大长度
  maxLength: Type.Optional(
    Type.Number({
      description: '最大字符长度',
      minimum: 1,
    }),
  ),

  // 显示字数统计
  showWordLimit: Type.Optional(
    Type.Boolean({
      description: '是否显示字数限制提示',
      default: false,
    }),
  ),

  // 自定义验证规则
  customValidation: Type.Optional(
    Type.Object(
      {
        // 正则表达式
        pattern: Type.Optional(
          Type.String({
            description: '验证正则表达式',
          }),
        ),

        // 自定义验证函数
        validator: Type.Optional(
          Type.String({
            description: '自定义验证函数代码',
          }),
        ),

        // 异步验证
        asyncValidator: Type.Optional(
          Type.String({
            description: '异步验证函数代码',
          }),
        ),

        // 错误消息
        errorMessage: Type.Optional(
          Type.String({
            description: '自定义错误消息',
          }),
        ),

        // 警告消息
        warningMessage: Type.Optional(
          Type.String({
            description: '自定义警告消息',
          }),
        ),
      },
      {
        description: '自定义验证配置',
      },
    ),
  ),

  // 验证触发时机
  validationTrigger: Type.Optional(
    Type.Union(
      [
        Type.Literal('change'), // 值改变时
        Type.Literal('blur'), // 失去焦点时
        Type.Literal('input'), // 输入时
        Type.Literal('submit'), // 提交时
        Type.Literal('manual'), // 手动触发
      ],
      {
        description: '验证触发时机',
        default: 'change',
      },
    ),
  ),

  // 自动验证
  autoValidate: Type.Optional(
    Type.Boolean({
      description: '是否自动验证',
      default: true,
    }),
  ),

  // 验证防抖延迟
  validationDebounce: Type.Optional(
    Type.Number({
      description: '验证防抖延迟（毫秒）',
      minimum: 0,
      default: 300,
    }),
  ),
}

/**
 * UI交互字段通用配置混入
 * 用于所有组件的UI交互相关配置
 */
export const uiInteractionMixin = {
  // 占位符文本
  placeholder: Type.Optional(
    Type.String({
      description: '占位符文本',
    }),
  ),

  // 帮助文本
  helpText: Type.Optional(
    Type.String({
      description: '帮助说明文本',
    }),
  ),

  // 提示文本
  tooltip: Type.Optional(
    Type.String({
      description: '鼠标悬停提示文本',
    }),
  ),

  // 自动聚焦
  autofocus: Type.Optional(
    Type.Boolean({
      description: '是否自动聚焦',
      default: false,
    }),
  ),

  // 清除按钮
  clearable: Type.Optional(
    Type.Boolean({
      description: '是否显示清除按钮',
      default: false,
    }),
  ),

  // 前缀图标
  prefixIcon: Type.Optional(
    Type.String({
      description: '前缀图标名称',
    }),
  ),

  // 后缀图标
  suffixIcon: Type.Optional(
    Type.String({
      description: '后缀图标名称',
    }),
  ),

  // 加载状态
  loading: Type.Optional(
    Type.Boolean({
      description: '是否显示加载状态',
      default: false,
    }),
  ),

  // 动画效果
  animation: Type.Optional(
    Type.Object(
      {
        // 是否启用动画
        enabled: Type.Optional(
          Type.Boolean({
            description: '是否启用动画效果',
            default: true,
          }),
        ),

        // 动画类型
        type: Type.Optional(
          Type.Union(
            [
              Type.Literal('fade'), // 渐变
              Type.Literal('slide'), // 滑动
              Type.Literal('zoom'), // 缩放
              Type.Literal('bounce'), // 弹跳
              Type.Literal('none'), // 无动画
            ],
            {
              description: '动画类型',
              default: 'fade',
            },
          ),
        ),

        // 动画时长
        duration: Type.Optional(
          Type.Number({
            description: '动画时长（毫秒）',
            minimum: 0,
            default: 300,
          }),
        ),
      },
      {
        description: '动画效果配置',
      },
    ),
  ),
}

/**
 * 无障碍支持字段混入
 * 用于所有组件的无障碍功能配置
 */
export const accessibilityMixin = {
  // ARIA标签
  ariaLabel: Type.Optional(
    Type.String({
      description: 'ARIA标签文本',
    }),
  ),

  // ARIA描述
  ariaDescription: Type.Optional(
    Type.String({
      description: 'ARIA描述文本',
    }),
  ),

  // Tab顺序
  tabIndex: Type.Optional(
    Type.Number({
      description: 'Tab键顺序',
      minimum: -1,
    }),
  ),

  // 键盘快捷键
  accessKey: Type.Optional(
    Type.String({
      description: '键盘快捷键',
      maxLength: 1,
    }),
  ),

  // 高对比度支持
  highContrast: Type.Optional(
    Type.Boolean({
      description: '是否支持高对比度模式',
      default: true,
    }),
  ),

  // 屏幕阅读器支持
  screenReader: Type.Optional(
    Type.Object(
      {
        // 是否支持屏幕阅读器
        enabled: Type.Optional(
          Type.Boolean({
            description: '是否支持屏幕阅读器',
            default: true,
          }),
        ),

        // 朗读文本
        readText: Type.Optional(
          Type.String({
            description: '屏幕阅读器朗读文本',
          }),
        ),

        // 跳过内容
        skipContent: Type.Optional(
          Type.Boolean({
            description: '是否允许跳过此内容',
            default: false,
          }),
        ),
      },
      {
        description: '屏幕阅读器配置',
      },
    ),
  ),
}

/**
 * 组合所有通用混入
 */
export const allCommonMixins = {
  ...medicalFieldsMixin,
  ...validationFieldsMixin,
  ...uiInteractionMixin,
  ...accessibilityMixin,
}

/**
 * 创建CRF组件Schema的通用工厂函数
 */
export const createCrfComponentMixins = (
  componentSpecificFields: Record<string, unknown> = {},
  includeMedical: boolean = true,
  includeValidation: boolean = true,
  includeUI: boolean = true,
  includeAccessibility: boolean = true,
) => {
  const mixins: Record<string, unknown> = { ...componentSpecificFields }

  if (includeMedical) {
    Object.assign(mixins, medicalFieldsMixin)
  }

  if (includeValidation) {
    Object.assign(mixins, validationFieldsMixin)
  }

  if (includeUI) {
    Object.assign(mixins, uiInteractionMixin)
  }

  if (includeAccessibility) {
    Object.assign(mixins, accessibilityMixin)
  }

  return mixins
}
