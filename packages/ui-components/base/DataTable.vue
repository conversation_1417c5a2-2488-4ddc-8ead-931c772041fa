<template>
  <div class="data-table w-full">
    <!-- 表格头部工具栏 -->
    <div
      v-if="showToolbar"
      class="table-toolbar mb-4 flex items-center justify-between"
    >
      <div class="toolbar-left flex items-center gap-3">
        <!-- 标题 -->
        <h3
          v-if="title"
          class="text-lg font-medium text-gray-900 dark:text-gray-100"
        >
          {{ title }}
        </h3>

        <!-- 批量操作 -->
        <div
          v-if="selectedRows.length > 0"
          class="batch-actions flex items-center gap-2"
        >
          <span class="text-sm text-gray-600 dark:text-gray-400">
            已选择 {{ selectedRows.length }} 项
          </span>
          <button
            v-for="action in batchActions"
            :key="action.key"
            type="button"
            :class="[
              'px-3 py-1 text-sm rounded border',
              action.type === 'danger'
                ? 'border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
            ]"
            @click="handleBatchAction(action)"
          >
            {{ action.label }}
          </button>
        </div>
      </div>

      <div class="toolbar-right flex items-center gap-3">
        <!-- 搜索框 -->
        <div v-if="searchable" class="search-box relative">
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="searchPlaceholder"
            class="w-64 px-3 py-2 pl-9 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            @input="handleSearch"
          />
          <svg
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <!-- 列设置 -->
        <button
          v-if="columnConfigurable"
          type="button"
          class="p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          @click="showColumnConfig = !showColumnConfig"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
            />
          </svg>
        </button>

        <!-- 刷新按钮 -->
        <button
          v-if="refreshable"
          type="button"
          class="p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          @click="handleRefresh"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 表格容器 -->
    <div
      class="table-container border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden"
    >
      <!-- 加载状态 -->
      <div
        v-if="loading"
        class="loading-overlay flex items-center justify-center py-12"
      >
        <div class="flex items-center gap-3">
          <svg
            class="animate-spin w-5 h-5 text-blue-600"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <span class="text-gray-600 dark:text-gray-400">{{
            loadingText
          }}</span>
        </div>
      </div>

      <!-- 表格主体 -->
      <div v-else class="table-wrapper overflow-x-auto">
        <table class="w-full">
          <!-- 表头 -->
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <!-- 选择列 -->
              <th v-if="selectable" class="w-12 px-4 py-3">
                <input
                  type="checkbox"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                  @change="toggleSelectAll"
                />
              </th>

              <!-- 数据列 -->
              <th
                v-for="column in visibleColumns"
                :key="column.key"
                :class="[
                  'px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider',
                  {
                    'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600':
                      column.sortable,
                    'w-auto': !column.width,
                    [`w-${column.width}`]: column.width,
                  },
                ]"
                @click="column.sortable && handleSort(column.key)"
              >
                <div class="flex items-center gap-2">
                  <span>{{ column.title }}</span>
                  <div v-if="column.sortable" class="sort-icons flex flex-col">
                    <svg
                      :class="[
                        'w-3 h-3',
                        sortField === column.key && sortOrder === 'asc'
                          ? 'text-blue-600'
                          : 'text-gray-400',
                      ]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                      />
                    </svg>
                    <svg
                      :class="[
                        'w-3 h-3 -mt-1',
                        sortField === column.key && sortOrder === 'desc'
                          ? 'text-blue-600'
                          : 'text-gray-400',
                      ]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      />
                    </svg>
                  </div>
                </div>
              </th>

              <!-- 操作列 -->
              <th
                v-if="actions.length > 0"
                class="w-32 px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                操作
              </th>
            </tr>
          </thead>

          <!-- 表体 -->
          <tbody
            class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600"
          >
            <tr
              v-for="(row, rowIndex) in paginatedData"
              :key="getRowKey(row, rowIndex)"
              :class="[
                'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150',
                {
                  'bg-blue-50 dark:bg-blue-900/20': isRowSelected(row),
                  'cursor-pointer': rowClickable,
                },
              ]"
              @click="rowClickable && handleRowClick(row, rowIndex)"
            >
              <!-- 选择列 -->
              <td v-if="selectable" class="px-4 py-3">
                <input
                  type="checkbox"
                  :checked="isRowSelected(row)"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                  @change="toggleRowSelection(row)"
                />
              </td>

              <!-- 数据列 -->
              <td
                v-for="column in visibleColumns"
                :key="column.key"
                class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
              >
                <!-- 自定义渲染 -->
                <slot
                  v-if="$slots[`column-${column.key}`]"
                  :name="`column-${column.key}`"
                  :row="row"
                  :column="column"
                  :value="getColumnValue(row, column.key)"
                  :index="rowIndex"
                />
                <!-- 默认渲染 -->
                <span v-else>{{ formatColumnValue(row, column) }}</span>
              </td>

              <!-- 操作列 -->
              <td v-if="actions.length > 0" class="px-4 py-3 text-center">
                <div class="flex items-center justify-center gap-2">
                  <button
                    v-for="action in getRowActions(row)"
                    :key="action.key"
                    type="button"
                    :class="[
                      'px-2 py-1 text-xs rounded border',
                      action.type === 'danger'
                        ? 'border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20'
                        : action.type === 'primary'
                          ? 'border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
                    ]"
                    :disabled="action.disabled && action.disabled(row)"
                    @click.stop="handleAction(action, row, rowIndex)"
                  >
                    {{ action.label }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 空状态 -->
        <div
          v-if="filteredData.length === 0"
          class="empty-state py-12 text-center"
        >
          <svg
            class="mx-auto w-12 h-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p class="mt-4 text-gray-500 dark:text-gray-400">{{ emptyText }}</p>
        </div>
      </div>
    </div>

    <!-- 分页器 -->
    <div
      v-if="paginated && filteredData.length > 0"
      class="pagination mt-4 flex items-center justify-between"
    >
      <div class="pagination-info text-sm text-gray-600 dark:text-gray-400">
        显示第 {{ (currentPage - 1) * pageSize + 1 }} -
        {{ Math.min(currentPage * pageSize, filteredData.length) }} 条， 共
        {{ filteredData.length }} 条记录
      </div>

      <div class="pagination-controls flex items-center gap-2">
        <button
          type="button"
          :disabled="currentPage === 1"
          class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700"
          @click="goToPage(currentPage - 1)"
        >
          上一页
        </button>

        <div class="page-numbers flex items-center gap-1">
          <button
            v-for="page in visiblePages"
            :key="page"
            type="button"
            :class="[
              'px-3 py-1 text-sm border rounded',
              page === currentPage
                ? 'bg-blue-600 text-white border-blue-600'
                : 'border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700',
            ]"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>

        <button
          type="button"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700"
          @click="goToPage(currentPage + 1)"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 列定义接口
interface TableColumn {
  key: string
  title: string
  width?: string
  sortable?: boolean
  formatter?: (value: any, row: any) => string
  visible?: boolean
}

// 操作定义接口
interface TableAction {
  key: string
  label: string
  type?: 'default' | 'primary' | 'danger'
  disabled?: (row: any) => boolean
  visible?: (row: any) => boolean
}

// Props定义
interface Props {
  data?: any[]
  columns?: TableColumn[]
  actions?: TableAction[]
  batchActions?: TableAction[]
  title?: string
  loading?: boolean
  loadingText?: string
  emptyText?: string
  searchable?: boolean
  searchPlaceholder?: string
  selectable?: boolean
  paginated?: boolean
  pageSize?: number
  showToolbar?: boolean
  columnConfigurable?: boolean
  refreshable?: boolean
  rowClickable?: boolean
  rowKey?: string | ((row: any) => string)
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => [],
  actions: () => [],
  batchActions: () => [],
  loadingText: '加载中...',
  emptyText: '暂无数据',
  searchPlaceholder: '搜索...',
  pageSize: 10,
  showToolbar: true,
  rowKey: 'id',
})

// Emits定义
const emit = defineEmits<{
  'row-click': [row: any, index: number]
  action: [action: TableAction, row: any, index: number]
  'batch-action': [action: TableAction, rows: any[]]
  sort: [field: string, order: 'asc' | 'desc']
  search: [query: string]
  refresh: []
  'selection-change': [rows: any[]]
}>()

// 响应式状态
const searchQuery = ref('')
const selectedRows = ref<any[]>([])
const sortField = ref<string>('')
const sortOrder = ref<'asc' | 'desc'>('asc')
const currentPage = ref(1)
const showColumnConfig = ref(false)

// 计算属性
const visibleColumns = computed(() => {
  return props.columns.filter((col) => col.visible !== false)
})

const filteredData = computed(() => {
  let result = [...props.data]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter((row) => {
      return visibleColumns.value.some((col) => {
        const value = getColumnValue(row, col.key)
        return String(value).toLowerCase().includes(query)
      })
    })
  }

  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      const aVal = getColumnValue(a, sortField.value)
      const bVal = getColumnValue(b, sortField.value)

      if (aVal < bVal) return sortOrder.value === 'asc' ? -1 : 1
      if (aVal > bVal) return sortOrder.value === 'asc' ? 1 : -1
      return 0
    })
  }

  return result
})

const paginatedData = computed(() => {
  if (!props.paginated) return filteredData.value

  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredData.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / props.pageSize)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  // 显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

const isAllSelected = computed(() => {
  return (
    paginatedData.value.length > 0 &&
    selectedRows.value.length === paginatedData.value.length
  )
})

const isIndeterminate = computed(() => {
  return (
    selectedRows.value.length > 0 &&
    selectedRows.value.length < paginatedData.value.length
  )
})

// 方法
const getRowKey = (row: any, index: number): string => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row)
  }
  return row[props.rowKey] || index.toString()
}

const getColumnValue = (row: any, key: string): any => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const formatColumnValue = (row: any, column: TableColumn): string => {
  const value = getColumnValue(row, column.key)
  if (column.formatter) {
    return column.formatter(value, row)
  }
  return value != null ? String(value) : ''
}

const getRowActions = (row: any): TableAction[] => {
  return props.actions.filter(
    (action) => !action.visible || action.visible(row),
  )
}

const isRowSelected = (row: any): boolean => {
  const key = getRowKey(row, 0)
  return selectedRows.value.some(
    (selectedRow) => getRowKey(selectedRow, 0) === key,
  )
}

const toggleRowSelection = (row: any) => {
  const key = getRowKey(row, 0)
  const index = selectedRows.value.findIndex(
    (selectedRow) => getRowKey(selectedRow, 0) === key,
  )

  if (index > -1) {
    selectedRows.value.splice(index, 1)
  } else {
    selectedRows.value.push(row)
  }

  emit('selection-change', [...selectedRows.value])
}

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedRows.value = []
  } else {
    selectedRows.value = [...paginatedData.value]
  }

  emit('selection-change', [...selectedRows.value])
}

const handleSort = (field: string) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }

  emit('sort', field, sortOrder.value)
}

const handleSearch = () => {
  currentPage.value = 1
  emit('search', searchQuery.value)
}

const handleRowClick = (row: any, index: number) => {
  emit('row-click', row, index)
}

const handleAction = (action: TableAction, row: any, index: number) => {
  emit('action', action, row, index)
}

const handleBatchAction = (action: TableAction) => {
  emit('batch-action', action, [...selectedRows.value])
}

const handleRefresh = () => {
  emit('refresh')
}

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// 监听器
watch(
  () => props.data,
  () => {
    // 数据变化时重置选择
    selectedRows.value = []
    currentPage.value = 1
  },
)

// 暴露方法给父组件
defineExpose({
  clearSelection: () => {
    selectedRows.value = []
    emit('selection-change', [])
  },
  selectAll: () => {
    selectedRows.value = [...paginatedData.value]
    emit('selection-change', [...selectedRows.value])
  },
  getSelectedRows: () => [...selectedRows.value],
  refresh: handleRefresh,
})
</script>

<style scoped>
.data-table {
  @apply w-full;
}

.table-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.table-wrapper::-webkit-scrollbar {
  height: 6px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}

.sort-icons {
  @apply transition-colors duration-150;
}
</style>
