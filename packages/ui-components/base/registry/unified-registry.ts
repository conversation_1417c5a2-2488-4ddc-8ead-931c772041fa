import type { App, Component } from 'vue'
import { withInstallByCode } from '@crf/shared-utils'

/**
 * 组件元数据接口
 */
export interface ComponentMeta {
  code: string
  name: string
  label: string
  description?: string
  category: ComponentCategory
  icon: string
  iconColor?: string
  order?: number
  visible?: boolean
  tags?: string[]
  version?: string
  author?: string
  deprecated?: boolean
  experimental?: boolean
}

/**
 * 组件分类枚举
 */
export enum ComponentCategory {
  BASIC = 'basic', // 基础组件
  INPUT = 'input', // 输入组件
  DISPLAY = 'display', // 展示组件
  LAYOUT = 'layout', // 布局组件
  NAVIGATION = 'navigation', // 导航组件
  FEEDBACK = 'feedback', // 反馈组件
  DATA = 'data', // 数据组件
  ADVANCED = 'advanced', // 高级组件
  MEDICAL = 'medical', // 医疗专用组件
  CUSTOM = 'custom', // 自定义组件
}

/**
 * 组件注册信息
 */
export interface ComponentRegistration {
  code: string
  component: Component
  configComponent?: Component
  meta: ComponentMeta
  name: string
  schema?: Record<string, unknown>
  installer?: Component
}

/**
 * 搜索配置
 */
export interface SearchConfig {
  query?: string
  category?: ComponentCategory
  tags?: string[]
  includeDeprecated?: boolean
  includeExperimental?: boolean
  limit?: number
  offset?: number
}

/**
 * 搜索结果
 */
export interface SearchResult {
  components: ComponentMeta[]
  total: number
  hasMore: boolean
}

/**
 * 统一组件注册中心
 * 管理所有组件的注册、发现和使用
 */
export class UnifiedComponentRegistry {
  private static instance: UnifiedComponentRegistry
  private componentMap = new Map<string, ComponentRegistration>()
  private categoryMap = new Map<ComponentCategory, Set<string>>()
  private nameMap = new Map<string, string>() // name -> code
  private vueApp?: App
  private initialized = false

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取注册中心实例
   */
  static getInstance(): UnifiedComponentRegistry {
    if (!this.instance) {
      this.instance = new UnifiedComponentRegistry()
    }
    return this.instance
  }

  /**
   * 初始化注册中心
   */
  initialize(app?: App): void {
    if (this.initialized) return

    this.vueApp = app
    this.initialized = true

    // 初始化分类映射
    Object.values(ComponentCategory).forEach((category) => {
      this.categoryMap.set(category, new Set())
    })
  }

  /**
   * 注册组件
   */
  register<T extends Component>(
    code: string,
    component: T,
    meta: Omit<ComponentMeta, 'code'>,
    options: {
      configComponent?: Component
      schema?: Record<string, unknown>
      autoInstall?: boolean
    } = {},
  ): ComponentRegistration {
    // 验证参数
    this.validateRegistration(code, component, meta)

    // 生成组件名
    const componentName = this.generateComponentName(code)

    // 创建安装器
    const installer = withInstallByCode(component, code)

    // 创建注册信息
    const registration: ComponentRegistration = {
      code,
      component: installer,
      configComponent: options.configComponent,
      meta: { ...meta, code },
      name: componentName,
      schema: options.schema,
      installer,
    }

    // 注册到Vue应用
    if (this.vueApp && options.autoInstall !== false) {
      this.vueApp.component(componentName, installer)
    }

    // 保存注册信息
    this.componentMap.set(code, registration)
    this.nameMap.set(componentName, code)

    // 更新分类映射
    const category = meta.category
    if (!this.categoryMap.has(category)) {
      this.categoryMap.set(category, new Set())
    }
    this.categoryMap.get(category)!.add(code)

    return registration
  }

  /**
   * 批量注册组件
   */
  registerBatch(
    components: Array<{
      code: string
      component: Component
      meta: Omit<ComponentMeta, 'code'>
      configComponent?: Component
      schema?: Record<string, unknown>
    }>,
  ): ComponentRegistration[] {
    return components.map(
      ({ code, component, meta, configComponent, schema }) =>
        this.register(code, component, meta, { configComponent, schema }),
    )
  }

  /**
   * 获取组件
   */
  getComponent(code: string): Component | null {
    const registration = this.componentMap.get(code)
    return registration?.component || null
  }

  /**
   * 获取组件配置组件
   */
  getConfigComponent(code: string): Component | null {
    const registration = this.componentMap.get(code)
    return registration?.configComponent || null
  }

  /**
   * 获取组件元数据
   */
  getMeta(code: string): ComponentMeta | null {
    const registration = this.componentMap.get(code)
    return registration?.meta || null
  }

  /**
   * 获取组件Schema
   */
  getSchema(code: string): Record<string, unknown> | null {
    const registration = this.componentMap.get(code)
    return registration?.schema || null
  }

  /**
   * 获取组件名称
   */
  getComponentName(code: string): string | null {
    const registration = this.componentMap.get(code)
    return registration?.name || null
  }

  /**
   * 通过名称获取代码
   */
  getCodeByName(name: string): string | null {
    return this.nameMap.get(name) || null
  }

  /**
   * 获取分类下的所有组件
   */
  getComponentsByCategory(category: ComponentCategory): ComponentMeta[] {
    const codes = this.categoryMap.get(category) || new Set()
    return Array.from(codes)
      .map((code) => this.getMeta(code))
      .filter((meta): meta is ComponentMeta => meta !== null)
      .filter((meta) => meta.visible !== false)
      .sort((a, b) => (a.order || 0) - (b.order || 0))
  }

  /**
   * 搜索组件
   */
  search(config: SearchConfig = {}): SearchResult {
    const {
      query = '',
      category,
      tags = [],
      includeDeprecated = false,
      includeExperimental = true,
      limit = 50,
      offset = 0,
    } = config

    let components = Array.from(this.componentMap.values())
      .map((reg) => reg.meta)
      .filter((meta) => meta.visible !== false)

    // 分类过滤
    if (category) {
      components = components.filter((meta) => meta.category === category)
    }

    // 关键词搜索
    if (query) {
      const searchQuery = query.toLowerCase()
      components = components.filter(
        (meta) =>
          meta.name.toLowerCase().includes(searchQuery) ||
          meta.label.toLowerCase().includes(searchQuery) ||
          meta.description?.toLowerCase().includes(searchQuery) ||
          meta.code.toLowerCase().includes(searchQuery) ||
          meta.tags?.some((tag) => tag.toLowerCase().includes(searchQuery)),
      )
    }

    // 标签过滤
    if (tags.length > 0) {
      components = components.filter((meta) =>
        tags.some((tag) => meta.tags?.includes(tag)),
      )
    }

    // 状态过滤
    if (!includeDeprecated) {
      components = components.filter((meta) => !meta.deprecated)
    }

    if (!includeExperimental) {
      components = components.filter((meta) => !meta.experimental)
    }

    // 排序
    components.sort((a, b) => {
      // 优先级：非废弃 > 非实验性 > 顺序
      if (a.deprecated !== b.deprecated) {
        return a.deprecated ? 1 : -1
      }
      if (a.experimental !== b.experimental) {
        return a.experimental ? 1 : -1
      }
      return (a.order || 0) - (b.order || 0)
    })

    const total = components.length
    const paginatedComponents = components.slice(offset, offset + limit)

    return {
      components: paginatedComponents,
      total,
      hasMore: offset + limit < total,
    }
  }

  /**
   * 获取所有组件代码
   */
  getAllCodes(): string[] {
    return Array.from(this.componentMap.keys())
  }

  /**
   * 获取所有组件元数据
   */
  getAllMeta(): ComponentMeta[] {
    return Array.from(this.componentMap.values()).map((reg) => reg.meta)
  }

  /**
   * 检查组件是否已注册
   */
  isRegistered(code: string): boolean {
    return this.componentMap.has(code)
  }

  /**
   * 注销组件
   */
  unregister(code: string): boolean {
    const registration = this.componentMap.get(code)
    if (!registration) return false

    // 从映射中移除
    this.componentMap.delete(code)
    this.nameMap.delete(registration.name)

    // 从分类映射中移除
    const category = registration.meta.category
    this.categoryMap.get(category)?.delete(code)

    return true
  }

  /**
   * 清空所有注册
   */
  clear(): void {
    this.componentMap.clear()
    this.nameMap.clear()
    this.categoryMap.forEach((set) => set.clear())
  }

  /**
   * 获取注册统计信息
   */
  getStats() {
    const categoryStats = new Map<ComponentCategory, number>()
    Object.values(ComponentCategory).forEach((category) => {
      categoryStats.set(category, this.categoryMap.get(category)?.size || 0)
    })

    const deprecatedCount = Array.from(this.componentMap.values()).filter(
      (reg) => reg.meta.deprecated,
    ).length

    const experimentalCount = Array.from(this.componentMap.values()).filter(
      (reg) => reg.meta.experimental,
    ).length

    return {
      total: this.componentMap.size,
      byCategory: Object.fromEntries(categoryStats),
      deprecated: deprecatedCount,
      experimental: experimentalCount,
    }
  }

  /**
   * 生成组件名称
   */
  private generateComponentName(code: string): string {
    // 转换为PascalCase，添加Crf前缀
    const pascalCode = code
      .split('-')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('')

    return `Crf${pascalCode}`
  }

  /**
   * 验证注册参数
   */
  private validateRegistration(
    code: string,
    component: Component,
    meta: Omit<ComponentMeta, 'code'>,
  ): void {
    if (!code || typeof code !== 'string') {
      throw new Error('组件代码必须是非空字符串')
    }

    if (!component) {
      throw new Error('组件不能为空')
    }

    if (!meta.name || !meta.label) {
      throw new Error('组件元数据必须包含name和label')
    }

    if (this.isRegistered(code)) {
      throw new Error(`组件 "${code}" 已经注册`)
    }

    if (!Object.values(ComponentCategory).includes(meta.category)) {
      throw new Error(`无效的组件分类: ${meta.category}`)
    }
  }
}

/**
 * 导出单例实例
 */
export const componentRegistry = UnifiedComponentRegistry.getInstance()

/**
 * 便捷注册函数
 */
export const registerComponent = (
  code: string,
  component: Component,
  meta: Omit<ComponentMeta, 'code'>,
  options?: {
    configComponent?: Component
    schema?: Record<string, unknown>
    autoInstall?: boolean
  },
) => {
  return componentRegistry.register(code, component, meta, options)
}

/**
 * 便捷获取函数
 */
export const getComponent = (code: string) => {
  return componentRegistry.getComponent(code)
}

export const getComponentMeta = (code: string) => {
  return componentRegistry.getMeta(code)
}

export const searchComponents = (config: SearchConfig) => {
  return componentRegistry.search(config)
}
