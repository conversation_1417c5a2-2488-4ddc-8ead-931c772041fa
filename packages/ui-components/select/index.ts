import Select from './SelectInput.vue'
import { withInstallByCode } from '@crf/shared-utils'

// 主要导出（新的命名规范）
export const CrfSelect = withInstallByCode(Select, 'crf-select')

// 默认导出
export default CrfSelect

// 导出组件和Schema
export { default as CrfSelectComponent } from './SelectInput.vue'

export { default as CrfSelectSchema } from './schema'
export type { CrfSelectSchema as CrfSelectSchemaType } from './schema'

// 导出类型
export type {
  CrfSelectProps,
  CrfSelectConfig,
  SelectOption,
  SelectOptionGroup,
} from '@crf/type-definitions'
