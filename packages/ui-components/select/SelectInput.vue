<template>
  <CrfBaseContainer
    :title="props.title || '下拉选择'"
    :description="props.description"
    :required="props.required"
    :disabled="props.disabled"
    :readonly="props.readonly"
    :error="validationState.status === 'error' ? validationState.message : ''"
    :direction="props.direction"
    :medical-type="props.medicalType"
    :validation-status="validationState.status"
  >
    <!-- 下拉选择器 -->
    <n-select
      v-model:value="internalValue"
      :options="selectOptions"
      :placeholder="props.placeholder || '请选择'"
      :disabled="shouldDisableInEditMode"
      :multiple="props.multiple"
      :clearable="props.clearable"
      :filterable="props.filterable"
      :size="nativeSize as 'small' | 'medium' | 'large'"
      @update:value="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    />

    <!-- 编辑模式下的选项管理 -->
    <div v-if="isInEditorEditMode" class="crf-mt-4">
      <div class="crf-text-sm crf-text-secondary crf-mb-2">选项管理</div>

      <!-- 可拖拽的选项列表 -->
      <draggable
        v-model="localOptions"
        item-key="value"
        ghost-class="crf-drag-ghost"
        chosen-class="crf-drag-chosen"
        drag-class="crf-drag-active"
        animation="200"
        @start="onDragStart"
        @end="onDragEnd"
        @change="onDragChange"
        class="crf-flex crf-flex-col crf-gap-2"
      >
        <template #item="{ element, index }">
          <div
            class="crf-option-wrapper crf-transition-colors"
            :class="{
              'crf-state-draggable': true,
              'crf-state-editing': optionEditStates[index],
            }"
          >
            <!-- 选项内容 -->
            <div class="crf-flex-1">
              <span
                v-if="!optionEditStates[index]"
                class="crf-text-sm crf-text-primary"
                >{{ element.label }}</span
              >
              <n-input
                v-else-if="editingOptions[index]"
                v-model:value="editingOptions[index].label"
                size="small"
                @blur="saveOptionEdit(index)"
                @keyup.enter="saveOptionEdit(index)"
                @keyup.esc="cancelOptionEdit(index)"
                @click.stop
              />
            </div>

            <!-- 编辑操作按钮 -->
            <div class="crf-option-actions">
              <button
                v-if="!optionEditStates[index]"
                class="crf-option-button crf-transition-colors"
                @click.stop="startOptionEdit(index)"
                title="编辑选项"
              >
                <n-icon><PencilOutline /></n-icon>
              </button>
              <button
                class="crf-option-button crf-option-button--danger crf-transition-colors"
                @click.stop="deleteOption(index)"
                title="删除选项"
                :disabled="!canDeleteOption"
              >
                <n-icon><TrashOutline /></n-icon>
              </button>
              <button
                class="crf-option-button crf-cursor-move crf-transition-colors"
                title="拖拽排序"
              >
                <n-icon><SwapVerticalOutline /></n-icon>
              </button>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- 添加选项按钮 -->
    <template #footer v-if="isInEditorEditMode">
      <button
        class="crf-add-option-button crf-transition-colors"
        @click="addOption"
        title="添加选项"
        :disabled="!canAddOption"
      >
        <n-icon><AddOutline /></n-icon>
        <span>添加选项</span>
      </button>
    </template>
  </CrfBaseContainer>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { NSelect, NInput, NIcon } from 'naive-ui'
import {
  AddOutline,
  PencilOutline,
  TrashOutline,
  SwapVerticalOutline,
} from '@vicons/ionicons5'
import draggable from 'vuedraggable'
import CrfBaseContainer from '../base/CrfBaseContainer.vue'
import { useFormComponent } from '@crf/vue-composables/useFormComponent'
import { useOptionManager } from '@crf/vue-composables/useOptionManager'
import type { Option } from '@crf/vue-composables/useOptionManager'

// 定义组件属性
export interface CrfSelectProps {
  /** 组件值 */
  modelValue?: string | number | (string | number)[]
  /** 组件标题 */
  title?: string
  /** 组件描述 */
  description?: string
  /** 占位符 */
  placeholder?: string
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 选项列表 */
  options?: Option[]
  /** 布局方向 */
  direction?: 'horizontal' | 'vertical'
  /** 是否多选 */
  multiple?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否可搜索 */
  filterable?: boolean
  /** 医疗数据类型 */
  medicalType?: 'vital-signs' | 'medication' | 'diagnosis' | 'procedure'
  /** 是否在编辑器编辑模式 */
  isInEditorEditMode?: boolean
  /** 验证规则 */
  validation?: Array<{
    required?: boolean
    message?: string
  }>
}

// 定义组件事件
export interface CrfSelectEmits {
  'update:modelValue': [
    value: string | number | (string | number)[] | undefined,
  ]
  'update:options': [options: Option[]]
  change: [value: string | number | (string | number)[] | undefined]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  validate: [
    result: { status: 'success' | 'warning' | 'error'; message: string },
  ]
}

const props = withDefaults(defineProps<CrfSelectProps>(), {
  direction: 'vertical',
  multiple: false,
  clearable: true,
  filterable: true,
  options: () => [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
  ],
})

const emit = defineEmits<CrfSelectEmits>()

// 使用表单组件通用逻辑
const {
  internalValue,
  validationState,
  shouldDisableInEditMode,
  nativeSize,
  handleChange,
  handleFocus,
  handleBlur,
  validate,
  clearValidation,
} = useFormComponent(props, emit as any, {
  defaultValue: props.multiple ? [] : '',
  validateOnChange: true,
  customValidator: (value: any) => {
    // 检查值是否在允许的选项中
    const validValues = localOptions.value.map((option: any) => option.value)

    if (props.multiple && Array.isArray(value)) {
      const invalidValues = value.filter((v: any) => !validValues.includes(v))
      if (invalidValues.length > 0) {
        return {
          isValid: false,
          status: 'error',
          errors: ['选择的值无效'],
          warnings: [],
          message: '选择的值无效',
        }
      }
    } else if (
      !props.multiple &&
      value !== undefined &&
      value !== null &&
      value !== ''
    ) {
      if (!validValues.includes(value as string | number)) {
        return {
          isValid: false,
          status: 'error',
          errors: ['选择的值无效'],
          warnings: [],
          message: '选择的值无效',
        }
      }
    }

    return {
      isValid: true,
      status: 'success',
      errors: [],
      warnings: [],
      message: '',
    }
  },
})

// 使用选项管理逻辑
const {
  localOptions,
  optionEditStates,
  editingOptions,
  canAddOption,
  canDeleteOption,
  addOption,
  startOptionEdit,
  saveOptionEdit,
  cancelOptionEdit,
  deleteOption,
  onDragStart,
  onDragEnd,
  onDragChange,
} = useOptionManager(
  {
    options: props.options,
    allowEdit: true,
    allowDelete: true,
    allowAdd: true,
    allowSort: true,
    minOptions: 1,
    maxOptions: 50,
    isInEditorEditMode: props.isInEditorEditMode,
  },
  emit as any,
)

// 转换选项格式为 Naive UI 需要的格式
const selectOptions = computed(() => {
  return localOptions.value.map((option: any) => ({
    label: option.label,
    value: option.value,
    disabled: option.disabled,
  }))
})

// 对外暴露的API
defineExpose({
  validate,
  clearValidation,
  internalValue,
  validationState,
})
</script>

<style lang="scss" scoped>
/* 下拉选择器组件的特殊样式覆盖 */
:deep(.n-select) {
  .n-base-selection {
    border-color: var(--crf-color-border-primary);

    &:hover {
      border-color: var(--crf-color-primary);
    }

    &.n-base-selection--focus {
      border-color: var(--crf-color-primary);
      box-shadow: 0 0 0 2px var(--crf-color-primary-light);
    }
  }

  .n-base-selection-placeholder {
    color: var(--crf-color-text-placeholder);
  }

  .n-base-selection-input {
    color: var(--crf-color-text-primary);
  }
}

/* 选项管理区域样式 */
.crf-option-wrapper {
  display: flex;
  align-items: center;
  gap: var(--crf-spacing-2);
  padding: var(--crf-spacing-2);
  border-radius: var(--crf-border-radius-sm);
  border: 1px solid transparent;
  background-color: var(--crf-color-bg-secondary);

  &.crf-state-draggable {
    cursor: move;

    &:hover {
      background-color: var(--crf-color-bg-hover);
      border-color: var(--crf-color-primary);
    }
  }

  &.crf-state-editing {
    background-color: var(--crf-color-bg-hover);
    border-color: var(--crf-color-primary);
  }
}

.crf-option-actions {
  display: flex;
  gap: var(--crf-spacing-1);
  opacity: 0;
  transition: opacity var(--crf-transition-duration-fast) ease;
}

.crf-option-wrapper:hover .crf-option-actions {
  opacity: 1;
}

.crf-option-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--crf-spacing-1);
  border: none;
  background: transparent;
  color: var(--crf-color-text-tertiary);
  border-radius: var(--crf-border-radius-xs);
  cursor: pointer;
  min-width: unset;
  height: 24px;
  width: 24px;

  &:hover {
    color: var(--crf-color-primary);
    background-color: var(--crf-color-bg-hover);
  }

  &--danger:hover {
    color: var(--crf-color-error);
  }

  &.crf-cursor-move {
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }
}
</style>
