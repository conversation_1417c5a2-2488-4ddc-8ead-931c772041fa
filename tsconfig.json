{"$schema": "https://json.schemastore.org/tsconfig", "display": "Root", "compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "bundler", "strict": true, "exactOptionalPropertyTypes": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "composite": true, "declaration": true, "declarationMap": false, "incremental": true, "baseUrl": ".", "paths": {"@crf/types": ["./packages/types/index.ts"], "@crf/types/*": ["./packages/types/*"], "@crf/utils": ["./packages/utils/index.ts"], "@crf/utils/*": ["./packages/utils/*"], "@crf/hooks": ["./packages/hooks/index.ts"], "@crf/hooks/*": ["./packages/hooks/*"], "@crf/constants": ["./packages/constants/index.ts"], "@crf/constants/*": ["./packages/constants/*"], "@crf/components": ["./packages/components/index.ts"], "@crf/components/*": ["./packages/components/*"], "@crf/theme": ["./packages/theme/src/index.ts"], "@crf/theme/*": ["./packages/theme/src/*"], "@crf/composables": ["./packages/composables/src/index.ts"], "@crf/composables/*": ["./packages/composables/src/*"], "@crf/editor-core": ["./packages/editor-core/src/index.ts"], "@crf/editor-core/*": ["./packages/editor-core/src/*"]}}, "references": [{"path": "./packages/types"}, {"path": "./packages/constants"}, {"path": "./packages/utils"}, {"path": "./packages/hooks"}, {"path": "./packages/components"}, {"path": "./packages/theme"}, {"path": "./packages/composables"}, {"path": "./packages/editor-core"}, {"path": "./apps/crf-editor"}], "files": [], "include": []}