# 构建输出
dist/
build/
coverage/
.nuxt/
.next/
docs/.vitepress/cache

# 依赖
node_modules/
.pnpm-store/

# 日志
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 临时文件
.DS_Store
.vscode/
.idea/
*.tmp
*.temp

# 生成的文件
*.d.ts
auto-imports.d.ts
components.d.ts
docs/components.d.ts

# 配置文件（避免格式化破坏）
tsconfig*.json
*.config.js
*.config.ts
vite.config.*
rollup.config.*
webpack.config.*

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# 文档
CHANGELOG.md
CHANGELOG.en-US.md
LICENSE
README.md

# 样式文件中的特殊格式
*.scss
*.sass
*.less
*.styl

# 第三方库
public/
static/
assets/icons/
