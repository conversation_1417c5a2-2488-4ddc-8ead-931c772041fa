version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: crf-postgres
    environment:
      POSTGRES_DB: crf_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - ./docker/data/postgres:/var/lib/postgresql/data
      - ./docker/init-db-enhanced.sh:/docker-entrypoint-initdb.d/01-init.sh:ro
      - ./docker/migrations/schema.sql:/docker-entrypoint-initdb.d/02-schema.sql:ro
      - ./docker/migrations:/docker-entrypoint-initdb.d/migrations:ro
    ports:
      - "5432:5432"
    networks:
      - crf-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d crf_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: crf-redis
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./docker/data/redis:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - crf-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: crf-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - ./docker/data/minio:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - crf-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # CRF 应用 (前端 + 后端)
  crf-app:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: crf-app
    environment:
      # 数据库配置
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: crf_db
      DB_USER: postgres
      DB_PASSWORD: postgres123
      
      # Redis 配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis123
      
      # MinIO 配置
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      MINIO_BUCKET: crf-files
      
      # 应用配置
      SERVER_PORT: 3001
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      FRONTEND_URL: http://localhost
      API_BASE_URL: http://localhost:3001
      
      # 环境
      NODE_ENV: production
      GIN_MODE: release
    volumes:
      - ./docker/logs:/app/logs
      - ./docker/uploads:/app/uploads
      - ./docker/config/.env:/app/.env:ro
    ports:
      - "80:80"
      - "3001:3001"
    networks:
      - crf-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  crf-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local