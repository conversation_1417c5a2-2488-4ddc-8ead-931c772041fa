# CRF 表单编辑器 Docker 部署指南

## 概述

本项目提供了完整的 Docker 部署方案，将前端 Vue3 应用、Go 后端 API、PostgreSQL 数据库、Redis 缓存和 MinIO 对象存储打包为统一的容器化解决方案。

## 架构说明

### 服务组件
- **crf-app**: 主应用容器（包含前端和后端）
- **postgres**: PostgreSQL 15 数据库
- **redis**: Redis 7 缓存服务
- **minio**: MinIO 对象存储服务

### 网络架构
- 所有服务运行在 `crf-network` 桥接网络中
- 前端通过 Nginx 反向代理访问后端 API
- 数据库、缓存和存储服务仅在内部网络可访问

## 快速开始

### 1. 一键部署
```bash
# 克隆项目
git clone <repository-url>
cd crf-frontend

# 执行一键部署脚本
chmod +x deploy.sh
./deploy.sh