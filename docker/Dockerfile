# 多阶段构建 Dockerfile
# 阶段1: 构建前端应用
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@9.15.4

# 复制 package.json 和 pnpm 相关文件
COPY ../package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY ../apps/crf-editor/package.json ./apps/crf-editor/
COPY ../packages ./packages/
COPY ../internal ./internal/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY .. .

# 构建前端应用
RUN pnpm run build

# 阶段2: 构建后端应用
FROM golang:1.21-alpine AS backend-builder

# 安装必要的工具
RUN apk add --no-cache git

# 设置工作目录
WORKDIR /app

# 复制 go mod 文件
COPY ../backend/go.mod backend/go.sum ./

# 下载依赖
RUN go mod download

# 复制后端源代码
COPY ../backend .

# 构建后端应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 阶段3: 最终运行镜像
FROM alpine:latest

# 安装必要的运行时依赖
RUN apk --no-cache add ca-certificates tzdata nginx postgresql-client redis

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 创建必要的目录
RUN mkdir -p /app/frontend /app/backend /app/logs /app/data /app/config /var/log/nginx /var/lib/nginx/tmp

# 从构建阶段复制文件
COPY --from=frontend-builder /app/apps/crf-editor/dist /app/frontend
COPY --from=backend-builder /app/main /app/backend/
COPY --from=backend-builder /app/config.yaml /app/config/

# 复制数据库初始化脚本
COPY ../backend/database/schema.sql /app/data/

# 复制配置文件
COPY nginx.conf /etc/nginx/nginx.conf
COPY supervisord.conf /etc/supervisord.conf
COPY docker-entrypoint.sh /app/
COPY .env.docker /app/config/.env

# 设置权限
RUN chown -R appuser:appgroup /app /var/log/nginx /var/lib/nginx && \
    chmod +x /app/docker-entrypoint.sh

# 安装 supervisor 来管理多个服务
RUN apk add --no-cache supervisor

# 暴露端口
EXPOSE 80 3001

# 设置工作目录
WORKDIR /app

# 切换到应用用户
USER appuser

# 启动脚本
ENTRYPOINT ["/app/docker-entrypoint.sh"]