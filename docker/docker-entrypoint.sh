#!/bin/sh

# 等待数据库启动
echo "等待数据库启动..."
while ! nc -z postgres 5432; do
  sleep 1
done
echo "数据库已启动"

# 等待 Redis 启动
echo "等待 Redis 启动..."
while ! nc -z redis 6379; do
  sleep 1
done
echo "Redis 已启动"

# 等待 MinIO 启动
echo "等待 MinIO 启动..."
while ! nc -z minio 9000; do
  sleep 1
done
echo "MinIO 已启动"

# 创建必要的目录
mkdir -p /app/logs /app/uploads

# 设置配置文件
if [ -f /app/config/.env ]; then
    export $(cat /app/config/.env | xargs)
fi

# 启动 supervisor
exec /usr/bin/supervisord -c /etc/supervisord.conf