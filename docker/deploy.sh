#!/bin/bash

# CRF 项目一键部署脚本

set -e

echo "=========================================="
echo "CRF 表单编辑器 Docker 部署脚本"
echo "=========================================="

# 检查 Docker 和 Docker Compose
check_dependencies() {
    echo "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        echo "错误: Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "错误: Docker Compose 未安装"
        exit 1
    fi
    
    echo "✓ Docker 和 Docker Compose 已安装"
}

# 创建必要的目录
setup_directories() {
    echo "创建必要的目录..."
    
    mkdir -p docker/data/postgres
    mkdir -p docker/data/redis
    mkdir -p docker/data/minio
    mkdir -p docker/logs
    mkdir -p docker/uploads
    mkdir -p docker/config
    
    # 设置权限
    chmod 755 docker/data/postgres
    chmod 755 docker/data/redis
    chmod 755 docker/data/minio
    chmod 755 docker/logs
    chmod 755 docker/uploads
    chmod 755 docker/config
    
    echo "✓ 目录创建完成"
}

# 设置配置文件
setup_config() {
    echo "设置配置文件..."
    
    # 复制环境配置
    if [ ! -f docker/config/.env ]; then
        cp docker/.env.docker docker/config/.env
        echo "✓ 环境配置文件已创建"
    else
        echo "✓ 环境配置文件已存在"
    fi
    
    # 设置执行权限
    chmod +x docker/docker-entrypoint.sh
    chmod +x docker/init-db.sh
    
    echo "✓ 配置文件设置完成"
}

# 构建和启动服务
deploy() {
    echo "构建和启动服务..."
    
    # 构建镜像
    echo "构建 Docker 镜像..."
    docker-compose build --no-cache
    
    # 启动服务
    echo "启动服务..."
    docker-compose up -d
    
    echo "✓ 服务启动完成"
}

# 等待服务启动
wait_for_services() {
    echo "等待服务启动..."
    
    # 等待数据库
    echo "等待数据库启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec crf-postgres pg_isready -U postgres -d crf_db &> /dev/null; then
            echo "✓ 数据库已启动"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        echo "错误: 数据库启动超时"
        exit 1
    fi
    
    # 等待应用
    echo "等待应用启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:80/health &> /dev/null; then
            echo "✓ 应用已启动"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        echo "警告: 应用启动检查超时，请手动检查"
    fi
}

# 显示部署信息
show_info() {
    echo ""
    echo "=========================================="
    echo "部署完成！"
    echo "=========================================="
    echo ""
    echo "服务访问地址:"
    echo "  前端应用: http://localhost"
    echo "  后端API:  http://localhost:3001"
    echo "  MinIO控制台: http://localhost:9001"
    echo ""
    echo "数据库连接信息:"
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: crf_db"
    echo "  用户: postgres"
    echo ""
    echo "数据存储位置:"
    echo "  数据库数据: ./docker/data/postgres"
    echo "  Redis数据: ./docker/data/redis"
    echo "  MinIO数据: ./docker/data/minio"
    echo "  应用日志: ./docker/logs"
    echo "  上传文件: ./docker/uploads"
    echo ""
    echo "常用命令:"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo "  进入应用: docker exec -it crf-app sh"
    echo ""
    echo "如需帮助，请查看 docker/Makefile 中的命令"
    echo "=========================================="
}

# 主函数
main() {
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            setup_directories
            setup_config
            deploy
            wait_for_services
            show_info
            ;;
        "clean")
            echo "清理所有 Docker 资源..."
            docker-compose down -v
            docker system prune -f
            echo "✓ 清理完成"
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "status")
            docker-compose ps
            ;;
        "help")
            echo "用法: $0 [deploy|clean|logs|status|help]"
            echo "  deploy - 部署应用 (默认)"
            echo "  clean  - 清理资源"
            echo "  logs   - 查看日志"
            echo "  status - 查看状态"
            echo "  help   - 显示帮助"
            ;;
        *)
            echo "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"