# 生产环境 Docker Compose 配置
version: '3.8'

services:
  # 使用外部数据库的配置
  crf-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crf-app-prod
    environment:
      # 生产环境数据库配置
      DATABASE_HOST: *************
      DATABASE_PORT: 5432
      DATABASE_USER: postgres
      DATABASE_PASSWORD: Ytsnjzhq@2025
      DATABASE_NAME: crf_db
      DATABASE_URL: ******************************************************/crf_db?sslmode=disable
      
      # 生产环境 Redis 配置
      REDIS_HOST: *************
      REDIS_PORT: 6379
      REDIS_PASSWORD: Ytsnjzhq@2025
      
      # 生产环境 MinIO 配置
      MINIO_ENDPOINT: *************:9000
      MINIO_ACCESS_KEY: minio_rRkfbm
      MINIO_SECRET_KEY: minio_BcMiih
      
      # 应用配置
      SERVER_PORT: 3001
      SERVER_HOST: 0.0.0.0
      JWT_SECRET: jzsy-web-crf-20220324
      ENVIRONMENT: production
      
      # 前端配置
      VITE_API_BASE_URL: https://your-domain.com/api
      VITE_APP_TITLE: CRF表单编辑器
    volumes:
      - ./docker/logs:/app/logs
      - ./docker/uploads:/app/uploads
      - ./docker/config:/app/config
    ports:
      - "80:80"
      - "3001:3001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3