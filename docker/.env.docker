# Docker 环境配置
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=Ytsnjzhq@2025
DATABASE_NAME=crf_db
DATABASE_URL=*************************************************/crf_db?sslmode=disable

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=Ytsnjzhq@2025

MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minio_rRkfbm
MINIO_SECRET_KEY=minio_BcMiih

SERVER_PORT=3001
SERVER_HOST=0.0.0.0
JWT_SECRET=jzsy-web-crf-20220324
ENVIRONMENT=production

VITE_API_BASE_URL=http://localhost:3001
VITE_APP_TITLE=CRF表单编辑器