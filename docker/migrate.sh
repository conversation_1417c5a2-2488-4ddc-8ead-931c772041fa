#!/bin/bash

# 数据库迁移管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if ! docker exec crf-postgres pg_isready -U postgres -d crf_db &> /dev/null; then
        log_error "数据库未运行或连接失败"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 列出所有迁移文件
list_migrations() {
    log_info "可用的迁移文件:"
    echo ""
    
    if [ -d "migrations" ]; then
        for file in migrations/*.sql; do
            if [ -f "$file" ]; then
                echo "  - $(basename "$file")"
            fi
        done
    else
        log_warning "migrations 目录不存在"
    fi
}

# 执行特定迁移
run_migration() {
    local migration_file="$1"
    
    if [ -z "$migration_file" ]; then
        log_error "请指定迁移文件名"
        exit 1
    fi
    
    local full_path="migrations/$migration_file"
    
    if [ ! -f "$full_path" ]; then
        log_error "迁移文件不存在: $full_path"
        exit 1
    fi
    
    log_info "执行迁移: $migration_file"
    
    # 复制文件到容器并执行
    docker cp "$full_path" crf-postgres:/tmp/migration.sql
    
    if docker exec crf-postgres psql -U postgres -d crf_db -f /tmp/migration.sql; then
        log_success "迁移执行成功: $migration_file"
        
        # 记录迁移历史
        docker exec crf-postgres psql -U postgres -d crf_db -c "
            INSERT INTO migrations (version, name, applied, applied_at) 
            VALUES ('$(basename "$migration_file" .sql)', '$migration_file', true, CURRENT_TIMESTAMP)
            ON CONFLICT (version) DO UPDATE SET 
                applied = true, 
                applied_at = CURRENT_TIMESTAMP;
        " || log_warning "无法记录迁移历史（可能是因为 migrations 表不存在）"
    else
        log_error "迁移执行失败: $migration_file"
        exit 1
    fi
    
    # 清理临时文件
    docker exec crf-postgres rm -f /tmp/migration.sql
}

# 执行所有迁移
run_all_migrations() {
    log_info "执行所有迁移文件..."
    
    if [ ! -d "migrations" ]; then
        log_warning "migrations 目录不存在"
        return
    fi
    
    local success_count=0
    local fail_count=0
    
    for file in migrations/*.sql; do
        if [ -f "$file" ]; then
            local filename=$(basename "$file")
            log_info "执行迁移: $filename"
            
            # 复制文件到容器并执行
            docker cp "$file" crf-postgres:/tmp/migration.sql
            
            if docker exec crf-postgres psql -U postgres -d crf_db -f /tmp/migration.sql 2>/dev/null; then
                log_success "✓ $filename"
                success_count=$((success_count + 1))
                
                # 记录迁移历史
                docker exec crf-postgres psql -U postgres -d crf_db -c "
                    INSERT INTO migrations (version, name, applied, applied_at) 
                    VALUES ('$(basename "$filename" .sql)', '$filename', true, CURRENT_TIMESTAMP)
                    ON CONFLICT (version) DO UPDATE SET 
                        applied = true, 
                        applied_at = CURRENT_TIMESTAMP;
                " 2>/dev/null || true
            else
                log_warning "✗ $filename (可能已经应用过)"
                fail_count=$((fail_count + 1))
            fi
            
            # 清理临时文件
            docker exec crf-postgres rm -f /tmp/migration.sql
        fi
    done
    
    echo ""
    log_info "迁移执行完成: 成功 $success_count 个，跳过 $fail_count 个"
}

# 查看迁移历史
show_migration_history() {
    log_info "迁移历史:"
    echo ""
    
    docker exec crf-postgres psql -U postgres -d crf_db -c "
        SELECT version, name, applied, applied_at 
        FROM migrations 
        ORDER BY applied_at DESC;
    " 2>/dev/null || {
        log_warning "无法查看迁移历史（可能是因为 migrations 表不存在）"
    }
}

# 回滚迁移（仅标记为未应用）
rollback_migration() {
    local migration_version="$1"
    
    if [ -z "$migration_version" ]; then
        log_error "请指定要回滚的迁移版本"
        exit 1
    fi
    
    log_warning "标记迁移为未应用: $migration_version"
    
    docker exec crf-postgres psql -U postgres -d crf_db -c "
        UPDATE migrations 
        SET applied = false, applied_at = NULL 
        WHERE version = '$migration_version';
    " || {
        log_error "回滚失败"
        exit 1
    }
    
    log_success "迁移已标记为未应用"
}

# 创建新的迁移文件
create_migration() {
    local migration_name="$1"
    
    if [ -z "$migration_name" ]; then
        log_error "请指定迁移名称"
        exit 1
    fi
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local filename="${timestamp}_${migration_name}.sql"
    local filepath="migrations/$filename"
    
    mkdir -p migrations
    
    cat > "$filepath" << EOF
-- Migration: $filename
-- Description: $migration_name
-- Created: $(date)

BEGIN;

-- 在这里添加你的迁移 SQL

-- 记录迁移
INSERT INTO migrations (version, name, applied, applied_at) 
VALUES ('${timestamp}_${migration_name}', '$filename', true, CURRENT_TIMESTAMP)
ON CONFLICT (version) DO UPDATE SET 
    applied = true, 
    applied_at = CURRENT_TIMESTAMP;

COMMIT;
EOF
    
    log_success "迁移文件已创建: $filepath"
}

# 数据库备份
backup_database() {
    local backup_name="${1:-$(date +%Y%m%d_%H%M%S)}"
    local backup_file="backups/db_backup_${backup_name}.sql"
    
    mkdir -p backups
    
    log_info "创建数据库备份: $backup_file"
    
    docker exec crf-postgres pg_dump -U postgres crf_db > "$backup_file"
    
    log_success "备份已创建: $backup_file"
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_warning "恢复数据库: $backup_file"
    read -p "这将覆盖当前数据库，确定继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker exec -i crf-postgres psql -U postgres crf_db < "$backup_file"
        log_success "数据库恢复完成"
    else
        log_info "操作已取消"
    fi
}

# 显示帮助
show_help() {
    echo "数据库迁移管理脚本"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  list                    - 列出所有迁移文件"
    echo "  run <file>             - 执行指定的迁移文件"
    echo "  run-all                - 执行所有迁移文件"
    echo "  history                - 查看迁移历史"
    echo "  rollback <version>     - 回滚指定迁移（仅标记）"
    echo "  create <name>          - 创建新的迁移文件"
    echo "  backup [name]          - 备份数据库"
    echo "  restore <file>         - 恢复数据库"
    echo "  help                   - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 list"
    echo "  $0 run 003_add_rbac_system.sql"
    echo "  $0 run-all"
    echo "  $0 create add_new_table"
    echo "  $0 backup production"
    echo "  $0 restore backups/db_backup_20240101_120000.sql"
}

# 主函数
main() {
    local command="${1:-help}"
    
    # 对于需要数据库连接的命令，先检查连接
    case "$command" in
        "run"|"run-all"|"history"|"rollback"|"backup"|"restore")
            check_database
            ;;
    esac
    
    case "$command" in
        "list")
            list_migrations
            ;;
        "run")
            run_migration "$2"
            ;;
        "run-all")
            run_all_migrations
            ;;
        "history")
            show_migration_history
            ;;
        "rollback")
            rollback_migration "$2"
            ;;
        "create")
            create_migration "$2"
            ;;
        "backup")
            backup_database "$2"
            ;;
        "restore")
            restore_database "$2"
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"