[supervisord]
nodaemon=true
user=appuser
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/app/logs/nginx_error.log
stdout_logfile=/app/logs/nginx_access.log
user=root

[program:backend]
command=/app/backend/main
directory=/app
autostart=true
autorestart=true
stderr_logfile=/app/logs/backend_error.log
stdout_logfile=/app/logs/backend_access.log
user=appuser
environment=
    DATABASE_HOST=postgres,
    DATABASE_PORT=5432,
    DATABASE_USER=postgres,
    DATABASE_PASSWORD=Ytsnjzhq@2025,
    DATABASE_NAME=crf_db,
    REDIS_HOST=redis,
    REDIS_PORT=6379,
    REDIS_PASSWORD=Ytsnjzhq@2025,
    MINIO_ENDPOINT=minio:9000,
    MINIO_ACCESS_KEY=minio_rRkfbm,
    MINIO_SECRET_KEY=minio_BcMiih,
    SERVER_PORT=3001,
    SERVER_HOST=0.0.0.0,
    JWT_SECRET=jzsy-web-crf-20220324,
    ENVIRONMENT=production