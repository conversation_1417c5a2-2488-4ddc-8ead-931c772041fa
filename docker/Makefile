# CRF Docker 部署管理 Makefile

.PHONY: help setup build start stop restart logs clean backup restore migrate status health

# 默认目标
help:
	@echo "CRF Docker 部署管理命令"
	@echo ""
	@echo "基础命令:"
	@echo "  make setup     - 初始化环境（创建目录、设置权限）"
	@echo "  make build     - 构建 Docker 镜像"
	@echo "  make start     - 启动所有服务"
	@echo "  make stop      - 停止所有服务"
	@echo "  make restart   - 重启所有服务"
	@echo "  make logs      - 查看所有服务日志"
	@echo "  make status    - 查看服务状态"
	@echo "  make health    - 执行健康检查"
	@echo ""
	@echo "部署命令:"
	@echo "  make deploy           - 一键部署（推荐）"
	@echo "  make deploy-clean     - 清理后部署"
	@echo "  make deploy-prod      - 生产环境部署"
	@echo ""
	@echo "数据管理:"
	@echo "  make backup           - 备份数据库"
	@echo "  make restore FILE=... - 恢复数据库"
	@echo "  make migrate          - 执行数据库迁移"
	@echo "  make migrate-list     - 列出迁移文件"
	@echo "  make migrate-history  - 查看迁移历史"
	@echo ""
	@echo "维护命令:"
	@echo "  make clean     - 清理所有数据和容器"
	@echo "  make clean-all - 深度清理（包括镜像）"
	@echo "  make update    - 更新并重新部署"
	@echo ""
	@echo "开发命令:"
	@echo "  make dev       - 开发模式启动"
	@echo "  make shell     - 进入应用容器"
	@echo "  make db-shell  - 进入数据库容器"

# 环境设置
setup:
	@echo "初始化环境..."
	@mkdir -p data/postgres data/redis data/minio logs uploads config backups migrations
	@chmod 755 data/postgres data/redis data/minio logs uploads config backups migrations
	@chmod +x deploy.sh migrate.sh docker-entrypoint.sh init-db.sh
	@if [ ! -f config/.env ]; then cp .env.docker config/.env; fi
	@echo "✓ 环境初始化完成"

# 构建镜像
build:
	@echo "构建 Docker 镜像..."
	@docker-compose build --no-cache

# 启动服务
start:
	@echo "启动服务..."
	@docker-compose up -d

# 停止服务
stop:
	@echo "停止服务..."
	@docker-compose down

# 重启服务
restart:
	@echo "重启服务..."
	@docker-compose restart

# 查看日志
logs:
	@docker-compose logs -f

# 查看特定服务日志
logs-app:
	@docker-compose logs -f crf-app

logs-db:
	@docker-compose logs -f postgres

logs-redis:
	@docker-compose logs -f redis

logs-minio:
	@docker-compose logs -f minio

# 查看状态
status:
	@echo "服务状态:"
	@docker-compose ps
	@echo ""
	@echo "容器资源使用:"
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# 健康检查
health:
	@echo "执行健康检查..."
	@./deploy.sh status

# 一键部署
deploy: setup
	@echo "开始一键部署..."
	@./deploy.sh deploy

# 清理后部署
deploy-clean: setup
	@echo "清理后部署..."
	@./deploy.sh deploy --clean

# 生产环境部署
deploy-prod: setup
	@echo "生产环境部署..."
	@./deploy.sh deploy --prod

# 数据库备份
backup:
	@echo "备份数据库..."
	@./migrate.sh backup

# 数据库恢复
restore:
	@if [ -z "$(FILE)" ]; then \
		echo "错误: 请指定备份文件，例如: make restore FILE=backups/db_backup_20240101.sql"; \
		exit 1; \
	fi
	@./migrate.sh restore $(FILE)

# 执行数据库迁移
migrate:
	@echo "执行数据库迁移..."
	@./migrate.sh run-all

# 列出迁移文件
migrate-list:
	@./migrate.sh list

# 查看迁移历史
migrate-history:
	@./migrate.sh history

# 创建新迁移
migrate-create:
	@if [ -z "$(NAME)" ]; then \
		echo "错误: 请指定迁移名称，例如: make migrate-create NAME=add_new_table"; \
		exit 1; \
	fi
	@./migrate.sh create $(NAME)

# 清理数据
clean:
	@echo "清理数据和容器..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f

# 深度清理
clean-all: clean
	@echo "深度清理（包括镜像）..."
	@docker image prune -a -f
	@sudo rm -rf data/*

# 更新部署
update: stop
	@echo "更新并重新部署..."
	@git pull
	@make build
	@make start

# 开发模式
dev:
	@echo "开发模式启动..."
	@docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 进入应用容器
shell:
	@docker exec -it crf-app sh

# 进入数据库容器
db-shell:
	@docker exec -it crf-postgres psql -U postgres -d crf_db

# 进入 Redis 容器
redis-shell:
	@docker exec -it crf-redis redis-cli

# 监控日志
monitor:
	@echo "监控所有服务日志..."
	@docker-compose logs -f --tail=100

# 性能监控
perf:
	@echo "性能监控..."
	@watch -n 2 'docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"'

# 网络诊断
network:
	@echo "网络诊断..."
	@docker network ls
	@echo ""
	@docker network inspect crf_crf-network

# 磁盘使用情况
disk:
	@echo "磁盘使用情况..."
	@du -sh data/*
	@echo ""
	@df -h

# 安全检查
security:
	@echo "安全检查..."
	@docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
		-v $(PWD):/app aquasec/trivy fs /app

# 导出配置
export-config:
	@echo "导出配置..."
	@mkdir -p exports
	@cp config/.env exports/
	@docker-compose config > exports/docker-compose-resolved.yml
	@echo "✓ 配置已导出到 exports/ 目录"

# 快速重启应用
quick-restart:
	@echo "快速重启应用..."
	@docker-compose restart crf-app

# 查看应用版本
version:
	@echo "应用版本信息:"
	@docker exec crf-app cat /app/version.txt 2>/dev/null || echo "版本信息不可用"

# 测试连接
test:
	@echo "测试服务连接..."
	@curl -f http://localhost/health || echo "前端连接失败"
	@curl -f http://localhost:3001/health || echo "后端连接失败"
	@docker exec crf-postgres pg_isready -U postgres -d crf_db || echo "数据库连接失败"
	@docker exec crf-redis redis-cli ping || echo "Redis连接失败"