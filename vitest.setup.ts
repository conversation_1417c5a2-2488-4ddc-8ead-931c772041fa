import { config } from '@vue/test-utils'
import { vi } from 'vitest'
import ResizeObserver from 'resize-observer-polyfill'

// 全局DOM API Polyfills
vi.stubGlobal('ResizeObserver', ResizeObserver)

// 模拟IntersectionObserver
const mockIntersectionObserver = vi.fn()
mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null
})
vi.stubGlobal('IntersectionObserver', mockIntersectionObserver)

// 模拟matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟URL API
vi.stubGlobal('URL', class URL {
  constructor(url: string) {
    return {
      href: url,
      origin: 'http://localhost:3000',
      pathname: '/',
      search: '',
      hash: ''
    }
  }
  static createObjectURL = vi.fn(() => 'mock-object-url')
  static revokeObjectURL = vi.fn()
})

// Vue Test Utils 全局配置
config.global.stubs = {
  // 模拟路由组件
  'router-link': {
    template: '<a><slot /></a>'
  },
  'router-view': {
    template: '<div><slot /></div>'
  },
  
  // 模拟第三方组件库
  'n-button': {
    template: '<button><slot /></button>'
  },
  'n-input': {
    template: '<input />'
  },
  'n-icon': {
    template: '<i><slot /></i>'
  },
  'n-dropdown': {
    template: '<div><slot /></div>'
  },
  'n-tooltip': {
    template: '<div><slot /></div>'
  },
  'n-tag': {
    template: '<span><slot /></span>'
  },
  'n-checkbox': {
    template: '<input type="checkbox" />'
  }
}

// 全局mocks
config.global.mocks = {
  $t: (key: string) => key, // 模拟国际化
  $route: {
    path: '/',
    query: {},
    params: {},
    name: 'home'
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }
}

// 全局插件
config.global.plugins = []

// Console 警告过滤
const originalConsoleWarn = console.warn
console.warn = (...args) => {
  // 过滤掉一些已知的测试环境警告
  const message = args[0]?.toString() || ''
  if (
    message.includes('Vue received a Component which was made a reactive object') ||
    message.includes('Non-function value encountered for slot') ||
    message.includes('[Vue warn]')
  ) {
    return
  }
  originalConsoleWarn.apply(console, args)
}

// 设置测试环境变量
process.env.NODE_ENV = 'test'
process.env.VITE_APP_BASE_URL = 'http://localhost:3000'
