# CRF 表单编辑器

基于 Vue3、TypeScript、UnoCSS、Vite 的现代化医疗专业领域 CRF（临床研究表单）制作工具。

## ✨ 特性

- 🎨 **可视化设计** - 拖拽式表单设计，所见即所得
- 🧩 **丰富组件** - 提供基础、选择、布局、医疗专业等多类组件
- ⚙️ **灵活配置** - 支持组件属性、验证规则、样式等全方位配置
- 📱 **响应式设计** - 支持多端适配，移动端友好
- 🔄 **实时预览** - 编辑过程中实时预览表单效果
- 💾 **导入导出** - 支持 JSON/YAML 格式的配置导入导出
- 🎯 **医疗专业** - 针对医疗领域定制的专业组件和模板
- 🚀 **高性能** - 基于 Vue3 Composition API，性能优异

## 🏗️ 技术架构

### 核心技术栈

- **前端框架**: Vue 3.5+ (Composition API)
- **类型系统**: TypeScript 5.0+
- **构建工具**: Vite 5.0+
- **样式方案**: UnoCSS + SCSS
- **状态管理**: Pinia
- **UI 组件库**: Element Plus + 自定义组件库
- **包管理**: pnpm (workspace)
- **架构模式**: Monorepo

### 项目结构

```
├── apps/                    # 应用层
│   └── crf-editor/         # CRF编辑器应用
│       ├── src/
│       │   ├── components/  # 编辑器组件
│       │   ├── pages/      # 页面组件
│       │   ├── stores/     # 状态管理
│       │   ├── types/      # 类型定义
│       │   └── utils/      # 工具函数
│       └── ...
├── packages/               # 包层
│   ├── components/         # 组件库
│   │   ├── base/          # 基础组件
│   │   ├── med/           # 医疗专业组件
│   │   └── schema/        # 组件Schema
│   ├── constants/          # 常量定义
│   ├── hooks/             # 组合式函数
│   ├── utils/             # 工具函数
│   └── ...                # 其他功能包
└── internal/              # 内部工具包
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 9.0.0

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd crf-frontend

# 安装依赖
pnpm install
```

### 启动开发服务器

```bash
# 启动编辑器应用
pnpm dev

# 或者指定应用启动
pnpm -C apps/crf-editor dev
```

### 构建生产版本

```bash
# 构建所有包
pnpm build

# 构建特定应用
pnpm -C apps/crf-editor build
```

## 📖 使用指南

### 基本使用

1. **设计表单**
    - 从左侧物料区拖拽组件到画布
    - 使用分组功能组织表单结构
    - 实时预览表单效果

2. **配置组件**
    - 选中组件后在右侧配置区设置属性
    - 支持基础属性、状态属性、验证规则等配置
    - 可自定义样式和CSS类名

3. **导出使用**
    - 导出 JSON 配置文件
    - 在应用中使用 FormRenderer 组件渲染表单

### 组件类型

#### 基础组件

- **单行文本** - 用于输入姓名、ID等短文本
- **多行文本** - 用于输入较长的文本内容
- **数字输入** - 用于输入数字类型数据

#### 选择组件

- **下拉选择** - 从预定义选项中选择
- **单选框** - 从多个选项中选择一个
- **复选框** - 从多个选项中选择多个

#### 布局组件

- **栅格布局** - 创建多列布局结构
- **分割线** - 分割不同的内容区域

#### 医疗专业组件

- **日期选择** - 选择日期或日期时间
- **标题组件** - 显示标题文本

### 表单渲染

```vue

<template>
  <FormRenderer
    :schema="formSchema"
    v-model="formData"
    @submit="handleSubmit"
    @change="handleChange"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import FormRenderer from '@/components/form-renderer/form-renderer.vue'

  const formSchema = ref({
    // 表单配置
  })

  const formData = ref({})

  const handleSubmit = (data) => {
    console.log('表单提交:', data)
  }

  const handleChange = (field, value) => {
    console.log('字段变化:', field, value)
  }
</script>
```

## 🔧 开发指南

### 组件开发

#### 创建新组件

1. 在 `packages/components` 下创建组件目录
2. 实现组件的 Vue 文件和类型定义
3. 在 `apps/crf-editor/src/components/config/block.ts` 中注册组件配置
4. 更新组件映射和导出

#### 组件配置 Schema

```typescript
interface ComponentConfigSchema {
  code: ComponentType
  name: string
  description: string
  icon: string
  iconColor?: string
  category: string
  tags?: string[]
  defaultProps: Partial<ComponentProps>
  configSchema: ConfigFieldSchema[]
  preview?: string
}
```

### 状态管理

使用 Pinia 进行状态管理，主要的 store：

- `useEditStore` - 编辑器状态管理
- 支持撤销/重做、组件操作、表单配置等

### 类型系统

项目使用 TypeScript 严格模式，主要类型定义在 `apps/crf-editor/src/typings/index.ts`：

- `FormSchema` - 表单配置主结构
- `ComponentBlock` - 组件块定义
- `ComponentProps` - 组件属性联合类型
- `ValidationRule` - 验证规则类型

## 🧪 测试

```bash
# 运行单元测试
pnpm test

# 运行测试覆盖率
pnpm test:coverage

# 运行 E2E 测试
pnpm test:e2e
```

## 📦 构建和部署

### 构建配置

项目使用 Vite 进行构建，配置文件：

- `apps/crf-editor/vite.config.ts` - 应用构建配置
- `packages/*/build.config.ts` - 包构建配置

### 部署

```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

## 🤝 贡献指南

### 开发规范

1. **代码风格**
    - 使用 ESLint + Prettier
    - 遵循 Vue 3 官方风格指南
    - TypeScript 严格模式

2. **提交规范**
    - 使用 Conventional Commits
    - 格式: `type(scope): description`
    - 类型: feat, fix, docs, style, refactor, test, chore

3. **分支管理**
    - `main` - 主分支，稳定版本
    - `develop` - 开发分支
    - `feature/*` - 功能分支
    - `hotfix/*` - 热修复分支

### 提交流程

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request
5. 代码审查
6. 合并代码

---

**CRF 表单编辑器** - 让医疗表单设计更简单、更专业！
