{"semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100, "endOfLine": "lf", "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.vue", "options": {"parser": "vue"}}, {"files": "*.json", "options": {"parser": "json"}}, {"files": "*.md", "options": {"parser": "markdown", "proseWrap": "always"}}]}